{"timestamp": "2025-08-13 11:25:54", "summary": {"total_tests": 27, "passed_tests": 27, "failed_tests": 0, "success_rate": 100.0}, "details": {"directory_structure": [{"path": "frontend/app.py", "success": true, "description": "Main Flask app"}, {"path": "frontend/config.py", "success": true, "description": "Flask config"}, {"path": "frontend/shared/templates", "success": true, "description": "Templates directory"}, {"path": "frontend/shared/static", "success": true, "description": "Static files directory"}, {"path": "frontend/email/routes", "success": true, "description": "Email routes directory"}, {"path": "frontend/analytics/routes", "success": true, "description": "Analytics routes directory"}, {"path": "frontend/file_management/routes", "success": true, "description": "File management routes directory"}, {"path": "frontend/eqc/routes", "success": true, "description": "EQC routes directory"}, {"path": "frontend/tasks/routes", "success": true, "description": "Tasks routes directory"}, {"path": "frontend/monitoring/routes", "success": true, "description": "Monitoring routes directory"}], "module_imports": [{"module": "frontend.config", "success": true, "description": "Frontend config"}, {"module": "frontend.app", "success": true, "description": "Frontend app"}, {"module": "frontend.email.routes.email_routes", "success": true, "description": "Email routes"}, {"module": "frontend.analytics.routes.analytics_routes", "success": true, "description": "Analytics routes"}, {"module": "frontend.file_management.routes.file_routes", "success": true, "description": "File routes"}, {"module": "frontend.eqc.routes.eqc_routes", "success": true, "description": "EQC routes"}, {"module": "frontend.tasks.routes.task_routes", "success": true, "description": "Task routes"}, {"module": "frontend.monitoring.routes.monitoring_routes", "success": true, "description": "Monitoring routes"}], "flask_app": {"success": true, "description": "Flask app creation"}, "routes": [{"route": "/", "success": true, "status_code": 302, "error": ""}, {"route": "/health", "success": true, "status_code": 200, "error": ""}, {"route": "/email", "success": true, "status_code": 308, "error": ""}, {"route": "/analytics", "success": true, "status_code": 308, "error": ""}, {"route": "/files", "success": true, "status_code": 308, "error": ""}, {"route": "/eqc", "success": true, "status_code": 308, "error": ""}, {"route": "/tasks", "success": true, "status_code": 308, "error": ""}, {"route": "/monitoring", "success": true, "status_code": 308, "error": ""}]}}