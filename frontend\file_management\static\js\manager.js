// 檔案管理模組
// 處理檔案列表、導航、顯示和過濾功能

export class FileManager {
    constructor(api) {
        this.api = api;
        this.allFiles = [];
        this.filteredFiles = [];
    }

    // 載入檔案列表
    async loadFiles(currentPath, subpath = '', statusCallback) {
        statusCallback('正在載入檔案列表...', 'loading');

        // 正規化並顯示路徑
        currentPath = this.normalizePath(currentPath);
        document.getElementById('currentPath').textContent = currentPath;

        try {
            let fullPath = currentPath;
            if (subpath) {
                fullPath = currentPath + '\\' + subpath;
                fullPath = this.normalizePath(fullPath);
            }

            const response = await fetch(`${this.api}/list?path=${encodeURIComponent(fullPath)}`);
            
            if (response.ok) {
                const result = await response.json();
                this.allFiles = result.files || [];
                this.filteredFiles = [...this.allFiles];
                this.displayFiles(this.filteredFiles);
                this.updateFileCount();
                statusCallback(`成功載入 ${this.allFiles.length} 個項目`, 'success');
                return { success: true, files: this.allFiles };
            } else {
                const errorData = await response.json();
                const errorMsg = `載入失敗：${errorData.error || '未知錯誤'}`;
                statusCallback(errorMsg, 'error');
                this.displayFiles([]);
                return { success: false, message: errorMsg };
            }
        } catch (error) {
            const errorMsg = '載入檔案失敗，請檢查網路連接';
            statusCallback(errorMsg, 'error');
            console.error('載入錯誤:', error);
            this.displayFiles([]);
            return { success: false, message: errorMsg };
        }
    }

    // 顯示檔案列表
    displayFiles(files) {
        const container = document.getElementById('fileList');
        
        if (!files || files.length === 0) {
            container.innerHTML = '<div class="empty-state"><i class="fas fa-folder-open"></i><p>此目錄為空或無法存取</p></div>';
            return;
        }
        
        const html = files.map(file => {
            const icon = this.getIcon(file.file_type, file.is_directory);
            const sizeText = file.is_directory ? '目錄' : this.formatSize(file.size_mb);
            
            return `
                <div class="file-item">
                    <div class="file-info">
                        <div class="file-icon ${icon.class}">
                            <i class="${icon.icon}"></i>
                        </div>
                        <div class="file-details">
                            <h4>${file.filename}</h4>
                            <div class="file-meta">
                                ${file.file_type} • ${sizeText} • ${this.formatTime(file.modified_time)}
                            </div>
                        </div>
                    </div>
                    <div class="file-actions">
                        <input type="checkbox" class="file-checkbox" value="${file.filename}" onchange="updateFileSelection()" style="margin-right:8px;">
                        ${file.is_directory ?
                            `<button class="btn btn-primary" onclick="navigateToFolder('${file.filename}')">
                                <i class="fas fa-folder-open"></i> 開啟
                            </button>` :
                            `<button class="btn btn-success" onclick="downloadFile('${file.filename}')" style="margin-left:8px;">
                                <i class="fas fa-download"></i> 下載
                            </button>`
                        }
                    </div>
                </div>
            `;
        }).join('');
        
        container.innerHTML = html;
    }

    // 更新檔案數量顯示
    updateFileCount() {
        const total = this.allFiles.length;
        const filtered = this.filteredFiles.length;
        const fileCount = document.getElementById('fileCount');
        
        if (total === filtered) {
            fileCount.textContent = `共 ${total} 個項目`;
        } else {
            fileCount.textContent = `顯示 ${filtered} / ${total} 個項目`;
        }
    }

    // 路徑正規化函數
    normalizePath(path) {
        // 先移除所有多餘的反斜線
        let normalized = path.replace(/\\+/g, '\\');
        // 確保 UNC 路徑以 \\ 開頭
        if (normalized.startsWith('\\') && !normalized.startsWith('\\\\')) {
            normalized = '\\' + normalized;
        }
        return normalized;
    }

    // 導航到資料夾
    navigateToFolder(folderName, currentPath, loadFilesCallback, debugLog) {
        debugLog(`導航前路徑: ${currentPath}`);
        debugLog(`要進入的資料夾: ${folderName}`);

        // 正規化當前路徑
        currentPath = this.normalizePath(currentPath);

        // 構建新路徑
        let newPath = currentPath;
        if (!newPath.endsWith('\\')) {
            newPath += '\\';
        }
        newPath += folderName;

        // 正規化新路徑
        newPath = this.normalizePath(newPath);

        debugLog(`準備載入新路徑: ${newPath}`);
        debugLog(`已更新currentPath: ${newPath}`);

        return newPath;
    }

    // 導航到上一層
    navigateUp(currentPath, debugLog) {
        // 正規化當前路徑
        currentPath = this.normalizePath(currentPath);

        // 分割路徑，移除空字串
        const pathParts = currentPath.split('\\').filter(p => p);
        debugLog(`當前路徑部分: ${JSON.stringify(pathParts)}`);

        if (pathParts.length > 2) { // 至少保留 server\share
            pathParts.pop();
            const newPath = '\\\\' + pathParts.join('\\');
            debugLog(`上一層路徑: ${newPath}`);
            return newPath;
        } else {
            debugLog('已在根目錄，無法再上一層');
            return currentPath;
        }
    }

    // 下載檔案
    async downloadFile(filename, currentPath, statusCallback) {
        statusCallback(`正在準備下載：${filename}`, 'loading');

        try {
            // 使用 GET 方法下載檔案
            const downloadUrl = `${this.api}/download?path=${encodeURIComponent(currentPath)}&filename=${encodeURIComponent(filename)}`;

            // 創建隱藏的下載連結
            const a = document.createElement('a');
            a.href = downloadUrl;
            a.download = filename;
            a.style.display = 'none';
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);

            statusCallback(`下載開始：${filename}`, 'success');
            return { success: true };
        } catch (error) {
            statusCallback(`下載錯誤：${filename}`, 'error');
            console.error('下載錯誤:', error);
            return { success: false, message: error.message };
        }
    }

    // 過濾檔案
    filterFiles(searchTerm, startDate, endDate) {
        this.filteredFiles = this.allFiles.filter(file => {
            let matches = true;

            // 檔名搜尋
            if (searchTerm) {
                matches = matches && file.filename.toLowerCase().includes(searchTerm.toLowerCase());
            }

            // 日期範圍過濾
            if (startDate || endDate) {
                const fileDate = new Date(file.modified_time);
                if (startDate) {
                    matches = matches && fileDate >= new Date(startDate);
                }
                if (endDate) {
                    matches = matches && fileDate <= new Date(endDate + 'T23:59:59');
                }
            }

            return matches;
        });

        this.displayFiles(this.filteredFiles);
        this.updateFileCount();

        const stats = document.getElementById('filterStats');
        stats.textContent = `找到 ${this.filteredFiles.length} 個符合條件的項目`;
        
        return this.filteredFiles;
    }

    // 清除過濾器
    clearFilters() {
        document.getElementById('searchInput').value = '';
        document.getElementById('startDate').value = '';
        document.getElementById('endDate').value = '';

        this.filteredFiles = [...this.allFiles];
        this.displayFiles(this.filteredFiles);
        this.updateFileCount();

        const stats = document.getElementById('filterStats');
        stats.textContent = `顯示所有 ${this.allFiles.length} 個項目`;
    }

    // 取得圖示
    getIcon(type, isDir) {
        if (isDir) return {class: 'folder', icon: 'fas fa-folder'};
        const map = {
            'Excel 檔案': {class: 'excel', icon: 'fas fa-file-excel'},
            '文字檔案': {class: 'text', icon: 'fas fa-file-alt'},
            '壓縮檔案': {class: 'archive', icon: 'fas fa-file-archive'}
        };
        return map[type] || {class: 'other', icon: 'fas fa-file'};
    }

    // 格式化檔案大小
    formatSize(mb) {
        if (mb >= 1024) return `${(mb/1024).toFixed(2)} GB`;
        if (mb >= 1) return `${mb.toFixed(2)} MB`;
        return `${(mb*1024).toFixed(0)} KB`;
    }

    // 格式化時間
    formatTime(iso) {
        return new Date(iso).toLocaleString('zh-TW', {
            year: 'numeric', month: '2-digit', day: '2-digit',
            hour: '2-digit', minute: '2-digit'
        });
    }

    // 取得所有檔案
    getAllFiles() {
        return this.allFiles;
    }

    // 取得過濾後的檔案
    getFilteredFiles() {
        return this.filteredFiles;
    }

    // 設定檔案列表（用於搜尋結果）
    setFiles(files) {
        this.allFiles = files;
        this.filteredFiles = [...files];
        this.displayFiles(this.filteredFiles);
        this.updateFileCount();
    }
}