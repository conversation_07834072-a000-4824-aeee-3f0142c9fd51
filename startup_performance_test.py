#!/usr/bin/env python3
"""
啟動性能專用測試模組
專門測試服務啟動時間、穩定性和資源使用
創建日期: 2025-08-13
作者: backend-architect agent
"""

import os
import sys
import time
import json
import psutil
import requests
import subprocess
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Any


class StartupPerformanceTester:
    """啟動性能測試器"""
    
    def __init__(self, base_url: str = "http://localhost:8000"):
        self.base_url = base_url
        self.project_root = Path(__file__).parent
        self.results = {
            'test_metadata': {
                'test_start_time': datetime.now().isoformat(),
                'base_url': base_url,
                'tester': 'startup-performance-tester'
            },
            'startup_measurements': {},
            'stability_tests': {},
            'resource_monitoring': {},
            'baseline_performance': {}
        }

    def log_measurement(self, category: str, measurement: str, value: Any, 
                       unit: str = "", success: bool = True, notes: str = ""):
        """記錄測量結果"""
        if category not in self.results:
            self.results[category] = {}
            
        self.results[category][measurement] = {
            'value': value,
            'unit': unit,
            'success': success,
            'notes': notes,
            'timestamp': datetime.now().isoformat()
        }
        
        status = "✅" if success else "❌"
        print(f"{status} [{category}] {measurement}: {value}{unit}")
        if notes:
            print(f"    {notes}")

    def check_service_status(self) -> Dict[str, Any]:
        """檢查服務狀態"""
        try:
            start_time = time.time()
            response = requests.get(f"{self.base_url}/health", timeout=5)
            response_time = time.time() - start_time
            
            return {
                'running': response.status_code == 200,
                'status_code': response.status_code,
                'response_time': response_time,
                'content': response.text[:100] if response.text else ""
            }
        except Exception as e:
            return {
                'running': False,
                'error': str(e),
                'response_time': None
            }

    def measure_cold_start_time(self) -> Dict[str, Any]:
        """測量冷啟動時間"""
        print("\n🚀 測量冷啟動時間...")
        
        # 檢查服務是否已運行
        initial_status = self.check_service_status()
        if initial_status['running']:
            print("⚠️ 服務已運行，無法進行冷啟動測試")
            self.log_measurement('startup_measurements', 'cold_start_skip', True, 
                               notes="服務已運行，跳過冷啟動測試")
            return {'skipped': True, 'reason': 'service_already_running'}
        
        print("服務未運行，開始監控啟動過程...")
        
        # 開始監控啟動
        start_time = time.time()
        max_wait_time = 60  # 最大等待60秒
        check_interval = 0.5  # 每500ms檢查一次
        
        startup_attempts = []
        
        while time.time() - start_time < max_wait_time:
            current_time = time.time()
            status = self.check_service_status()
            
            startup_attempts.append({
                'elapsed_time': current_time - start_time,
                'running': status['running'],
                'status_code': status.get('status_code'),
                'response_time': status.get('response_time')
            })
            
            if status['running']:
                startup_time = current_time - start_time
                self.log_measurement('startup_measurements', 'cold_start_time', 
                                   round(startup_time, 2), "秒", True,
                                   f"服務在 {startup_time:.2f}秒 內啟動完成")
                
                return {
                    'startup_time': startup_time,
                    'attempts': startup_attempts,
                    'success': True
                }
            
            time.sleep(check_interval)
        
        # 啟動超時
        total_time = time.time() - start_time
        self.log_measurement('startup_measurements', 'cold_start_timeout', 
                           round(total_time, 2), "秒", False,
                           f"服務在 {total_time:.2f}秒 內未能啟動")
        
        return {
            'startup_time': None,
            'timeout': True,
            'total_wait_time': total_time,
            'attempts': startup_attempts,
            'success': False
        }

    def measure_warm_start_performance(self) -> Dict[str, Any]:
        """測量熱啟動性能（服務已運行時的響應能力）"""
        print("\n🔥 測量熱啟動性能...")
        
        if not self.check_service_status()['running']:
            print("⚠️ 服務未運行，無法進行熱啟動測試")
            return {'skipped': True, 'reason': 'service_not_running'}
        
        # 測試多個端點的首次響應時間
        test_endpoints = [
            '/',
            '/health',
            '/analytics',
            '/email',
            '/eqc',
            '/files',
            '/monitoring',
            '/tasks'
        ]
        
        warm_start_results = {}
        
        for endpoint in test_endpoints:
            endpoint_results = []
            
            # 對每個端點測試3次
            for attempt in range(3):
                start_time = time.time()
                try:
                    response = requests.get(f"{self.base_url}{endpoint}", timeout=10)
                    response_time = time.time() - start_time
                    
                    endpoint_results.append({
                        'attempt': attempt + 1,
                        'response_time': response_time,
                        'status_code': response.status_code,
                        'content_length': len(response.content),
                        'success': response.status_code == 200
                    })
                    
                except Exception as e:
                    endpoint_results.append({
                        'attempt': attempt + 1,
                        'error': str(e),
                        'success': False
                    })
                
                # 在嘗試之間等待一小段時間
                time.sleep(0.1)
            
            # 計算統計數據
            successful_attempts = [r for r in endpoint_results if r['success']]
            
            if successful_attempts:
                response_times = [r['response_time'] for r in successful_attempts]
                warm_start_results[endpoint] = {
                    'avg_response_time': sum(response_times) / len(response_times),
                    'min_response_time': min(response_times),
                    'max_response_time': max(response_times),
                    'success_rate': len(successful_attempts) / len(endpoint_results) * 100,
                    'attempts': endpoint_results
                }
                
                avg_time = warm_start_results[endpoint]['avg_response_time']
                self.log_measurement('startup_measurements', f'warm_start{endpoint}', 
                                   round(avg_time * 1000, 2), "ms", True,
                                   f"平均響應時間，成功率: {warm_start_results[endpoint]['success_rate']:.1f}%")
            else:
                warm_start_results[endpoint] = {
                    'error': '所有嘗試都失敗',
                    'attempts': endpoint_results
                }
                self.log_measurement('startup_measurements', f'warm_start{endpoint}', 
                                   0, "ms", False, "所有響應嘗試失敗")
        
        return warm_start_results

    def test_service_stability(self, duration_minutes: int = 2) -> Dict[str, Any]:
        """測試服務穩定性"""
        print(f"\n🧪 測試服務穩定性 ({duration_minutes}分鐘)...")
        
        if not self.check_service_status()['running']:
            print("⚠️ 服務未運行，無法進行穩定性測試")
            return {'skipped': True, 'reason': 'service_not_running'}
        
        start_time = time.time()
        end_time = start_time + (duration_minutes * 60)
        
        stability_data = {
            'checks': [],
            'errors': [],
            'response_times': [],
            'memory_usage': [],
            'cpu_usage': []
        }
        
        check_count = 0
        
        while time.time() < end_time:
            check_count += 1
            current_time = time.time()
            
            # 健康檢查
            try:
                response_start = time.time()
                response = requests.get(f"{self.base_url}/health", timeout=5)
                response_time = time.time() - response_start
                
                stability_data['checks'].append({
                    'check_number': check_count,
                    'elapsed_time': current_time - start_time,
                    'response_time': response_time,
                    'status_code': response.status_code,
                    'success': response.status_code == 200
                })
                
                if response.status_code == 200:
                    stability_data['response_times'].append(response_time)
                else:
                    stability_data['errors'].append({
                        'check_number': check_count,
                        'status_code': response.status_code,
                        'elapsed_time': current_time - start_time
                    })
                    
            except Exception as e:
                stability_data['errors'].append({
                    'check_number': check_count,
                    'error': str(e),
                    'elapsed_time': current_time - start_time
                })
            
            # 系統資源監控
            try:
                # 查找Python進程
                python_memory = 0
                python_cpu = 0
                process_count = 0
                
                for proc in psutil.process_iter(['pid', 'name', 'memory_info', 'cpu_percent']):
                    try:
                        if 'python' in proc.info['name'].lower():
                            python_memory += proc.info['memory_info'].rss / 1024 / 1024  # MB
                            python_cpu += proc.info['cpu_percent']
                            process_count += 1
                    except:
                        continue
                
                stability_data['memory_usage'].append({
                    'check_number': check_count,
                    'memory_mb': python_memory,
                    'process_count': process_count
                })
                
                stability_data['cpu_usage'].append({
                    'check_number': check_count,
                    'cpu_percent': python_cpu
                })
                
            except Exception as e:
                print(f"資源監控錯誤: {e}")
            
            # 等待下次檢查
            time.sleep(5)  # 每5秒檢查一次
        
        # 計算穩定性統計
        total_checks = len(stability_data['checks'])
        successful_checks = len(stability_data['response_times'])
        error_count = len(stability_data['errors'])
        
        stability_summary = {
            'duration_minutes': duration_minutes,
            'total_checks': total_checks,
            'successful_checks': successful_checks,
            'error_count': error_count,
            'success_rate': (successful_checks / total_checks * 100) if total_checks > 0 else 0,
            'avg_response_time': sum(stability_data['response_times']) / len(stability_data['response_times']) if stability_data['response_times'] else 0,
            'detailed_data': stability_data
        }
        
        self.log_measurement('stability_tests', 'service_stability', 
                           round(stability_summary['success_rate'], 2), "%", 
                           stability_summary['success_rate'] >= 95,
                           f"在{duration_minutes}分鐘內進行了{total_checks}次檢查，{error_count}次錯誤")
        
        return stability_summary

    def monitor_resource_usage(self, duration_minutes: int = 5) -> Dict[str, Any]:
        """監控資源使用情況"""
        print(f"\n📊 監控資源使用 ({duration_minutes}分鐘)...")
        
        if not self.check_service_status()['running']:
            print("⚠️ 服務未運行，無法進行資源監控")
            return {'skipped': True, 'reason': 'service_not_running'}
        
        start_time = time.time()
        end_time = start_time + (duration_minutes * 60)
        
        resource_data = {
            'memory_snapshots': [],
            'cpu_snapshots': [],
            'system_info': {}
        }
        
        # 獲取系統基本信息
        try:
            resource_data['system_info'] = {
                'cpu_count': psutil.cpu_count(),
                'memory_total_gb': psutil.virtual_memory().total / 1024 / 1024 / 1024,
                'platform': sys.platform
            }
        except:
            pass
        
        snapshot_count = 0
        
        while time.time() < end_time:
            snapshot_count += 1
            current_time = time.time()
            elapsed_time = current_time - start_time
            
            try:
                # 系統記憶體
                system_memory = psutil.virtual_memory()
                
                # Python進程記憶體和CPU
                python_processes = []
                total_python_memory = 0
                total_python_cpu = 0
                
                for proc in psutil.process_iter(['pid', 'name', 'memory_info', 'cpu_percent']):
                    try:
                        if 'python' in proc.info['name'].lower():
                            memory_mb = proc.info['memory_info'].rss / 1024 / 1024
                            cpu_percent = proc.info['cpu_percent']
                            
                            python_processes.append({
                                'pid': proc.info['pid'],
                                'memory_mb': memory_mb,
                                'cpu_percent': cpu_percent
                            })
                            
                            total_python_memory += memory_mb
                            total_python_cpu += cpu_percent
                    except:
                        continue
                
                # 記憶體快照
                resource_data['memory_snapshots'].append({
                    'snapshot': snapshot_count,
                    'elapsed_time': elapsed_time,
                    'system_memory_percent': system_memory.percent,
                    'system_memory_available_gb': system_memory.available / 1024 / 1024 / 1024,
                    'python_total_memory_mb': total_python_memory,
                    'python_process_count': len(python_processes),
                    'python_processes': python_processes
                })
                
                # CPU快照
                cpu_percent = psutil.cpu_percent(interval=1)
                resource_data['cpu_snapshots'].append({
                    'snapshot': snapshot_count,
                    'elapsed_time': elapsed_time,
                    'system_cpu_percent': cpu_percent,
                    'python_total_cpu_percent': total_python_cpu,
                    'python_process_count': len(python_processes)
                })
                
            except Exception as e:
                print(f"資源監控快照錯誤: {e}")
            
            # 每30秒一次快照
            time.sleep(30)
        
        # 計算資源使用統計
        if resource_data['memory_snapshots']:
            memory_usage = [s['python_total_memory_mb'] for s in resource_data['memory_snapshots']]
            cpu_usage = [s['python_total_cpu_percent'] for s in resource_data['cpu_snapshots']]
            
            resource_summary = {
                'monitoring_duration_minutes': duration_minutes,
                'snapshot_count': len(resource_data['memory_snapshots']),
                'memory_stats': {
                    'avg_memory_mb': sum(memory_usage) / len(memory_usage) if memory_usage else 0,
                    'min_memory_mb': min(memory_usage) if memory_usage else 0,
                    'max_memory_mb': max(memory_usage) if memory_usage else 0,
                    'memory_growth_mb': memory_usage[-1] - memory_usage[0] if len(memory_usage) > 1 else 0
                },
                'cpu_stats': {
                    'avg_cpu_percent': sum(cpu_usage) / len(cpu_usage) if cpu_usage else 0,
                    'min_cpu_percent': min(cpu_usage) if cpu_usage else 0,
                    'max_cpu_percent': max(cpu_usage) if cpu_usage else 0
                },
                'detailed_data': resource_data
            }
            
            avg_memory = resource_summary['memory_stats']['avg_memory_mb']
            avg_cpu = resource_summary['cpu_stats']['avg_cpu_percent']
            
            self.log_measurement('resource_monitoring', 'avg_memory_usage', 
                               round(avg_memory, 2), "MB", True,
                               f"平均記憶體使用，CPU: {avg_cpu:.1f}%")
            
            self.log_measurement('resource_monitoring', 'memory_growth', 
                               round(resource_summary['memory_stats']['memory_growth_mb'], 2), "MB",
                               resource_summary['memory_stats']['memory_growth_mb'] < 50,
                               f"記憶體增長量，警告閾值: 50MB")
            
            return resource_summary
        
        return {'error': '無法收集資源數據'}

    def establish_performance_baseline(self) -> Dict[str, Any]:
        """建立性能基準線"""
        print("\n📈 建立性能基準線...")
        
        if not self.check_service_status()['running']:
            print("⚠️ 服務未運行，無法建立基準線")
            return {'skipped': True, 'reason': 'service_not_running'}
        
        baseline_tests = {
            'single_request': [],
            'concurrent_light': [],
            'memory_baseline': None,
            'cpu_baseline': None
        }
        
        # 1. 單一請求基準測試
        print("執行單一請求基準測試...")
        for i in range(10):
            start_time = time.time()
            try:
                response = requests.get(f"{self.base_url}/", timeout=10)
                response_time = time.time() - start_time
                
                baseline_tests['single_request'].append({
                    'attempt': i + 1,
                    'response_time': response_time,
                    'status_code': response.status_code,
                    'content_length': len(response.content),
                    'success': response.status_code == 200
                })
            except Exception as e:
                baseline_tests['single_request'].append({
                    'attempt': i + 1,
                    'error': str(e),
                    'success': False
                })
            
            time.sleep(0.5)
        
        # 2. 輕度併發基準測試
        print("執行輕度併發基準測試...")
        import concurrent.futures
        
        def make_request():
            start_time = time.time()
            try:
                response = requests.get(f"{self.base_url}/", timeout=10)
                return {
                    'response_time': time.time() - start_time,
                    'status_code': response.status_code,
                    'success': response.status_code == 200
                }
            except Exception as e:
                return {
                    'error': str(e),
                    'success': False
                }
        
        with concurrent.futures.ThreadPoolExecutor(max_workers=5) as executor:
            futures = [executor.submit(make_request) for _ in range(20)]
            baseline_tests['concurrent_light'] = [f.result() for f in futures]
        
        # 3. 記憶體和CPU基準
        print("測量記憶體和CPU基準...")
        try:
            python_memory = 0
            python_cpu = 0
            process_count = 0
            
            for proc in psutil.process_iter(['pid', 'name', 'memory_info', 'cpu_percent']):
                try:
                    if 'python' in proc.info['name'].lower():
                        python_memory += proc.info['memory_info'].rss / 1024 / 1024
                        python_cpu += proc.info['cpu_percent']
                        process_count += 1
                except:
                    continue
            
            baseline_tests['memory_baseline'] = {
                'python_memory_mb': python_memory,
                'process_count': process_count
            }
            
            baseline_tests['cpu_baseline'] = {
                'python_cpu_percent': python_cpu,
                'system_cpu_percent': psutil.cpu_percent()
            }
            
        except Exception as e:
            print(f"基準線資源測量錯誤: {e}")
        
        # 計算基準線統計
        successful_single = [r for r in baseline_tests['single_request'] if r['success']]
        successful_concurrent = [r for r in baseline_tests['concurrent_light'] if r['success']]
        
        baseline_summary = {
            'single_request_stats': {
                'avg_response_time': sum(r['response_time'] for r in successful_single) / len(successful_single) if successful_single else 0,
                'success_rate': len(successful_single) / len(baseline_tests['single_request']) * 100,
                'total_tests': len(baseline_tests['single_request'])
            },
            'concurrent_stats': {
                'avg_response_time': sum(r['response_time'] for r in successful_concurrent) / len(successful_concurrent) if successful_concurrent else 0,
                'success_rate': len(successful_concurrent) / len(baseline_tests['concurrent_light']) * 100,
                'total_tests': len(baseline_tests['concurrent_light'])
            },
            'resource_baseline': {
                'memory_mb': baseline_tests['memory_baseline']['python_memory_mb'] if baseline_tests['memory_baseline'] else 0,
                'cpu_percent': baseline_tests['cpu_baseline']['python_cpu_percent'] if baseline_tests['cpu_baseline'] else 0
            },
            'detailed_data': baseline_tests
        }
        
        single_avg = baseline_summary['single_request_stats']['avg_response_time']
        concurrent_avg = baseline_summary['concurrent_stats']['avg_response_time']
        
        self.log_measurement('baseline_performance', 'single_request_baseline', 
                           round(single_avg * 1000, 2), "ms", True,
                           f"單一請求平均響應時間")
        
        self.log_measurement('baseline_performance', 'concurrent_baseline', 
                           round(concurrent_avg * 1000, 2), "ms", True,
                           f"併發請求平均響應時間")
        
        if baseline_tests['memory_baseline']:
            self.log_measurement('baseline_performance', 'memory_baseline', 
                               round(baseline_tests['memory_baseline']['python_memory_mb'], 2), "MB", True,
                               f"基準記憶體使用")
        
        return baseline_summary

    def run_complete_startup_test(self) -> Dict[str, Any]:
        """執行完整的啟動性能測試"""
        print("=" * 80)
        print("🔬 啟動性能專用測試系統")
        print(f"測試目標: {self.base_url}")
        print("=" * 80)
        
        try:
            # 1. 冷啟動測試（如果可能）
            self.results['startup_measurements']['cold_start'] = self.measure_cold_start_time()
            
            # 2. 熱啟動性能測試
            self.results['startup_measurements']['warm_start'] = self.measure_warm_start_performance()
            
            # 3. 建立性能基準線
            self.results['baseline_performance'] = self.establish_performance_baseline()
            
            # 4. 服務穩定性測試
            self.results['stability_tests'] = self.test_service_stability(duration_minutes=2)
            
            # 5. 資源使用監控
            self.results['resource_monitoring'] = self.monitor_resource_usage(duration_minutes=3)
            
            # 添加測試完成時間
            self.results['test_metadata']['test_end_time'] = datetime.now().isoformat()
            self.results['test_metadata']['total_duration'] = (
                datetime.fromisoformat(self.results['test_metadata']['test_end_time']) - 
                datetime.fromisoformat(self.results['test_metadata']['test_start_time'])
            ).total_seconds()
            
            return self.results
            
        except Exception as e:
            print(f"❌ 啟動性能測試過程中發生錯誤: {str(e)}")
            return None

    def save_results(self, filename: str = None) -> str:
        """保存測試結果"""
        if filename is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"startup_performance_report_{timestamp}.json"
        
        filepath = self.project_root / filename
        
        with open(filepath, 'w', encoding='utf-8') as f:
            json.dump(self.results, f, indent=2, ensure_ascii=False)
        
        print(f"\n📄 啟動性能報告已保存: {filepath}")
        return str(filepath)

    def print_summary(self):
        """打印測試摘要"""
        print("\n" + "=" * 80)
        print("📋 啟動性能測試摘要")
        print("=" * 80)
        
        # 啟動時間摘要
        if 'startup_measurements' in self.results:
            startup_data = self.results['startup_measurements']
            
            if 'cold_start' in startup_data and startup_data['cold_start'].get('success'):
                cold_time = startup_data['cold_start']['startup_time']
                print(f"冷啟動時間: {cold_time:.2f}秒")
            
            if 'warm_start' in startup_data:
                warm_data = startup_data['warm_start']
                successful_endpoints = [ep for ep, data in warm_data.items() if 'avg_response_time' in data]
                if successful_endpoints:
                    avg_warm_time = sum(warm_data[ep]['avg_response_time'] for ep in successful_endpoints) / len(successful_endpoints)
                    print(f"熱啟動平均響應: {avg_warm_time * 1000:.2f}ms")
        
        # 穩定性摘要
        if 'stability_tests' in self.results and 'success_rate' in self.results['stability_tests']:
            stability = self.results['stability_tests']
            print(f"服務穩定性: {stability['success_rate']:.1f}% ({stability['successful_checks']}/{stability['total_checks']})")
        
        # 資源使用摘要
        if 'resource_monitoring' in self.results and 'memory_stats' in self.results['resource_monitoring']:
            resource = self.results['resource_monitoring']
            avg_memory = resource['memory_stats']['avg_memory_mb']
            memory_growth = resource['memory_stats']['memory_growth_mb']
            print(f"平均記憶體使用: {avg_memory:.2f}MB (增長: {memory_growth:+.2f}MB)")
        
        # 基準性能摘要
        if 'baseline_performance' in self.results:
            baseline = self.results['baseline_performance']
            if 'single_request_stats' in baseline:
                single_avg = baseline['single_request_stats']['avg_response_time'] * 1000
                print(f"單一請求基準: {single_avg:.2f}ms")
            if 'concurrent_stats' in baseline:
                concurrent_avg = baseline['concurrent_stats']['avg_response_time'] * 1000
                print(f"併發請求基準: {concurrent_avg:.2f}ms")
        
        print("=" * 80)


def main():
    """主函數"""
    if len(sys.argv) > 1 and sys.argv[1] == '--help':
        print("""
啟動性能專用測試系統
用法: python startup_performance_test.py [選項]

選項:
  --help          顯示此幫助信息
  --url URL       指定測試URL (默認: http://localhost:8000)

測試項目:
  1. 冷啟動時間測量
  2. 熱啟動性能測試
  3. 服務穩定性測試
  4. 資源使用監控
  5. 性能基準線建立

範例:
  python startup_performance_test.py
  python startup_performance_test.py --url http://localhost:5000
        """)
        return
    
    # 解析命令行參數
    base_url = "http://localhost:8000"
    
    for i, arg in enumerate(sys.argv):
        if arg == '--url' and i + 1 < len(sys.argv):
            base_url = sys.argv[i + 1]
    
    # 創建測試器並運行測試
    tester = StartupPerformanceTester(base_url)
    results = tester.run_complete_startup_test()
    
    if results:
        # 保存結果
        report_file = tester.save_results()
        
        # 打印摘要
        tester.print_summary()
        
        print(f"\n✅ 啟動性能測試完成！詳細報告: {report_file}")
    else:
        print("\n❌ 啟動性能測試失敗")
        sys.exit(1)


if __name__ == "__main__":
    main()