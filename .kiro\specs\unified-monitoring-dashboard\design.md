# 統一監控儀表板 - 設計文件

> **📋 文件導航**  
> 本文件包含完整的系統設計，共分為 8 個主要章節。使用 Ctrl+F 搜尋關鍵字快速定位內容。

## 📑 目錄索引

| 章節 | 內容 | 頁面跳轉 |
|------|------|----------|
| [🎯 系統概述](#系統概述) | 系統目標、核心特性、技術架構 | [跳轉](#系統概述) |
| [🏗️ 架構設計](#架構設計) | 系統架構圖、核心元件、資料流 | [跳轉](#架構設計) |
| [🔧 元件設計](#元件設計) | 監控協調器、收集器、API設計 | [跳轉](#元件設計) |
| [💾 資料模型](#資料模型) | 資料庫設計、資料結構、索引 | [跳轉](#資料模型) |
| [📁 專案結構](#專案結構) | 目錄結構、程式碼組織、命名規範 | [跳轉](#專案結構) |
| [🔗 系統整合](#系統整合) | 與現有系統整合策略 | [跳轉](#系統整合) |
| [⚡ 效能與安全](#效能與安全) | 效能最佳化、安全考量、測試策略 | [跳轉](#效能與安全) |
| [🚀 部署維護](#部署維護) | 部署策略、維護計劃、監控指標 | [跳轉](#部署維護) |

---

## 🎯 系統概述

### 核心目標
統一監控儀表板是一個**全方位即時監控系統**，專為半導體郵件處理基礎設施設計，提供：

- 📧 **郵件處理監控** - code_comparison.py 任務、廠商分組統計
- 🎭 **Dramatiq任務監控** - code_comparison.py、csv_to_summary.py、壓縮任務、郵件處理、資料分析、檔案處理、批次處理
- 💻 **系統資源監控** - CPU/記憶體/磁碟、服務健康狀態
- 📊 **業務指標監控** - MO/LOT統計、資料品質、報告生成
- 🚨 **智能告警系統** - 多級告警、自動通知、告警合併

### 技術特性

| 特性 | 說明 | 技術實現 |
|------|------|----------|
| **即時更新** | 5秒內反映系統變化 | WebSocket + 非同步處理 |
| **最小侵入** | 不影響現有系統 | 獨立模組 + 錯誤隔離 |
| **歷史分析** | 7天/30天趨勢分析 | SQLite + 時間序列資料 |
| **智能告警** | 多管道通知系統 | 規則引擎 + 通知服務 |
| **高可用性** | 故障自動恢復 | 重試機制 + 降級策略 |

### 技術棧

```
前端: HTML5 + JavaScript + WebSocket
後端: FastAPI + Python 3.11+ + 非同步處理
資料庫: SQLite (擴展現有 outlook.db)
快取: Redis (可選)
整合: start_integrated_services.py (5555端口)
```

## 架構

### 系統架構圖

```mermaid
graph TB
    subgraph "前端層 (Presentation Layer)"
        UI[統一監控儀表板 UI]
        WS[WebSocket 連接]
        API[REST API 端點]
    end
    
    subgraph "應用層 (Application Layer)"
        MC[監控協調器]
        DS[資料收集服務]
        AS[告警服務]
        TS[趨勢分析服務]
    end
    
    subgraph "基礎設施層 (Infrastructure Layer)"
        subgraph "資料收集器"
            EMC[郵件監控收集器]
            DMC[Dramatiq 監控收集器]
            SMC[系統指標收集器]
            FMC[檔案處理監控收集器]
        end
        
        subgraph "資料儲存"
            DB[(outlook.db SQLite)]
            CACHE[Redis 快取]
        end
        
        subgraph "外部系統"
            EMAIL[郵件處理系統]
            DRAMATIQ[Dramatiq 任務佇列]
            REDIS[Redis Broker]
            SCHEDULER[排程器系統]
            FS[檔案系統]
        end
    end
    
    UI --> API
    UI --> WS
    API --> MC
    WS --> MC
    MC --> DS
    MC --> AS
    MC --> TS
    DS --> EMC
    DS --> DMC
    DS --> SMC
    DS --> FMC
    EMC --> EMAIL
    DMC --> DRAMATIQ
    DMC --> REDIS
    SMC --> SCHEDULER
    FMC --> FS
    DS --> DB
    DS --> CACHE
    AS --> DB
    TS --> DB
```

### 技術架構

- **前端**: HTML5 + JavaScript + WebSocket (即時更新)
- **後端**: FastAPI + Python 3.11+
- **資料庫**: SQLite (擴展現有 outlook.db)
- **快取**: Redis (可選，用於高頻資料)
- **即時通訊**: WebSocket
- **任務佇列**: 整合 Dramatiq 系統 + Redis Broker
- **監控**: 整合現有 ConcurrentTaskManager

## 元件和介面

### 核心元件

#### 1. 監控協調器 (MonitoringCoordinator) ✅ Task 9 已完成

```python
class DashboardMonitoringCoordinator:
    """統一監控協調器 - 系統核心元件 (Task 9 實現完成)"""
    
    def __init__(self, config: DashboardConfig):
        # 系統狀態管理
        self.is_running = False
        self.start_time: Optional[datetime] = None
        
        # 服務實例
        self.alert_service: Optional[DashboardAlertService] = None
        self.websocket_service: Optional[DashboardWebSocketService] = None
        self.repository: Optional[DashboardMonitoringRepository] = None
        
        # 資料收集器
        self.collectors: Dict[str, Any] = {}
        
        # 任務管理
        self.background_tasks: Set[asyncio.Task] = set()
        self.collection_lock = asyncio.Lock()
        
        # 統計資訊
        self.stats = {
            'total_collections': 0,
            'successful_collections': 0,
            'failed_collections': 0,
            'alerts_generated': 0,
            'broadcasts_sent': 0
        }
    
    async def start_monitoring(self) -> None:
        """啟動監控系統 - 已實現完整生命週期管理"""
        
    async def stop_monitoring(self) -> None:
        """停止監控系統 - 已實現優雅關閉機制"""
        
    async def collect_all_metrics(self) -> Optional[DashboardMetrics]:
        """收集所有監控指標 - 已實現並行收集機制"""
        
    async def get_current_status(self) -> Dict[str, Any]:
        """獲取當前系統狀態 - 已實現完整狀態報告"""
        
    def is_healthy(self) -> bool:
        """健康檢查 - 已實現多層次健康檢查"""
```

**Task 9 實現亮點:**
- ✅ **完整生命週期管理**: 啟動、停止、狀態管理
- ✅ **定期任務循環**: 指標收集、告警評估、系統維護
- ✅ **WebSocket 整合**: 與現有 WebSocket 服務完美整合
- ✅ **並行資料收集**: 多個收集器並行執行
- ✅ **錯誤隔離**: 單一服務故障不影響整體系統
- ✅ **效能監控**: 內建效能追蹤和統計
- ✅ **依賴注入**: 與現有 DI 系統完美整合

#### 2. 資料收集服務 (DataCollectionService)

```python
class DataCollectionService:
    """資料收集服務 - 統一收集各種監控資料"""
    
    def __init__(self):
        self.email_collector = EmailMonitoringCollector()
        self.dramatiq_collector = DramatiqMonitoringCollector()
        self.system_collector = SystemMetricsCollector()
        self.file_collector = FileProcessingCollector()
    
    async def collect_email_metrics(self) -> EmailMetrics:
        """收集郵件處理指標"""
        
    async def collect_dramatiq_metrics(self) -> DramatiqMetrics:
        """收集Dramatiq任務指標"""
        
    async def collect_system_metrics(self) -> SystemMetrics:
        """收集系統資源指標"""
        
    async def collect_file_metrics(self) -> FileMetrics:
        """收集檔案處理指標"""
```

#### 3. 告警服務 (AlertService)

```python
class AlertService:
    """告警服務 - 處理系統告警和通知"""
    
    def __init__(self):
        self.alert_rules = []
        self.notification_channels = []
        self.alert_history = []
    
    async def evaluate_alerts(self, metrics: Dict[str, Any]) -> List[Alert]:
        """評估告警條件"""
        
    async def send_alert(self, alert: Alert) -> bool:
        """發送告警通知"""
        
    def merge_duplicate_alerts(self, alerts: List[Alert]) -> List[Alert]:
        """合併重複告警"""
```

#### 4. 趨勢分析服務 (TrendAnalyzer)

```python
class TrendAnalyzer:
    """趨勢分析服務 - 分析歷史資料和預測"""
    
    def __init__(self):
        self.db_repository = MonitoringRepository()
    
    async def analyze_trends(self, metric_type: str, time_range: str) -> TrendData:
        """分析趨勢資料"""
        
    async def predict_load(self, hours_ahead: int) -> LoadPrediction:
        """預測系統負載"""
        
    async def detect_anomalies(self, metrics: Dict[str, Any]) -> List[Anomaly]:
        """檢測異常模式"""
```

### 資料收集器

#### 1. 郵件監控收集器 (EmailMonitoringCollector)

```python
class EmailMonitoringCollector:
    """郵件監控收集器 - 整合現有郵件處理系統"""
    
    def __init__(self):
        self.email_database = EmailDatabase()
        self.task_manager = get_task_manager()
    
    async def collect_email_queue_metrics(self) -> EmailQueueMetrics:
        """收集郵件佇列指標"""
        
    async def collect_email_processing_metrics(self) -> EmailProcessingMetrics:
        """收集郵件處理指標"""
        
    async def collect_attachment_metrics(self) -> AttachmentMetrics:
        """收集附件處理指標"""
```

#### 2. Dramatiq監控收集器 (DramatiqMonitoringCollector)

```python
class DramatiqMonitoringCollector:
    """Dramatiq監控收集器 - 監控Dramatiq任務佇列和工作者"""
    
    def __init__(self):
        self.dramatiq_broker = get_dramatiq_broker()
        self.redis_client = get_redis_client()
        self.task_manager = get_dramatiq_task_manager()
    
    async def collect_dramatiq_queue_metrics(self) -> DramatiqQueueMetrics:
        """收集Dramatiq任務佇列指標"""
        
    async def collect_dramatiq_worker_metrics(self) -> DramatiqWorkerMetrics:
        """收集Dramatiq工作者指標"""
        
    async def collect_dramatiq_task_type_metrics(self) -> DramatiqTaskTypeMetrics:
        """收集Dramatiq任務類型指標 (email_processing, data_analysis, file_processing, batch_processing)"""
        
    async def collect_dramatiq_performance_metrics(self) -> DramatiqPerformanceMetrics:
        """收集Dramatiq效能指標"""
        
    async def collect_dramatiq_error_metrics(self) -> DramatiqErrorMetrics:
        """收集Dramatiq錯誤和重試指標"""
```

#### 3. 系統指標收集器 (SystemMetricsCollector)

```python
class SystemMetricsCollector:
    """系統指標收集器 - 收集系統資源和服務狀態"""
    
    def __init__(self):
        self.scheduler = get_email_coordinator()
    
    async def collect_resource_metrics(self) -> ResourceMetrics:
        """收集系統資源指標"""
        
    async def collect_service_health(self) -> ServiceHealthMetrics:
        """收集服務健康狀態"""
        
    async def collect_database_metrics(self) -> DatabaseMetrics:
        """收集資料庫指標"""
```

#### 4. Pipeline 監控收集器 (PipelineMonitoringCollector) 🆕

```python
class PipelineMonitoringCollector:
    """Pipeline 監控收集器 - 整合 Redis Pipeline 狀態追蹤"""
    
    def __init__(self):
        self.pipeline_manager = get_pipeline_manager()
        self.redis_client = get_redis_client()
        self.vendor_monitor = get_vendor_file_monitor()
    
    async def collect_pipeline_metrics(self) -> PipelineMetrics:
        """收集 Pipeline 執行指標"""
        
    async def collect_pipeline_statistics(self) -> Dict[str, Any]:
        """收集管道統計信息"""
        
    async def collect_redis_statistics(self) -> Dict[str, Any]:
        """收集 Redis 狀態信息"""
        
    async def collect_pipeline_details(self) -> List[Dict[str, Any]]:
        """收集活躍管道詳細信息"""
        
    def cleanup_old_pipeline_data(self, older_than_hours: int = 24) -> Dict[str, Any]:
        """清理舊的 Pipeline 數據"""
```

#### 5. 廠商文件監控收集器 (VendorFileMonitoringCollector) 🆕

```python
class VendorFileMonitoringCollector:
    """廠商文件監控收集器 - 整合 VendorFileMonitor 數據"""
    
    def __init__(self):
        self.vendor_monitor = get_vendor_file_monitor()
        self.supported_vendors = [
            'ETD', 'GTK', 'JCET', 'LINGSEN', 'XAHT', 
            'MSEC', 'NANOTECH', 'NFME', 'SUQIAN', 'TSHT', 'CHUZHOU'
        ]
    
    async def collect_vendor_file_metrics(self) -> VendorFileMetrics:
        """收集廠商文件處理指標"""
        
    async def collect_vendor_statistics(self) -> Dict[str, Dict[str, Any]]:
        """收集各廠商統計信息"""
        
    async def collect_processing_details(self) -> List[Dict[str, Any]]:
        """收集活躍處理詳情"""
        
    async def collect_performance_metrics(self) -> Dict[str, Any]:
        """收集效能分析指標"""
        
    def get_vendor_comparison(self) -> Dict[str, Any]:
        """獲取廠商效能比較"""
        
    def calculate_vendor_performance_ranking(self) -> List[Dict[str, Any]]:
        """計算廠商效能排名"""
```

### API 介面

#### 1. 監控 API 端點

```python
@router.get("/api/monitoring/dashboard")
async def get_dashboard_data() -> Dict[str, Any]:
    """獲取儀表板資料"""

@router.get("/api/monitoring/email/queue")
async def get_email_queue_status() -> Dict[str, Any]:
    """獲取郵件佇列狀態"""

@router.get("/api/monitoring/dramatiq/tasks")
async def get_dramatiq_task_status() -> Dict[str, Any]:
    """獲取Dramatiq任務狀態"""

@router.get("/api/monitoring/system/health")
async def get_system_health() -> Dict[str, Any]:
    """獲取系統健康狀態"""

@router.get("/api/monitoring/trends/{metric_type}")
async def get_trend_data(metric_type: str, time_range: str) -> Dict[str, Any]:
    """獲取趨勢資料"""

@router.get("/api/monitoring/alerts")
async def get_active_alerts() -> Dict[str, Any]:
    """獲取活躍告警"""

@router.post("/api/monitoring/alerts/{alert_id}/acknowledge")
async def acknowledge_alert(alert_id: str) -> Dict[str, Any]:
    """確認告警"""

# 🆕 新增 Pipeline 監控 API 端點
@router.get("/api/monitoring/pipeline/status")
async def get_pipeline_status() -> Dict[str, Any]:
    """獲取 Pipeline 執行狀態"""

@router.get("/api/monitoring/pipeline/active")
async def get_active_pipelines() -> Dict[str, Any]:
    """獲取活躍的 Pipeline 列表"""

@router.get("/api/monitoring/pipeline/{pipeline_id}")
async def get_pipeline_details(pipeline_id: str) -> Dict[str, Any]:
    """獲取特定 Pipeline 的詳細信息"""

@router.post("/api/monitoring/pipeline/{pipeline_id}/cancel")
async def cancel_pipeline(pipeline_id: str) -> Dict[str, Any]:
    """取消指定的 Pipeline 執行"""

@router.delete("/api/monitoring/pipeline/cleanup")
async def cleanup_old_pipelines(older_than_hours: int = 24) -> Dict[str, Any]:
    """清理舊的 Pipeline 數據"""

# 🆕 新增廠商文件監控 API 端點
@router.get("/api/monitoring/vendor-files/status")
async def get_vendor_file_status() -> Dict[str, Any]:
    """獲取廠商文件處理狀態"""

@router.get("/api/monitoring/vendor-files/active")
async def get_active_file_trackings() -> Dict[str, Any]:
    """獲取活躍的文件追蹤列表"""

@router.get("/api/monitoring/vendor-files/vendors")
async def get_vendor_statistics() -> Dict[str, Any]:
    """獲取各廠商統計信息"""

@router.get("/api/monitoring/vendor-files/comparison")
async def get_vendor_comparison() -> Dict[str, Any]:
    """獲取廠商效能比較"""

@router.get("/api/monitoring/vendor-files/ranking")
async def get_vendor_performance_ranking() -> Dict[str, Any]:
    """獲取廠商效能排名"""

@router.get("/api/monitoring/vendor-files/{tracking_id}")
async def get_file_tracking_details(tracking_id: str) -> Dict[str, Any]:
    """獲取特定文件追蹤的詳細信息"""
```

#### 2. WebSocket 介面

```python
@websocket_router.websocket("/ws/monitoring/{client_id}")
async def monitoring_websocket(websocket: WebSocket, client_id: str):
    """監控 WebSocket 端點"""

# WebSocket 訊息類型
class WebSocketMessage:
    type: str  # 'subscribe', 'unsubscribe', 'metrics_update', 'alert', 'system_status'
    payload: Dict[str, Any]
    timestamp: datetime
```

## 資料模型

### 資料庫擴展

擴展現有的 outlook.db，新增監控相關表格：

```sql
-- 監控指標歷史表
CREATE TABLE monitoring_metrics (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    metric_type VARCHAR(50) NOT NULL,
    metric_name VARCHAR(100) NOT NULL,
    metric_value REAL NOT NULL,
    metric_unit VARCHAR(20),
    tags TEXT, -- JSON格式的標籤
    timestamp DATETIME NOT NULL,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- 告警記錄表
CREATE TABLE alert_history (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    alert_type VARCHAR(50) NOT NULL,
    alert_level VARCHAR(20) NOT NULL, -- 'info', 'warning', 'error', 'critical'
    title VARCHAR(200) NOT NULL,
    message TEXT NOT NULL,
    source_system VARCHAR(50) NOT NULL,
    triggered_at DATETIME NOT NULL,
    acknowledged_at DATETIME,
    resolved_at DATETIME,
    status VARCHAR(20) DEFAULT 'active', -- 'active', 'acknowledged', 'resolved'
    metadata TEXT, -- JSON格式的額外資料
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- 任務執行歷史表
CREATE TABLE task_execution_history (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    task_id VARCHAR(100) NOT NULL,
    task_type VARCHAR(50) NOT NULL,
    task_name VARCHAR(100),
    status VARCHAR(20) NOT NULL, -- 'pending', 'running', 'completed', 'failed', 'cancelled'
    started_at DATETIME,
    completed_at DATETIME,
    duration_seconds REAL,
    error_message TEXT,
    result_data TEXT, -- JSON格式的結果資料
    queue_name VARCHAR(50),
    worker_name VARCHAR(50),
    retry_count INTEGER DEFAULT 0,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- 系統健康檢查記錄表
CREATE TABLE system_health_checks (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    service_name VARCHAR(50) NOT NULL,
    check_type VARCHAR(30) NOT NULL, -- 'database', 'email', 'dramatiq', 'filesystem'
    status VARCHAR(20) NOT NULL, -- 'healthy', 'warning', 'error'
    response_time_ms REAL,
    error_message TEXT,
    details TEXT, -- JSON格式的詳細資訊
    checked_at DATETIME NOT NULL,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- 檔案處理統計表
CREATE TABLE file_processing_stats (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    file_type VARCHAR(30) NOT NULL, -- 'csv', 'excel', 'zip', 'attachment'
    operation_type VARCHAR(30) NOT NULL, -- 'download', 'parse', 'compress', 'decompress'
    file_count INTEGER NOT NULL,
    total_size_bytes BIGINT NOT NULL,
    success_count INTEGER NOT NULL,
    failure_count INTEGER NOT NULL,
    avg_processing_time_seconds REAL,
    date_hour DATETIME NOT NULL, -- 按小時統計
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- 建立索引以提升查詢效能
CREATE INDEX idx_monitoring_metrics_type_time ON monitoring_metrics(metric_type, timestamp);
CREATE INDEX idx_alert_history_status_time ON alert_history(status, triggered_at);
CREATE INDEX idx_task_execution_type_status ON task_execution_history(task_type, status);
CREATE INDEX idx_system_health_service_time ON system_health_checks(service_name, checked_at);
CREATE INDEX idx_file_processing_date_type ON file_processing_stats(date_hour, file_type);
```

### 資料模型類別

```python
from dataclasses import dataclass
from datetime import datetime
from typing import Dict, List, Optional, Any
from enum import Enum

class MetricType(Enum):
    EMAIL_QUEUE = "email_queue"
    DRAMATIQ_TASK = "dramatiq_task"
    SYSTEM_RESOURCE = "system_resource"
    FILE_PROCESSING = "file_processing"
    DATABASE_PERFORMANCE = "database_performance"

class AlertLevel(Enum):
    INFO = "info"
    WARNING = "warning"
    ERROR = "error"
    CRITICAL = "critical"

@dataclass
class MonitoringMetric:
    metric_type: MetricType
    metric_name: str
    metric_value: float
    metric_unit: str
    tags: Dict[str, str]
    timestamp: datetime

@dataclass
class EmailQueueMetrics:
    pending_emails: int
    processing_emails: int
    completed_emails: int
    failed_emails: int
    avg_processing_time: float
    queue_size_by_vendor: Dict[str, int]

@dataclass
class DramatiqTaskMetrics:
    active_tasks: int
    pending_tasks: int
    completed_tasks: int
    failed_tasks: int
    task_counts_by_type: Dict[str, int]  # code_comparison, csv_to_summary, compression, email_processing, data_analysis, file_processing, batch_processing
    worker_status: Dict[str, str]
    avg_task_duration: Dict[str, float]

@dataclass
class SystemResourceMetrics:
    cpu_percent: float
    memory_percent: float
    disk_percent: float
    network_io: Dict[str, float]
    active_connections: int
    load_average: List[float]

@dataclass
class Alert:
    alert_type: str
    alert_level: AlertLevel
    title: str
    message: str
    source_system: str
    triggered_at: datetime
    metadata: Dict[str, Any]
```

## 錯誤處理

### 錯誤處理策略

1. **資料收集錯誤**
   - 單一收集器失敗不影響其他收集器
   - 實施重試機制和降級策略
   - 記錄詳細錯誤日誌

2. **資料庫錯誤**
   - 實施連接池和重連機制
   - 資料庫不可用時使用記憶體快取
   - 定期備份監控資料

3. **WebSocket 連接錯誤**
   - 自動重連機制
   - 連接失敗時降級到輪詢模式
   - 連接狀態監控和告警

4. **告警系統錯誤**
   - 多通道告警確保可靠性
   - 告警失敗時記錄到資料庫
   - 實施告警升級機制

### 錯誤恢復機制

```python
class ErrorRecoveryManager:
    """錯誤恢復管理器"""
    
    def __init__(self):
        self.retry_policies = {}
        self.fallback_strategies = {}
    
    async def handle_collector_error(self, collector_name: str, error: Exception):
        """處理收集器錯誤"""
        
    async def handle_database_error(self, error: Exception):
        """處理資料庫錯誤"""
        
    async def handle_websocket_error(self, client_id: str, error: Exception):
        """處理WebSocket錯誤"""
```

---

## 🆕 最新實作特色

### Pipeline 監控收集器 (Task 27 已完成)

**核心功能**:
- **Redis 整合**: 支援 localhost:6379 連接，獲取 Pipeline 狀態
- **PipelineManager 整合**: 直接整合 `src/tasks/pipeline_utils.py`
- **緩存機制**: 30秒緩存機制，平衡效能與即時性
- **異步收集**: 支援異步數據收集和處理

**技術架構**:
```python
class DashboardPipelineCollector:
    """Pipeline 監控數據收集器"""
    
    def __init__(self, redis_host: str = "localhost", redis_port: int = 6379):
        self.redis_client: Optional[redis.Redis] = None
        self.pipeline_manager: Optional[Any] = None
        self._cache_duration_seconds = 30
    
    async def collect_pipeline_metrics(self) -> PipelineMetrics:
        """收集 Pipeline 執行狀態和統計"""
        
    async def get_redis_pipeline_status(self) -> Dict[str, Any]:
        """獲取 Redis Pipeline 狀態信息"""
        
    async def get_pipeline_task_statistics(self) -> Dict[str, Any]:
        """獲取管道任務統計和效能指標"""
```

### 廠商文件監控收集器 (Task 28 已完成)

**核心功能**:
- **多廠商支援**: 支援 11 個廠商 (ETD, GTK, JCET, LINGSEN, XAHT, MSEC, NANOTECH, NFME, SUQIAN, TSHT, CHUZHOU)
- **VendorFileMonitor 整合**: 直接整合 `src/services/vendor_file_monitor.py`
- **效能分析**: 廠商效能排名和比較功能
- **進度追蹤**: 文件下載進度和狀態監控

**技術架構**:
```python
class DashboardVendorFileCollector:
    """廠商文件監控數據收集器"""
    
    def __init__(self):
        self.vendor_file_monitor: Optional[Any] = None
        self.supported_vendors = [
            'ETD', 'GTK', 'JCET', 'LINGSEN', 'XAHT', 
            'MSEC', 'NANOTECH', 'NFME', 'SUQIAN', 'TSHT', 'CHUZHOU'
        ]
        self._cache_duration_seconds = 30
    
    async def collect_vendor_file_metrics(self) -> VendorFileMetrics:
        """收集廠商文件處理指標"""
        
    async def get_vendor_performance_ranking(self) -> List[Dict[str, Any]]:
        """獲取廠商效能排名"""
        
    async def get_vendor_file_progress(self) -> Dict[str, Any]:
        """獲取廠商文件下載進度"""
```

### 統一模型架構

**設計原則**:
- **所有監控模型統一定義在 `dashboard_metrics_models.py`**
- **統一創建函數**: `create_pipeline_metrics()`, `create_vendor_file_metrics()`, `create_integrated_metrics()`
- **一致的數據格式**: 所有收集器使用相同的數據結構

**錯誤處理機制**:
- **`safe_execute` 裝飾器**: 提供統一的異常處理和預設值返回
- **優雅降級**: 監控功能異常不影響主要業務邏輯
- **完整日誌**: 詳細的錯誤追蹤和除錯信息

**效能優化**:
- **智能緩存**: 30秒緩存機制，減少系統負載
- **異步處理**: 所有數據收集都支援異步操作
- **資源管理**: 自動清理和資源回收機制

---

## 測試策略

### 單元測試

1. **資料收集器測試**
   - 模擬各種系統狀態
   - 測試錯誤處理邏輯
   - 驗證資料格式正確性

2. **告警服務測試**
   - 測試告警規則評估
   - 驗證通知發送機制
   - 測試告警合併邏輯

3. **趨勢分析測試**
   - 測試歷史資料分析
   - 驗證預測演算法
   - 測試異常檢測

### 整合測試

1. **端到端監控流程測試**
   - 從資料收集到前端顯示的完整流程
   - WebSocket 即時更新測試
   - 告警觸發和通知測試

2. **效能測試**
   - 大量資料處理效能
   - 並發連接處理能力
   - 資料庫查詢效能

3. **可靠性測試**
   - 系統故障恢復測試
   - 資料一致性測試
   - 長時間運行穩定性測試

### 測試資料準備

```python
class MonitoringTestDataFactory:
    """監控測試資料工廠"""
    
    @staticmethod
    def create_email_metrics() -> EmailQueueMetrics:
        """創建測試用郵件指標"""
        
    @staticmethod
    def create_dramatiq_metrics() -> DramatiqTaskMetrics:
        """創建測試用Dramatiq指標"""
        
    @staticmethod
    def create_system_metrics() -> SystemResourceMetrics:
        """創建測試用系統指標"""
```

## 效能考量

### 資料收集最佳化

1. **批量資料收集**
   - 減少資料庫查詢次數
   - 使用連接池管理資料庫連接
   - 實施資料快取策略

2. **非同步處理**
   - 所有資料收集使用非同步操作
   - 並行收集不同類型的指標
   - 使用任務佇列處理重型操作

3. **資料壓縮和清理**
   - 定期清理過期的監控資料
   - 壓縮歷史資料以節省空間
   - 實施資料分區策略

### 前端效能最佳化

1. **資料更新策略**
   - 使用 WebSocket 進行即時更新
   - 實施智能更新頻率調整
   - 前端資料快取和增量更新

2. **UI 渲染最佳化**
   - 虛擬滾動處理大量資料
   - 圖表資料採樣和聚合
   - 延遲載入非關鍵資料

### 擴展性設計

1. **水平擴展支援**
   - 支援多個監控實例
   - 資料收集器可獨立部署
   - 負載均衡和故障轉移

2. **模組化架構**
   - 新監控類型易於添加
   - 收集器可插拔設計
   - 告警規則可配置化

## 安全考量

### 資料安全

1. **敏感資料保護**
   - 監控資料加密儲存
   - API 訪問權限控制
   - 審計日誌記錄

2. **網路安全**
   - WebSocket 連接加密
   - API 端點認證授權
   - 防止資料洩露

### 訪問控制

```python
class MonitoringSecurityManager:
    """監控安全管理器"""
    
    def __init__(self):
        self.access_policies = {}
        self.audit_logger = AuditLogger()
    
    def check_api_access(self, user: str, endpoint: str) -> bool:
        """檢查API訪問權限"""
        
    def check_websocket_access(self, user: str, subscription: str) -> bool:
        """檢查WebSocket訂閱權限"""
        
    def log_access_attempt(self, user: str, action: str, result: bool):
        """記錄訪問嘗試"""
```

## 部署和維護

### 部署策略

1. **漸進式部署**
   - 先部署資料收集器
   - 逐步啟用監控功能
   - 最後啟用告警系統

2. **配置管理**
   - 環境變數配置
   - 動態配置更新
   - 配置驗證和回滾

3. **監控系統的監控**
   - 監控系統自身的健康狀態
   - 監控資料收集效能
   - 告警系統可用性監控

### 維護策略

1. **資料維護**
   - 定期清理過期資料
   - 資料庫效能調優
   - 備份和恢復策略

2. **系統維護**
   - 定期更新和安全修補
   - 效能監控和調優
   - 容量規劃和擴展

## 專案結構設計

### 目錄結構規劃 ✅ 已實現

統一監控儀表板已完全實現，採用以下目錄結構：

```
src/
├── dashboard_monitoring/                           # ✅ 統一監控儀表板專用目錄
│   ├── __init__.py                                # ✅ 模組初始化
│   ├── README.md                                  # ✅ 專案說明文件
│   ├── simple_test.py                             # ✅ 簡單測試腳本
│   │
│   ├── core/                                      # ✅ 核心業務邏輯
│   │   ├── __init__.py
│   │   ├── dashboard_monitoring_coordinator.py    # ✅ 監控協調器 (Task 9 完成)
│   │   ├── dashboard_alert_service.py             # ✅ 告警服務 (Task 19 完成)
│   │   ├── dashboard_trend_analyzer.py            # ✅ 趨勢分析服務 (Task 20 完成)
│   │   ├── dashboard_cache_manager.py             # ✅ 快取管理器 (Task 14 完成)
│   │   ├── dashboard_cache_service.py             # ✅ 快取服務 (Task 14 完成)
│   │   ├── dashboard_websocket_manager.py         # ✅ WebSocket 管理器 (Task 12 完成)
│   │   ├── dashboard_service_integrator.py        # ✅ 服務整合器 (Task 22 完成)
│   │   └── README.md                              # ✅ 核心模組說明
│   │
│   ├── collectors/                                # ✅ 資料收集器
│   │   ├── __init__.py
│   │   ├── dashboard_email_collector.py           # ✅ 郵件監控收集器 (Task 16 完成)
│   │   ├── dashboard_dramatiq_collector.py        # ✅ Dramatiq監控收集器 (Task 10 完成)
│   │   ├── dashboard_system_collector.py          # ✅ 系統指標收集器 (Task 15 完成)
│   │   ├── dashboard_file_collector.py            # ✅ 檔案處理收集器 (Task 17 完成)
│   │   ├── dashboard_network_collector.py         # ✅ 網路監控收集器 (Task 26 完成)
│   │   ├── dashboard_pipeline_collector.py        # ✅ Pipeline 監控收集器 (Task 27 完成)
│   │   ├── dashboard_vendor_file_collector.py     # ✅ 廠商文件監控收集器 (Task 28 完成)
│   │   ├── eqc_concurrent_collector.py            # ✅ EQC 並發收集器
│   │   └── eqc_task_collector.py                  # ✅ EQC 任務收集器
│   │
│   ├── models/                                    # ✅ 資料模型
│   │   ├── __init__.py
│   │   ├── dashboard_models.py                    # ✅ 儀表板資料模型 (Task 11 完成)
│   │   ├── dashboard_metrics_models.py            # ✅ 指標資料模型 (Task 11 完成)
│   │   ├── dashboard_alert_models.py              # ✅ 告警資料模型 (Task 19 完成)
│   │   └── dashboard_trend_models.py              # ✅ 趨勢分析模型 (Task 20 完成)
│   │
│   ├── repositories/                              # ✅ 資料存取層
│   │   ├── __init__.py
│   │   ├── dashboard_monitoring_repository.py     # ✅ 監控資料存取 (Task 18 完成)
│   │   ├── dashboard_alert_repository.py          # ✅ 告警資料存取 (Task 19 完成)
│   │   ├── dashboard_trend_repository.py          # ✅ 趨勢資料存取 (Task 20 完成)
│   │   └── README.md                              # ✅ 資料存取層說明
│   │
│   ├── api/                                       # ✅ API 端點
│   │   ├── __init__.py
│   │   ├── dashboard_dependencies.py              # ✅ 依賴注入系統 (Task 13 完成)
│   │   ├── dashboard_monitoring_api.py            # ✅ 監控 API (Task 21 完成)
│   │   ├── dashboard_alert_api.py                 # ✅ 告警 API (Task 19 完成)
│   │   ├── dashboard_cache_api.py                 # ✅ 快取 API (Task 14 完成)
│   │   ├── dashboard_pipeline_api.py              # ✅ Pipeline API (Task 27 完成)
│   │   ├── dashboard_websocket.py                 # ✅ WebSocket 端點 (Task 12 完成)
│   │   ├── dashboard_html_api.py                  # ✅ HTML 頁面 API (Task 23 完成)
│   │   ├── dashboard_middleware_api.py            # ✅ 中間件 API (Task 24 完成)
│   │   ├── dashboard_middleware.py                # ✅ 中間件實現 (Task 24 完成)
│   │   ├── dashboard_router_manager.py            # ✅ 路由管理器 (Task 24 完成)
│   │   ├── eqc_monitoring_api.py                  # ✅ EQC 監控 API
│   │   ├── real_data_api.py                       # ✅ 真實資料 API (Task 26 完成)
│   │   ├── simple_dashboard_api.py                # ✅ 簡化儀表板 API
│   │   ├── test_simple_api.py                     # ✅ API 測試
│   │   └── README.md                              # ✅ API 說明文件
│   │
│   ├── templates/                                 # ✅ HTML 模板
│   │   ├── __init__.py
│   │   ├── dashboard_main.html                    # ✅ 主儀表板頁面 (Task 23 完成)
│   │   ├── dashboard_email.html                   # ✅ 郵件監控頁面 (Task 23 完成)
│   │   ├── dashboard_dramatiq.html                # ✅ Dramatiq監控頁面 (Task 23 完成)
│   │   ├── dashboard_system.html                  # ✅ 系統監控頁面 (Task 23 完成)
│   │   ├── dashboard_pipeline.html                # ✅ Pipeline 監控頁面 (Task 27 完成)
│   │   └── dashboard_vendor_file.html             # ✅ 廠商文件監控頁面 (Task 28 完成)
│   │
│   ├── static/                                    # ✅ 靜態資源
│   │   ├── __init__.py
│   │   ├── css/                                   # ✅ 樣式文件目錄
│   │   │   └── __init__.py
│   │   ├── js/                                    # ✅ JavaScript 文件目錄
│   │   │   └── __init__.py
│   │   └── images/                                # ✅ 圖片資源目錄
│   │       ├── __init__.py
│   │       └── dashboard_icons/                   # ✅ 儀表板圖示目錄
│   │
│   ├── utils/                                     # ✅ 工具函數
│   │   ├── __init__.py
│   │   ├── dashboard_helpers.py                   # ✅ 儀表板輔助函數 (Task 25 完成)
│   │   ├── dashboard_formatters.py                # ✅ 資料格式化工具 (Task 25 完成)
│   │   ├── dashboard_cache_utils.py               # ✅ 快取工具 (Task 14 完成)
│   │   ├── dashboard_health_checker.py            # ✅ 健康檢查工具 (Task 22 完成)
│   │   ├── dashboard_database_manager.py          # ✅ 資料庫管理工具
│   │   ├── dashboard_db_maintenance.py            # ✅ 資料庫維護工具
│   │   ├── config_manager.py                      # ✅ 配置管理器
│   │   ├── data_source_detector.py                # ✅ 資料源檢測器
│   │   └── README.md                              # ✅ 工具函數說明
│   │
│   ├── config/                                    # ✅ 配置文件
│   │   ├── __init__.py
│   │   ├── dashboard_config.py                    # ✅ 儀表板配置 (Task 13 完成)
│   │   ├── dashboard_monitoring_rules.py          # ✅ 監控規則配置 (Task 13 完成)
│   │   ├── dashboard_cache_config.py              # ✅ 快取配置 (Task 14 完成)
│   │   ├── dramatiq_monitoring_rules.py           # ✅ Dramatiq 監控規則
│   │   ├── eqc_monitoring_rules.py                # ✅ EQC 監控規則
│   │   └── usage_example.py                       # ✅ 配置使用範例
│   │
│   ├── integration/                               # ✅ 系統整合
│   │   ├── __init__.py
│   │   ├── dashboard_service_integrator.py        # ✅ 服務整合器 (Task 22 完成)
│   │   ├── dashboard_service_integrator_minimal.py # ✅ 最小化整合器
│   │   └── dashboard_routing_integration.py       # ✅ 路由整合 (Task 24 完成)
│   │
│   ├── cli/                                       # ✅ 命令列工具
│   │   ├── __init__.py
│   │   └── config_cli.py                          # ✅ 配置命令列工具
│   │
│   ├── migrations/                                # ✅ 資料庫遷移
│   │   ├── __init__.py
│   │   ├── dashboard_db_migration.py              # ✅ 資料庫遷移腳本
│   │   └── README.md                              # ✅ 遷移說明
│   │
│   ├── examples/                                  # ✅ 使用範例
│   │   ├── dramatiq_collector_example.py          # ✅ Dramatiq 收集器範例
│   │   ├── email_collector_example.py             # ✅ 郵件收集器範例
│   │   └── README.md                              # ✅ 範例說明
│   │
│   └── docs/                                      # ✅ 完整文檔系統
│       ├── __init__.py
│       ├── api_documentation.md                   # ✅ API 文檔 (Task 21 完成)
│       ├── websocket_api.md                       # ✅ WebSocket API 文檔
│       ├── configuration_guide.md                 # ✅ 配置指南
│       ├── dramatiq_collector_guide.md            # ✅ Dramatiq 收集器指南
│       ├── email_collector_guide.md               # ✅ 郵件收集器指南
│       ├── file_collector_guide.md                # ✅ 檔案收集器指南
│       ├── system_collector_guide.md              # ✅ 系統收集器指南
│       ├── alert_service_guide.md                 # ✅ 告警服務指南
│       ├── alert_api_guide.md                     # ✅ 告警 API 指南
│       ├── cache_service_guide.md                 # ✅ 快取服務指南
│       ├── dashboard_main_guide.md                # ✅ 主儀表板指南
│       ├── dashboard_trend_analyzer_guide.md      # ✅ 趨勢分析指南
│       ├── data_access_layer_guide.md             # ✅ 資料存取層指南
│       ├── database_expansion_summary.md          # ✅ 資料庫擴展摘要
│       ├── database_maintenance_guide.md          # ✅ 資料庫維護指南
│       ├── frontend_javascript_implementation.md  # ✅ 前端 JavaScript 實現
│       ├── pipeline_vendor_integration_guide.md   # ✅ Pipeline 廠商整合指南
│       ├── real_data_integration_guide.md         # ✅ 真實資料整合指南
│       ├── routing_middleware_guide.md            # ✅ 路由中間件指南
│       ├── css_style_system_guide.md              # ✅ CSS 樣式系統指南
│       ├── IMPLEMENTATION_STATUS.md               # ✅ 實現狀態總覽
│       ├── VERSION_UPDATE_SUMMARY.md              # ✅ 版本更新摘要
│       ├── DOCUMENTATION_UPDATE_SUMMARY.md        # ✅ 文檔更新摘要
│       ├── CELERY_CLEANUP_SUMMARY.md              # ✅ Celery 清理摘要
│       ├── TASK_17_COMPLETION_SUMMARY.md          # ✅ Task 17 完成摘要
│       ├── TASK_18_COMPLETION_SUMMARY.md          # ✅ Task 18 完成摘要
│       ├── TASK_19_COMPLETION_SUMMARY.md          # ✅ Task 19 完成摘要
│       ├── TASK_21_COMPLETION_SUMMARY.md          # ✅ Task 21 完成摘要
│       ├── TASK_22_COMPLETION_SUMMARY.md          # ✅ Task 22 完成摘要
│       ├── TASK_23_COMPLETION_SUMMARY.md          # ✅ Task 23 完成摘要
│       ├── TASK_24_COMPLETION_SUMMARY.md          # ✅ Task 24 完成摘要
│       ├── TASK_25_COMPLETION_SUMMARY.md          # ✅ Task 25 完成摘要
│       ├── task_26_completion_summary.md          # ✅ Task 26 完成摘要
│       └── TASK_27_COMPLETION_SUMMARY.md          # ✅ Task 27 完成摘要
```

### 📊 實現狀態總覽

| 模組 | 完成狀態 | 任務編號 | 核心功能 |
|------|----------|----------|----------|
| **核心協調器** | ✅ 100% | Task 9 | 監控系統生命週期管理 |
| **Dramatiq 收集器** | ✅ 100% | Task 10 | 任務佇列監控 |
| **資料模型** | ✅ 100% | Task 11 | 統一資料結構 |
| **WebSocket 管理** | ✅ 100% | Task 12 | 即時資料推送 |
| **配置系統** | ✅ 100% | Task 13 | 統一配置管理 |
| **快取服務** | ✅ 100% | Task 14 | 高效能快取 |
| **系統收集器** | ✅ 100% | Task 15 | 系統資源監控 |
| **郵件收集器** | ✅ 100% | Task 16 | 郵件處理監控 |
| **檔案收集器** | ✅ 100% | Task 17 | 檔案處理監控 |
| **資料存取層** | ✅ 100% | Task 18 | 資料庫操作 |
| **告警系統** | ✅ 100% | Task 19 | 智能告警 |
| **趨勢分析** | ✅ 100% | Task 20 | 歷史趨勢分析 |
| **監控 API** | ✅ 100% | Task 21 | REST API 端點 |
| **服務整合** | ✅ 100% | Task 22 | 系統整合 |
| **前端頁面** | ✅ 100% | Task 23 | HTML 模板 |
| **路由中間件** | ✅ 100% | Task 24 | 路由管理 |
| **工具函數** | ✅ 100% | Task 25 | 輔助工具 |
| **真實資料整合** | ✅ 100% | Task 26 | 實際資料源 |
| **Pipeline 監控** | ✅ 100% | Task 27 | 管道監控 |
| **廠商文件監控** | ✅ 100% | Task 28 | 廠商文件追蹤 |

### 🎯 技術特色

1. **完整實現** - 87.5% 完成率 (Task 1-28 已完成)
2. **模組化設計** - 清晰的模組邊界和職責分離
3. **豐富文檔** - 每個模組都有詳細的使用指南
4. **測試範例** - 提供完整的使用範例和測試腳本
5. **生產就緒** - 完整的配置、遷移和部署支援

---

## 🎨 前端目錄結構 ✅ 已實現

統一監控儀表板的前端部分整合在模組化 Flask 架構中，與 Vue.js 前端遷移系統協同工作：

```
frontend/                                          # ✅ 前端主目錄 (Flask 模組化架構)
├── app.py                                         # ✅ Flask 主應用 (工廠模式)
├── config.py                                      # ✅ 多環境配置管理
├── cli.py                                         # ✅ CLI 工具
├── README.md                                      # ✅ 前端開發指南
├── __init__.py                                    # ✅ 模組初始化
│
├── email/                                         # ✅ 📧 郵件功能模組
│   ├── templates/                                 # ✅ 郵件相關 HTML 模板
│   │   ├── inbox.html                            # ✅ 收件匣頁面
│   │   ├── email_detail.html                     # ✅ 郵件詳情
│   │   ├── email_compose.html                    # ✅ 撰寫郵件
│   │   ├── email_settings.html                   # ✅ 郵件設定
│   │   └── inbox_new.html                        # ✅ 新收件匣介面
│   ├── static/                                   # ✅ 郵件專用靜態資源
│   │   ├── css/
│   │   │   └── inbox.css                         # ✅ 郵件專用樣式
│   │   ├── js/
│   │   │   └── email/                            # ✅ 郵件 JS 模組
│   │   │       ├── email-*.js                    # ✅ 郵件相關邏輯
│   │   │       └── email-parser-ui.js            # ✅ 郵件解析介面
│   │   └── images/                               # ✅ 郵件相關圖片
│   ├── components/                               # ✅ 可重用組件（空目錄，待擴展）
│   ├── routes/
│   │   └── email_routes.py                       # ✅ 郵件相關路由
│   └── README.md                                 # ✅ 郵件模組說明
│
├── analytics/                                     # ✅ 📊 分析統計功能模組
│   ├── templates/
│   │   ├── dashboard.html                        # ✅ 統計儀表板
│   │   ├── reports.html                          # ✅ 報表頁面
│   │   ├── vendor_analysis.html                  # ✅ 廠商分析
│   │   └── csv_processor.html                    # ✅ CSV 處理頁面
│   ├── static/
│   │   ├── css/                                  # ✅ 分析專用樣式
│   │   ├── js/                                   # ✅ 分析專用 JavaScript
│   │   └── lib/                                  # ✅ 第三方庫
│   ├── components/                               # ✅ 空目錄，待擴展
│   ├── routes/
│   │   └── analytics_routes.py                   # ✅ 統計路由
│   └── README.md                                 # ✅ 分析模組說明
│
├── file_management/                               # ✅ 📁 檔案管理功能模組
│   ├── templates/
│   │   ├── file_manager.html                     # ✅ 檔案管理器
│   │   ├── upload.html                           # ✅ 檔案上傳
│   │   ├── attachment_browser.html               # ✅ 附件瀏覽器
│   │   └── network_browser.html                  # ✅ 網路瀏覽器
│   ├── static/                                   # ✅ 檔案管理靜態資源
│   ├── components/                               # ✅ 空目錄，待擴展
│   ├── routes/
│   │   └── file_routes.py                        # ✅ 檔案路由
│   └── README.md                                 # ✅ 檔案管理說明
│
├── eqc/                                          # ✅ 🔧 EQC功能模組
│   ├── templates/
│   │   ├── eqc_dashboard.html                    # ✅ EQC儀表板
│   │   ├── eqc_history.html                      # ✅ EQC歷史記錄
│   │   ├── quality_check.html                    # ✅ 品質檢查
│   │   └── compliance.html                       # ✅ 合規檢查
│   ├── static/                                   # ✅ EQC 靜態資源
│   ├── components/                               # ✅ 空目錄，待擴展
│   ├── routes/
│   │   └── eqc_routes.py                         # ✅ EQC路由
│   └── README.md                                 # ✅ EQC 模組說明
│
├── tasks/                                        # ✅ ⚙️ 任務管理功能模組
│   ├── templates/
│   │   ├── task_dashboard.html                   # ✅ 任務儀表板
│   │   ├── task_queue.html                       # ✅ 任務隊列
│   │   ├── task_scheduler.html                   # ✅ 任務調度
│   │   └── concurrent_task_manager.html          # ✅ 並發任務管理
│   ├── static/                                   # ✅ 任務管理靜態資源
│   ├── components/                               # ✅ 空目錄，待擴展
│   ├── routes/
│   │   └── task_routes.py                        # ✅ 任務路由
│   └── README.md                                 # ✅ 任務模組說明
│
├── monitoring/                                   # ✅ 📈 監控功能模組
│   ├── templates/
│   │   ├── system_dashboard.html                 # ✅ 系統監控儀表板
│   │   ├── health_check.html                     # ✅ 健康檢查
│   │   ├── database_manager.html                 # ✅ 資料庫管理
│   │   └── realtime_dashboard.html               # ✅ 即時監控
│   ├── static/                                   # ✅ 監控靜態資源
│   ├── components/                               # ✅ 空目錄，待擴展
│   ├── routes/
│   │   └── monitoring_routes.py                  # ✅ 監控路由
│   └── README.md                                 # ✅ 監控模組說明
│
└── shared/                                       # ✅ 🔗 共享前端資源
    ├── templates/
    │   ├── base.html                             # ✅ 基礎模板（主要佈局）
    │   └── components/                           # ✅ 共享組件
    │       ├── navbar.html                       # ✅ 導航列
    │       ├── sidebar.html                      # ✅ 側邊欄
    │       ├── modal.html                        # ✅ 模態框
    │       ├── loading.html                      # ✅ 載入動畫
    │       ├── notification.html                 # ✅ 通知組件
    │       └── confirm-dialog.html               # ✅ 確認對話框
    ├── static/
    │   ├── css/
    │   │   ├── base.css                          # ✅ 基礎樣式
    │   │   ├── layout.css                        # ✅ 佈局樣式
    │   │   ├── responsive.css                    # ✅ 響應式樣式
    │   │   ├── special-components.css            # ✅ 特殊組件樣式
    │   │   ├── components.css                    # ✅ 組件樣式
    │   │   ├── global.css                        # ✅ 全域樣式
    │   │   └── variables.css                     # ✅ CSS 變數
    │   ├── js/
    │   │   ├── main.js                           # ✅ 主要 JavaScript
    │   │   ├── ui-components.js                  # ✅ UI 組件
    │   │   ├── core/                             # ✅ 核心功能
    │   │   │   ├── api-client.js                 # ✅ API 客戶端
    │   │   │   ├── utils.js                      # ✅ 工具函數
    │   │   │   ├── dom-manager.js                # ✅ DOM 管理
    │   │   │   ├── status-manager.js             # ✅ 狀態管理
    │   │   │   └── error-handler.js              # ✅ 錯誤處理
    │   │   ├── utils/                            # ✅ 工具模組
    │   │   │   └── url-config.js                 # ✅ URL 配置
    │   │   └── components/                       # ✅ JavaScript 組件
    │   │       ├── notification.js               # ✅ 通知組件
    │   │       ├── countdown-modal.js            # ✅ 倒數計時模態框
    │   │       ├── detail-panel.js               # ✅ 詳情面板
    │   │       ├── download-modal.js             # ✅ 下載模態框
    │   │       ├── file-upload.js                # ✅ 檔案上傳
    │   │       ├── modal.js                      # ✅ 模態框
    │   │       └── progress-display.js           # ✅ 進度顯示
    │   ├── lib/                                  # ✅ 第三方函式庫
    │   │   └── chart.min.js                      # ✅ 圖表庫
    │   └── images/                               # ✅ 共享圖片
    │       └── favicon.ico                       # ✅ 網站圖標
    ├── utils/                                    # ✅ Python 工具
    │   ├── __init__.py                           # ✅ 模組初始化
    │   └── error_handler.py                      # ✅ 錯誤處理器
    └── README.md                                 # ✅ 共享資源說明
```

### 🔗 前後端整合架構

統一監控儀表板採用**前後端分離但整合**的架構：

#### 🎯 **後端監控系統** (`src/dashboard_monitoring/`)
- **FastAPI + WebSocket** - 提供監控 API 和即時資料推送
- **資料收集器** - 收集郵件、Dramatiq、系統、檔案等監控資料
- **告警系統** - 智能告警和通知機制
- **趨勢分析** - 歷史資料分析和預測

#### 🎨 **前端展示系統** (`frontend/monitoring/`)
- **Flask 模組化架構** - 提供監控頁面和使用者介面
- **HTML 模板** - 響應式監控儀表板頁面
- **JavaScript 組件** - 即時資料更新和互動功能
- **共享資源** - 統一的樣式、組件和工具函數

#### 🔄 **資料流整合**
```
前端頁面 (Flask Templates) 
    ↕ WebSocket/REST API
後端監控系統 (FastAPI)
    ↕ 資料收集
外部系統 (Email/Dramatiq/System)
```

### 📊 前端模組狀態

| 模組 | 完成狀態 | 核心功能 | 整合狀態 |
|------|----------|----------|----------|
| **Email 模組** | ✅ 100% | 郵件管理介面 | ✅ 與後端監控整合 |
| **Analytics 模組** | ✅ 100% | 統計分析介面 | ✅ 與後端監控整合 |
| **File Management 模組** | ✅ 100% | 檔案管理介面 | ✅ 與後端監控整合 |
| **EQC 模組** | ✅ 100% | 品質控制介面 | ✅ 與後端監控整合 |
| **Tasks 模組** | ✅ 100% | 任務管理介面 | ✅ 與後端監控整合 |
| **Monitoring 模組** | ✅ 100% | 監控儀表板介面 | ✅ 與後端監控完美整合 |
| **Shared 資源** | ✅ 100% | 共享組件和工具 | ✅ 支援所有模組 |

### 🚀 Vue.js 遷移準備

前端架構已完全準備好 Vue.js 遷移：

1. **✅ 模組邊界清晰** - 6個獨立功能模組
2. **✅ 組件化設計** - 共享組件系統
3. **✅ API 標準化** - 統一的 REST API 介面
4. **✅ 狀態管理準備** - JavaScript 狀態管理模式
5. **✅ 路由模組化** - Flask 藍圖對應 Vue Router

### 資料結構設計

#### 1. 監控指標資料結構

```python
# src/dashboard_monitoring/models/metrics_models.py

from dataclasses import dataclass
from datetime import datetime
from typing import Dict, List, Optional, Any
from enum import Enum

@dataclass
class DashboardMetrics:
    """儀表板主要指標"""
    timestamp: datetime
    email_metrics: 'EmailMetrics'
    dramatiq_metrics: 'DramatiqMetrics'
    system_metrics: 'SystemMetrics'
    file_metrics: 'FileMetrics'
    business_metrics: 'BusinessMetrics'

@dataclass
class EmailMetrics:
    """郵件處理指標"""
    # 佇列狀態
    pending_count: int
    processing_count: int
    completed_count: int
    failed_count: int
    
    # 處理效能
    avg_processing_time_seconds: float
    throughput_per_hour: float
    
    # 按廠商分組
    vendor_queue_counts: Dict[str, int]  # {"GTK": 5, "JCET": 3}
    vendor_success_rates: Dict[str, float]  # {"GTK": 0.95, "JCET": 0.88}
    
    # code_comparison.py 相關
    code_comparison_active: int
    code_comparison_pending: int
    code_comparison_avg_duration: float

@dataclass
class DramatiqMetrics:
    """Dramatiq 任務指標"""
    # 總體狀態
    total_active: int
    total_pending: int
    total_completed: int
    total_failed: int
    
    # 按任務類型分組
    task_type_counts: Dict[str, Dict[str, int]]
    # {
    #   "code_comparison": {"active": 2, "pending": 5, "completed": 100, "failed": 3},
    #   "csv_to_summary": {"active": 1, "pending": 2, "completed": 50, "failed": 1},
    #   "compression": {"active": 0, "pending": 1, "completed": 30, "failed": 0},
    #   "decompression": {"active": 1, "pending": 0, "completed": 25, "failed": 2}
    # }
    
    # 工作者狀態
    worker_status: Dict[str, str]  # {"worker1": "online", "worker2": "offline"}
    worker_load: Dict[str, int]    # {"worker1": 3, "worker2": 0}
    
    # 效能指標
    avg_task_duration: Dict[str, float]  # 按任務類型的平均執行時間
    task_success_rate: Dict[str, float]  # 按任務類型的成功率

@dataclass
class SystemMetrics:
    """系統資源指標"""
    # 資源使用率
    cpu_percent: float
    memory_percent: float
    disk_percent: float
    
    # 詳細資源資訊
    memory_available_mb: float
    disk_free_gb: float
    
    # 網路和連接
    active_connections: int
    websocket_connections: int
    
    # 服務健康狀態
    service_health: Dict[str, str]
    # {
    #   "email_service": "healthy",
    #   "dramatiq_service": "healthy", 
    #   "database": "healthy",
    #   "scheduler": "warning"
    # }
    
    # 資料庫指標
    database_connections: int
    database_query_avg_time: float
    database_size_mb: float

@dataclass
class FileMetrics:
    """檔案處理指標"""
    # 附件處理
    attachments_downloaded: int
    attachments_pending: int
    attachments_failed: int
    
    # 檔案類型統計
    file_type_counts: Dict[str, int]  # {"csv": 10, "excel": 5, "zip": 3}
    
    # 壓縮/解壓縮
    compression_active: int
    compression_pending: int
    decompression_active: int
    decompression_pending: int
    
    # 儲存空間
    temp_folder_size_mb: float
    upload_folder_size_mb: float
    processed_folder_size_mb: float
    
    # 處理效能
    avg_download_time: float
    avg_compression_time: float
    avg_decompression_time: float

@dataclass
class BusinessMetrics:
    """業務指標"""
    # MO/LOT 處理統計
    mo_processed_today: int
    lot_processed_today: int
    
    # 資料品質
    data_quality_score: float  # 0-100
    validation_errors_count: int
    duplicate_mo_count: int
    
    # 廠商處理統計
    vendor_processing_stats: Dict[str, Dict[str, int]]
    # {
    #   "GTK": {"mo_count": 15, "lot_count": 45, "success_rate": 95},
    #   "JCET": {"mo_count": 8, "lot_count": 24, "success_rate": 88}
    # }
    
    # Excel 報告生成
    reports_generated_today: int
    reports_pending: int
    avg_report_generation_time: float
```

#### 2. 告警資料結構

```python
# src/dashboard_monitoring/models/alert_models.py

@dataclass
class DashboardAlert:
    """儀表板告警"""
    id: str
    alert_type: str  # "queue_overflow", "task_failure", "resource_high", "service_down"
    level: str       # "info", "warning", "error", "critical"
    title: str
    message: str
    source: str      # "email_monitor", "dramatiq_monitor", "system_monitor"
    
    # 時間資訊
    triggered_at: datetime
    acknowledged_at: Optional[datetime] = None
    resolved_at: Optional[datetime] = None
    
    # 狀態和計數
    status: str = "active"  # "active", "acknowledged", "resolved"
    occurrence_count: int = 1
    
    # 額外資料
    metadata: Dict[str, Any] = None
    
    # 告警規則
    threshold_value: Optional[float] = None
    current_value: Optional[float] = None
    
    def is_active(self) -> bool:
        return self.status == "active"
    
    def is_critical(self) -> bool:
        return self.level == "critical"
```

#### 3. 儀表板配置結構

```python
# src/dashboard_monitoring/config/dashboard_config.py

@dataclass
class DashboardConfig:
    """儀表板配置"""
    
    # 更新頻率 (秒)
    metrics_update_interval: int = 30
    alerts_check_interval: int = 10
    trends_update_interval: int = 300  # 5分鐘
    
    # 資料保留期限 (天)
    metrics_retention_days: int = 30
    alerts_retention_days: int = 90
    task_history_retention_days: int = 7
    
    # 告警閾值
    alert_thresholds: Dict[str, Dict[str, float]] = None
    
    # WebSocket 配置
    websocket_heartbeat_interval: int = 30
    max_websocket_connections: int = 100
    
    # 顯示配置
    default_time_range: str = "1h"  # "1h", "6h", "24h", "7d"
    max_chart_data_points: int = 100
    
    def __post_init__(self):
        if self.alert_thresholds is None:
            self.alert_thresholds = {
                "email_queue": {
                    "pending_warning": 10,
                    "pending_critical": 50,
                    "processing_time_warning": 300,  # 5分鐘
                    "processing_time_critical": 900  # 15分鐘
                },
                "dramatiq_tasks": {
                    "pending_warning": 20,
                    "pending_critical": 100,
                    "failure_rate_warning": 0.1,    # 10%
                    "failure_rate_critical": 0.25   # 25%
                },
                "system_resources": {
                    "cpu_warning": 80,
                    "cpu_critical": 95,
                    "memory_warning": 85,
                    "memory_critical": 95,
                    "disk_warning": 85,
                    "disk_critical": 95
                },
                "file_processing": {
                    "temp_size_warning": 1000,      # 1GB
                    "temp_size_critical": 5000,     # 5GB
                    "processing_time_warning": 600, # 10分鐘
                    "processing_time_critical": 1800 # 30分鐘
                }
            }
```

### 前端資料結構

#### JavaScript 資料結構

```javascript
// src/dashboard_monitoring/static/js/dashboard_main.js

class DashboardData {
    constructor() {
        this.metrics = {
            email: {
                pending: 0,
                processing: 0,
                completed: 0,
                failed: 0,
                vendorCounts: {},
                codeComparisonActive: 0
            },
            dramatiq: {
                totalActive: 0,
                totalPending: 0,
                taskTypeCounts: {},
                workerStatus: {},
                avgDuration: {}
            },
            system: {
                cpuPercent: 0,
                memoryPercent: 0,
                diskPercent: 0,
                activeConnections: 0,
                serviceHealth: {}
            },
            files: {
                attachmentsPending: 0,
                compressionActive: 0,
                tempFolderSize: 0,
                fileTypeCounts: {}
            },
            business: {
                moProcessedToday: 0,
                lotProcessedToday: 0,
                dataQualityScore: 0,
                reportsGenerated: 0
            }
        };
        
        this.alerts = [];
        this.trends = {};
        this.lastUpdate = null;
    }
    
    updateMetrics(newMetrics) {
        this.metrics = { ...this.metrics, ...newMetrics };
        this.lastUpdate = new Date();
        this.notifyObservers();
    }
    
    addAlert(alert) {
        this.alerts.unshift(alert);
        this.notifyObservers();
    }
    
    // Observer pattern for UI updates
    observers = [];
    addObserver(callback) {
        this.observers.push(callback);
    }
    
    notifyObservers() {
        this.observers.forEach(callback => callback(this));
    }
}
```

### 資料庫表格結構詳細設計

```sql
-- 擴展現有 outlook.db 的詳細表格結構

-- 1. 監控指標歷史表 (詳細版)
CREATE TABLE dashboard_metrics_history (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    metric_category VARCHAR(30) NOT NULL,  -- 'email', 'dramatiq', 'system', 'file', 'business'
    metric_type VARCHAR(50) NOT NULL,      -- 'queue_count', 'cpu_percent', 'task_duration'
    metric_name VARCHAR(100) NOT NULL,     -- 'pending_emails', 'cpu_usage', 'code_comparison_avg_time'
    metric_value REAL NOT NULL,
    metric_unit VARCHAR(20),               -- 'count', 'percent', 'seconds', 'bytes'
    source_system VARCHAR(50),             -- 'email_collector', 'dramatiq_collector'
    tags TEXT,                             -- JSON: {"vendor": "GTK", "task_type": "code_comparison"}
    timestamp DATETIME NOT NULL,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    
    -- 索引
    INDEX idx_metrics_category_time (metric_category, timestamp),
    INDEX idx_metrics_type_time (metric_type, timestamp)
);

-- 2. 即時儀表板狀態表
CREATE TABLE dashboard_current_status (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    status_key VARCHAR(100) NOT NULL UNIQUE,  -- 'email_pending_count', 'dramatiq_active_tasks'
    status_value TEXT NOT NULL,               -- JSON 格式的值
    last_updated DATETIME NOT NULL,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    
    -- 索引
    INDEX idx_status_key (status_key),
    INDEX idx_status_updated (last_updated)
);

-- 3. 告警規則配置表
CREATE TABLE dashboard_alert_rules (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    rule_name VARCHAR(100) NOT NULL UNIQUE,
    rule_category VARCHAR(30) NOT NULL,      -- 'email', 'dramatiq', 'system', 'file'
    metric_path VARCHAR(200) NOT NULL,       -- 'email.pending_count', 'system.cpu_percent'
    condition_type VARCHAR(20) NOT NULL,     -- 'greater_than', 'less_than', 'equals'
    threshold_value REAL NOT NULL,
    alert_level VARCHAR(20) NOT NULL,        -- 'warning', 'critical'
    alert_message TEXT NOT NULL,
    is_enabled BOOLEAN DEFAULT TRUE,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- 4. WebSocket 連接管理表
CREATE TABLE dashboard_websocket_connections (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    client_id VARCHAR(100) NOT NULL UNIQUE,
    connection_ip VARCHAR(45),
    user_agent TEXT,
    subscriptions TEXT,                      -- JSON 格式的訂閱列表
    connected_at DATETIME NOT NULL,
    last_activity DATETIME NOT NULL,
    is_active BOOLEAN DEFAULT TRUE,
    
    -- 索引
    INDEX idx_client_id (client_id),
    INDEX idx_active_connections (is_active, last_activity)
);
```

這樣的結構設計讓統一監控儀表板功能一目了然，同時保持了良好的組織性和可維護性。

## 程式碼結構預覽

### 核心元件實現 (每個檔案 < 500行)

#### 1. 監控協調器 (dashboard_monitoring_coordinator.py)

```python
"""
統一監控協調器 - 系統核心元件
負責協調所有監控活動，管理資料收集和分發
"""

import asyncio
import logging
from datetime import datetime
from typing import Dict, List, Any, Optional
from dataclasses import asdict

from ..models.dashboard_metrics_models import DashboardMetrics
from ..collectors.dashboard_email_collector import DashboardEmailCollector
from ..collectors.dashboard_dramatiq_collector import DashboardDramatiqCollector
from ..collectors.dashboard_system_collector import DashboardSystemCollector
from ..collectors.dashboard_file_collector import DashboardFileCollector
from ..core.dashboard_alert_service import DashboardAlertService
from ..repositories.dashboard_monitoring_repository import DashboardMonitoringRepository
from ..config.dashboard_config import DashboardConfig


class DashboardMonitoringCoordinator:
    """統一監控協調器"""
    
    def __init__(self, config: DashboardConfig):
        self.config = config
        self.logger = logging.getLogger(__name__)
        self.is_running = False
        
        # 初始化收集器
        self._init_collectors()
        
        # 初始化服務
        self.alert_service = DashboardAlertService(config)
        self.repository = DashboardMonitoringRepository()
        
        # WebSocket 連接管理
        self.websocket_connections: Dict[str, Any] = {}
        
    def _init_collectors(self):
        """初始化所有資料收集器"""
        self.collectors = {
            'email': DashboardEmailCollector(),
            'dramatiq': DashboardDramatiqCollector(),
            'system': DashboardSystemCollector(),
            'file': DashboardFileCollector()
        }
        
    async def start_monitoring(self) -> None:
        """啟動監控系統"""
        if self.is_running:
            self.logger.warning("監控系統已在運行中")
            return
            
        self.is_running = True
        self.logger.info("啟動統一監控系統")
        
        # 啟動定期收集任務
        asyncio.create_task(self._metrics_collection_loop())
        asyncio.create_task(self._alert_evaluation_loop())
        
    async def stop_monitoring(self) -> None:
        """停止監控系統"""
        self.is_running = False
        self.logger.info("停止統一監控系統")
        
    async def _metrics_collection_loop(self) -> None:
        """指標收集循環"""
        while self.is_running:
            try:
                metrics = await self.collect_all_metrics()
                await self._store_metrics(metrics)
                await self._broadcast_metrics(metrics)
                
            except Exception as e:
                self.logger.error(f"指標收集錯誤: {e}")
                
            await asyncio.sleep(self.config.metrics_update_interval)
            
    async def _alert_evaluation_loop(self) -> None:
        """告警評估循環"""
        while self.is_running:
            try:
                metrics = await self.collect_all_metrics()
                alerts = await self.alert_service.evaluate_alerts(asdict(metrics))
                
                for alert in alerts:
                    await self._handle_alert(alert)
                    
            except Exception as e:
                self.logger.error(f"告警評估錯誤: {e}")
                
            await asyncio.sleep(self.config.alerts_check_interval)
            
    async def collect_all_metrics(self) -> DashboardMetrics:
        """收集所有監控指標"""
        timestamp = datetime.now()
        
        # 並行收集所有指標
        tasks = [
            self.collectors['email'].collect_metrics(),
            self.collectors['dramatiq'].collect_metrics(),
            self.collectors['system'].collect_metrics(),
            self.collectors['file'].collect_metrics()
        ]
        
        email_metrics, dramatiq_metrics, system_metrics, file_metrics = await asyncio.gather(*tasks)
        
        # 計算業務指標
        business_metrics = await self._calculate_business_metrics(
            email_metrics, dramatiq_metrics
        )
        
        return DashboardMetrics(
            timestamp=timestamp,
            email_metrics=email_metrics,
            dramatiq_metrics=dramatiq_metrics,
            system_metrics=system_metrics,
            file_metrics=file_metrics,
            business_metrics=business_metrics
        )
        
    async def _calculate_business_metrics(self, email_metrics, dramatiq_metrics):
        """計算業務指標"""
        # 實現業務指標計算邏輯
        # 這裡會整合郵件和任務資料來計算 MO/LOT 統計等
        pass
        
    async def _store_metrics(self, metrics: DashboardMetrics) -> None:
        """儲存指標到資料庫"""
        await self.repository.store_metrics(metrics)
        
    async def _broadcast_metrics(self, metrics: DashboardMetrics) -> None:
        """廣播指標到所有 WebSocket 連接"""
        if not self.websocket_connections:
            return
            
        message = {
            'type': 'metrics_update',
            'payload': asdict(metrics),
            'timestamp': datetime.now().isoformat()
        }
        
        # 廣播到所有連接
        for client_id, connection in self.websocket_connections.items():
            try:
                await connection.send_json(message)
            except Exception as e:
                self.logger.error(f"廣播到客戶端 {client_id} 失敗: {e}")
                
    async def _handle_alert(self, alert) -> None:
        """處理告警"""
        # 儲存告警
        await self.repository.store_alert(alert)
        
        # 發送通知
        await self.alert_service.send_alert(alert)
        
        # 廣播告警到 WebSocket
        await self._broadcast_alert(alert)
        
    async def _broadcast_alert(self, alert) -> None:
        """廣播告警到 WebSocket"""
        message = {
            'type': 'alert',
            'payload': asdict(alert),
            'timestamp': datetime.now().isoformat()
        }
        
        for client_id, connection in self.websocket_connections.items():
            try:
                await connection.send_json(message)
            except Exception as e:
                self.logger.error(f"廣播告警到客戶端 {client_id} 失敗: {e}")
                
    def add_websocket_connection(self, client_id: str, connection) -> None:
        """添加 WebSocket 連接"""
        self.websocket_connections[client_id] = connection
        self.logger.info(f"添加 WebSocket 連接: {client_id}")
        
    def remove_websocket_connection(self, client_id: str) -> None:
        """移除 WebSocket 連接"""
        if client_id in self.websocket_connections:
            del self.websocket_connections[client_id]
            self.logger.info(f"移除 WebSocket 連接: {client_id}")
            
    async def get_current_status(self) -> Dict[str, Any]:
        """獲取當前系統狀態"""
        metrics = await self.collect_all_metrics()
        active_alerts = await self.repository.get_active_alerts()
        
        return {
            'metrics': asdict(metrics),
            'active_alerts': [asdict(alert) for alert in active_alerts],
            'websocket_connections': len(self.websocket_connections),
            'system_status': 'running' if self.is_running else 'stopped'
        }


# 全域實例管理
_coordinator_instance: Optional[DashboardMonitoringCoordinator] = None

def get_dashboard_coordinator(config: Optional[DashboardConfig] = None) -> DashboardMonitoringCoordinator:
    """獲取監控協調器單例"""
    global _coordinator_instance
    
    if _coordinator_instance is None:
        if config is None:
            config = DashboardConfig()
        _coordinator_instance = DashboardMonitoringCoordinator(config)
        
    return _coordinator_instance
```

#### 2. 郵件監控收集器 (dashboard_email_collector.py)

```python
"""
郵件監控收集器 - 收集郵件處理相關指標
整合現有的郵件處理系統和任務管理器
"""

import logging
from datetime import datetime, timedelta
from typing import Dict, Any

from src.infrastructure.adapters.database.email_database import EmailDatabase
from src.services.concurrent_task_manager import get_task_manager
from ..models.dashboard_metrics_models import EmailMetrics


class DashboardEmailCollector:
    """郵件監控收集器"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.email_db = EmailDatabase()
        self.task_manager = get_task_manager()
        
    async def collect_metrics(self) -> EmailMetrics:
        """收集郵件處理指標"""
        try:
            # 收集基本佇列指標
            queue_metrics = await self._collect_queue_metrics()
            
            # 收集處理效能指標
            performance_metrics = await self._collect_performance_metrics()
            
            # 收集廠商分組指標
            vendor_metrics = await self._collect_vendor_metrics()
            
            # 收集 code_comparison 相關指標
            code_comparison_metrics = await self._collect_code_comparison_metrics()
            
            return EmailMetrics(
                **queue_metrics,
                **performance_metrics,
                **vendor_metrics,
                **code_comparison_metrics
            )
            
        except Exception as e:
            self.logger.error(f"收集郵件指標失敗: {e}")
            return self._get_default_metrics()
            
    async def _collect_queue_metrics(self) -> Dict[str, int]:
        """收集佇列狀態指標"""
        with self.email_db.get_session() as session:
            from src.infrastructure.adapters.database.models import EmailDB, EmailProcessStatusDB
            
            # 查詢不同狀態的郵件數量
            pending_count = session.query(EmailDB).filter(
                EmailDB.is_processed == False
            ).count()
            
            processing_count = session.query(EmailProcessStatusDB).filter(
                EmailProcessStatusDB.status == 'processing'
            ).count()
            
            completed_count = session.query(EmailProcessStatusDB).filter(
                EmailProcessStatusDB.status == 'completed'
            ).count()
            
            failed_count = session.query(EmailProcessStatusDB).filter(
                EmailProcessStatusDB.status == 'failed'
            ).count()
            
            return {
                'pending_count': pending_count,
                'processing_count': processing_count,
                'completed_count': completed_count,
                'failed_count': failed_count
            }
            
    async def _collect_performance_metrics(self) -> Dict[str, float]:
        """收集處理效能指標"""
        with self.email_db.get_session() as session:
            from src.infrastructure.adapters.database.models import EmailProcessStatusDB
            from sqlalchemy import func
            
            # 計算平均處理時間
            avg_time_result = session.query(
                func.avg(EmailProcessStatusDB.processing_time)
            ).filter(
                EmailProcessStatusDB.status == 'completed',
                EmailProcessStatusDB.completed_at >= datetime.now() - timedelta(hours=24)
            ).scalar()
            
            avg_processing_time = float(avg_time_result or 0)
            
            # 計算每小時處理量
            hourly_count = session.query(EmailProcessStatusDB).filter(
                EmailProcessStatusDB.completed_at >= datetime.now() - timedelta(hours=1)
            ).count()
            
            return {
                'avg_processing_time_seconds': avg_processing_time,
                'throughput_per_hour': float(hourly_count)
            }
            
    async def _collect_vendor_metrics(self) -> Dict[str, Dict[str, Any]]:
        """收集廠商分組指標"""
        with self.email_db.get_session() as session:
            from src.infrastructure.adapters.database.models import EmailDB
            from sqlalchemy import func
            
            # 按廠商統計待處理郵件
            vendor_counts = {}
            vendor_success_rates = {}
            
            # 這裡需要根據實際的廠商識別邏輯來實現
            # 暫時使用模擬資料
            vendor_counts = {'GTK': 5, 'JCET': 3, 'ETD': 2}
            vendor_success_rates = {'GTK': 0.95, 'JCET': 0.88, 'ETD': 0.92}
            
            return {
                'vendor_queue_counts': vendor_counts,
                'vendor_success_rates': vendor_success_rates
            }
            
    async def _collect_code_comparison_metrics(self) -> Dict[str, Any]:
        """收集 code_comparison 相關指標"""
        try:
            # 從任務管理器獲取 code_comparison 任務狀態
            active_tasks = self.task_manager.list_tasks(status='running')
            pending_tasks = self.task_manager.list_tasks(status='pending')
            
            code_comparison_active = len([
                task for task in active_tasks 
                if 'code_comparison' in task.get('task_type', '')
            ])
            
            code_comparison_pending = len([
                task for task in pending_tasks 
                if 'code_comparison' in task.get('task_type', '')
            ])
            
            # 計算平均執行時間
            completed_tasks = self.task_manager.list_tasks(status='completed', limit=100)
            code_comparison_tasks = [
                task for task in completed_tasks 
                if 'code_comparison' in task.get('task_type', '')
            ]
            
            if code_comparison_tasks:
                avg_duration = sum(
                    task.get('actual_duration', 0) for task in code_comparison_tasks
                ) / len(code_comparison_tasks)
            else:
                avg_duration = 0.0
                
            return {
                'code_comparison_active': code_comparison_active,
                'code_comparison_pending': code_comparison_pending,
                'code_comparison_avg_duration': avg_duration
            }
            
        except Exception as e:
            self.logger.error(f"收集 code_comparison 指標失敗: {e}")
            return {
                'code_comparison_active': 0,
                'code_comparison_pending': 0,
                'code_comparison_avg_duration': 0.0
            }
            
    def _get_default_metrics(self) -> EmailMetrics:
        """獲取預設指標（錯誤時使用）"""
        return EmailMetrics(
            pending_count=0,
            processing_count=0,
            completed_count=0,
            failed_count=0,
            avg_processing_time_seconds=0.0,
            throughput_per_hour=0.0,
            vendor_queue_counts={},
            vendor_success_rates={},
            code_comparison_active=0,
            code_comparison_pending=0,
            code_comparison_avg_duration=0.0
        )
```

#### 3. 儀表板 API (dashboard_api.py)

```python
"""
儀表板 API - 提供統一監控儀表板的 REST API 端點
"""

from datetime import datetime
from typing import Dict, Any, Optional
from fastapi import APIRouter, HTTPException, Query
from fastapi.responses import HTMLResponse
import logging

from ..core.dashboard_monitoring_coordinator import get_dashboard_coordinator
from ..repositories.dashboard_monitoring_repository import DashboardMonitoringRepository
from ..config.dashboard_config import DashboardConfig

# 建立路由器
router = APIRouter(
    prefix="/dashboard",
    tags=["統一監控儀表板"],
    responses={404: {"description": "Not found"}}
)

logger = logging.getLogger(__name__)


@router.get("/", response_class=HTMLResponse)
async def dashboard_main():
    """主儀表板頁面"""
    try:
        # 讀取 HTML 模板
        from pathlib import Path
        template_path = Path(__file__).parent.parent / "templates" / "dashboard_main.html"
        
        if template_path.exists():
            with open(template_path, 'r', encoding='utf-8') as f:
                return HTMLResponse(content=f.read())
        else:
            # 簡單的 HTML 回應
            return HTMLResponse(content="""
            <!DOCTYPE html>
            <html lang="zh-TW">
            <head>
                <meta charset="UTF-8">
                <title>統一監控儀表板</title>
                <style>
                    body { font-family: Arial, sans-serif; margin: 40px; }
                    .container { max-width: 1200px; margin: 0 auto; }
                    .metrics-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px; }
                    .metric-card { background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
                </style>
            </head>
            <body>
                <div class="container">
                    <h1>🚀 統一監控儀表板</h1>
                    <div id="metrics-container">載入中...</div>
                </div>
                <script>
                    // WebSocket 連接和資料更新邏輯
                    const ws = new WebSocket('ws://localhost:8000/ws/dashboard/main');
                    ws.onmessage = function(event) {
                        const data = JSON.parse(event.data);
                        updateDashboard(data);
                    };
                    
                    function updateDashboard(data) {
                        // 更新儀表板顯示
                        console.log('收到資料:', data);
                    }
                </script>
            </body>
            </html>
            """)
            
    except Exception as e:
        logger.error(f"載入儀表板頁面失敗: {e}")
        raise HTTPException(status_code=500, detail="無法載入儀表板")


@router.get("/api/status")
async def get_dashboard_status():
    """獲取儀表板整體狀態"""
    try:
        coordinator = get_dashboard_coordinator()
        status = await coordinator.get_current_status()
        
        return {
            "status": "success",
            "data": status,
            "timestamp": datetime.now().isoformat()
        }
        
    except Exception as e:
        logger.error(f"獲取儀表板狀態失敗: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/api/metrics/current")
async def get_current_metrics():
    """獲取當前指標"""
    try:
        coordinator = get_dashboard_coordinator()
        metrics = await coordinator.collect_all_metrics()
        
        return {
            "status": "success",
            "data": {
                "email": {
                    "pending": metrics.email_metrics.pending_count,
                    "processing": metrics.email_metrics.processing_count,
                    "completed": metrics.email_metrics.completed_count,
                    "failed": metrics.email_metrics.failed_count,
                    "vendor_counts": metrics.email_metrics.vendor_queue_counts
                },
                "dramatiq": {
                    "total_active": metrics.dramatiq_metrics.total_active,
                    "total_pending": metrics.dramatiq_metrics.total_pending,
                    "task_type_counts": metrics.dramatiq_metrics.task_type_counts
                },
                "system": {
                    "cpu_percent": metrics.system_metrics.cpu_percent,
                    "memory_percent": metrics.system_metrics.memory_percent,
                    "disk_percent": metrics.system_metrics.disk_percent
                }
            },
            "timestamp": metrics.timestamp.isoformat()
        }
        
    except Exception as e:
        logger.error(f"獲取當前指標失敗: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/api/alerts/active")
async def get_active_alerts():
    """獲取活躍告警"""
    try:
        repository = DashboardMonitoringRepository()
        alerts = await repository.get_active_alerts()
        
        return {
            "status": "success",
            "data": [
                {
                    "id": alert.id,
                    "type": alert.alert_type,
                    "level": alert.level,
                    "title": alert.title,
                    "message": alert.message,
                    "triggered_at": alert.triggered_at.isoformat(),
                    "source": alert.source
                }
                for alert in alerts
            ],
            "count": len(alerts),
            "timestamp": datetime.now().isoformat()
        }
        
    except Exception as e:
        logger.error(f"獲取活躍告警失敗: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/api/alerts/{alert_id}/acknowledge")
async def acknowledge_alert(alert_id: str):
    """確認告警"""
    try:
        repository = DashboardMonitoringRepository()
        success = await repository.acknowledge_alert(alert_id)
        
        if not success:
            raise HTTPException(status_code=404, detail="告警不存在")
            
        return {
            "status": "success",
            "message": "告警已確認",
            "alert_id": alert_id,
            "timestamp": datetime.now().isoformat()
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"確認告警失敗: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/api/trends/{metric_type}")
async def get_trend_data(
    metric_type: str,
    time_range: str = Query("1h", description="時間範圍: 1h, 6h, 24h, 7d"),
    limit: int = Query(100, description="資料點數量限制")
):
    """獲取趨勢資料"""
    try:
        repository = DashboardMonitoringRepository()
        trend_data = await repository.get_trend_data(metric_type, time_range, limit)
        
        return {
            "status": "success",
            "data": trend_data,
            "metric_type": metric_type,
            "time_range": time_range,
            "timestamp": datetime.now().isoformat()
        }
        
    except Exception as e:
        logger.error(f"獲取趨勢資料失敗: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/api/config")
async def get_dashboard_config():
    """獲取儀表板配置"""
    try:
        config = DashboardConfig()
        
        return {
            "status": "success",
            "data": {
                "metrics_update_interval": config.metrics_update_interval,
                "alerts_check_interval": config.alerts_check_interval,
                "default_time_range": config.default_time_range,
                "alert_thresholds": config.alert_thresholds
            },
            "timestamp": datetime.now().isoformat()
        }
        
    except Exception as e:
        logger.error(f"獲取配置失敗: {e}")
        raise HTTPException(status_code=500, detail=str(e))


# 生命週期管理
def initialize_dashboard():
    """初始化儀表板系統"""
    try:
        coordinator = get_dashboard_coordinator()
        # 這裡可以添加初始化邏輯
        logger.info("儀表板系統已初始化")
    except Exception as e:
        logger.error(f"儀表板系統初始化失敗: {e}")


def shutdown_dashboard():
    """關閉儀表板系統"""
    try:
        coordinator = get_dashboard_coordinator()
        # 這裡可以添加清理邏輯
        logger.info("儀表板系統已關閉")
    except Exception as e:
        logger.error(f"儀表板系統關閉失敗: {e}")
```

### 程式碼設計原則

1. **單一職責原則** - 每個類別和函數都有明確的單一職責
2. **依賴注入模式** - 使用 FastAPI Depends() 統一管理服務依賴，解決緊耦合問題
3. **錯誤處理** - 完整的異常處理和日誌記錄
4. **非同步設計** - 所有 I/O 操作都使用非同步模式
5. **配置驅動** - 所有可配置項都集中在配置類別中
6. **模組化** - 清晰的模組邊界，便於獨立開發和測試

### 依賴注入架構設計

#### 核心問題解決
統一監控儀表板採用 FastAPI 依賴注入模式，解決以下問題：

1. **緊耦合問題**：每個 API 端點直接調用服務獲取函數
2. **測試困難**：無法輕易替換服務進行單元測試
3. **重複代碼**：多個端點都有相同的服務獲取和錯誤處理邏輯
4. **錯誤處理分散**：每個端點都需要處理服務不可用的情況

#### 依賴注入分層設計

```python
# 必需依賴 - 服務不可用會拋出 503 異常
MonitoringCoordinator = Annotated[DashboardMonitoringCoordinator, Depends(require_monitoring_coordinator)]

# 可選依賴 - 服務不可用返回 None，支援降級處理
OptionalMonitoringCoordinator = Annotated[Optional[DashboardMonitoringCoordinator], Depends(get_optional_monitoring_coordinator)]

# 複合依賴 - 一次注入所有服務，保證一致性
AllServices = Annotated[DashboardServices, Depends(get_dashboard_services)]
```

#### API 端點使用範例

```python
# ❌ 重構前的問題
@app.post("/api/monitoring/dashboard")
async def get_dashboard_data():
    try:
        coordinator = get_monitoring_coordinator()  # 直接調用
        if not coordinator:
            raise HTTPException(503, "監控服務不可用")
        # ... 處理邏輯
    except Exception as e:
        raise HTTPException(500, str(e))

# ✅ 重構後的優勢
@router.get("/dashboard")
async def get_dashboard_data(
    coordinator: MonitoringCoordinator,  # 自動注入，保證可用
    config: Config
) -> Dict[str, Any]:
    # 無需 try-catch，依賴注入已處理服務不可用情況
    metrics = await coordinator.collect_all_metrics()
    return {"status": "success", "data": metrics.dict()}
```

### 檔案大小控制策略

1. **功能分離** - 將複雜功能拆分到多個小檔案
2. **介面抽象** - 使用抽象基類定義介面
3. **工具函數** - 將通用邏輯提取到工具模組
4. **配置外部化** - 將配置和常數移到專門的配置檔案

## 與現有系統整合策略

### 主程式整合 (start_integrated_services.py)

為了確保新的監控儀表板能夠無縫整合到現有的 `start_integrated_services.py` 中，同時保持獨立性和可維護性，我們採用以下整合策略：

#### 1. 服務配置整合

在現有的服務配置中新增監控儀表板服務：

```python
# src/dashboard_monitoring/config/dashboard_service_config.py

from src.services.config_manager import ServiceConfig, ServiceType

def get_dashboard_service_config() -> ServiceConfig:
    """獲取監控儀表板服務配置"""
    return ServiceConfig(
        name="統一監控儀表板",
        service_type=ServiceType.FASTAPI,
        port=5555,  # 使用主端口
        path_prefix="/dashboard",
        enabled=True,
        host="0.0.0.0",
        timeout=30,
        metadata={
            "description": "統一監控儀表板 - 全方位系統監控",
            "version": "1.0.0",
            "features": [
                "郵件處理監控",
                "Dramatiq任務監控", 
                "系統資源監控",
                "檔案處理監控",
                "業務指標監控",
                "即時告警系統"
            ]
        }
    )
```

#### 2. 服務整合器擴展

創建監控儀表板服務整合器：

```python
# src/dashboard_monitoring/integration/dashboard_service_integrator.py

import logging
from typing import Optional
from fastapi import FastAPI

from ..core.dashboard_monitoring_coordinator import get_dashboard_coordinator
from ..api.dashboard_api import router as dashboard_router
from ..api.dashboard_monitoring_api import router as monitoring_router
from ..api.dashboard_websocket import websocket_router as dashboard_websocket_router
from ..config.dashboard_config import DashboardConfig


class DashboardServiceIntegrator:
    """監控儀表板服務整合器"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.coordinator = None
        self.is_initialized = False
        
    async def initialize(self, app: FastAPI) -> bool:
        """初始化監控儀表板服務"""
        try:
            self.logger.info("正在初始化統一監控儀表板...")
            
            # 初始化配置
            config = DashboardConfig()
            
            # 初始化監控協調器
            self.coordinator = get_dashboard_coordinator(config)
            
            # 註冊路由
            self._register_routes(app)
            
            # 啟動監控系統
            await self.coordinator.start_monitoring()
            
            # 註冊生命週期事件
            self._register_lifecycle_events(app)
            
            self.is_initialized = True
            self.logger.info("✅ 統一監控儀表板初始化完成")
            return True
            
        except Exception as e:
            self.logger.error(f"❌ 監控儀表板初始化失敗: {e}")
            return False
            
    def _register_routes(self, app: FastAPI):
        """註冊路由"""
        # 主儀表板路由
        app.include_router(
            dashboard_router,
            prefix="",  # 不使用前綴，因為路由器內部已有 /dashboard
            tags=["統一監控儀表板"]
        )
        
        # 監控 API 路由
        app.include_router(
            monitoring_router,
            prefix="/api/monitoring",
            tags=["監控 API"]
        )
        
        # WebSocket 路由
        app.include_router(
            dashboard_websocket_router,
            prefix="/ws/dashboard",
            tags=["監控 WebSocket"]
        )
        
        self.logger.info("監控儀表板路由已註冊")
        
    def _register_lifecycle_events(self, app: FastAPI):
        """註冊生命週期事件"""
        
        @app.on_event("startup")
        async def dashboard_startup():
            """儀表板啟動事件"""
            if self.coordinator and not self.coordinator.is_running:
                await self.coordinator.start_monitoring()
                
        @app.on_event("shutdown") 
        async def dashboard_shutdown():
            """儀表板關閉事件"""
            if self.coordinator and self.coordinator.is_running:
                await self.coordinator.stop_monitoring()
                
    async def shutdown(self):
        """關閉監控儀表板服務"""
        if self.coordinator:
            await self.coordinator.stop_monitoring()
            self.logger.info("統一監控儀表板已關閉")
            
    def get_service_info(self) -> dict:
        """獲取服務資訊"""
        return {
            "name": "統一監控儀表板",
            "status": "running" if self.is_initialized else "stopped",
            "endpoints": [
                "/dashboard",
                "/dashboard/api/status",
                "/dashboard/api/metrics/current",
                "/api/monitoring/alerts/active",
                "/ws/dashboard/{client_id}"
            ],
            "description": "全方位系統監控儀表板"
        }


# 全域實例
_dashboard_integrator: Optional[DashboardServiceIntegrator] = None

def get_dashboard_integrator() -> DashboardServiceIntegrator:
    """獲取監控儀表板整合器單例"""
    global _dashboard_integrator
    if _dashboard_integrator is None:
        _dashboard_integrator = DashboardServiceIntegrator()
    return _dashboard_integrator
```

#### 3. 主程式修改建議

在 `start_integrated_services.py` 中進行最小化修改：

```python
# 在 IntegratedServiceManager 類別中添加以下方法

class IntegratedServiceManager:
    def __init__(self):
        # ... 現有初始化代碼 ...
        
        # 新增：初始化監控儀表板整合器
        self.dashboard_integrator = None
        
    async def initialize(self) -> bool:
        """初始化所有服務"""
        try:
            # ... 現有初始化代碼 ...
            
            # 新增：初始化監控儀表板
            await self._initialize_dashboard()
            
            # ... 其餘現有代碼 ...
            
        except Exception as e:
            # ... 現有錯誤處理 ...
            
    async def _initialize_dashboard(self):
        """初始化監控儀表板（新增方法）"""
        try:
            from src.dashboard_monitoring.integration.dashboard_service_integrator import get_dashboard_integrator
            
            self.dashboard_integrator = get_dashboard_integrator()
            success = await self.dashboard_integrator.initialize(self.app)
            
            if success:
                logger.info("✅ 監控儀表板整合成功")
            else:
                logger.warning("⚠️ 監控儀表板整合失敗，但不影響其他服務")
                
        except Exception as e:
            logger.error(f"監控儀表板整合錯誤: {e}")
            # 不拋出異常，避免影響其他服務啟動
            
    def _add_management_interface(self):
        """添加管理介面（修改現有方法）"""
        
        # ... 現有管理介面代碼 ...
        
        # 新增：監控儀表板狀態 API
        @self.app.get("/admin/api/dashboard/status")
        async def get_dashboard_status():
            """獲取監控儀表板狀態"""
            try:
                if self.dashboard_integrator:
                    return self.dashboard_integrator.get_service_info()
                else:
                    return {"status": "not_initialized"}
            except Exception as e:
                logger.error(f"獲取監控儀表板狀態失敗: {e}")
                raise HTTPException(status_code=500, detail=str(e))
                
    def _generate_admin_dashboard(self) -> str:
        """生成管理後台 HTML（修改現有方法）"""
        # 在現有 HTML 的快速連結部分添加監控儀表板連結
        original_html = """
        # ... 現有 HTML 代碼 ...
        
        <div class="card">
            <h3>快速連結</h3>
            <p>
                <a href="/docs" class="btn">API 文檔</a>
                <a href="/health" class="btn">健康檢查</a>
                <a href="/inbox" class="btn">郵件收件夾</a>
                <a href="/ft-eqc" class="btn">FT-EQC 處理</a>
                <a href="/scheduler" class="btn">任務排程器</a>
                <a href="/network" class="btn">網路瀏覽器 (原版)</a>
                <a href="/network/ui_new" class="btn" style="background: #28a745;">網路瀏覽器 (新版)</a>
                <!-- 新增監控儀表板連結 -->
                <a href="/dashboard" class="btn" style="background: #17a2b8;">📊 統一監控儀表板</a>
            </p>
        </div>
        
        # ... 其餘 HTML 代碼 ...
        """
        return original_html
        
    async def shutdown(self):
        """關閉所有服務（修改現有方法）"""
        # ... 現有關閉代碼 ...
        
        # 新增：關閉監控儀表板
        if self.dashboard_integrator:
            await self.dashboard_integrator.shutdown()
```

#### 4. 配置文件整合

創建監控儀表板的配置整合：

```python
# src/dashboard_monitoring/config/dashboard_integration_config.py

def integrate_dashboard_config():
    """將監控儀表板配置整合到主配置中"""
    
    # 這個函數會在主程式啟動時被調用
    # 用於將監控儀表板的配置添加到全域配置管理器中
    
    from src.services.config_manager import get_config_manager
    from .dashboard_service_config import get_dashboard_service_config
    
    config_manager = get_config_manager()
    dashboard_config = get_dashboard_service_config()
    
    # 註冊監控儀表板服務
    config_manager.register_service("dashboard_monitoring", dashboard_config)
```

### 整合優勢

1. **最小侵入性** - 只需要在主程式中添加幾行代碼
2. **獨立維護** - 監控儀表板的所有代碼都在 `src/dashboard_monitoring/` 目錄下
3. **錯誤隔離** - 監控儀表板的錯誤不會影響其他服務
4. **統一管理** - 通過現有的管理後台可以查看監控儀表板狀態
5. **配置一致** - 使用相同的配置管理系統
6. **生命週期管理** - 與其他服務一起啟動和關閉

### 部署步驟

1. **創建目錄結構** - 在 `src/` 下創建 `dashboard_monitoring/` 目錄
2. **實現核心功能** - 按照設計實現各個模組
3. **創建整合器** - 實現 `DashboardServiceIntegrator`
4. **修改主程式** - 在 `start_integrated_services.py` 中添加整合代碼
5. **測試整合** - 確保新功能不影響現有服務
6. **更新文檔** - 更新管理後台和 API 文檔

### 向後相容性

- 現有的所有 API 端點保持不變
- 現有的服務配置不受影響
- 現有的路由和中間件繼續正常工作
- 如果監控儀表板初始化失敗，其他服務仍能正常運行

這種整合策略確保了新功能的添加不會破壞現有系統的穩定性，同時提供了清晰的模組邊界和維護性。