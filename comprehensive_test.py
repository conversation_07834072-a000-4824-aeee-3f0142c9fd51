#!/usr/bin/env python3
"""
全面的功能驗證測試
測試所有現有頁面是否正常載入，驗證所有現有功能是否正常運作
"""

import requests
import json
import time
import sys
from datetime import datetime

def run_comprehensive_test():
    """執行全面的功能驗證測試"""
    base_url = "http://localhost:8000"
    results = {
        'test_start_time': datetime.now().isoformat(),
        'base_url': base_url,
        'tests': [],
        'summary': {}
    }
    
    def log_test(name, success, message="", status_code=None, response_time=0):
        """記錄測試結果"""
        test_result = {
            'name': name,
            'success': success,
            'message': message,
            'status_code': status_code,
            'response_time_ms': round(response_time * 1000, 2),
            'timestamp': datetime.now().isoformat()
        }
        results['tests'].append(test_result)
        
        status = "✅ PASS" if success else "❌ FAIL"
        print(f"{status} {name}")
        if message:
            print(f"    {message}")
        if response_time > 0:
            print(f"    回應時間: {test_result['response_time_ms']}ms")
        return success
    
    print("🚀 開始執行全面功能驗證測試...")
    print(f"測試目標: {base_url}")
    print("=" * 60)
    
    session = requests.Session()
    session.timeout = 10
    
    # 1. 健康檢查測試
    print("\n📋 1. 健康檢查測試")
    try:
        start_time = time.time()
        response = session.get(f"{base_url}/health")
        response_time = time.time() - start_time
        
        if response.status_code == 200:
            health_data = response.json()
            log_test("健康檢查端點", True, f"狀態: {health_data.get('status')}", 200, response_time)
            
            # 檢查模組狀態
            modules = health_data.get('modules', {})
            expected_modules = ['email', 'analytics', 'file_management', 'eqc', 'tasks', 'monitoring']
            
            for module in expected_modules:
                if module in modules and modules[module]:
                    log_test(f"模組檢查: {module}", True, "模組狀態正常")
                else:
                    log_test(f"模組檢查: {module}", False, "模組狀態異常或缺失")
        else:
            log_test("健康檢查端點", False, f"HTTP {response.status_code}", response.status_code, response_time)
    except Exception as e:
        log_test("健康檢查端點", False, f"請求異常: {str(e)}")
    
    # 2. 主要路由測試
    print("\n🏠 2. 主要路由測試")
    main_routes = [
        ('/', 302, "主頁重定向"),
        ('/health', 200, "健康檢查"),
        ('/favicon.ico', 200, "Favicon"),
    ]
    
    for route, expected_status, description in main_routes:
        try:
            start_time = time.time()
            response = session.get(f"{base_url}{route}", allow_redirects=False)
            response_time = time.time() - start_time
            
            success = response.status_code == expected_status
            log_test(description, success, f"URL: {route}", response.status_code, response_time)
        except Exception as e:
            log_test(description, False, f"請求異常: {str(e)}")
    
    # 3. 模組頁面測試
    print("\n🔧 3. 模組頁面測試")
    module_pages = {
        'email': ['/email/', '/email/inbox'],
        'analytics': ['/analytics/', '/analytics/dashboard'],
        'file_management': ['/files/', '/files/manager'],
        'eqc': ['/eqc/', '/eqc/dashboard'],
        'tasks': ['/tasks/', '/tasks/dashboard'],
        'monitoring': ['/monitoring/', '/monitoring/dashboard']
    }
    
    for module_name, pages in module_pages.items():
        print(f"\n--- 測試 {module_name} 模組 ---")
        for page in pages:
            try:
                start_time = time.time()
                response = session.get(f"{base_url}{page}")
                response_time = time.time() - start_time
                
                success = response.status_code == 200
                log_test(f"{module_name}: {page}", success, 
                        f"頁面載入{'成功' if success else '失敗'}", 
                        response.status_code, response_time)
            except Exception as e:
                log_test(f"{module_name}: {page}", False, f"請求異常: {str(e)}")
    
    # 4. 靜態資源測試
    print("\n📁 4. 靜態資源測試")
    static_resources = [
        '/static/css/base.css',
        '/static/css/layout.css',
        '/static/js/core/error-handler.js'
    ]
    
    for resource in static_resources:
        try:
            start_time = time.time()
            response = session.get(f"{base_url}{resource}")
            response_time = time.time() - start_time
            
            # 靜態資源可能不存在，所以 404 也算正常
            success = response.status_code in [200, 404]
            status_msg = "存在" if response.status_code == 200 else "不存在"
            log_test(f"靜態資源: {resource}", success, 
                    f"資源{status_msg}", response.status_code, response_time)
        except Exception as e:
            log_test(f"靜態資源: {resource}", False, f"請求異常: {str(e)}")
    
    # 生成測試摘要
    total_tests = len(results['tests'])
    passed_tests = sum(1 for test in results['tests'] if test['success'])
    failed_tests = total_tests - passed_tests
    success_rate = (passed_tests / total_tests * 100) if total_tests > 0 else 0
    
    results['summary'] = {
        'total_tests': total_tests,
        'passed_tests': passed_tests,
        'failed_tests': failed_tests,
        'success_rate': round(success_rate, 2),
        'overall_success': failed_tests == 0,
        'test_end_time': datetime.now().isoformat()
    }
    
    # 列印摘要
    print("\n" + "=" * 60)
    print("📊 功能驗證測試結果摘要")
    print("=" * 60)
    
    overall_status = "✅ 全部通過" if results['summary']['overall_success'] else "❌ 有測試失敗"
    print(f"整體狀態: {overall_status}")
    print(f"總測試數: {total_tests}")
    print(f"通過測試: {passed_tests}")
    print(f"失敗測試: {failed_tests}")
    print(f"成功率: {success_rate:.1f}%")
    
    # 列出失敗的測試
    failed_tests_list = [test for test in results['tests'] if not test['success']]
    if failed_tests_list:
        print("\n❌ 失敗的測試:")
        for test in failed_tests_list:
            print(f"  - {test['name']}: {test['message']}")
    
    # 儲存結果到檔案
    try:
        with open('functional_verification_results.json', 'w', encoding='utf-8') as f:
            json.dump(results, f, ensure_ascii=False, indent=2)
        print(f"\n📄 詳細測試結果已儲存到: functional_verification_results.json")
    except Exception as e:
        print(f"\n❌ 儲存測試結果失敗: {str(e)}")
    
    print("\n" + "=" * 60)
    
    return results['summary']['overall_success']

if __name__ == "__main__":
    success = run_comprehensive_test()
    sys.exit(0 if success else 1)