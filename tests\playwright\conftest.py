"""
Playwright 測試配置文件
"""

import pytest
import asyncio
import os
from pathlib import Path


def pytest_configure(config):
    """配置 pytest"""
    # 創建截圖目錄
    screenshots_dir = Path("tests/screenshots")
    screenshots_dir.mkdir(parents=True, exist_ok=True)


@pytest.fixture(scope="session")
def event_loop():
    """創建事件循環"""
    loop = asyncio.get_event_loop_policy().new_event_loop()
    yield loop
    loop.close()


@pytest.fixture(autouse=True)
def setup_test_environment():
    """設置測試環境"""
    # 確保測試目錄存在
    test_dirs = [
        "tests/screenshots",
        "tests/reports",
        "tests/logs"
    ]
    
    for test_dir in test_dirs:
        Path(test_dir).mkdir(parents=True, exist_ok=True)
    
    yield
    
    # 清理（如果需要）
    pass
