<!-- 通知訊息容器 -->
<div class="notification-container" id="notification-container">
    <!-- 通知訊息會動態插入到這裡 -->
</div>

<!-- 通知訊息模板 -->
<template id="notification-template">
    <div class="notification">
        <div class="notification-icon">
            <i class="fas fa-info-circle"></i>
        </div>
        <div class="notification-content">
            <div class="notification-title"></div>
            <div class="notification-message"></div>
        </div>
        <div class="notification-actions">
            <button class="notification-close" aria-label="關閉通知">
                <i class="fas fa-times"></i>
            </button>
        </div>
        <div class="notification-progress">
            <div class="progress-bar"></div>
        </div>
    </div>
</template>

<!-- 通知訊息 JavaScript -->
<script>
class NotificationManager {
    constructor() {
        this.container = document.getElementById('notification-container');
        this.template = document.getElementById('notification-template');
        this.notifications = new Map();
        this.nextId = 1;
        
        this.init();
    }
    
    init() {
        if (!this.container || !this.template) {
            console.warn('通知系統初始化失敗：找不到必要的 DOM 元素');
            return;
        }
        
        // 設置容器位置
        this.setupContainer();
    }
    
    setupContainer() {
        // 確保通知容器在正確的位置
        if (!this.container.parentNode) {
            document.body.appendChild(this.container);
        }
    }
    
    show(options = {}) {
        const {
            title = '',
            message = '',
            type = 'info', // success, error, warning, info
            duration = 5000, // 0 表示不自動關閉
            persistent = false,
            actions = [],
            onClose = null
        } = options;
        
        const id = this.nextId++;
        const notification = this.createNotification(id, {
            title,
            message,
            type,
            duration,
            persistent,
            actions,
            onClose
        });
        
        // 添加到容器
        this.container.appendChild(notification);
        this.notifications.set(id, notification);
        
        // 觸發動畫
        setTimeout(() => {
            notification.classList.add('show');
        }, 10);
        
        // 設置自動關閉
        if (duration > 0 && !persistent) {
            setTimeout(() => {
                this.hide(id);
            }, duration);
        }
        
        return id;
    }
    
    createNotification(id, options) {
        const { title, message, type, actions, onClose } = options;
        
        // 複製模板
        const notification = this.template.content.cloneNode(true).querySelector('.notification');
        notification.dataset.id = id;
        notification.classList.add(`notification-${type}`);
        
        // 設置圖示
        const icon = notification.querySelector('.notification-icon i');
        if (icon) {
            switch (type) {
                case 'success':
                    icon.className = 'fas fa-check-circle';
                    break;
                case 'error':
                    icon.className = 'fas fa-exclamation-circle';
                    break;
                case 'warning':
                    icon.className = 'fas fa-exclamation-triangle';
                    break;
                default:
                    icon.className = 'fas fa-info-circle';
            }
        }
        
        // 設置標題
        const titleElement = notification.querySelector('.notification-title');
        if (titleElement) {
            if (title) {
                titleElement.textContent = title;
                titleElement.style.display = 'block';
            } else {
                titleElement.style.display = 'none';
            }
        }
        
        // 設置訊息
        const messageElement = notification.querySelector('.notification-message');
        if (messageElement) {
            messageElement.textContent = message;
        }
        
        // 設置關閉按鈕
        const closeButton = notification.querySelector('.notification-close');
        if (closeButton) {
            closeButton.addEventListener('click', () => {
                this.hide(id);
                if (onClose) onClose();
            });
        }
        
        // 設置自定義操作
        if (actions && actions.length > 0) {
            const actionsContainer = notification.querySelector('.notification-actions');
            actions.forEach(action => {
                const button = document.createElement('button');
                button.className = `btn btn-sm btn-${action.type || 'secondary'}`;
                button.textContent = action.text;
                button.addEventListener('click', () => {
                    if (action.handler) action.handler();
                    if (action.closeOnClick !== false) this.hide(id);
                });
                actionsContainer.insertBefore(button, closeButton);
            });
        }
        
        return notification;
    }
    
    hide(id) {
        const notification = this.notifications.get(id);
        if (!notification) return;
        
        notification.classList.add('hide');
        
        setTimeout(() => {
            if (notification.parentNode) {
                notification.parentNode.removeChild(notification);
            }
            this.notifications.delete(id);
        }, 300);
    }
    
    hideAll() {
        this.notifications.forEach((notification, id) => {
            this.hide(id);
        });
    }
    
    // 便捷方法：成功通知
    success(message, title = '成功', options = {}) {
        return this.show({
            title,
            message,
            type: 'success',
            duration: 3000,
            ...options
        });
    }
    
    // 便捷方法：錯誤通知
    error(message, title = '錯誤', options = {}) {
        return this.show({
            title,
            message,
            type: 'error',
            duration: 0, // 錯誤訊息不自動關閉
            persistent: true,
            ...options
        });
    }
    
    // 便捷方法：警告通知
    warning(message, title = '警告', options = {}) {
        return this.show({
            title,
            message,
            type: 'warning',
            duration: 5000,
            ...options
        });
    }
    
    // 便捷方法：資訊通知
    info(message, title = '提示', options = {}) {
        return this.show({
            title,
            message,
            type: 'info',
            duration: 4000,
            ...options
        });
    }
    
    // 便捷方法：載入通知
    loading(message, title = '處理中', options = {}) {
        const notification = this.show({
            title,
            message,
            type: 'info',
            duration: 0,
            persistent: true,
            ...options
        });
        
        // 添加載入動畫
        const notificationElement = this.notifications.get(notification);
        if (notificationElement) {
            notificationElement.classList.add('notification-loading');
            
            // 隱藏關閉按鈕
            const closeButton = notificationElement.querySelector('.notification-close');
            if (closeButton) {
                closeButton.style.display = 'none';
            }
        }
        
        return notification;
    }
    
    // 便捷方法：進度通知
    progress(message, progress = 0, title = '處理中', options = {}) {
        const id = this.show({
            title,
            message,
            type: 'info',
            duration: 0,
            persistent: true,
            ...options
        });
        
        this.updateProgress(id, progress);
        return id;
    }
    
    // 更新進度
    updateProgress(id, progress, message = null) {
        const notification = this.notifications.get(id);
        if (!notification) return;
        
        // 更新進度條
        const progressBar = notification.querySelector('.progress-bar');
        if (progressBar) {
            progressBar.style.width = `${Math.max(0, Math.min(100, progress))}%`;
        }
        
        // 更新訊息
        if (message) {
            const messageElement = notification.querySelector('.notification-message');
            if (messageElement) {
                messageElement.textContent = message;
            }
        }
        
        // 顯示進度條
        const progressContainer = notification.querySelector('.notification-progress');
        if (progressContainer) {
            progressContainer.style.display = 'block';
        }
    }
    
    // 完成進度通知
    completeProgress(id, message = '完成', options = {}) {
        this.updateProgress(id, 100, message);
        
        setTimeout(() => {
            this.hide(id);
            
            // 顯示完成通知
            if (options.showComplete !== false) {
                this.success(message, '完成');
            }
        }, 1000);
    }
}

// 全域通知管理器
if (typeof window !== 'undefined') {
    window.NotificationManager = NotificationManager;
    
    // 自動初始化
    document.addEventListener('DOMContentLoaded', function() {
        if (!window.notificationManager) {
            window.notificationManager = new NotificationManager();
        }
    });
}
</script>