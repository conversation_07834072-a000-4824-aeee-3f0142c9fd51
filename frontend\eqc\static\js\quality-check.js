/**
 * Quality Check Module
 * 品質檢查模組 JavaScript
 */

class QualityCheckManager {
    constructor() {
        this.currentChecks = [];
        this.checkHistory = [];
        this.checkRules = [];
        this.isRunning = false;
        this.websocket = null;
        this.refreshInterval = 5000; // 5秒刷新一次
        this.intervalId = null;
        
        this.init();
    }
    
    init() {
        console.log('Quality Check Manager: Initializing...');
        this.bindEvents();
        this.loadQualityChecks();
        this.loadCheckRules();
        this.initWebSocket();
        this.startAutoRefresh();
    }
    
    bindEvents() {
        // 開始品質檢查
        document.addEventListener('click', (e) => {
            if (e.target.matches('.start-check-btn')) {
                this.handleStartCheck(e);
            }
        });
        
        // 停止品質檢查
        document.addEventListener('click', (e) => {
            if (e.target.matches('.stop-check-btn')) {
                this.handleStopCheck(e);
            }
        });
        
        // 查看檢查詳情
        document.addEventListener('click', (e) => {
            if (e.target.matches('.view-details-btn')) {
                this.handleViewDetails(e);
            }
        });
        
        // 重新執行檢查
        document.addEventListener('click', (e) => {
            if (e.target.matches('.rerun-check-btn')) {
                this.handleRerunCheck(e);
            }
        });
        
        // 匯出檢查報告
        document.addEventListener('click', (e) => {
            if (e.target.matches('.export-report-btn')) {
                this.handleExportReport(e);
            }
        });
        
        // 檢查規則管理
        document.addEventListener('click', (e) => {
            if (e.target.matches('.manage-rules-btn')) {
                this.handleManageRules();
            }
        });
        
        // 設定檢查參數
        document.addEventListener('click', (e) => {
            if (e.target.matches('.configure-check-btn')) {
                this.handleConfigureCheck(e);
            }
        });
        
        // 篩選器變更
        document.addEventListener('change', (e) => {
            if (e.target.matches('.check-filter')) {
                this.handleFilterChange(e);
            }
        });
    }
    
    initWebSocket() {
        try {
            const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
            const wsUrl = `${protocol}//${window.location.host}/ws/quality-check`;
            
            this.websocket = new WebSocket(wsUrl);
            
            this.websocket.onopen = () => {
                console.log('Quality Check WebSocket connected');
                this.updateConnectionStatus(true);
            };
            
            this.websocket.onmessage = (event) => {
                const data = JSON.parse(event.data);
                this.handleWebSocketMessage(data);
            };
            
            this.websocket.onclose = () => {
                console.log('Quality Check WebSocket disconnected');
                this.updateConnectionStatus(false);
                
                // 自動重連
                setTimeout(() => {
                    this.initWebSocket();
                }, 5000);
            };
            
            this.websocket.onerror = (error) => {
                console.error('Quality Check WebSocket error:', error);
            };
            
        } catch (error) {
            console.error('Failed to initialize WebSocket:', error);
        }
    }
    
    handleWebSocketMessage(data) {
        switch (data.type) {
            case 'check_started':
                this.handleCheckStarted(data.payload);
                break;
            case 'check_progress':
                this.handleCheckProgress(data.payload);
                break;
            case 'check_completed':
                this.handleCheckCompleted(data.payload);
                break;
            case 'check_failed':
                this.handleCheckFailed(data.payload);
                break;
            case 'rule_violation':
                this.handleRuleViolation(data.payload);
                break;
            default:
                console.log('Unknown message type:', data.type);
        }
    }
    
    async loadQualityChecks() {
        try {
            const response = await fetch('/eqc/api/quality-checks');
            
            if (response.ok) {
                const data = await response.json();
                this.currentChecks = data.current || [];
                this.checkHistory = data.history || [];
                this.displayQualityChecks();
                this.updateStatistics();
            }
        } catch (error) {
            console.error('Failed to load quality checks:', error);
            this.showError('載入品質檢查資料失敗');
        }
    }
    
    async loadCheckRules() {
        try {
            const response = await fetch('/eqc/api/check-rules');
            
            if (response.ok) {
                this.checkRules = await response.json();
                this.displayCheckRules();
            }
        } catch (error) {
            console.error('Failed to load check rules:', error);
        }
    }
    
    displayQualityChecks() {
        this.displayCurrentChecks();
        this.displayCheckHistory();
    }
    
    displayCurrentChecks() {
        const container = document.querySelector('.current-checks');
        if (!container) return;
        
        container.innerHTML = '';
        
        if (this.currentChecks.length === 0) {
            container.innerHTML = `
                <div class="no-checks">
                    <p>目前沒有進行中的品質檢查</p>
                    <button class="btn btn-primary start-check-btn">開始新檢查</button>
                </div>
            `;
            return;
        }
        
        this.currentChecks.forEach(check => {
            const checkElement = this.createCheckElement(check);
            container.appendChild(checkElement);
        });
    }
    
    displayCheckHistory() {
        const container = document.querySelector('.check-history');
        if (!container) return;
        
        container.innerHTML = '';
        
        if (this.checkHistory.length === 0) {
            container.innerHTML = `
                <div class="no-history">
                    <p>暫無檢查歷史記錄</p>
                </div>
            `;
            return;
        }
        
        // 只顯示最近的20個檢查記錄
        const recentHistory = this.checkHistory.slice(0, 20);
        
        recentHistory.forEach(check => {
            const historyElement = this.createHistoryElement(check);
            container.appendChild(historyElement);
        });
    }
    
    createCheckElement(check) {
        const element = document.createElement('div');
        element.className = `quality-check-item status-${check.status}`;
        element.dataset.checkId = check.id;
        
        const progress = check.progress || 0;
        const statusClass = this.getStatusClass(check.status);
        
        element.innerHTML = `
            <div class="check-header">
                <div class="check-info">
                    <h5 class="check-name">${check.name}</h5>
                    <span class="check-type">${this.getCheckTypeText(check.type)}</span>
                    <span class="check-status status-${statusClass}">${this.getStatusText(check.status)}</span>
                </div>
                <div class="check-controls">
                    ${check.status === 'running' ? `
                        <button class="btn btn-sm btn-warning stop-check-btn" data-check-id="${check.id}" title="停止檢查">
                            <i class="fas fa-stop"></i>
                        </button>
                    ` : `
                        <button class="btn btn-sm btn-success start-check-btn" data-check-id="${check.id}" title="重新開始">
                            <i class="fas fa-play"></i>
                        </button>
                    `}
                    <button class="btn btn-sm btn-info view-details-btn" data-check-id="${check.id}" title="檢視詳情">
                        <i class="fas fa-info"></i>
                    </button>
                    <button class="btn btn-sm btn-secondary configure-check-btn" data-check-id="${check.id}" title="設定">
                        <i class="fas fa-cog"></i>
                    </button>
                </div>
            </div>
            
            <div class="check-progress">
                <div class="progress">
                    <div class="progress-bar progress-bar-${statusClass}" style="width: ${progress}%"></div>
                </div>
                <span class="progress-text">${progress}% - ${check.current_step || '準備中'}</span>
            </div>
            
            <div class="check-metrics">
                <div class="metric">
                    <span class="metric-value">${check.items_checked || 0}</span>
                    <span class="metric-label">已檢查項目</span>
                </div>
                <div class="metric">
                    <span class="metric-value">${check.violations_found || 0}</span>
                    <span class="metric-label">發現問題</span>
                </div>
                <div class="metric">
                    <span class="metric-value">${check.rules_applied || 0}</span>
                    <span class="metric-label">應用規則</span>
                </div>
                <div class="metric">
                    <span class="metric-value">${this.formatDuration(check.elapsed_time || 0)}</span>
                    <span class="metric-label">執行時間</span>
                </div>
            </div>
            
            ${check.description ? `
                <div class="check-description">
                    <p>${check.description}</p>
                </div>
            ` : ''}
            
            ${check.error ? `
                <div class="check-error">
                    <i class="fas fa-exclamation-triangle"></i>
                    <span>${check.error}</span>
                </div>
            ` : ''}
        `;
        
        return element;
    }
    
    createHistoryElement(check) {
        const element = document.createElement('div');
        element.className = `history-item status-${check.status}`;
        element.dataset.checkId = check.id;
        
        const duration = check.duration ? this.formatDuration(check.duration) : 'N/A';
        const statusClass = this.getStatusClass(check.status);
        
        element.innerHTML = `
            <div class="history-header">
                <div class="history-info">
                    <h6 class="check-name">${check.name}</h6>
                    <span class="check-type">${this.getCheckTypeText(check.type)}</span>
                    <span class="check-status status-${statusClass}">${this.getStatusText(check.status)}</span>
                </div>
                <div class="history-time">
                    <span class="completed-time">${this.formatDateTime(check.completed_at)}</span>
                </div>
            </div>
            
            <div class="history-summary">
                <div class="summary-item">
                    <span class="label">檢查項目：</span>
                    <span class="value">${check.total_items || 0}</span>
                </div>
                <div class="summary-item">
                    <span class="label">發現問題：</span>
                    <span class="value">${check.violations_found || 0}</span>
                </div>
                <div class="summary-item">
                    <span class="label">執行時間：</span>
                    <span class="value">${duration}</span>
                </div>
                <div class="summary-item">
                    <span class="label">通過率：</span>
                    <span class="value">${this.calculatePassRate(check)}%</span>
                </div>
            </div>
            
            <div class="history-actions">
                <button class="btn btn-sm btn-info view-details-btn" data-check-id="${check.id}" title="檢視詳情">
                    詳情
                </button>
                <button class="btn btn-sm btn-secondary export-report-btn" data-check-id="${check.id}" title="匯出報告">
                    匯出
                </button>
                <button class="btn btn-sm btn-primary rerun-check-btn" data-check-id="${check.id}" title="重新執行">
                    重跑
                </button>
            </div>
        `;
        
        return element;
    }
    
    displayCheckRules() {
        const container = document.querySelector('.check-rules-list');
        if (!container) return;
        
        container.innerHTML = '';
        
        this.checkRules.forEach(rule => {
            const ruleElement = this.createRuleElement(rule);
            container.appendChild(ruleElement);
        });
    }
    
    createRuleElement(rule) {
        const element = document.createElement('div');
        element.className = `rule-item ${rule.enabled ? 'enabled' : 'disabled'}`;
        element.dataset.ruleId = rule.id;
        
        element.innerHTML = `
            <div class="rule-header">
                <div class="rule-info">
                    <h6 class="rule-name">${rule.name}</h6>
                    <span class="rule-category">${rule.category}</span>
                    <span class="rule-severity severity-${rule.severity}">${this.getSeverityText(rule.severity)}</span>
                </div>
                <div class="rule-toggle">
                    <input type="checkbox" class="form-check-input" ${rule.enabled ? 'checked' : ''} 
                           onchange="toggleRule('${rule.id}', this.checked)">
                </div>
            </div>
            <div class="rule-description">
                <p>${rule.description}</p>
            </div>
            <div class="rule-stats">
                <span class="violations-count">${rule.violations_count || 0} 次違規</span>
                <span class="last-triggered">${rule.last_triggered ? '最後觸發：' + this.formatDateTime(rule.last_triggered) : '從未觸發'}</span>
            </div>
        `;
        
        return element;
    }
    
    updateStatistics() {
        if (!this.currentChecks || !this.checkHistory) return;
        
        const stats = this.calculateStatistics();
        
        this.updateElement('.stat-total-checks', stats.totalChecks);
        this.updateElement('.stat-running-checks', stats.runningChecks);
        this.updateElement('.stat-violations-found', stats.violationsFound);
        this.updateElement('.stat-avg-pass-rate', `${stats.avgPassRate}%`);
        this.updateElement('.stat-rules-active', stats.activeRules);
        this.updateElement('.stat-last-check', stats.lastCheck);
    }
    
    calculateStatistics() {
        const totalChecks = this.currentChecks.length + this.checkHistory.length;
        const runningChecks = this.currentChecks.filter(c => c.status === 'running').length;
        
        let totalViolations = 0;
        let totalItems = 0;
        
        [...this.currentChecks, ...this.checkHistory].forEach(check => {
            totalViolations += check.violations_found || 0;
            totalItems += check.total_items || 0;
        });
        
        const avgPassRate = totalItems > 0 ? Math.round(((totalItems - totalViolations) / totalItems) * 100) : 100;
        const activeRules = this.checkRules.filter(r => r.enabled).length;
        
        const lastCheck = this.checkHistory.length > 0 ? 
            this.formatDateTime(this.checkHistory[0].completed_at) : '無';
        
        return {
            totalChecks,
            runningChecks,
            violationsFound: totalViolations,
            avgPassRate,
            activeRules,
            lastCheck
        };
    }
    
    async handleStartCheck(e) {
        if (this.isRunning) {
            this.showWarning('已有檢查正在進行中');
            return;
        }
        
        const checkType = e.target.dataset.checkType || 'comprehensive';
        const checkId = e.target.dataset.checkId;
        
        const button = e.target.closest('button');
        const originalHTML = button.innerHTML;
        
        button.innerHTML = '<i class="fas fa-spinner fa-spin"></i>';
        button.disabled = true;
        
        try {
            const response = await fetch('/eqc/api/quality-checks/start', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    type: checkType,
                    check_id: checkId,
                    configuration: this.getCheckConfiguration()
                })
            });
            
            if (response.ok) {
                const result = await response.json();
                this.showSuccess('品質檢查已開始');
                this.isRunning = true;
                
                // 如果是異步執行，開始監控進度
                if (result.async) {
                    this.monitorCheckProgress(result.check_id);
                }
                
                this.loadQualityChecks();
            } else {
                const error = await response.json();
                throw new Error(error.message || '開始檢查失敗');
            }
        } catch (error) {
            console.error('Start check error:', error);
            this.showError('開始品質檢查失敗：' + error.message);
        } finally {
            button.innerHTML = originalHTML;
            button.disabled = false;
        }
    }
    
    async handleStopCheck(e) {
        const checkId = e.target.closest('button').dataset.checkId;
        
        if (!confirm('確定要停止這個品質檢查嗎？')) return;
        
        try {
            const response = await fetch(`/eqc/api/quality-checks/${checkId}/stop`, {
                method: 'POST'
            });
            
            if (response.ok) {
                this.showSuccess('品質檢查已停止');
                this.isRunning = false;
                this.loadQualityChecks();
            } else {
                throw new Error('停止檢查失敗');
            }
        } catch (error) {
            console.error('Stop check error:', error);
            this.showError('停止品質檢查失敗：' + error.message);
        }
    }
    
    async handleViewDetails(e) {
        const checkId = e.target.closest('button').dataset.checkId;
        
        try {
            const response = await fetch(`/eqc/api/quality-checks/${checkId}/details`);
            
            if (response.ok) {
                const details = await response.json();
                this.showCheckDetailsModal(details);
            } else {
                throw new Error('載入詳情失敗');
            }
        } catch (error) {
            console.error('Load details error:', error);
            this.showError('載入檢查詳情失敗：' + error.message);
        }
    }
    
    showCheckDetailsModal(details) {
        const modal = document.getElementById('checkDetailsModal');
        if (!modal) return;
        
        const content = modal.querySelector('.check-details-content');
        if (content) {
            content.innerHTML = this.renderCheckDetailsHTML(details);
        }
        
        const modalInstance = new bootstrap.Modal(modal);
        modalInstance.show();
    }
    
    renderCheckDetailsHTML(details) {
        return `
            <div class="check-details">
                <h5>${details.name}</h5>
                
                <div class="details-overview">
                    <div class="overview-item">
                        <span class="label">檢查類型：</span>
                        <span class="value">${this.getCheckTypeText(details.type)}</span>
                    </div>
                    <div class="overview-item">
                        <span class="label">狀態：</span>
                        <span class="value status-${this.getStatusClass(details.status)}">${this.getStatusText(details.status)}</span>
                    </div>
                    <div class="overview-item">
                        <span class="label">進度：</span>
                        <span class="value">${details.progress || 0}%</span>
                    </div>
                    <div class="overview-item">
                        <span class="label">執行時間：</span>
                        <span class="value">${this.formatDuration(details.elapsed_time || 0)}</span>
                    </div>
                </div>
                
                <div class="details-metrics">
                    <h6>檢查結果統計</h6>
                    <div class="metrics-grid">
                        <div class="metric-card">
                            <div class="metric-value">${details.total_items || 0}</div>
                            <div class="metric-label">總檢查項目</div>
                        </div>
                        <div class="metric-card">
                            <div class="metric-value">${details.violations_found || 0}</div>
                            <div class="metric-label">發現違規</div>
                        </div>
                        <div class="metric-card">
                            <div class="metric-value">${details.rules_applied || 0}</div>
                            <div class="metric-label">應用規則</div>
                        </div>
                        <div class="metric-card">
                            <div class="metric-value">${this.calculatePassRate(details)}%</div>
                            <div class="metric-label">通過率</div>
                        </div>
                    </div>
                </div>
                
                ${details.violations && details.violations.length > 0 ? `
                    <div class="details-violations">
                        <h6>發現的問題 (${details.violations.length})</h6>
                        <div class="violations-list">
                            ${details.violations.slice(0, 10).map(violation => `
                                <div class="violation-item severity-${violation.severity}">
                                    <div class="violation-header">
                                        <span class="rule-name">${violation.rule_name}</span>
                                        <span class="severity severity-${violation.severity}">${this.getSeverityText(violation.severity)}</span>
                                    </div>
                                    <div class="violation-description">${violation.description}</div>
                                    <div class="violation-location">位置：${violation.location}</div>
                                </div>
                            `).join('')}
                            ${details.violations.length > 10 ? `<p>... 還有 ${details.violations.length - 10} 個問題</p>` : ''}
                        </div>
                    </div>
                ` : ''}
                
                ${details.error ? `
                    <div class="details-error">
                        <h6>錯誤訊息</h6>
                        <div class="error-message">${details.error}</div>
                    </div>
                ` : ''}
            </div>
        `;
    }
    
    handleCheckStarted(data) {
        this.isRunning = true;
        this.showSuccess(`品質檢查「${data.name}」已開始`);
        this.loadQualityChecks();
    }
    
    handleCheckProgress(data) {
        // 更新進度顯示
        const checkElement = document.querySelector(`[data-check-id="${data.check_id}"]`);
        if (checkElement) {
            const progressBar = checkElement.querySelector('.progress-bar');
            const progressText = checkElement.querySelector('.progress-text');
            
            if (progressBar) {
                progressBar.style.width = `${data.progress}%`;
            }
            
            if (progressText) {
                progressText.textContent = `${data.progress}% - ${data.current_step || ''}`;
            }
            
            // 更新指標
            this.updateCheckMetrics(checkElement, data);
        }
    }
    
    handleCheckCompleted(data) {
        this.isRunning = false;
        this.showSuccess(`品質檢查「${data.name}」已完成`);
        this.loadQualityChecks();
        
        // 如果發現嚴重問題，顯示警告
        if (data.critical_violations > 0) {
            this.showWarning(`發現 ${data.critical_violations} 個嚴重問題，請立即處理！`);
        }
    }
    
    handleCheckFailed(data) {
        this.isRunning = false;
        this.showError(`品質檢查「${data.name}」失敗：${data.error}`);
        this.loadQualityChecks();
    }
    
    handleRuleViolation(data) {
        // 顯示實時違規通知
        this.showViolationNotification(data);
    }
    
    updateCheckMetrics(element, data) {
        const metricsItems = element.querySelectorAll('.metric-value');
        if (metricsItems.length >= 4) {
            metricsItems[0].textContent = data.items_checked || 0;
            metricsItems[1].textContent = data.violations_found || 0;
            metricsItems[2].textContent = data.rules_applied || 0;
            metricsItems[3].textContent = this.formatDuration(data.elapsed_time || 0);
        }
    }
    
    showViolationNotification(violation) {
        const notification = document.createElement('div');
        notification.className = `violation-notification severity-${violation.severity}`;
        notification.innerHTML = `
            <div class="notification-header">
                <i class="fas fa-exclamation-triangle"></i>
                <span>發現品質問題</span>
            </div>
            <div class="notification-body">
                <strong>${violation.rule_name}</strong><br>
                ${violation.description}
            </div>
        `;
        
        const container = document.querySelector('.violations-notifications') || document.body;
        container.appendChild(notification);
        
        // 自動移除通知
        setTimeout(() => {
            if (notification.parentNode) {
                notification.remove();
            }
        }, 8000);
    }
    
    getCheckConfiguration() {
        // 從表單或設定中獲取檢查配置
        return {
            scope: document.querySelector('#checkScope')?.value || 'all',
            severity_threshold: document.querySelector('#severityThreshold')?.value || 'medium',
            include_historical: document.querySelector('#includeHistorical')?.checked || false,
            auto_fix: document.querySelector('#autoFix')?.checked || false,
            detailed_report: document.querySelector('#detailedReport')?.checked || true
        };
    }
    
    startAutoRefresh() {
        this.intervalId = setInterval(() => {
            if (this.isRunning) {
                this.loadQualityChecks();
            }
        }, this.refreshInterval);
    }
    
    stopAutoRefresh() {
        if (this.intervalId) {
            clearInterval(this.intervalId);
            this.intervalId = null;
        }
    }
    
    updateConnectionStatus(connected) {
        const indicator = document.querySelector('.websocket-status');
        if (indicator) {
            indicator.className = `websocket-status ${connected ? 'connected' : 'disconnected'}`;
            indicator.textContent = connected ? 'WebSocket 已連接' : 'WebSocket 連接中斷';
        }
    }
    
    calculatePassRate(check) {
        const total = check.total_items || 0;
        const violations = check.violations_found || 0;
        
        if (total === 0) return 100;
        return Math.round(((total - violations) / total) * 100);
    }
    
    formatDuration(seconds) {
        if (seconds < 60) {
            return `${Math.floor(seconds)}秒`;
        } else if (seconds < 3600) {
            return `${Math.floor(seconds / 60)}分${Math.floor(seconds % 60)}秒`;
        } else {
            return `${Math.floor(seconds / 3600)}小時${Math.floor((seconds % 3600) / 60)}分`;
        }
    }
    
    formatDateTime(dateString) {
        const date = new Date(dateString);
        return date.toLocaleDateString('zh-TW') + ' ' + date.toLocaleTimeString('zh-TW');
    }
    
    getCheckTypeText(type) {
        const typeMap = {
            'comprehensive': '全面檢查',
            'email_quality': '郵件品質',
            'data_integrity': '資料完整性',
            'performance': '效能檢查',
            'security': '安全檢查',
            'compliance': '合規檢查',
            'custom': '自訂檢查'
        };
        
        return typeMap[type] || type;
    }
    
    getStatusText(status) {
        const statusMap = {
            'pending': '待開始',
            'running': '執行中',
            'completed': '已完成',
            'failed': '失敗',
            'stopped': '已停止',
            'cancelled': '已取消'
        };
        
        return statusMap[status] || status;
    }
    
    getStatusClass(status) {
        const statusMap = {
            'pending': 'warning',
            'running': 'info',
            'completed': 'success',
            'failed': 'danger',
            'stopped': 'secondary',
            'cancelled': 'secondary'
        };
        
        return statusMap[status] || 'secondary';
    }
    
    getSeverityText(severity) {
        const severityMap = {
            'low': '低',
            'medium': '中',
            'high': '高',
            'critical': '嚴重'
        };
        
        return severityMap[severity] || severity;
    }
    
    updateElement(selector, value) {
        const element = document.querySelector(selector);
        if (element) {
            element.textContent = value;
        }
    }
    
    showSuccess(message) {
        this.showNotification(message, 'success');
    }
    
    showError(message) {
        this.showNotification(message, 'danger');
    }
    
    showWarning(message) {
        this.showNotification(message, 'warning');
    }
    
    showNotification(message, type) {
        const notification = document.createElement('div');
        notification.className = `alert alert-${type} alert-dismissible`;
        notification.innerHTML = `
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;
        
        const container = document.querySelector('.quality-check-container');
        if (container) {
            container.insertBefore(notification, container.firstChild);
        }
        
        // 自動移除通知
        setTimeout(() => {
            if (notification.parentNode) {
                notification.remove();
            }
        }, 5000);
    }
    
    destroy() {
        this.stopAutoRefresh();
        if (this.websocket) {
            this.websocket.close();
        }
    }
}

// 全局函數供 HTML 使用
function toggleRule(ruleId, enabled) {
    fetch(`/eqc/api/check-rules/${ruleId}/toggle`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({ enabled: enabled })
    }).then(response => {
        if (response.ok) {
            console.log(`Rule ${ruleId} ${enabled ? 'enabled' : 'disabled'}`);
        }
    }).catch(error => {
        console.error('Toggle rule error:', error);
    });
}

// 頁面載入完成後初始化
document.addEventListener('DOMContentLoaded', function() {
    if (document.querySelector('.quality-check-container')) {
        window.qualityCheckManager = new QualityCheckManager();
    }
});

// 頁面卸載時清理
window.addEventListener('beforeunload', function() {
    if (window.qualityCheckManager) {
        window.qualityCheckManager.destroy();
    }
});