<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>資料庫管理</title>
    <link rel="stylesheet" href="{{ url_for('shared.static', filename='css/global.css') }}">
    <link rel="stylesheet" href="{{ url_for('monitoring.static', filename='css/database.css') }}">
    <!-- DataTables CSS -->
    <link rel="stylesheet" href="https://cdn.datatables.net/1.13.6/css/jquery.dataTables.min.css">
    <!-- DataTables Responsive CSS -->
    <link rel="stylesheet" href="https://cdn.datatables.net/responsive/2.5.0/css/responsive.dataTables.min.css">
    <style>
        .search-results-info {
            background-color: #d1ecf1;
            color: #0c5460;
            padding: 10px;
            border-radius: 4px;
            border: 1px solid #bee5eb;
            margin-top: 10px;
        }
        
        .sql-query-section {
            margin-bottom: 25px;
            padding: 20px;
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 8px;
        }
        .sql-query-section h3 {
            margin: 0 0 15px 0;
            color: #495057;
            font-size: 18px;
        }
        .search-container {
            display: flex;
            gap: 10px;
            margin-bottom: 15px;
            align-items: center;
        }
        .form-input {
            flex: 1;
            padding: 8px 12px;
            border: 1px solid #ced4da;
            border-radius: 4px;
            font-size: 14px;
        }
        .form-input:focus {
            outline: none;
            border-color: #007bff;
            box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
        }
        .search-tips {
            background-color: #e7f3ff;
            padding: 12px;
            border-radius: 4px;
            border-left: 4px solid #007bff;
            margin-bottom: 15px;
        }
        .search-tips p {
            margin: 0;
            color: #495057;
            font-size: 14px;
        }
        .query-container {
            display: flex;
            gap: 10px;
            margin-bottom: 15px;
            align-items: flex-end;
        }
        .query-container textarea {
            flex: 1;
            padding: 8px 12px;
            border: 1px solid #ffb74d;
            border-radius: 4px;
            font-size: 14px;
            font-family: monospace;
        }
        .query-container textarea:focus {
            outline: none;
            border-color: #ff9800;
            box-shadow: 0 0 0 0.2rem rgba(255, 152, 0, 0.25);
        }
        .query-suggestions {
            margin-top: 15px;
            padding: 15px;
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
        }
        .query-suggestions h4 {
            margin: 0 0 10px 0;
            color: #495057;
            font-size: 14px;
        }
        .suggestion-buttons {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
        }
        .btn-small {
            padding: 4px 8px;
            font-size: 12px;
            background-color: #007bff;
            color: white;
            border: none;
            border-radius: 3px;
            cursor: pointer;
            transition: background-color 0.2s;
        }
        .btn-small:hover {
            background-color: #0056b3;
        }
        .error-message {
            background-color: #f8d7da;
            color: #721c24;
            padding: 10px;
            border-radius: 4px;
            border: 1px solid #f5c6cb;
            margin-top: 10px;
        }
        
        /* 額外的表格邊框強化 */
        table.dataTable.display {
            border-collapse: collapse !important;
        }
        
        table.dataTable.display thead th,
        table.dataTable.display tbody td {
            border: 1px solid #dee2e6 !important;
        }
        
        table.dataTable.display tbody tr {
            border-bottom: 1px solid #dee2e6 !important;
        }
        
        /* 確保操作按鈕容器有適當的內邊距 */
        .action-buttons {
            white-space: nowrap;
            padding: 4px;
        }
        
        /* 改善標籤和狀態顯示 */
        .extraction-method-tag,
        .parse-status-tag,
        .vendor-code-tag {
            display: inline-block;
            margin: 1px;
            padding: 2px 6px;
            font-weight: 500;
            font-size: 0.75rem;
            border: 1px solid rgba(0,0,0,0.1);
            border-radius: 3px;
            background-color: #f8f9fa;
        }

        /* 狀態特定顏色 */
        .parse-status-parsed {
            background-color: #d4edda;
            color: #155724;
            border-color: #c3e6cb;
        }

        .parse-status-failed {
            background-color: #f8d7da;
            color: #721c24;
            border-color: #f5c6cb;
        }

        .extraction-method-llm {
            background-color: #e3f2fd;
            color: #1565c0;
            border-color: #bbdefb;
        }

        .extraction-method-traditional {
            background-color: #f3e5f5;
            color: #7b1fa2;
            border-color: #ce93d8;
        }

        /* 廠商代碼突出顯示 */
        .vendor-code-tag {
            font-weight: 600;
            background-color: #fff3cd;
            color: #856404;
            border-color: #ffeaa7;
        }

        /* 響應式表格改進 */
        .dataTables_wrapper {
            overflow-x: auto;
        }

        /* 表格容器改進 */
        .data-container {
            max-width: 100%;
            overflow-x: auto;
        }

        /* 表格欄位內容截斷提示 */
        .truncated-content {
            position: relative;
            cursor: help;
        }

        .truncated-content:hover::after {
            content: "點擊查看完整內容";
            position: absolute;
            bottom: -25px;
            left: 0;
            background: #333;
            color: white;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            white-space: nowrap;
            z-index: 1000;
        }

        /* DataTables 響應式改進 */
        table.dataTable.dtr-inline.collapsed > tbody > tr > td.dtr-control:before {
            background-color: #007bff;
        }

        /* 批量操作面板樣式 */
        .batch-actions-panel {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 12px 16px;
            margin-bottom: 16px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .batch-actions-panel.hidden {
            display: none;
        }

        .batch-info {
            font-weight: 500;
            color: #495057;
        }

        .batch-buttons {
            display: flex;
            gap: 8px;
        }

        .batch-buttons .btn {
            padding: 6px 12px;
            font-size: 14px;
        }

        /* 確保表格在小螢幕上的可讀性 */
        @media (max-width: 1200px) {
            .data-container {
                padding: 0.5rem;
            }

            #data-table th,
            #data-table td {
                padding: 6px 8px;
                font-size: 14px;
            }

            .batch-actions-panel {
                flex-direction: column;
                gap: 8px;
                text-align: center;
            }

            .batch-buttons {
                justify-content: center;
            }
        }
    </style>
</head>
<body>
    <div class="db-manager-container">
        <!-- 頂部導航 -->
        <header class="db-header">
            <div class="header-left">
                <h1>🗄️ 資料庫管理</h1>
                <a href="/email/" class="btn btn-secondary">
                    <span class="btn-icon">◀</span>
                    <span class="btn-text">返回郵件收件夾</span>
                </a>
            </div>
            <div class="header-right">
                <div class="db-info">
                    <span class="info-item">資料庫: <strong>{{ db_path }}</strong></span>
                    <span class="info-item">大小: <strong id="db-size">計算中...</strong></span>
                </div>
            </div>
        </header>

        <!-- 表格選擇與搜尋 -->
        <div class="table-selector">
            <label for="table-select">選擇資料表：</label>
            <select id="table-select" class="form-select">
                <option value="">-- 請選擇資料表 --</option>
                <option value="emails">emails - 郵件</option>
                <option value="senders">senders - 寄件者</option>
                <option value="attachments">attachments - 附件</option>
                <option value="email_process_status">email_process_status - 處理狀態</option>
            </select>
            <button id="refresh-btn" class="btn btn-primary">
                <span class="btn-icon">🔄</span>
                <span class="btn-text">重新整理</span>
            </button>
            <button id="export-csv-btn" class="btn btn-secondary" disabled>
                <span class="btn-icon">📥</span>
                <span class="btn-text">匯出 CSV</span>
            </button>
        </div>

        <!-- 搜尋與查詢區域 -->
        <div class="sql-query-section">
            <h3>🔍 搜尋資料</h3>
            <div class="search-container">
                <input type="text" id="search-input" placeholder="輸入搜尋關鍵字（例如：tong、郵件主旨、廠商代碼...）" class="form-input">
                <button id="search-btn" class="btn btn-primary">搜尋</button>
                <button id="clear-search-btn" class="btn btn-secondary">清除</button>
                <button id="select-all-search-btn" class="btn btn-info" style="margin-left: 10px;">
                    ☑️ 全選搜尋結果
                </button>
            </div>
            <div class="search-tips">
                <p>💡 <strong>搜尋提示：</strong>輸入「tong」可找到「<EMAIL>」，可搜尋寄件者、主旨、內容、廠商代碼等多個欄位</p>
            </div>
            <div id="search-results-info" class="search-results-info hidden">
                <span id="search-results-text"></span>
            </div>
            
            <!-- 隱藏的SQL查詢區域 -->
            <div style="display: none;">
                <textarea id="sql-query" placeholder="輸入 SELECT 查詢語句..." rows="3"></textarea>
                <button id="execute-query-btn" class="btn btn-primary">執行查詢</button>
            </div>
            
            <div class="query-suggestions">
                <h4>常用查詢範例：</h4>
                <div class="suggestion-buttons">
                    <button class="btn btn-small" onclick="setQuery('SELECT id, sender, subject, received_time, vendor_code, pd, lot, extraction_method FROM emails WHERE extraction_method = \'llm\' ORDER BY parsed_at DESC LIMIT 50')">LLM 解析的郵件</button>
                    <button class="btn btn-small" onclick="setQuery('SELECT id, sender, subject, received_time, vendor_code, pd, lot, extraction_method FROM emails WHERE extraction_method = \'traditional\' ORDER BY parsed_at DESC LIMIT 50')">傳統解析的郵件</button>
                    <button class="btn btn-small" onclick="setQuery('SELECT vendor_code, extraction_method, COUNT(*) as count FROM emails WHERE parse_status = \'parsed\' AND vendor_code IS NOT NULL GROUP BY vendor_code, extraction_method ORDER BY vendor_code, extraction_method')">各廠商解析方法統計</button>
                    <button class="btn btn-small" onclick="setQuery('SELECT id, sender, subject, received_time, parse_error FROM emails WHERE parse_status = \'failed\' ORDER BY received_time DESC LIMIT 50')">解析失敗的郵件</button>
                    <button class="btn btn-small" onclick="setQuery('SELECT vendor_code, COUNT(*) as total, COUNT(CASE WHEN parse_status = \'parsed\' THEN 1 END) as parsed, ROUND(COUNT(CASE WHEN parse_status = \'parsed\' THEN 1 END) * 100.0 / COUNT(*), 2) as success_rate FROM emails WHERE vendor_code IS NOT NULL GROUP BY vendor_code ORDER BY success_rate DESC')">廠商解析成功率</button>
                </div>
            </div>
            <div id="query-error" class="error-message hidden"></div>
        </div>

        <!-- 表格資訊 -->
        <div id="table-info" class="table-info hidden">
            <h3 id="table-name">資料表名稱</h3>
            <div class="table-stats">
                <span>總記錄數: <strong id="record-count">0</strong></span>
                <span>欄位數: <strong id="column-count">0</strong></span>
            </div>
        </div>

        <!-- 批量操作面板 -->
        <div id="batch-actions-panel" class="batch-actions-panel hidden">
            <div class="batch-info">
                <span id="selected-count">0</span> 筆記錄已選擇
            </div>
            <div class="batch-buttons">
                <button id="batch-delete-btn" class="btn btn-danger">
                    <span class="btn-icon">🗑️</span>
                    <span class="btn-text">批量刪除</span>
                </button>
                <button id="clear-selection-btn" class="btn btn-secondary">
                    <span class="btn-icon">✖️</span>
                    <span class="btn-text">清除選擇</span>
                </button>
            </div>
        </div>

        <!-- 資料表格容器 -->
        <div id="data-container" class="data-container">
            <div id="loading" class="loading hidden">
                <div class="loading-spinner"></div>
                <p>載入資料中...</p>
            </div>
            <table id="data-table" class="display" style="width:100%">
                <!-- 動態生成 -->
            </table>
        </div>

        <!-- 詳情模態框 -->
        <div id="detail-modal" class="modal hidden">
            <div class="modal-content">
                <div class="modal-header">
                    <h3 id="modal-title">記錄詳情</h3>
                    <button class="close-btn" onclick="closeDetailModal()">✕</button>
                </div>
                <div id="modal-body" class="modal-body">
                    <!-- 動態生成 -->
                </div>
                <div class="modal-footer">
                    <button class="btn btn-secondary" onclick="closeDetailModal()">關閉</button>
                </div>
            </div>
        </div>
    </div>

    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.7.0.min.js"></script>
    <!-- DataTables JS -->
    <script src="https://cdn.datatables.net/1.13.6/js/jquery.dataTables.min.js"></script>
    <!-- DataTables Responsive -->
    <script src="https://cdn.datatables.net/responsive/2.5.0/js/dataTables.responsive.min.js"></script>
    <!-- 自定義 JS -->
    <script src="{{ url_for('monitoring.static', filename='js/database.js') }}"></script>
    <script src="{{ url_for('monitoring.static', filename='js/database-extensions.js') }}"></script>

    <script>
        // 添加工具提示功能
        $(document).ready(function() {
            // 為截斷的內容添加點擊事件
            $(document).on('click', '.truncated-content', function() {
                const fullText = $(this).attr('title');
                if (fullText) {
                    alert(fullText);
                }
            });

            // 改進的懸停效果
            $(document).on('mouseenter', '.truncated-content', function() {
                $(this).css({
                    'background-color': '#f8f9fa',
                    'cursor': 'pointer'
                });
            }).on('mouseleave', '.truncated-content', function() {
                $(this).css({
                    'background-color': 'transparent',
                    'cursor': 'default'
                });
            });
        });
    </script>
</body>
</html>