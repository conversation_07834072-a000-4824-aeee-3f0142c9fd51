/**
 * FT Summary 批量處理 JavaScript
 * 處理表單提交、API 呼叫、進度顯示等功能
 */

class FTSummaryProcessor {
    constructor() {
        this.form = document.getElementById('ftSummaryForm');
        this.processBtn = document.getElementById('processBtn');
        this.progressContainer = document.getElementById('progressContainer');
        this.progressFill = document.getElementById('progressFill');
        this.progressText = document.getElementById('progressText');
        this.resultContainer = document.getElementById('resultContainer');
        this.statsContainer = document.getElementById('statsContainer');
        this.downloadContainer = document.getElementById('downloadContainer');

        // 文件上傳相關元素（現在是 side-card 布局）
        this.uploadZone = document.getElementById('uploadZone');
        this.fileInput = document.getElementById('fileInput');
        this.uploadStatus = document.getElementById('uploadStatus');

        // 7zip 路徑設定相關元素
        this.sevenZipPath = document.getElementById('sevenZipPath');
        this.testSevenZipBtn = document.getElementById('testSevenZipBtn');
        this.saveSevenZipBtn = document.getElementById('saveSevenZipBtn');
        this.sevenZipStatus = document.getElementById('sevenZipStatus');

        // 文件上傳相關
        this.uploadedFilePath = null;
        this.uploadProcessed = false;  // 標記是否已經處理過上傳的文件
        this.fileUploadComponent = null;

        this.init();
    }
    
    init() {
        // 檢查服務狀態
        this.checkServiceStatus();

        // 綁定表單提交事件
        this.form.addEventListener('submit', (e) => {
            e.preventDefault();
            this.startProcessing();
        });

        // 綁定資料夾路徑輸入框事件
        const folderInput = document.getElementById('folderPath');
        folderInput.addEventListener('input', this.validateInput.bind(this));

        // 初始化文件上傳組件
        this.initFileUpload();

        // 初始化 7zip 路徑設定
        this.initSevenZipConfig();

        // 初始驗證
        this.validateInput();
    }
    
    async checkServiceStatus() {
        // 檢查服務狀態
        try {
            const response = await fetch('/ft-eqc/api/ft-summary-status');
            const status = await response.json();
            
            const modeIndicator = document.getElementById('modeIndicator');
            const modeText = document.getElementById('modeText');
            
            if (status.ft_summary_available) {
                modeIndicator.style.display = 'block';
                modeIndicator.style.background = '#d4edda';
                modeIndicator.style.border = '1px solid #c3e6cb';
                modeIndicator.style.color = '#155724';
                modeText.textContent = 'FT Summary 服務正常：支援 CSV 到 Excel 轉換';
            } else {
                modeIndicator.style.display = 'block';
                modeIndicator.style.background = '#f8d7da';
                modeIndicator.style.border = '1px solid #f5c6cb';
                modeIndicator.style.color = '#721c24';
                modeText.textContent = '服務不可用：請安裝依賴套件';
                
                // 禁用表單
                this.processBtn.disabled = true;
                this.processBtn.innerHTML = '<i class="fas fa-exclamation-triangle"></i> 服務不可用';
            }
        } catch (error) {
            console.error('檢查服務狀態失敗:', error);
        }
    }



    async initFileUpload() {
        // 初始化文件上傳組件
        console.log('初始化文件上傳組件...');
        console.log('FileUpload 是否可用:', typeof FileUpload !== 'undefined');

        if (typeof FileUpload !== 'undefined') {
            try {
                this.fileUploadComponent = new FileUpload();
                await this.fileUploadComponent.init();
                console.log('文件上傳組件初始化成功');

                // 監聽上傳成功事件
                document.addEventListener('fileUploadSuccess', (event) => {
                    this.handleFileUploadSuccess(event.detail);
                });
            } catch (error) {
                console.error('文件上傳組件初始化失敗:', error);
            }
        } else {
            console.warn('FileUpload 組件未載入，嘗試手動初始化上傳功能');
            this.initManualFileUpload();
        }
    }

    initManualFileUpload() {
        // 手動初始化文件上傳功能
        const uploadZone = this.uploadZone;
        const fileInput = this.fileInput;

        if (!uploadZone || !fileInput) {
            console.error('上傳元素未找到');
            return;
        }

        // 點擊上傳
        uploadZone.addEventListener('click', () => {
            console.log('點擊上傳區域');
            fileInput.click();
        });

        // 拖放事件
        uploadZone.addEventListener('dragover', (e) => {
            e.preventDefault();
            uploadZone.classList.add('dragover');
        });

        uploadZone.addEventListener('dragleave', (e) => {
            e.preventDefault();
            uploadZone.classList.remove('dragover');
        });

        uploadZone.addEventListener('drop', (e) => {
            e.preventDefault();
            uploadZone.classList.remove('dragover');
            const files = e.dataTransfer.files;
            if (files.length > 0) {
                this.handleManualFileUpload(files[0]);
            }
        });

        // 文件選擇事件
        fileInput.addEventListener('change', (e) => {
            console.log('文件選擇事件觸發');
            if (e.target.files.length > 0) {
                this.handleManualFileUpload(e.target.files[0]);
            }
        });

        console.log('手動文件上傳功能初始化完成');
    }

    async handleManualFileUpload(file) {
        console.log('開始處理文件上傳:', file.name);

        // 簡單的文件驗證
        const allowedTypes = ['.zip', '.7z', '.rar', '.tar', '.gz'];
        const fileExt = '.' + file.name.split('.').pop().toLowerCase();

        if (!allowedTypes.includes(fileExt)) {
            alert(`不支援的檔案格式！請上傳 ${allowedTypes.join('、')} 檔案。`);
            return;
        }

        const maxSizeMB = 1000;
        if (file.size > maxSizeMB * 1024 * 1024) {
            const fileSizeMB = (file.size / (1024 * 1024)).toFixed(1);
            alert(`檔案太大！最大支援 ${maxSizeMB}MB，您的檔案為 ${fileSizeMB}MB`);
            return;
        }

        // 顯示上傳狀態
        if (this.uploadStatus) {
            this.uploadStatus.style.display = 'block';
            this.uploadStatus.innerHTML = `<i class="fas fa-spinner fa-spin"></i> 正在上傳 ${file.name}...`;
            this.uploadStatus.style.color = '#ffc107';
        }

        try {
            // 獲取處理模式
            const processingMode = document.querySelector('input[name="processing_mode"]:checked')?.value || 'summary_only';

            // 創建 FormData
            const formData = new FormData();
            formData.append('file', file);
            formData.append('processing_mode', processingMode);

            // 上傳文件並自動處理
            const response = await fetch('/ft-eqc/api/upload_and_process_ft_summary', {
                method: 'POST',
                body: formData
            });

            const result = await response.json();

            if (response.ok && result.status === 'success') {
                console.log('文件上傳並處理成功:', result);

                // 保存處理結果路徑
                if (result.processing_result && result.processing_result.target_directory) {
                    this.uploadedFilePath = result.processing_result.target_directory;
                    this.uploadProcessed = true;  // 標記已經處理完成
                }

                // 更新狀態
                if (this.uploadStatus) {
                    const processingInfo = result.processing_result || {};
                    const processingTime = processingInfo.processing_time_seconds || 0;
                    const outputFile = processingInfo.output_file || '';

                    this.uploadStatus.innerHTML = `
                        <div style="color: #28a745; margin-bottom: 4px;">
                            <i class="fas fa-check"></i> ${file.name} 上傳並處理完成
                        </div>
                        <div style="font-size: 10px; color: #6c757d;">
                            ${outputFile ? `成功產生 ${outputFile}，` : ''}耗時 ${processingTime.toFixed(1)}秒
                        </div>
                    `;
                    this.uploadStatus.style.color = '#28a745';
                }

                // 如果有下載連結，顯示下載按鈕
                if (result.download_url) {
                    this.showUploadDownloadLink(result.download_url);
                }

                // 重新驗證輸入
                this.validateInput();

                const processingInfo = result.processing_result || {};
                const outputFile = processingInfo.output_file || '';
                const processingTime = processingInfo.processing_time_seconds || 0;

                alert(`文件上傳並處理完成！\n${outputFile ? `成功產生 ${outputFile}` : '處理完成'}\n耗時 ${processingTime.toFixed(1)}秒\n${result.download_url ? '可以點擊下載按鈕獲取結果' : ''}`);

            } else {
                throw new Error(result.message || '上傳失敗');
            }

        } catch (error) {
            console.error('文件上傳失敗:', error);

            // 檢查是否為重複上傳錯誤
            if (error.message && error.message.includes('檔案重複上傳')) {
                this.handleDuplicateUploadError(file, error.message);
            } else {
                if (this.uploadStatus) {
                    this.uploadStatus.innerHTML = `<i class="fas fa-times"></i> 上傳失敗: ${error.message}`;
                    this.uploadStatus.style.color = '#dc3545';
                }

                alert(`文件上傳失敗: ${error.message}`);
            }
        }
    }

    handleFileUploadSuccess(uploadResult) {
        console.log('文件上傳成功:', uploadResult);

        // 保存上傳的文件路徑
        if (uploadResult.extracted_path) {
            this.uploadedFilePath = uploadResult.extracted_path;
        } else if (uploadResult.file_path) {
            this.uploadedFilePath = uploadResult.file_path;
        }

        // 重新驗證輸入
        this.validateInput();

        // 顯示成功訊息
        if (typeof StatusManager !== 'undefined') {
            StatusManager.showToast('文件上傳成功，可以開始處理', 'success');
        }
    }

    validateInput() {
        // 檢查路徑輸入或文件上傳
        const folderPath = document.getElementById('folderPath').value.trim();
        const hasPath = folderPath.length > 0;
        const hasUpload = this.uploadedFilePath !== null;

        // 任一方式有效即可
        const isValid = hasPath || hasUpload;

        this.processBtn.disabled = !isValid;

        if (!isValid) {
            this.processBtn.innerHTML = '<i class="fas fa-exclamation-triangle"></i> 請輸入路徑或上傳檔案';
        } else {
            this.processBtn.innerHTML = '<i class="fas fa-play"></i> 開始批量處理';
        }
    }
    
    async startProcessing() {
        // 檢查是否已經通過文件上傳完成處理
        if (this.uploadedFilePath && this.uploadProcessed) {
            this.showError('檔案已經處理完成，請查看上傳區域的下載連結');
            return;
        }

        // 優先使用上傳的文件，其次使用路徑輸入
        let folderPath = '';

        if (this.uploadedFilePath) {
            folderPath = this.uploadedFilePath;
        } else {
            folderPath = document.getElementById('folderPath').value.trim();
            if (!folderPath) {
                this.showError('請輸入資料夾路徑或上傳檔案');
                return;
            }
        }

        // 移除勾選框，預設為覆寫模式
        const forceOverwrite = true;
        // 獲取處理模式
        const processingMode = document.querySelector('input[name="processing_mode"]:checked').value;
        
        try {
            // 顯示處理狀態
            this.showProcessing();
            
            // 呼叫 API
            const response = await fetch('/ft-eqc/api/process_ft_summary', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    folder_path: folderPath,
                    force_overwrite: forceOverwrite,
                    processing_mode: processingMode
                })
            });
            
            const result = await response.json();
            
            if (response.ok && result.status === 'success') {
                this.showSuccess(result);
            } else {
                this.showError(result.message || result.error_message || '處理失敗');
            }
            
        } catch (error) {
            console.error('處理錯誤:', error);
            this.showError(`網路錯誤: ${error.message}`);
        }
    }
    
    showProcessing() {
        // 禁用按鈕
        this.processBtn.disabled = true;
        this.processBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 處理中...';
        
        // 顯示進度條
        this.progressContainer.style.display = 'block';
        this.progressFill.style.width = '0%';
        this.progressText.textContent = '正在掃描檔案...';
        
        // 隱藏結果
        this.resultContainer.style.display = 'none';
        
        // 模擬進度更新
        this.simulateProgress();
    }
    
    simulateProgress() {
        let progress = 0;
        const interval = setInterval(() => {
            progress += Math.random() * 15;
            if (progress > 90) {
                progress = 90;
                clearInterval(interval);
            }
            
            this.progressFill.style.width = `${progress}%`;
            
            if (progress < 30) {
                this.progressText.textContent = '正在掃描 CSV 檔案...';
            } else if (progress < 60) {
                this.progressText.textContent = '正在處理檔案...';
            } else {
                this.progressText.textContent = '正在生成 FT Summary...';
            }
        }, 500);
    }
    
    showSuccess(result) {
        // 完成進度條
        this.progressFill.style.width = '100%';
        this.progressText.textContent = '處理完成！';
        
        setTimeout(() => {
            this.progressContainer.style.display = 'none';
            
            // 顯示成功結果
            this.resultContainer.className = 'ft-summary-result success';
            this.resultContainer.style.display = 'block';
            
            document.getElementById('resultMessage').innerHTML = `
                <h4><i class="fas fa-check-circle"></i> 處理成功！</h4>
                <p>${result.message}</p>
            `;
            
            // 顯示統計資料
            this.updateStats(result);
            this.statsContainer.style.display = 'grid';
            
            // 顯示檔案清單
            this.showFileLists(result);
            document.getElementById('fileListContainer').style.display = 'block';
            
            // 顯示下載連結
            if (result.download_url) {
                // 如果有壓縮檔下載連結（遠端路徑處理）
                this.showArchiveDownloadLink(result.download_url);
            } else if (result.ft_summary_output_file) {
                // 一般的檔案下載連結
                this.showDownloadLink(result.ft_summary_output_file);
            }
            
            // 恢復按鈕
            this.resetButton();
        }, 1000);
    }
    
    showError(message) {
        // 隱藏進度條
        this.progressContainer.style.display = 'none';
        
        // 顯示錯誤結果
        this.resultContainer.className = 'ft-summary-result error';
        this.resultContainer.style.display = 'block';
        
        document.getElementById('resultMessage').innerHTML = `
            <h4><i class="fas fa-exclamation-triangle"></i> 處理失敗</h4>
            <p>${message}</p>
        `;
        
        // 隱藏統計和下載
        this.statsContainer.style.display = 'none';
        this.downloadContainer.style.display = 'none';
        
        // 恢復按鈕
        this.resetButton();
    }
    
    updateStats(result) {
        document.getElementById('totalFiles').textContent = result.total_files || 0;
        document.getElementById('processedFiles').textContent = result.processed_files || 0;
        document.getElementById('skippedFiles').textContent = result.skipped_files || 0;
        document.getElementById('ftSummaryFiles').textContent = result.ft_summary_files || 0;
        document.getElementById('eqcSummaryFiles').textContent = result.eqc_summary_files || 0;
        document.getElementById('processingTime').textContent = 
            `${(result.processing_time_seconds || 0).toFixed(1)}s`;
    }
    
    showFileLists(result) {
        // 顯示FT檔案清單
        const ftFileList = document.getElementById('ftFileList');
        if (result.ft_file_list && result.ft_file_list.length > 0) {
            ftFileList.innerHTML = result.ft_file_list
                .map(filename => `<div class="ft-summary-file-item">${filename}</div>`)
                .join('');
        } else {
            ftFileList.innerHTML = '<div class="ft-summary-file-item empty">無 FT 檔案</div>';
        }
        
        // 顯示EQC檔案清單
        const eqcFileList = document.getElementById('eqcFileList');
        if (result.eqc_file_list && result.eqc_file_list.length > 0) {
            eqcFileList.innerHTML = result.eqc_file_list
                .map(filename => `<div class="ft-summary-file-item">${filename}</div>`)
                .join('');
        } else {
            eqcFileList.innerHTML = '<div class="ft-summary-file-item empty">無 EQC 檔案</div>';
        }
    }
    
    showDownloadLink(filePath) {
        this.downloadContainer.style.display = 'block';
        
        const downloadBtn = document.getElementById('downloadBtn');
        
        // 建立下載連結
        downloadBtn.onclick = () => {
            // 由於檔案在伺服器端，我們需要提供一個下載端點
            // 這裡暫時顯示檔案路徑，後續可以實作檔案下載功能
            this.copyToClipboard(filePath);
        };
    }
    
    copyToClipboard(text) {
        navigator.clipboard.writeText(text).then(() => {
            // 暫時修改按鈕文字
            const downloadBtn = document.getElementById('downloadBtn');
            const originalText = downloadBtn.innerHTML;
            downloadBtn.innerHTML = '<i class="fas fa-check"></i> 路徑已複製到剪貼板';
            
            setTimeout(() => {
                downloadBtn.innerHTML = originalText;
            }, 2000);
        }).catch(err => {
            console.error('無法複製到剪貼板:', err);
            alert(`檔案路徑: ${text}`);
        });
    }
    
    resetButton() {
        this.processBtn.disabled = false;
        this.processBtn.innerHTML = '<i class="fas fa-play"></i> 開始批量處理';
        this.validateInput();
    }

    showArchiveDownloadLink(downloadUrl) {
        // 顯示壓縮檔下載連結
        const downloadBtn = document.getElementById('downloadBtn');
        if (downloadBtn) {
            downloadBtn.href = downloadUrl;
            downloadBtn.innerHTML = '<i class="fas fa-download"></i> 下載處理結果 (ZIP)';
            downloadBtn.onclick = () => {
                // 可以添加下載追蹤邏輯
                console.log('開始下載壓縮檔:', downloadUrl);
            };
            this.downloadContainer.style.display = 'block';
        }
    }

    handleDuplicateUploadError(file, errorMessage) {
        // 處理重複上傳錯誤
        console.log('處理重複上傳錯誤:', errorMessage);

        if (this.uploadStatus) {
            this.uploadStatus.innerHTML = `
                <div style="color: #ffc107; margin-bottom: 8px;">
                    <i class="fas fa-exclamation-triangle"></i> ${errorMessage}
                </div>
                <button id="clearCacheBtn" style="
                    background: #dc3545;
                    color: white;
                    border: none;
                    padding: 4px 8px;
                    border-radius: 4px;
                    font-size: 11px;
                    cursor: pointer;
                    margin-right: 8px;
                ">
                    清除快取並重新上傳
                </button>
                <button id="waitRetryBtn" style="
                    background: #6c757d;
                    color: white;
                    border: none;
                    padding: 4px 8px;
                    border-radius: 4px;
                    font-size: 11px;
                    cursor: pointer;
                ">
                    等待後重試
                </button>
            `;
            this.uploadStatus.style.display = 'block';

            // 綁定清除快取按鈕事件
            const clearCacheBtn = document.getElementById('clearCacheBtn');
            if (clearCacheBtn) {
                clearCacheBtn.onclick = () => this.clearCacheAndRetryUpload(file);
            }

            // 綁定等待重試按鈕事件
            const waitRetryBtn = document.getElementById('waitRetryBtn');
            if (waitRetryBtn) {
                waitRetryBtn.onclick = () => {
                    this.uploadStatus.innerHTML = '<i class="fas fa-clock"></i> 請等待 5 分鐘後重試';
                    this.uploadStatus.style.color = '#6c757d';
                };
            }
        }
    }

    async clearCacheAndRetryUpload(file) {
        try {
            console.log('清除快取並重新上傳...');

            // 更新狀態
            if (this.uploadStatus) {
                this.uploadStatus.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 正在清除快取...';
                this.uploadStatus.style.color = '#ffc107';
            }

            // 清除重複上傳快取
            const clearResponse = await fetch('/ft-eqc/api/clear_duplicate_cache', {
                method: 'POST'
            });

            const clearResult = await clearResponse.json();

            if (clearResponse.ok && clearResult.status === 'success') {
                console.log('快取清除成功:', clearResult);

                // 等待一下再重新上傳
                await new Promise(resolve => setTimeout(resolve, 1000));

                // 重新上傳文件
                await this.handleManualFileUpload(file);

            } else {
                throw new Error(clearResult.message || '清除快取失敗');
            }

        } catch (error) {
            console.error('清除快取失敗:', error);

            if (this.uploadStatus) {
                this.uploadStatus.innerHTML = `<i class="fas fa-times"></i> 清除快取失敗: ${error.message}`;
                this.uploadStatus.style.color = '#dc3545';
            }

            alert(`清除快取失敗: ${error.message}`);
        }
    }

    initSevenZipConfig() {
        // 初始化 7zip 路徑配置
        console.log('初始化 7zip 路徑配置...');

        // 載入儲存的路徑
        this.loadSevenZipPath();

        // 綁定測試按鈕事件
        if (this.testSevenZipBtn) {
            this.testSevenZipBtn.addEventListener('click', () => {
                this.testSevenZipPath();
            });
        }

        // 綁定儲存按鈕事件
        if (this.saveSevenZipBtn) {
            this.saveSevenZipBtn.addEventListener('click', () => {
                this.saveSevenZipPath();
            });
        }

        // 綁定路徑輸入框事件
        if (this.sevenZipPath) {
            this.sevenZipPath.addEventListener('input', () => {
                // 清除之前的狀態
                if (this.sevenZipStatus) {
                    this.sevenZipStatus.style.display = 'none';
                }
            });
        }

        console.log('7zip 路徑配置初始化完成');
    }

    loadSevenZipPath() {
        // 從 localStorage 載入儲存的 7zip 路徑
        try {
            const savedPath = localStorage.getItem('sevenZipPath');
            if (savedPath && this.sevenZipPath) {
                this.sevenZipPath.value = savedPath;
                console.log('載入儲存的 7zip 路徑:', savedPath);
            }
        } catch (error) {
            console.warn('載入 7zip 路徑失敗:', error);
        }
    }

    async testSevenZipPath() {
        // 測試 7zip 路徑是否有效
        const path = this.sevenZipPath?.value?.trim();

        if (!path) {
            this.showSevenZipStatus('請輸入 7zip 路徑', 'error');
            return;
        }

        try {
            this.showSevenZipStatus('正在測試 7zip 路徑...', 'info');

            // 調用後端 API 測試路徑
            const response = await fetch('/ft-eqc/api/test_7zip_path', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ path: path })
            });

            const result = await response.json();

            if (response.ok && result.status === 'success') {
                this.showSevenZipStatus('7zip 路徑測試成功！', 'success');
            } else {
                this.showSevenZipStatus(`測試失敗: ${result.message}`, 'error');
            }

        } catch (error) {
            console.error('測試 7zip 路徑失敗:', error);
            this.showSevenZipStatus(`測試失敗: ${error.message}`, 'error');
        }
    }

    async saveSevenZipPath() {
        // 儲存 7zip 路徑
        const path = this.sevenZipPath?.value?.trim();

        if (!path) {
            this.showSevenZipStatus('請輸入 7zip 路徑', 'error');
            return;
        }

        try {
            this.showSevenZipStatus('正在儲存 7zip 路徑...', 'info');

            // 調用後端 API 儲存路徑
            const response = await fetch('/ft-eqc/api/save_7zip_path', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ path: path })
            });

            const result = await response.json();

            if (response.ok && result.status === 'success') {
                // 同時儲存到 localStorage
                localStorage.setItem('sevenZipPath', path);
                this.showSevenZipStatus('7zip 路徑儲存成功！', 'success');
            } else {
                this.showSevenZipStatus(`儲存失敗: ${result.message}`, 'error');
            }

        } catch (error) {
            console.error('儲存 7zip 路徑失敗:', error);
            this.showSevenZipStatus(`儲存失敗: ${error.message}`, 'error');
        }
    }

    showSevenZipStatus(message, type) {
        // 顯示 7zip 狀態訊息
        if (!this.sevenZipStatus) return;

        const colors = {
            success: '#28a745',
            error: '#dc3545',
            warning: '#ffc107',
            info: '#17a2b8'
        };

        const icons = {
            success: 'fas fa-check',
            error: 'fas fa-times',
            warning: 'fas fa-exclamation-triangle',
            info: 'fas fa-info-circle'
        };

        this.sevenZipStatus.innerHTML = `<i class="${icons[type]}"></i> ${message}`;
        this.sevenZipStatus.style.color = colors[type];
        this.sevenZipStatus.style.display = 'block';
    }

    showUploadDownloadLink(downloadUrl) {
        // 在上傳狀態區域顯示下載連結
        if (!this.uploadStatus) return;

        const currentContent = this.uploadStatus.innerHTML;
        const downloadButton = `
            <div style="margin-top: 8px;">
                <a href="${downloadUrl}"
                   style="
                       display: inline-block;
                       padding: 6px 12px;
                       background: #007bff;
                       color: white;
                       text-decoration: none;
                       border-radius: 4px;
                       font-size: 11px;
                       transition: background-color 0.3s ease;
                   "
                   onmouseover="this.style.background='#0056b3'"
                   onmouseout="this.style.background='#007bff'"
                   download>
                    <i class="fas fa-download"></i> 下載處理結果
                </a>
            </div>
        `;

        this.uploadStatus.innerHTML = currentContent + downloadButton;
    }
}

// DOM 載入完成後初始化
document.addEventListener('DOMContentLoaded', () => {
    new FTSummaryProcessor();
});

// 工具函數：格式化檔案大小
function formatFileSize(bytes) {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

// 工具函數：格式化時間
function formatDuration(seconds) {
    if (seconds < 60) {
        return `${seconds.toFixed(1)}秒`;
    } else if (seconds < 3600) {
        const minutes = Math.floor(seconds / 60);
        const remainingSeconds = (seconds % 60).toFixed(1);
        return `${minutes}分${remainingSeconds}秒`;
    } else {
        const hours = Math.floor(seconds / 3600);
        const minutes = Math.floor((seconds % 3600) / 60);
        return `${hours}小時${minutes}分鐘`;
    }
}