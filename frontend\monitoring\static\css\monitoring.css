/* Monitoring Module CSS - 監控模組樣式 */

/* 監控容器樣式 */
.monitoring-container {
    padding: 20px;
    background: #f8f9fa;
    min-height: 100vh;
}

.monitoring-header {
    background: white;
    padding: 20px;
    border-radius: 8px;
    margin-bottom: 20px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.monitoring-title {
    font-size: 24px;
    font-weight: 600;
    color: #333;
    margin: 0;
}

.system-status {
    display: flex;
    align-items: center;
    gap: 10px;
    font-size: 14px;
    font-weight: 500;
}

.status-indicator {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    display: inline-block;
}

.status-indicator.healthy {
    background-color: #28a745;
}

.status-indicator.warning {
    background-color: #ffc107;
}

.status-indicator.critical {
    background-color: #dc3545;
}

/* 即時儀表板樣式 */
.realtime-dashboard {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.metric-card {
    background: white;
    border-radius: 8px;
    padding: 20px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    text-align: center;
    position: relative;
    overflow: hidden;
}

.metric-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 4px;
    background: linear-gradient(90deg, #007bff, #28a745);
}

.metric-card.warning::before {
    background: #ffc107;
}

.metric-card.critical::before {
    background: #dc3545;
}

.metric-icon {
    font-size: 32px;
    color: #007bff;
    margin-bottom: 10px;
}

.metric-value {
    font-size: 28px;
    font-weight: 700;
    color: #333;
    margin-bottom: 5px;
}

.metric-label {
    font-size: 14px;
    color: #666;
    margin-bottom: 10px;
}

.metric-change {
    font-size: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 4px;
}

.metric-change.positive {
    color: #28a745;
}

.metric-change.negative {
    color: #dc3545;
}

/* 系統健康檢查樣式 */
.health-check-container {
    background: white;
    border-radius: 8px;
    padding: 20px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    margin-bottom: 20px;
}

.health-check-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 1px solid #e9ecef;
}

.health-check-title {
    font-size: 18px;
    font-weight: 600;
    color: #333;
}

.health-check-time {
    font-size: 12px;
    color: #666;
}

.health-services {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
}

.service-item {
    display: flex;
    align-items: center;
    padding: 12px;
    background: #f8f9fa;
    border-radius: 8px;
    gap: 10px;
}

.service-status {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    flex-shrink: 0;
}

.service-status.online {
    background-color: #28a745;
}

.service-status.offline {
    background-color: #dc3545;
}

.service-status.degraded {
    background-color: #ffc107;
}

.service-name {
    font-size: 14px;
    font-weight: 500;
    color: #333;
}

.service-response-time {
    font-size: 12px;
    color: #666;
    margin-left: auto;
}

/* 資料庫管理器樣式 */
.database-manager {
    background: white;
    border-radius: 8px;
    padding: 20px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    margin-bottom: 20px;
}

.db-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 15px;
    margin-bottom: 20px;
}

.db-stat-item {
    text-align: center;
    padding: 15px;
    background: #f8f9fa;
    border-radius: 8px;
}

.db-stat-value {
    font-size: 20px;
    font-weight: 600;
    color: #007bff;
    margin-bottom: 5px;
}

.db-stat-label {
    font-size: 12px;
    color: #666;
}

.db-actions {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
    margin-bottom: 20px;
}

.db-action-btn {
    padding: 8px 16px;
    border: none;
    border-radius: 4px;
    font-size: 14px;
    cursor: pointer;
    transition: background-color 0.2s;
}

.db-action-btn.backup {
    background-color: #28a745;
    color: white;
}

.db-action-btn.optimize {
    background-color: #17a2b8;
    color: white;
}

.db-action-btn.analyze {
    background-color: #ffc107;
    color: #212529;
}

/* 日誌查看器樣式 */
.log-viewer {
    background: white;
    border-radius: 8px;
    padding: 20px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    margin-bottom: 20px;
}

.log-controls {
    display: flex;
    gap: 15px;
    margin-bottom: 20px;
    padding: 15px;
    background: #f8f9fa;
    border-radius: 8px;
    flex-wrap: wrap;
}

.log-control {
    display: flex;
    align-items: center;
    gap: 8px;
}

.log-control label {
    font-size: 14px;
    font-weight: 500;
    color: #333;
}

.log-control select,
.log-control input {
    padding: 6px 10px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 14px;
}

.log-content {
    background: #1e1e1e;
    color: #d4d4d4;
    padding: 15px;
    border-radius: 8px;
    font-family: 'Consolas', 'Monaco', monospace;
    font-size: 13px;
    line-height: 1.4;
    max-height: 400px;
    overflow-y: auto;
}

.log-line {
    margin-bottom: 2px;
    white-space: pre-wrap;
    word-break: break-word;
}

.log-line.error {
    color: #f48771;
}

.log-line.warning {
    color: #dcdcaa;
}

.log-line.info {
    color: #9cdcfe;
}

.log-line.debug {
    color: #808080;
}

/* 圖表容器樣式 */
.monitoring-charts {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
    margin-bottom: 20px;
}

.chart-container {
    background: white;
    border-radius: 8px;
    padding: 20px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.chart-title {
    font-size: 16px;
    font-weight: 600;
    color: #333;
    margin-bottom: 15px;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.chart-wrapper {
    position: relative;
    height: 250px;
}

.chart-legend {
    margin-top: 15px;
    display: flex;
    gap: 15px;
    flex-wrap: wrap;
}

.legend-item {
    display: flex;
    align-items: center;
    gap: 5px;
    font-size: 12px;
    color: #666;
}

.legend-color {
    width: 12px;
    height: 12px;
    border-radius: 2px;
}

/* 告警系統樣式 */
.alerts-container {
    background: white;
    border-radius: 8px;
    padding: 20px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    margin-bottom: 20px;
}

.alert-item {
    display: flex;
    align-items: center;
    padding: 12px;
    margin-bottom: 10px;
    border-radius: 8px;
    border-left: 4px solid #dee2e6;
}

.alert-item.critical {
    background-color: #f8d7da;
    border-left-color: #dc3545;
    color: #721c24;
}

.alert-item.warning {
    background-color: #fff3cd;
    border-left-color: #ffc107;
    color: #856404;
}

.alert-item.info {
    background-color: #d1ecf1;
    border-left-color: #17a2b8;
    color: #0c5460;
}

.alert-icon {
    font-size: 18px;
    margin-right: 12px;
}

.alert-content {
    flex: 1;
}

.alert-title {
    font-size: 14px;
    font-weight: 600;
    margin-bottom: 4px;
}

.alert-message {
    font-size: 13px;
    opacity: 0.9;
}

.alert-time {
    font-size: 12px;
    opacity: 0.7;
    margin-left: auto;
}

/* 響應式設計 */
@media (max-width: 768px) {
    .realtime-dashboard {
        grid-template-columns: 1fr;
    }
    
    .monitoring-charts {
        grid-template-columns: 1fr;
    }
    
    .monitoring-header {
        flex-direction: column;
        gap: 15px;
        text-align: center;
    }
    
    .log-controls {
        flex-direction: column;
        gap: 10px;
    }
    
    .db-actions {
        justify-content: center;
    }
    
    .health-services {
        grid-template-columns: 1fr;
    }
    
    .chart-wrapper {
        height: 200px;
    }
    
    .monitoring-container {
        padding: 10px;
    }
}