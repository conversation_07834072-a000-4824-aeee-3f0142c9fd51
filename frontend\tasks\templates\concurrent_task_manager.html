<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>並發任務管理器 - 企業級任務監控</title>
    <link rel="stylesheet" href="{{ url_for('shared.static', filename='css/global.css') }}">
    <link rel="stylesheet" href="{{ url_for('tasks.static', filename='css/tasks.css') }}">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 12px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            text-shadow: 0 2px 4px rgba(0,0,0,0.3);
        }

        .header .subtitle {
            opacity: 0.9;
            font-size: 1.1em;
        }

        .main-content {
            padding: 30px;
        }

        .task-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .task-card {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            border-left: 4px solid #667eea;
            transition: transform 0.2s ease;
        }

        .task-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }

        .task-title {
            font-size: 1.2em;
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 10px;
        }

        .task-status {
            display: inline-block;
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 0.8em;
            font-weight: 500;
            text-transform: uppercase;
        }

        .status-running {
            background: #e3f2fd;
            color: #1976d2;
        }

        .status-completed {
            background: #e8f5e8;
            color: #388e3c;
        }

        .status-failed {
            background: #ffebee;
            color: #d32f2f;
        }

        .controls {
            display: flex;
            gap: 15px;
            margin-bottom: 30px;
            flex-wrap: wrap;
        }

        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 8px;
        }

        .btn-primary {
            background: #667eea;
            color: white;
        }

        .btn-primary:hover {
            background: #5a6fd8;
            transform: translateY(-1px);
        }

        .btn-secondary {
            background: #6c757d;
            color: white;
        }

        .btn-secondary:hover {
            background: #5a6268;
        }

        .btn-success {
            background: #28a745;
            color: white;
        }

        .btn-success:hover {
            background: #218838;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>⚙️ 並發任務管理器</h1>
            <div class="subtitle">企業級任務監控與管理系統</div>
        </div>

        <div class="main-content">
            <div class="controls">
                <button class="btn btn-primary" onclick="refreshTasks()">
                    <i class="fas fa-sync-alt"></i> 重新整理
                </button>
                <button class="btn btn-success" onclick="startNewTask()">
                    <i class="fas fa-plus"></i> 新增任務
                </button>
                <button class="btn btn-secondary" onclick="clearCompleted()">
                    <i class="fas fa-trash"></i> 清除已完成
                </button>
            </div>

            <div class="task-grid" id="taskGrid">
                <!-- 任務卡片將動態生成 -->
                <div class="task-card">
                    <div class="task-title">範例任務</div>
                    <div class="task-status status-running">執行中</div>
                    <div style="margin-top: 10px; color: #666; font-size: 0.9em;">
                        這是一個範例任務，展示任務管理器的功能
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 載入核心依賴 -->
    <script src="{{ url_for('shared.static', filename='js/core/utils.js') }}"></script>
    <script src="{{ url_for('shared.static', filename='js/core/api-client.js') }}"></script>
    <script src="{{ url_for('tasks.static', filename='js/concurrent-task-manager.js') }}"></script>

    <script>
        function refreshTasks() {
            console.log('重新整理任務列表');
            // 實作任務重新整理邏輯
        }

        function startNewTask() {
            console.log('啟動新任務');
            // 實作新任務啟動邏輯
        }

        function clearCompleted() {
            console.log('清除已完成任務');
            // 實作清除已完成任務邏輯
        }
    </script>
</body>
</html>