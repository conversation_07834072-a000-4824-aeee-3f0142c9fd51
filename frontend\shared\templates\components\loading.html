<!-- 載入動畫組件 -->
<div class="loading-overlay" id="global-loading" style="display: none;">
    <div class="loading-content">
        <div class="loading-spinner large">
            <div class="spinner-ring"></div>
            <div class="spinner-ring"></div>
            <div class="spinner-ring"></div>
            <div class="spinner-ring"></div>
        </div>
        <div class="loading-text" id="loading-text">載入中...</div>
        <div class="loading-progress" id="loading-progress" style="display: none;">
            <div class="progress-bar">
                <div class="progress-fill" id="loading-progress-fill"></div>
            </div>
            <div class="progress-text" id="loading-progress-text">0%</div>
        </div>
    </div>
</div>

<!-- 小型載入動畫模板 -->
<template id="small-loading-template">
    <div class="loading-spinner small">
        <div class="spinner-ring"></div>
        <div class="spinner-ring"></div>
        <div class="spinner-ring"></div>
        <div class="spinner-ring"></div>
    </div>
</template>

<!-- 載入動畫 JavaScript -->
<script>
class LoadingManager {
    constructor() {
        this.overlay = document.getElementById('global-loading');
        this.loadingText = document.getElementById('loading-text');
        this.loadingProgress = document.getElementById('loading-progress');
        this.progressFill = document.getElementById('loading-progress-fill');
        this.progressText = document.getElementById('loading-progress-text');
        this.smallTemplate = document.getElementById('small-loading-template');
        
        this.isVisible = false;
        this.currentProgress = 0;
        
        this.init();
    }
    
    init() {
        if (!this.overlay) {
            console.warn('載入管理器初始化失敗：找不到載入覆蓋層');
            return;
        }
        
        // 設置 ESC 鍵隱藏（可選）
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape' && this.isVisible) {
                // 通常載入過程中不應該允許取消，但可以根據需要啟用
                // this.hide();
            }
        });
    }
    
    show(options = {}) {
        const {
            text = '載入中...',
            showProgress = false,
            progress = 0,
            allowCancel = false
        } = options;
        
        if (!this.overlay) return;
        
        // 設置載入文字
        if (this.loadingText) {
            this.loadingText.textContent = text;
        }
        
        // 設置進度條
        if (showProgress && this.loadingProgress) {
            this.loadingProgress.style.display = 'block';
            this.updateProgress(progress);
        } else if (this.loadingProgress) {
            this.loadingProgress.style.display = 'none';
        }
        
        // 顯示覆蓋層
        this.overlay.style.display = 'flex';
        document.body.classList.add('loading-active');
        this.isVisible = true;
        
        // 防止頁面滾動
        document.body.style.overflow = 'hidden';
    }
    
    hide() {
        if (!this.overlay) return;
        
        this.overlay.style.display = 'none';
        document.body.classList.remove('loading-active');
        this.isVisible = false;
        
        // 恢復頁面滾動
        document.body.style.overflow = '';
        
        // 重置進度
        this.currentProgress = 0;
        if (this.progressFill) {
            this.progressFill.style.width = '0%';
        }
        if (this.progressText) {
            this.progressText.textContent = '0%';
        }
    }
    
    updateText(text) {
        if (this.loadingText) {
            this.loadingText.textContent = text;
        }
    }
    
    updateProgress(progress, text = null) {
        this.currentProgress = Math.max(0, Math.min(100, progress));
        
        if (this.progressFill) {
            this.progressFill.style.width = `${this.currentProgress}%`;
        }
        
        if (this.progressText) {
            this.progressText.textContent = `${Math.round(this.currentProgress)}%`;
        }
        
        if (text && this.loadingText) {
            this.loadingText.textContent = text;
        }
        
        // 如果進度達到 100%，可以自動隱藏
        if (this.currentProgress >= 100) {
            setTimeout(() => {
                this.hide();
            }, 500);
        }
    }
    
    // 創建小型載入動畫
    createSmallSpinner() {
        if (!this.smallTemplate) return null;
        
        return this.smallTemplate.content.cloneNode(true);
    }
    
    // 為元素添加載入狀態
    addLoadingToElement(element, options = {}) {
        if (!element) return;
        
        const {
            text = '載入中...',
            size = 'small',
            overlay = true
        } = options;
        
        // 保存原始內容
        if (!element.dataset.originalContent) {
            element.dataset.originalContent = element.innerHTML;
        }
        
        // 創建載入內容
        let loadingContent;
        if (overlay) {
            loadingContent = `
                <div class="element-loading-overlay">
                    <div class="loading-spinner ${size}">
                        <div class="spinner-ring"></div>
                        <div class="spinner-ring"></div>
                        <div class="spinner-ring"></div>
                        <div class="spinner-ring"></div>
                    </div>
                    <div class="loading-text">${text}</div>
                </div>
            `;
        } else {
            loadingContent = `
                <div class="inline-loading">
                    <div class="loading-spinner ${size}">
                        <div class="spinner-ring"></div>
                        <div class="spinner-ring"></div>
                        <div class="spinner-ring"></div>
                        <div class="spinner-ring"></div>
                    </div>
                    <span class="loading-text">${text}</span>
                </div>
            `;
        }
        
        element.innerHTML = loadingContent;
        element.classList.add('loading-state');
        
        // 禁用元素
        if (element.tagName === 'BUTTON' || element.tagName === 'INPUT') {
            element.disabled = true;
        }
    }
    
    // 移除元素的載入狀態
    removeLoadingFromElement(element) {
        if (!element) return;
        
        // 恢復原始內容
        if (element.dataset.originalContent) {
            element.innerHTML = element.dataset.originalContent;
            delete element.dataset.originalContent;
        }
        
        element.classList.remove('loading-state');
        
        // 啟用元素
        if (element.tagName === 'BUTTON' || element.tagName === 'INPUT') {
            element.disabled = false;
        }
    }
    
    // 便捷方法：顯示載入
    showLoading(text = '載入中...') {
        this.show({ text });
    }
    
    // 便捷方法：顯示進度載入
    showProgress(text = '處理中...', progress = 0) {
        this.show({
            text,
            showProgress: true,
            progress
        });
    }
    
    // 便捷方法：隱藏載入
    hideLoading() {
        this.hide();
    }
    
    // 便捷方法：按鈕載入狀態
    buttonLoading(button, text = '處理中...') {
        this.addLoadingToElement(button, {
            text,
            size: 'small',
            overlay: false
        });
    }
    
    // 便捷方法：移除按鈕載入狀態
    buttonLoaded(button) {
        this.removeLoadingFromElement(button);
    }
    
    // 便捷方法：區域載入狀態
    areaLoading(element, text = '載入中...') {
        this.addLoadingToElement(element, {
            text,
            size: 'medium',
            overlay: true
        });
    }
    
    // 便捷方法：移除區域載入狀態
    areaLoaded(element) {
        this.removeLoadingFromElement(element);
    }
}

// 全域載入管理器
if (typeof window !== 'undefined') {
    window.LoadingManager = LoadingManager;
    
    // 自動初始化
    document.addEventListener('DOMContentLoaded', function() {
        if (!window.loadingManager) {
            window.loadingManager = new LoadingManager();
        }
    });
}
</script>