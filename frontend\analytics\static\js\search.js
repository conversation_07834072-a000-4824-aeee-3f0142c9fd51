// 搜尋引擎模組
// 處理產品搜尋和 LLM 智慧搜尋功能

export class SearchEngine {
    constructor(api) {
        this.api = api;
        this.currentTaskId = null;
        this.pollIntervalId = null;
        this.searchAbortController = null;
        this.searchStartTime = null;
    }

    // 執行產品搜尋
    async performProductSearch(productName, currentPath, searchParams, statusCallback, resultCallback) {
        if (!productName.trim()) {
            statusCallback('請輸入產品名稱', 'error');
            return;
        }

        // 如果有正在進行的搜尋，先停止它
        if (this.pollIntervalId) {
            clearInterval(this.pollIntervalId);
            this.pollIntervalId = null;
        }
        if (this.searchAbortController && !this.searchAbortController.signal.aborted) {
            this.searchAbortController.abort();
        }

        // 重置搜尋狀態
        this.currentTaskId = null;
        this.searchAbortController = new AbortController();

        // 顯示初始狀態
        statusCallback(`📡 正在提交搜尋任務...`, 'loading');
        document.getElementById('productSearchBtn').disabled = true;
        document.getElementById('cancelSearchBtn').style.display = 'inline-block';

        this.searchStartTime = Date.now();

        try {
            const searchRequest = {
                product_name: productName,
                base_path: currentPath,
                search_directory: searchParams.searchDirectory,
                time_range_type: searchParams.timeRangeType,
                file_types: searchParams.selectedFileTypes.length > 0 ? searchParams.selectedFileTypes : null,
                min_size_mb: searchParams.minSize || null,
                max_size_mb: searchParams.maxSize || null,
                include_directories: searchParams.includeDirs,
                max_results: searchParams.maxResults || 1000
            };

            // 如果是自訂時間範圍，添加自訂日期
            if (searchParams.timeRangeType === 'custom') {
                if (searchParams.startDate) searchRequest.custom_start_date = searchParams.startDate + 'T00:00:00';
                if (searchParams.endDate) searchRequest.custom_end_date = searchParams.endDate + 'T23:59:59';
            }

            console.log('產品搜尋請求:', searchRequest);

            // 步驟 1: 提交任務，這個呼叫應該會很快回傳
            const response = await fetch(`${this.api}/search/product`, {
                method: 'POST',
                headers: {'Content-Type': 'application/json'},
                body: JSON.stringify(searchRequest)
            });

            if (!response.ok) {
                const errorData = await response.json();
                throw new Error(errorData.detail || `提交任務失敗: HTTP ${response.status}`);
            }

            const result = await response.json();
            console.log('任務提交結果:', result);

            if (result.status === 'processing' && result.task_id) {
                // 設定當前任務ID
                this.currentTaskId = result.task_id;

                statusCallback(`✅ 任務已提交 (ID: ${result.task_id.substring(0, 8)}...). 正在等待結果...`, 'loading');
                // 步驟 2: 開始輪詢任務狀態
                this.pollTaskStatus(result.task_id, productName, statusCallback, resultCallback);
            } else if (result.status === 'completed' && result.result) {
                // 處理 eager 模式的立即完成結果
                this.currentTaskId = result.task_id;
                statusCallback('🎉 搜尋立即完成！', 'success');

                // 重置 UI 狀態
                this.resetSearchUI();

                // 直接處理結果，不需要輪詢
                this.handleSearchResult(result.result, resultCallback);
            } else if (result.status === 'failed') {
                throw new Error(result.error || '搜尋任務失敗');
            } else {
                throw new Error(result.message || '提交任務時發生未知錯誤');
            }

        } catch (error) {
            console.error('提交搜尋任務錯誤:', error);
            statusCallback(`❌ 提交搜尋任務失敗: ${error.message}`, 'error');

            // 重新啟用搜尋按鈕
            this.resetSearchUI();
        }
    }

    // 輪詢任務狀態
    pollTaskStatus(taskId, productName, statusCallback, resultCallback) {
        this.pollIntervalId = setInterval(async () => {
            try {
                const response = await fetch(`${this.api}/task/status/${taskId}`);
                if (!response.ok) {
                    // 如果查詢本身失敗，停止輪詢
                    clearInterval(this.pollIntervalId);
                    statusCallback(`❌ 查詢任務狀態時發生網路錯誤`, 'error');
                    this.resetSearchUI();
                    return;
                }

                const result = await response.json();
                const elapsed = ((Date.now() - this.searchStartTime) / 1000).toFixed(1);

                if (result.status === 'SUCCESS') {
                    clearInterval(this.pollIntervalId);
                    statusCallback('🎉 搜尋完成！可以開始下一次搜尋', 'success');
                    this.resetSearchUI();

                    // 處理成功的結果
                    this.handleSearchResult(result.result, resultCallback);

                } else if (result.status === 'FAILURE') {
                    clearInterval(this.pollIntervalId);
                    const errorMsg = result.error_message || '搜尋任務失敗';
                    statusCallback(`❌ 搜尋任務失敗: ${errorMsg}`, 'error');
                    this.resetSearchUI();

                } else {
                    // 任務仍在執行中 (PENDING, STARTED, RETRY, PROGRESS)
                    let statusMsg = `⚙️ 任務正在執行中... (${result.status}) - ${elapsed}秒`;

                    // 顯示更詳細的進度資訊
                    if (result.info && result.info.status) {
                        statusMsg = `⚙️ ${result.info.status} - ${elapsed}秒`;
                    } else if (result.status === 'PENDING') {
                        statusMsg = `⏳ 任務等待中... - ${elapsed}秒`;
                    } else if (result.status === 'STARTED') {
                        statusMsg = `🚀 任務已開始執行... - ${elapsed}秒`;
                    } else if (result.status === 'PROGRESS') {
                        statusMsg = `⚙️ 任務執行中... - ${elapsed}秒`;
                    }

                    statusCallback(statusMsg, 'loading');
                }
            } catch (error) {
                clearInterval(this.pollIntervalId);
                statusCallback(`❌ 查詢任務狀態時發生網路錯誤: ${error.message}`, 'error');
                this.resetSearchUI();
            }
        }, 3000); // 每 3 秒查詢一次
    }

    // 處理搜尋結果
    handleSearchResult(resultData, resultCallback) {
        // 處理成功搜尋結果的邏輯
        console.log('收到最終結果:', resultData);

        const actualSearchTime = ((Date.now() - this.searchStartTime) / 1000).toFixed(2);

        if (resultData && resultData.matched_files) {
            // 轉換檔案格式
            const files = resultData.matched_files.map(file => ({
                filename: file.name,
                size: file.size,
                size_mb: file.size / (1024 * 1024),
                modified_time: file.modified_time,
                file_type: file.is_directory ? '目錄' : (file.name.split('.').pop() || 'unknown'),
                is_directory: file.is_directory,
                path: file.path
            }));

            // 排序：目錄在前，檔案在後
            files.sort((a, b) => {
                if (a.is_directory && !b.is_directory) return -1;
                if (!a.is_directory && b.is_directory) return 1;
                return a.filename.localeCompare(b.filename);
            });

            const serverTime = resultData.search_duration ? resultData.search_duration.toFixed(2) : '未知';

            // 準備結果資料
            const searchResult = {
                files: files,
                totalFiles: resultData.total_files,
                totalSizeMB: resultData.total_size_mb,
                searchDuration: actualSearchTime,
                serverDuration: serverTime,
                productFolder: resultData.product_folder
            };

            // 檢查是否找到檔案
            if (resultData.total_files === 0) {
                searchResult.emptyResult = true;
                searchResult.totalFilesInDirectory = resultData.total_files_in_directory;
            }

            resultCallback(searchResult);
        } else {
            resultCallback({
                error: true,
                message: '搜尋結果格式錯誤'
            });
        }
    }

    // 取消產品搜尋
    cancelProductSearch(statusCallback) {
        // 停止輪詢
        if (this.pollIntervalId) {
            clearInterval(this.pollIntervalId);
            this.pollIntervalId = null;
        }

        // 重設 UI 狀態和變數
        this.resetSearchUI();

        statusCallback('🛑 搜尋已取消', 'error');
    }

    // 重置搜尋 UI 狀態
    resetSearchUI() {
        document.getElementById('productSearchBtn').disabled = false;
        document.getElementById('cancelSearchBtn').style.display = 'none';
        this.searchAbortController = null;
        this.pollIntervalId = null;
        this.currentTaskId = null;
    }

    // 執行智慧搜尋
    async performSmartSearch(query, currentPath, maxResults, statusCallback, resultCallback) {
        if (!query.trim()) {
            statusCallback('請輸入搜尋查詢', 'error');
            return;
        }

        statusCallback('🧠 正在使用 LLM 分析您的查詢...', 'loading');
        document.getElementById('smartSearchBtn').disabled = true;

        try {
            const searchRequest = {
                query: query,
                path: currentPath,
                max_results: maxResults || 100
            };

            console.log('智慧搜尋請求:', searchRequest);

            const response = await fetch(`${this.api}/smart-search`, {
                method: 'POST',
                headers: {'Content-Type': 'application/json'},
                body: JSON.stringify(searchRequest)
            });

            const result = await response.json();
            console.log('智慧搜尋結果:', result);

            if (result.status === 'success') {
                // 處理搜尋結果
                if (result.results && result.results.length > 0) {
                    // 合併所有產品的檔案
                    const files = [];
                    result.results.forEach(productResult => {
                        productResult.files.forEach(file => {
                            files.push({
                                filename: file.name,
                                size: file.size_mb * 1024 * 1024, // 轉換為位元組
                                size_mb: file.size_mb,
                                modified_time: file.modified_time,
                                file_type: file.is_directory ? '目錄' : file.file_type,
                                is_directory: file.is_directory,
                                path: file.path
                            });
                        });
                    });

                    // 排序：目錄在前，檔案在後
                    files.sort((a, b) => {
                        if (a.is_directory && !b.is_directory) return -1;
                        if (!a.is_directory && b.is_directory) return 1;
                        return a.filename.localeCompare(b.filename);
                    });

                    const smartSearchResult = {
                        success: true,
                        files: files,
                        interpretation: result.interpretation,
                        analysis: result.analysis,
                        suggestions: result.suggestions,
                        searchDuration: result.search_duration,
                        query: query
                    };

                    statusCallback(`🎉 智慧搜尋完成！找到 ${result.analysis.total_files} 個檔案，來自 ${result.analysis.total_products} 個產品，耗時 ${result.search_duration.toFixed(2)} 秒`, 'success');
                    resultCallback(smartSearchResult);
                } else {
                    statusCallback('🤔 未找到匹配的結果，請嘗試調整搜尋條件', 'error');
                    resultCallback({
                        success: false,
                        interpretation: result.interpretation,
                        analysis: result.analysis,
                        suggestions: result.suggestions
                    });
                }
            } else {
                const errorMsg = result.error_message || '智慧搜尋失敗';
                statusCallback(`❌ ${errorMsg}`, 'error');
                resultCallback({
                    success: false,
                    message: errorMsg
                });
            }

        } catch (error) {
            console.error('智慧搜尋錯誤:', error);
            statusCallback('❌ 搜尋時發生網路錯誤', 'error');
            resultCallback({
                success: false,
                message: '網路錯誤'
            });
        } finally {
            document.getElementById('smartSearchBtn').disabled = false;
        }
    }

    // 取得目前任務狀態
    getCurrentTaskStatus() {
        return {
            taskId: this.currentTaskId,
            isRunning: !!this.pollIntervalId,
            startTime: this.searchStartTime
        };
    }
}