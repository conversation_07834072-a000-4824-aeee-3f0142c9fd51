/**
 * 統一監控儀表板 - WebSocket 連接管理
 * 負責處理即時資料更新和連接管理
 */

class DashboardWebSocket {
    constructor() {
        this.ws = null;
        this.clientId = this.generateClientId();
        this.reconnectAttempts = 0;
        this.maxReconnectAttempts = 5;
        this.reconnectDelay = 1000; // 1秒
        this.heartbeatInterval = null;
        this.isConnected = false;
        this.subscriptions = new Set();
        this.messageHandlers = new Map();
        
        // 事件監聽器
        this.onConnect = null;
        this.onDisconnect = null;
        this.onError = null;
        this.onMessage = null;
        
        this.init();
    }
    
    generateClientId() {
        return 'dashboard_' + Math.random().toString(36).substring(2, 11) + '_' + Date.now();
    }
    
    init() {
        this.connect();
        this.setupEventHandlers();
    }
    
    connect() {
        try {
            // 根據當前協議選擇 WebSocket 協議
            const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
            const wsUrl = `${protocol}//${window.location.host}/dashboard/ws`;
            
            console.log(`正在連接 WebSocket: ${wsUrl}`);
            this.ws = new WebSocket(wsUrl);
            
            this.ws.onopen = this.handleOpen.bind(this);
            this.ws.onmessage = this.handleMessage.bind(this);
            this.ws.onclose = this.handleClose.bind(this);
            this.ws.onerror = this.handleError.bind(this);
            
        } catch (error) {
            console.error('WebSocket 連接失敗:', error);
            this.scheduleReconnect();
        }
    }
    
    handleOpen(event) {
        console.log('WebSocket 連接已建立');
        this.isConnected = true;
        this.reconnectAttempts = 0;
        
        // 更新連接狀態指示器
        this.updateConnectionStatus('connected');
        
        // 啟動心跳
        this.startHeartbeat();
        
        // 訂閱所有監控資料
        this.subscribeToAll();
        
        // 觸發連接事件
        if (this.onConnect) {
            this.onConnect(event);
        }
    }
    
    handleMessage(event) {
        try {
            const data = JSON.parse(event.data);
            console.log('收到 WebSocket 訊息:', data);
            
            // 處理不同類型的訊息
            switch (data.type) {
                case 'metrics_update':
                    this.handleMetricsUpdate(data.payload);
                    break;
                case 'alert':
                    this.handleAlert(data.payload);
                    break;
                case 'system_status':
                    this.handleSystemStatus(data.payload);
                    break;
                case 'task_details':
                    this.handleTaskDetails(data.payload);
                    break;
                case 'trend_data':
                    this.handleTrendData(data.payload);
                    break;
                case 'heartbeat':
                    this.handleHeartbeat(data.payload);
                    break;
                case 'connection_ack':
                    this.handleConnectionAck(data.payload);
                    break;
                case 'connection_info':
                    this.handleConnectionInfo(data.payload);
                    break;
                case 'error':
                    this.handleServerError(data.payload);
                    break;
                default:
                    console.warn('未知的訊息類型:', data.type);
            }
            
            // 觸發通用訊息事件
            if (this.onMessage) {
                this.onMessage(data);
            }
            
            // 觸發特定訊息處理器
            if (this.messageHandlers.has(data.type)) {
                this.messageHandlers.get(data.type)(data.payload);
            }
            
        } catch (error) {
            console.error('處理 WebSocket 訊息失敗:', error);
        }
    }
    
    handleClose(event) {
        console.log('WebSocket 連接已關閉:', event.code, event.reason);
        this.isConnected = false;
        
        // 停止心跳
        this.stopHeartbeat();
        
        // 更新連接狀態指示器
        this.updateConnectionStatus('disconnected');
        
        // 觸發斷線事件
        if (this.onDisconnect) {
            this.onDisconnect(event);
        }
        
        // 如果不是正常關閉，嘗試重連
        if (event.code !== 1000) {
            this.scheduleReconnect();
        }
    }
    
    handleError(event) {
        console.error('WebSocket 錯誤:', event);
        
        // 更新連接狀態指示器
        this.updateConnectionStatus('error');
        
        // 觸發錯誤事件
        if (this.onError) {
            this.onError(event);
        }
    }
    
    scheduleReconnect() {
        if (this.reconnectAttempts >= this.maxReconnectAttempts) {
            console.error('達到最大重連次數，停止重連');
            this.updateConnectionStatus('failed');
            return;
        }
        
        this.reconnectAttempts++;
        const delay = this.reconnectDelay * Math.pow(2, this.reconnectAttempts - 1); // 指數退避
        
        console.log(`${delay}ms 後嘗試第 ${this.reconnectAttempts} 次重連`);
        
        setTimeout(() => {
            if (!this.isConnected) {
                this.connect();
            }
        }, delay);
    }
    
    startHeartbeat() {
        this.heartbeatInterval = setInterval(() => {
            if (this.isConnected) {
                this.send({
                    type: 'heartbeat',
                    timestamp: new Date().toISOString()
                });
            }
        }, 30000); // 30秒心跳
    }
    
    stopHeartbeat() {
        if (this.heartbeatInterval) {
            clearInterval(this.heartbeatInterval);
            this.heartbeatInterval = null;
        }
    }
    
    send(data) {
        if (this.isConnected && this.ws.readyState === WebSocket.OPEN) {
            try {
                this.ws.send(JSON.stringify(data));
                return true;
            } catch (error) {
                console.error('發送 WebSocket 訊息失敗:', error);
                return false;
            }
        } else {
            console.warn('WebSocket 未連接，無法發送訊息');
            return false;
        }
    }
    
    subscribe(subscriptionType) {
        this.subscriptions.add(subscriptionType);
        
        if (this.isConnected) {
            this.send({
                type: 'subscribe',
                payload: {
                    types: [subscriptionType]
                }
            });
        }
    }
    
    unsubscribe(subscriptionType) {
        this.subscriptions.delete(subscriptionType);
        
        if (this.isConnected) {
            this.send({
                type: 'unsubscribe',
                payload: {
                    types: [subscriptionType]
                }
            });
        }
    }
    
    subscribeToAll() {
        // 訂閱所有監控資料類型
        const subscriptions = [
            'metrics_update',
            'alert',
            'system_status',
            'email_metrics',
            'dramatiq_metrics',
            'system_metrics',
            'file_metrics',
            'business_metrics'
        ];
        
        subscriptions.forEach(sub => this.subscribe(sub));
    }
    
    addMessageHandler(messageType, handler) {
        this.messageHandlers.set(messageType, handler);
    }
    
    removeMessageHandler(messageType) {
        this.messageHandlers.delete(messageType);
    }
    
    updateConnectionStatus(status) {
        const indicator = document.getElementById('connectionIndicator');
        const statusText = document.querySelector('.connection-text');
        
        if (indicator) {
            indicator.className = 'connection-indicator';
            
            switch (status) {
                case 'connected':
                    indicator.classList.add('connected');
                    if (statusText) statusText.textContent = 'WebSocket 已連接';
                    break;
                case 'disconnected':
                    indicator.classList.add('disconnected');
                    if (statusText) statusText.textContent = 'WebSocket 已斷線';
                    break;
                case 'error':
                    indicator.classList.add('error');
                    if (statusText) statusText.textContent = 'WebSocket 錯誤';
                    break;
                case 'failed':
                    indicator.classList.add('failed');
                    if (statusText) statusText.textContent = 'WebSocket 連接失敗';
                    break;
                default:
                    if (statusText) statusText.textContent = 'WebSocket';
            }
        }
    }
    
    handleMetricsUpdate(metrics) {
        // 更新最後更新時間
        const lastUpdateElement = document.getElementById('lastUpdate');
        if (lastUpdateElement) {
            const now = new Date();
            lastUpdateElement.textContent = `最後更新: ${now.toLocaleTimeString()}`;
        }
        
        // 觸發指標更新事件
        window.dispatchEvent(new CustomEvent('metricsUpdate', {
            detail: metrics
        }));
    }
    
    handleAlert(alert) {
        // 顯示告警橫幅
        this.showAlertBanner(alert);
        
        // 觸發告警事件
        window.dispatchEvent(new CustomEvent('newAlert', {
            detail: alert
        }));
    }
    
    handleSystemStatus(status) {
        // 更新系統狀態指示器
        const statusIndicator = document.getElementById('statusIndicator');
        const statusText = document.getElementById('statusText');
        
        if (statusIndicator && statusText) {
            statusIndicator.className = 'status-indicator';
            
            switch (status.level) {
                case 'healthy':
                    statusIndicator.classList.add('healthy');
                    statusText.textContent = '系統正常';
                    break;
                case 'warning':
                    statusIndicator.classList.add('warning');
                    statusText.textContent = '系統警告';
                    break;
                case 'error':
                    statusIndicator.classList.add('error');
                    statusText.textContent = '系統錯誤';
                    break;
                default:
                    statusText.textContent = '系統狀態未知';
            }
        }
    }
    
    handleTaskDetails(taskDetails) {
        // 觸發任務詳情事件
        window.dispatchEvent(new CustomEvent('taskDetails', {
            detail: taskDetails
        }));
    }
    
    handleTrendData(trendData) {
        // 觸發趨勢資料事件
        window.dispatchEvent(new CustomEvent('trendData', {
            detail: trendData
        }));
    }
    
    handleConnectionAck(data) {
        console.log('收到連接確認:', data);
        // 可以在這裡處理連接確認邏輯
    }
    
    handleConnectionInfo(data) {
        console.log('收到連接資訊:', data);
        // 處理連接資訊，例如訂閱確認等
        if (data.action === 'subscribed') {
            console.log('訂閱成功:', data.subscriptions);
        } else if (data.action === 'unsubscribed') {
            console.log('取消訂閱成功:', data.unsubscribed);
        }
    }
    
    handleServerError(error) {
        console.error('伺服器錯誤:', error);
        // 顯示伺服器錯誤通知
        this.showErrorNotification(error);
    }
    
    handleHeartbeat(data) {
        // 處理心跳回應
        console.debug('收到心跳回應:', data);
    }
    
    showErrorNotification(error) {
        const alertBanner = document.getElementById('alertBanner');
        const alertContent = document.getElementById('alertContent');
        
        if (alertBanner && alertContent) {
            alertContent.innerHTML = `
                <strong>伺服器錯誤</strong>
                <span>${error.message || '未知錯誤'}</span>
            `;
            
            alertBanner.className = 'alert-banner error';
            alertBanner.style.display = 'block';
            
            // 10秒後自動隱藏
            setTimeout(() => {
                alertBanner.style.display = 'none';
            }, 10000);
        }
    }
    
    showAlertBanner(alert) {
        const alertBanner = document.getElementById('alertBanner');
        const alertContent = document.getElementById('alertContent');
        
        if (alertBanner && alertContent) {
            alertContent.innerHTML = `
                <strong>${alert.title}</strong>
                <span>${alert.message}</span>
            `;
            
            alertBanner.className = `alert-banner ${alert.level}`;
            alertBanner.style.display = 'block';
            
            // 5秒後自動隱藏（除非是嚴重告警）
            if (alert.level !== 'critical') {
                setTimeout(() => {
                    alertBanner.style.display = 'none';
                }, 5000);
            }
        }
    }
    
    setupEventHandlers() {
        // 頁面關閉時關閉 WebSocket 連接
        window.addEventListener('beforeunload', () => {
            if (this.ws) {
                this.ws.close(1000, 'Page unload');
            }
        });
        
        // 頁面可見性變化時處理連接
        document.addEventListener('visibilitychange', () => {
            if (document.hidden) {
                // 頁面隱藏時可以選擇暫停某些訂閱
                console.log('頁面隱藏，保持 WebSocket 連接');
            } else {
                // 頁面顯示時確保連接正常
                if (!this.isConnected) {
                    console.log('頁面顯示，嘗試重新連接 WebSocket');
                    this.connect();
                }
            }
        });
        
        // 告警橫幅關閉按鈕
        const alertClose = document.getElementById('alertClose');
        if (alertClose) {
            alertClose.addEventListener('click', () => {
                const alertBanner = document.getElementById('alertBanner');
                if (alertBanner) {
                    alertBanner.style.display = 'none';
                }
            });
        }
    }
    
    close() {
        this.stopHeartbeat();
        if (this.ws) {
            this.ws.close(1000, 'Manual close');
        }
    }
    
    getConnectionInfo() {
        return {
            clientId: this.clientId,
            isConnected: this.isConnected,
            reconnectAttempts: this.reconnectAttempts,
            subscriptions: Array.from(this.subscriptions)
        };
    }
}

// 全域 WebSocket 實例
let dashboardWebSocket = null;

// 初始化 WebSocket 連接
function initDashboardWebSocket() {
    if (!dashboardWebSocket) {
        dashboardWebSocket = new DashboardWebSocket();
        
        // 設置事件處理器
        dashboardWebSocket.onConnect = () => {
            console.log('儀表板 WebSocket 已連接');
            hideLoadingOverlay();
        };
        
        dashboardWebSocket.onDisconnect = () => {
            console.log('儀表板 WebSocket 已斷線');
            showLoadingOverlay('連接已斷線，嘗試重新連接...');
        };
        
        dashboardWebSocket.onError = (error) => {
            console.error('儀表板 WebSocket 錯誤:', error);
        };
    }
    
    return dashboardWebSocket;
}

// 獲取 WebSocket 實例
function getDashboardWebSocket() {
    return dashboardWebSocket || initDashboardWebSocket();
}

// 顯示載入遮罩
function showLoadingOverlay(message = '載入監控資料中...') {
    const overlay = document.getElementById('loadingOverlay');
    const text = overlay?.querySelector('.loading-text');
    
    if (overlay) {
        if (text) text.textContent = message;
        overlay.classList.add('show');
    }
}

// 隱藏載入遮罩
function hideLoadingOverlay() {
    const overlay = document.getElementById('loadingOverlay');
    if (overlay) {
        overlay.classList.remove('show');
    }
}

// 導出到全域作用域
window.DashboardWebSocket = DashboardWebSocket;
window.initDashboardWebSocket = initDashboardWebSocket;
window.getDashboardWebSocket = getDashboardWebSocket;