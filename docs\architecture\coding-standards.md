# 編碼標準 (Coding Standards)

**專案**: Outlook Summary System - 半導體郵件處理系統  
**版本**: 5.1 (Vue 3 前端遷移準備版本)  
**最後更新**: 2025-08-11

## 📋 目錄

- [Python 編碼標準](#python-編碼標準)
- [JavaScript 編碼標準](#javascript-編碼標準)
- [Vue 3 + Composition API 標準](#vue-3--composition-api-標準)
- [HTML/CSS 標準](#htmlcss-標準)
- [Flask/FastAPI 架構標準](#flaskfastapi-架構標準)
- [測試標準](#測試標準)
- [Git 提交標準](#git-提交標準)
- [安全編碼標準](#安全編碼標準)

---

## 🐍 Python 編碼標準

### 基本規範 (PEP 8)

```python
# ✅ 正確示例
class EmailProcessor:
    """郵件處理器類別"""
    
    def __init__(self, config: dict) -> None:
        self.config = config
        self._processed_count = 0
    
    def process_email(self, email_data: dict) -> bool:
        """處理單封郵件
        
        Args:
            email_data: 郵件數據字典
            
        Returns:
            bool: 處理成功返回True
            
        Raises:
            EmailProcessingError: 處理失敗時拋出
        """
        try:
            # 處理邏輯
            return True
        except Exception as e:
            logger.error(f"郵件處理失敗: {e}")
            raise EmailProcessingError(str(e))

# ❌ 錯誤示例
class emailprocessor:
    def __init__(self,config):
        self.config=config
    
    def processEmail(self,emailData):
        # 處理郵件
        pass
```

### 命名規範

| 類型 | 規範 | 範例 |
|------|------|------|
| 類別 | PascalCase | `EmailProcessor`, `DataValidator` |
| 函數/方法 | snake_case | `process_email()`, `validate_data()` |
| 變數 | snake_case | `email_count`, `is_valid` |
| 常數 | UPPER_SNAKE_CASE | `MAX_RETRY_COUNT`, `API_TIMEOUT` |
| 私有屬性 | _snake_case | `_processed_count`, `_config` |
| 模組 | snake_case | `email_processor.py`, `data_validator.py` |

### 類型註解

```python
from typing import List, Dict, Optional, Union
from dataclasses import dataclass

@dataclass
class EmailMessage:
    subject: str
    sender: str
    recipients: List[str]
    content: str
    attachments: Optional[List[str]] = None

def filter_emails(
    emails: List[EmailMessage], 
    criteria: Dict[str, Union[str, int]]
) -> List[EmailMessage]:
    """過濾郵件列表"""
    return [email for email in emails if meets_criteria(email, criteria)]
```

### 異常處理

```python
class EmailProcessingError(Exception):
    """郵件處理自定義異常"""
    pass

def process_email_batch(emails: List[dict]) -> Dict[str, int]:
    """批量處理郵件"""
    results = {"success": 0, "failed": 0}
    
    for email in emails:
        try:
            process_single_email(email)
            results["success"] += 1
        except EmailProcessingError as e:
            logger.warning(f"處理郵件失敗 {email.get('id', 'unknown')}: {e}")
            results["failed"] += 1
        except Exception as e:
            logger.error(f"未預期錯誤: {e}")
            results["failed"] += 1
    
    return results
```

---

## 🌐 JavaScript 編碼標準

### ES6+ 語法

```javascript
// ✅ 正確示例 - 使用 ES6 類別和模組
class EmailManager {
    constructor(config = {}) {
        this.config = { timeout: 5000, ...config };
        this.emailList = [];
    }
    
    async fetchEmails() {
        try {
            const response = await fetch('/api/email/list');
            const data = await response.json();
            
            if (!response.ok) {
                throw new Error(data.message || '獲取郵件失敗');
            }
            
            this.emailList = data.emails || [];
            return this.emailList;
        } catch (error) {
            console.error('郵件獲取錯誤:', error);
            this.showErrorMessage(error.message);
            throw error;
        }
    }
    
    showErrorMessage(message) {
        const notification = document.createElement('div');
        notification.className = 'alert alert-danger';
        notification.textContent = message;
        document.body.appendChild(notification);
        
        setTimeout(() => notification.remove(), 5000);
    }
}

// 匯出模組
export default EmailManager;
export { EmailManager };

// ❌ 錯誤示例
function EmailManager(config) {
    var self = this;
    this.config = config;
    this.fetchEmails = function() {
        $.get('/api/email/list', function(data) {
            // 處理回應
        }).fail(function() {
            alert('錯誤');
        });
    };
}
```

### 命名規範

| 類型 | 規範 | 範例 |
|------|------|------|
| 類別 | PascalCase | `EmailManager`, `DataValidator` |
| 函數/方法 | camelCase | `fetchEmails()`, `validateForm()` |
| 變數 | camelCase | `emailCount`, `isValid` |
| 常數 | UPPER_SNAKE_CASE | `API_TIMEOUT`, `MAX_RETRIES` |
| 檔案 | kebab-case | `email-manager.js`, `data-validator.js` |

### 錯誤處理

```javascript
class ApiClient {
    static async request(url, options = {}) {
        const defaultOptions = {
            method: 'GET',
            headers: {
                'Content-Type': 'application/json',
                'X-Requested-With': 'XMLHttpRequest'
            }
        };
        
        try {
            const response = await fetch(url, { ...defaultOptions, ...options });
            const data = await response.json();
            
            if (!response.ok) {
                throw new ApiError(data.message, response.status, data);
            }
            
            return data;
        } catch (error) {
            if (error instanceof ApiError) {
                throw error;
            }
            throw new ApiError('網路錯誤', 0, { originalError: error });
        }
    }
}

class ApiError extends Error {
    constructor(message, status = 0, details = {}) {
        super(message);
        this.name = 'ApiError';
        this.status = status;
        this.details = details;
    }
}
```

---

## 🎨 Vue 3 + Composition API 標準

### 組件結構規範

```vue
<!-- ✅ 正確示例 - 使用 <script setup> 語法 -->
<template>
  <div class="email-list-container">
    <header class="email-list-header">
      <h1>收件匣 ({{ emailCount }})</h1>
      <button 
        class="btn btn-primary" 
        @click="refreshEmails"
        :disabled="isLoading"
      >
        {{ isLoading ? '載入中...' : '重新整理' }}
      </button>
    </header>
    
    <main class="email-list-main">
      <EmailItem
        v-for="email in filteredEmails"
        :key="email.id"
        :email="email"
        @click="selectEmail"
        @delete="deleteEmail"
      />
    </main>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import EmailItem from '@/components/EmailItem.vue'
import { useEmailStore } from '@/stores/emailStore'
import { useNotification } from '@/composables/useNotification'

// Props 定義
interface Props {
  initialFilter?: string
  showUnreadOnly?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  initialFilter: '',
  showUnreadOnly: false
})

// Emits 定義
interface Emits {
  emailSelected: [email: Email]
  emailDeleted: [emailId: string]
}

const emit = defineEmits<Emits>()

// 狀態管理
const emailStore = useEmailStore()
const { showSuccess, showError } = useNotification()

// 響應式狀態
const isLoading = ref(false)
const searchQuery = ref(props.initialFilter)

// 計算屬性
const emailCount = computed(() => emailStore.emails.length)

const filteredEmails = computed(() => {
  let emails = emailStore.emails
  
  if (props.showUnreadOnly) {
    emails = emails.filter(email => !email.isRead)
  }
  
  if (searchQuery.value) {
    emails = emails.filter(email => 
      email.subject.toLowerCase().includes(searchQuery.value.toLowerCase())
    )
  }
  
  return emails
})

// 方法
async function refreshEmails() {
  isLoading.value = true
  
  try {
    await emailStore.fetchEmails()
    showSuccess('郵件列表已更新')
  } catch (error) {
    console.error('獲取郵件失敗:', error)
    showError('無法載入郵件列表')
  } finally {
    isLoading.value = false
  }
}

function selectEmail(email: Email) {
  emit('emailSelected', email)
}

async function deleteEmail(emailId: string) {
  try {
    await emailStore.deleteEmail(emailId)
    emit('emailDeleted', emailId)
    showSuccess('郵件已刪除')
  } catch (error) {
    console.error('刪除郵件失敗:', error)
    showError('無法刪除郵件')
  }
}

// 生命周期
onMounted(() => {
  if (emailStore.emails.length === 0) {
    refreshEmails()
  }
})
</script>

<style scoped>
.email-list-container {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.email-list-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-md);
  border-bottom: 1px solid var(--border-color);
}

.email-list-main {
  flex: 1;
  overflow-y: auto;
  padding: var(--spacing-sm);
}
</style>

<!-- ❌ 錯誤示例 - Options API (不推薦) -->
<script>
export default {
  data() {
    return {
      emails: [],
      isLoading: false
    }
  },
  methods: {
    async fetchEmails() {
      // 舊式寫法
    }
  }
}
</script>
```

### 組合式函數 (Composables)

```typescript
// composables/useEmailOperations.ts
import { ref } from 'vue'
import type { Email } from '@/types/email'
import { emailAPI } from '@/api/emailAPI'

export function useEmailOperations() {
  const isOperating = ref(false)
  const selectedEmails = ref<Set<string>>(new Set())
  
  async function markAsRead(emailIds: string[]) {
    isOperating.value = true
    
    try {
      await emailAPI.markAsRead(emailIds)
      return { success: true }
    } catch (error) {
      console.error('標記已讀失敗:', error)
      return { success: false, error }
    } finally {
      isOperating.value = false
    }
  }
  
  async function deleteEmails(emailIds: string[]) {
    isOperating.value = true
    
    try {
      await emailAPI.deleteEmails(emailIds)
      // 清除已刪除的選擇項目
      emailIds.forEach(id => selectedEmails.value.delete(id))
      return { success: true }
    } catch (error) {
      console.error('刪除郵件失敗:', error)
      return { success: false, error }
    } finally {
      isOperating.value = false
    }
  }
  
  function toggleEmailSelection(emailId: string) {
    if (selectedEmails.value.has(emailId)) {
      selectedEmails.value.delete(emailId)
    } else {
      selectedEmails.value.add(emailId)
    }
  }
  
  function selectAllEmails(emails: Email[]) {
    emails.forEach(email => selectedEmails.value.add(email.id))
  }
  
  function clearSelection() {
    selectedEmails.value.clear()
  }
  
  return {
    isOperating: readonly(isOperating),
    selectedEmails: readonly(selectedEmails),
    markAsRead,
    deleteEmails,
    toggleEmailSelection,
    selectAllEmails,
    clearSelection
  }
}
```

### Pinia 狀態管理

```typescript
// stores/emailStore.ts
import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import type { Email, EmailFilter } from '@/types/email'
import { emailAPI } from '@/api/emailAPI'

export const useEmailStore = defineStore('email', () => {
  // 狀態
  const emails = ref<Email[]>([])
  const currentFilter = ref<EmailFilter>({
    vendor: '',
    dateRange: null,
    isRead: null
  })
  const isLoading = ref(false)
  const error = ref<string | null>(null)
  
  // Getters (計算屬性)
  const unreadCount = computed(() => 
    emails.value.filter(email => !email.isRead).length
  )
  
  const filteredEmails = computed(() => {
    let filtered = emails.value
    
    if (currentFilter.value.vendor) {
      filtered = filtered.filter(email => 
        email.vendor === currentFilter.value.vendor
      )
    }
    
    if (currentFilter.value.isRead !== null) {
      filtered = filtered.filter(email => 
        email.isRead === currentFilter.value.isRead
      )
    }
    
    return filtered
  })
  
  // Actions
  async function fetchEmails() {
    isLoading.value = true
    error.value = null
    
    try {
      const response = await emailAPI.getEmails()
      emails.value = response.data
    } catch (err) {
      error.value = err instanceof Error ? err.message : '獲取郵件失敗'
      throw err
    } finally {
      isLoading.value = false
    }
  }
  
  async function deleteEmail(emailId: string) {
    try {
      await emailAPI.deleteEmail(emailId)
      const index = emails.value.findIndex(email => email.id === emailId)
      if (index !== -1) {
        emails.value.splice(index, 1)
      }
    } catch (err) {
      error.value = err instanceof Error ? err.message : '刪除郵件失敗'
      throw err
    }
  }
  
  function setFilter(filter: Partial<EmailFilter>) {
    currentFilter.value = { ...currentFilter.value, ...filter }
  }
  
  function clearError() {
    error.value = null
  }
  
  return {
    // 狀態
    emails: readonly(emails),
    currentFilter: readonly(currentFilter),
    isLoading: readonly(isLoading),
    error: readonly(error),
    
    // Getters
    unreadCount,
    filteredEmails,
    
    // Actions
    fetchEmails,
    deleteEmail,
    setFilter,
    clearError
  }
})
```

### TypeScript 類型定義

```typescript
// types/email.ts
export interface Email {
  id: string
  messageId: string
  subject: string
  sender: string
  recipient: string
  content: string
  isRead: boolean
  receivedAt: Date
  attachments: EmailAttachment[]
  vendor: VendorType
  pd?: string
  lot?: string
  mo?: string
  yieldValue?: string
}

export interface EmailAttachment {
  id: string
  filename: string
  size: number
  contentType: string
  downloadUrl: string
}

export enum VendorType {
  ETD = 'etd',
  GTK = 'gtk',
  JCET = 'jcet',
  LINGSEN = 'lingsen',
  XAHT = 'xaht'
}

export interface EmailFilter {
  vendor: VendorType | ''
  dateRange: [Date, Date] | null
  isRead: boolean | null
}

export interface ApiResponse<T> {
  success: boolean
  data: T
  message: string
  error?: string
}
```

### Vue Router 設定

```typescript
// router/index.ts
import { createRouter, createWebHistory } from 'vue-router'
import type { RouteRecordRaw } from 'vue-router'

const routes: RouteRecordRaw[] = [
  {
    path: '/',
    redirect: '/email/inbox'
  },
  {
    path: '/email',
    component: () => import('@/layouts/EmailLayout.vue'),
    children: [
      {
        path: 'inbox',
        name: 'EmailInbox',
        component: () => import('@/views/EmailInboxView.vue'),
        meta: { title: '收件匣' }
      },
      {
        path: 'detail/:id',
        name: 'EmailDetail',
        component: () => import('@/views/EmailDetailView.vue'),
        props: true,
        meta: { title: '郵件詳情' }
      }
    ]
  },
  {
    path: '/analytics',
    component: () => import('@/layouts/AnalyticsLayout.vue'),
    children: [
      {
        path: 'dashboard',
        name: 'AnalyticsDashboard',
        component: () => import('@/views/AnalyticsDashboardView.vue'),
        meta: { title: '分析儀表板' }
      }
    ]
  }
]

const router = createRouter({
  history: createWebHistory(),
  routes,
  scrollBehavior(to, from, savedPosition) {
    if (savedPosition) {
      return savedPosition
    }
    return { top: 0 }
  }
})

// 路由守衛
router.beforeEach((to, from, next) => {
  // 設定頁面標題
  if (to.meta.title) {
    document.title = `${to.meta.title} - 半導體郵件處理系統`
  }
  
  next()
})

export default router
```

### 組件命名規範

| 類型 | 格式 | 範例 |
|------|------|------|
| 組件檔案 | PascalCase.vue | EmailList.vue, DataTable.vue |
| 組合式函數 | useXxx.ts | useEmailOperations.ts, useNotification.ts |
| Store | xxxStore.ts | emailStore.ts, userStore.ts |
| 類型定義 | xxx.ts | email.ts, api.ts |
| 工具函數 | xxxUtils.ts | dateUtils.ts, validationUtils.ts |

---

## 🎨 HTML/CSS 標準

### 語意化 HTML

```html
<!-- ✅ 正確示例 -->
<main class="email-container">
    <header class="email-header">
        <h1 class="email-title">收件匣</h1>
        <nav class="email-nav">
            <button class="btn btn-primary" data-action="refresh">重新整理</button>
        </nav>
    </header>
    
    <section class="email-list" role="list">
        <article class="email-item" role="listitem" data-email-id="123">
            <h2 class="email-subject">郵件主旨</h2>
            <p class="email-sender">寄件者: <EMAIL></p>
            <time class="email-date" datetime="2025-08-11T14:30:00Z">
                2025/08/11 14:30
            </time>
        </article>
    </section>
</main>

<!-- ❌ 錯誤示例 -->
<div class="container">
    <div class="title">收件匣</div>
    <div class="list">
        <div class="item">
            <div>郵件主旨</div>
            <div>寄件者</div>
        </div>
    </div>
</div>
```

### CSS 組織

```css
/* ✅ 正確示例 - 模組化 CSS */

/* 基礎變數 */
:root {
    --primary-color: #007bff;
    --danger-color: #dc3545;
    --border-radius: 0.375rem;
    --spacing-sm: 0.5rem;
    --spacing-md: 1rem;
}

/* 組件樣式 */
.email-item {
    padding: var(--spacing-md);
    border: 1px solid #dee2e6;
    border-radius: var(--border-radius);
    margin-bottom: var(--spacing-sm);
    transition: box-shadow 0.15s ease-in-out;
}

.email-item:hover {
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.email-item--unread {
    background-color: #f8f9fa;
    font-weight: 600;
}

/* 響應式設計 */
@media (max-width: 768px) {
    .email-item {
        padding: var(--spacing-sm);
    }
}
```

---

## 🌶️ Flask/FastAPI 架構標準

### Flask 藍圖組織

```python
# frontend/email/routes/email_routes.py
from flask import Blueprint, render_template, request, jsonify
from frontend.shared.utils.error_handler import handle_api_error
from frontend.shared.utils.api_response import ApiResponse

email_bp = Blueprint(
    'email', 
    __name__, 
    template_folder='../templates',
    static_folder='../static',
    static_url_path='/static/email'
)

@email_bp.route('/')
@email_bp.route('/inbox')
def inbox():
    """郵件收件匣頁面"""
    try:
        return render_template('email/inbox.html')
    except Exception as e:
        return handle_api_error(e)

@email_bp.route('/api/list')
def api_email_list():
    """獲取郵件列表API"""
    try:
        page = request.args.get('page', 1, type=int)
        size = request.args.get('size', 20, type=int)
        
        # 業務邏輯
        emails = get_email_list(page, size)
        total = get_email_count()
        
        return jsonify(ApiResponse.paginated(emails, page, size, total))
    except Exception as e:
        return jsonify(ApiResponse.error(str(e))), 500
```

### FastAPI 服務組織

```python
# backend/email/api/routes.py
from fastapi import APIRouter, HTTPException, Depends
from pydantic import BaseModel
from typing import List, Optional

router = APIRouter(prefix="/email", tags=["email"])

class EmailResponse(BaseModel):
    id: str
    subject: str
    sender: str
    is_read: bool

@router.get("/list", response_model=List[EmailResponse])
async def get_emails(
    page: int = 1,
    size: int = 20,
    email_service: EmailService = Depends(get_email_service)
):
    """獲取郵件列表"""
    try:
        emails = await email_service.get_emails(page, size)
        return [EmailResponse(**email.dict()) for email in emails]
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))
```

---

## 🧪 測試標準

### Pytest 測試結構

```python
# tests/test_email_service.py
import pytest
from unittest.mock import Mock, patch
from frontend.email.routes.email_routes import email_bp
from frontend.shared.utils.error_handler import handle_api_error

class TestEmailService:
    """郵件服務測試類別"""
    
    @pytest.fixture
    def email_service(self):
        """測試用的郵件服務實例"""
        return EmailService()
    
    @pytest.fixture
    def sample_email_data(self):
        """範例郵件數據"""
        return {
            "subject": "測試郵件",
            "sender": "<EMAIL>",
            "content": "測試內容"
        }
    
    def test_create_email_success(self, email_service, sample_email_data):
        """測試創建郵件成功"""
        # Arrange
        expected_email = Email(**sample_email_data)
        
        # Act
        result = email_service.create_email(sample_email_data)
        
        # Assert
        assert result.subject == expected_email.subject
        assert result.sender == expected_email.sender
        assert result.content == expected_email.content
    
    def test_create_email_missing_subject_fails(self, email_service):
        """測試缺少主旨時創建郵件失敗"""
        # Arrange
        invalid_data = {"sender": "<EMAIL>"}
        
        # Act & Assert
        with pytest.raises(ValueError, match="郵件主旨不能為空"):
            email_service.create_email(invalid_data)
    
    @patch('backend.email.services.email_service.database')
    def test_save_email_database_error(self, mock_db, email_service, sample_email_data):
        """測試數據庫錯誤處理"""
        # Arrange
        mock_db.save.side_effect = Exception("數據庫錯誤")
        
        # Act & Assert
        with pytest.raises(Exception, match="數據庫錯誤"):
            email_service.create_email(sample_email_data)
```

### 測試覆蓋率要求

```bash
# Python 測試 - 最低覆蓋率: 80%
pytest --cov=frontend --cov=src --cov-report=html --cov-report=term

# Vue 3 測試 - 最低覆蓋率: 85%
npm run test:coverage
```

### Vue 3 測試範例

```typescript
// tests/components/EmailList.test.ts
import { describe, it, expect, beforeEach, vi } from 'vitest'
import { mount } from '@vue/test-utils'
import { createPinia, setActivePinia } from 'pinia'
import EmailList from '@/components/EmailList.vue'
import { useEmailStore } from '@/stores/emailStore'

describe('EmailList.vue', () => {
  beforeEach(() => {
    setActivePinia(createPinia())
  })

  it('renders email list correctly', async () => {
    const wrapper = mount(EmailList, {
      props: {
        initialFilter: '',
        showUnreadOnly: false
      }
    })

    expect(wrapper.find('.email-list-container').exists()).toBe(true)
    expect(wrapper.find('.email-list-header h1').text()).toContain('收件匣')
  })

  it('handles email refresh correctly', async () => {
    const emailStore = useEmailStore()
    const fetchSpy = vi.spyOn(emailStore, 'fetchEmails').mockResolvedValue()

    const wrapper = mount(EmailList)
    const refreshButton = wrapper.find('button')
    
    await refreshButton.trigger('click')
    
    expect(fetchSpy).toHaveBeenCalled()
  })

  it('filters emails based on search query', async () => {
    const emailStore = useEmailStore()
    emailStore.emails = [
      { id: '1', subject: '測試郵件 1', isRead: false },
      { id: '2', subject: '重要通知', isRead: true }
    ]

    const wrapper = mount(EmailList)
    const searchInput = wrapper.find('input[type="search"]')
    
    await searchInput.setValue('測試')
    
    expect(wrapper.vm.filteredEmails).toHaveLength(1)
    expect(wrapper.vm.filteredEmails[0].subject).toBe('測試郵件 1')
  })

  it('emits emailSelected event when email is clicked', async () => {
    const wrapper = mount(EmailList)
    
    await wrapper.vm.selectEmail({ id: '1', subject: '測試' })
    
    expect(wrapper.emitted('emailSelected')).toBeTruthy()
    expect(wrapper.emitted('emailSelected')[0]).toEqual([{ id: '1', subject: '測試' }])
  })
})
```

### Composable 測試

```typescript
// tests/composables/useEmailOperations.test.ts
import { describe, it, expect, vi } from 'vitest'
import { useEmailOperations } from '@/composables/useEmailOperations'
import { emailAPI } from '@/api/emailAPI'

vi.mock('@/api/emailAPI')

describe('useEmailOperations', () => {
  it('marks emails as read successfully', async () => {
    const mockMarkAsRead = vi.mocked(emailAPI.markAsRead)
    mockMarkAsRead.mockResolvedValue()

    const { markAsRead } = useEmailOperations()
    const result = await markAsRead(['1', '2'])

    expect(result.success).toBe(true)
    expect(mockMarkAsRead).toHaveBeenCalledWith(['1', '2'])
  })

  it('handles mark as read error', async () => {
    const mockMarkAsRead = vi.mocked(emailAPI.markAsRead)
    mockMarkAsRead.mockRejectedValue(new Error('API 錯誤'))

    const { markAsRead } = useEmailOperations()
    const result = await markAsRead(['1'])

    expect(result.success).toBe(false)
    expect(result.error).toBeInstanceOf(Error)
  })

  it('toggles email selection correctly', () => {
    const { toggleEmailSelection, selectedEmails } = useEmailOperations()

    toggleEmailSelection('email1')
    expect(selectedEmails.value.has('email1')).toBe(true)

    toggleEmailSelection('email1')
    expect(selectedEmails.value.has('email1')).toBe(false)
  })
})
```

---

## 📝 Git 提交標準

### 提交訊息格式

```bash
# 格式: type(scope): description
# 
# 詳細說明 (可選)
# 
# 相關問題: #123

# 範例:
feat(email): 新增郵件批量處理功能

新增批量處理郵件的API端點和前端界面：
- 支援多選郵件操作  
- 批量標記為已讀/未讀
- 批量刪除功能
- 整合 Dramatiq 異步處理

相關任務: task/3-migrate-files

# 類型說明:
# feat: 新功能
# fix: 錯誤修復
# docs: 文檔更新
# style: 代碼格式調整
# refactor: 重構
# test: 測試相關
# chore: 工具/配置變更
```

### 分支命名

```bash
# 功能分支
feature/email-batch-processing
feature/vue-migration

# 錯誤修復分支
fix/email-parsing-error
hotfix/security-vulnerability

# 任務分支
task/1-create-structure
task/2-refactor-app
```

---

## 🔒 安全編碼標準

### 輸入驗證

```python
from flask import request
from pydantic import BaseModel, validator
import bleach

class EmailInput(BaseModel):
    subject: str
    content: str
    
    @validator('subject')
    def validate_subject(cls, v):
        if not v or len(v.strip()) == 0:
            raise ValueError('郵件主旨不能為空')
        if len(v) > 200:
            raise ValueError('郵件主旨過長')
        return bleach.clean(v.strip())
    
    @validator('content')
    def validate_content(cls, v):
        # XSS 防護
        return bleach.clean(v, tags=['p', 'br', 'strong', 'em'])

@email_bp.route('/api/create', methods=['POST'])
def create_email():
    try:
        # 驗證輸入
        email_data = EmailInput(**request.json)
        
        # 處理邏輯
        result = email_service.create_email(email_data.dict())
        return jsonify(ApiResponse.success(result))
    except ValidationError as e:
        return jsonify(ApiResponse.error("輸入驗證失敗", details=e.errors())), 400
```

### SQL 注入防護

```python
# ✅ 正確示例 - 使用 SQLAlchemy ORM
def get_emails_by_sender(sender_email: str):
    return session.query(Email).filter(Email.sender == sender_email).all()

# ✅ 正確示例 - 使用參數化查詢
def get_emails_raw_sql(sender_email: str):
    query = "SELECT * FROM emails WHERE sender = %s"
    return session.execute(query, (sender_email,))

# ❌ 錯誤示例 - 直接字符串拼接
def get_emails_unsafe(sender_email: str):
    query = f"SELECT * FROM emails WHERE sender = '{sender_email}'"
    return session.execute(query)
```

---

## 📊 程式碼品質檢查

### 自動化檢查工具

```yaml
# .pre-commit-config.yaml
repos:
  - repo: https://github.com/psf/black
    rev: 22.3.0
    hooks:
      - id: black
        language_version: python3
  
  - repo: https://github.com/pycqa/flake8
    rev: 4.0.1
    hooks:
      - id: flake8
        args: [--max-line-length=88]
  
  - repo: https://github.com/pre-commit/mirrors-eslint
    rev: v8.15.0
    hooks:
      - id: eslint
        files: \.(js|ts|jsx|tsx)$
        types: [file]
```

### 品質指標

| 指標 | 目標值 | 工具 |
|------|--------|------|
| 測試覆蓋率 | ≥ 80% | pytest-cov |
| 程式碼重複率 | ≤ 3% | radon |
| 循環複雜度 | ≤ 10 | flake8-complexity |
| 型別覆蓋率 | ≥ 90% | mypy |

---

## 📚 最佳實踐總結

### 開發流程

1. **編碼前**: 閱讀相關模組的現有代碼，理解架構模式
2. **開發中**: 遵循命名規範，添加適當註解，處理異常
3. **測試**: 編寫單元測試，確保覆蓋率要求
4. **提交前**: 運行 linter 和測試，檢查無誤後提交
5. **程式碼審查**: 關注安全性、效能、可維護性

### 團隊協作

- **統一工具**: 使用相同的 IDE 配置和格式化工具
- **文檔更新**: 代碼變更時同步更新相關文檔
- **知識分享**: 定期分享最佳實踐和常見問題解決方案

---

**文檔維護**: 本編碼標準將隨團隊實踐不斷更新完善，請定期檢查最新版本。