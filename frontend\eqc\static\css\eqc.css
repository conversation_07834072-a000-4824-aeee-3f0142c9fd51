/* EQC Module CSS - 電子品質控制模組樣式 */

/* EQC 容器樣式 */
.eqc-container {
    padding: 20px;
    background: #f8f9fa;
    min-height: 100vh;
}

.eqc-header {
    background: white;
    padding: 20px;
    border-radius: 8px;
    margin-bottom: 20px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.eqc-title {
    font-size: 24px;
    font-weight: 600;
    color: #333;
    margin: 0;
}

.eqc-status {
    display: flex;
    align-items: center;
    gap: 10px;
}

.compliance-score {
    font-size: 18px;
    font-weight: 600;
    color: #28a745;
    background: #d4edda;
    padding: 8px 16px;
    border-radius: 20px;
}

.compliance-score.warning {
    color: #856404;
    background: #fff3cd;
}

.compliance-score.danger {
    color: #721c24;
    background: #f8d7da;
}

/* EQC 儀表板樣式 */
.eqc-dashboard {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 20px;
    margin-bottom: 20px;
}

.eqc-main-panel {
    background: white;
    border-radius: 8px;
    padding: 20px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.eqc-side-panel {
    background: white;
    border-radius: 8px;
    padding: 20px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    height: fit-content;
}

/* 品質檢查卡片 */
.quality-check-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 20px;
    margin-bottom: 20px;
}

.quality-check-card {
    background: white;
    border-radius: 8px;
    padding: 20px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    border-left: 4px solid #dee2e6;
    transition: all 0.2s ease;
}

.quality-check-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.quality-check-card.passed {
    border-left-color: #28a745;
}

.quality-check-card.failed {
    border-left-color: #dc3545;
}

.quality-check-card.warning {
    border-left-color: #ffc107;
}

.quality-check-card.pending {
    border-left-color: #17a2b8;
}

.check-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
}

.check-title {
    font-size: 16px;
    font-weight: 600;
    color: #333;
}

.check-status {
    font-size: 12px;
    padding: 4px 8px;
    border-radius: 12px;
    text-transform: uppercase;
    font-weight: 500;
}

.check-status.passed {
    background-color: #d4edda;
    color: #155724;
}

.check-status.failed {
    background-color: #f8d7da;
    color: #721c24;
}

.check-status.warning {
    background-color: #fff3cd;
    color: #856404;
}

.check-status.pending {
    background-color: #d1ecf1;
    color: #0c5460;
}

.check-description {
    font-size: 14px;
    color: #666;
    margin-bottom: 15px;
    line-height: 1.4;
}

.check-metrics {
    display: flex;
    justify-content: space-between;
    margin-bottom: 10px;
}

.check-metric {
    text-align: center;
}

.metric-value {
    font-size: 18px;
    font-weight: 600;
    color: #333;
}

.metric-label {
    font-size: 12px;
    color: #666;
}

/* EQC 歷史記錄樣式 */
.eqc-history {
    background: white;
    border-radius: 8px;
    padding: 20px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    margin-bottom: 20px;
}

.history-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 1px solid #e9ecef;
}

.history-title {
    font-size: 18px;
    font-weight: 600;
    color: #333;
}

.history-filters {
    display: flex;
    gap: 10px;
}

.history-timeline {
    position: relative;
    padding-left: 30px;
}

.history-timeline::before {
    content: '';
    position: absolute;
    left: 15px;
    top: 0;
    bottom: 0;
    width: 2px;
    background: #e9ecef;
}

.timeline-item {
    position: relative;
    margin-bottom: 20px;
    padding: 15px;
    background: #f8f9fa;
    border-radius: 8px;
}

.timeline-item::before {
    content: '';
    position: absolute;
    left: -22px;
    top: 20px;
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background: #6c757d;
}

.timeline-item.passed::before {
    background: #28a745;
}

.timeline-item.failed::before {
    background: #dc3545;
}

.timeline-item.warning::before {
    background: #ffc107;
}

.timeline-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
}

.timeline-title {
    font-size: 14px;
    font-weight: 600;
    color: #333;
}

.timeline-time {
    font-size: 12px;
    color: #666;
}

.timeline-description {
    font-size: 13px;
    color: #666;
    margin-bottom: 8px;
}

.timeline-details {
    font-size: 12px;
    color: #999;
}

/* 合規性報告樣式 */
.compliance-report {
    background: white;
    border-radius: 8px;
    padding: 20px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    margin-bottom: 20px;
}

.compliance-overview {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 15px;
    margin-bottom: 25px;
}

.compliance-stat {
    text-align: center;
    padding: 15px;
    background: #f8f9fa;
    border-radius: 8px;
}

.compliance-percentage {
    font-size: 24px;
    font-weight: 700;
    margin-bottom: 5px;
}

.compliance-percentage.excellent {
    color: #28a745;
}

.compliance-percentage.good {
    color: #17a2b8;
}

.compliance-percentage.fair {
    color: #ffc107;
}

.compliance-percentage.poor {
    color: #dc3545;
}

.compliance-category {
    font-size: 12px;
    color: #666;
    text-transform: uppercase;
    font-weight: 500;
}

/* 品質趨勢圖表 */
.quality-trends {
    background: white;
    border-radius: 8px;
    padding: 20px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    margin-bottom: 20px;
}

.trends-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
}

.trends-title {
    font-size: 18px;
    font-weight: 600;
    color: #333;
}

.trends-period {
    display: flex;
    gap: 5px;
}

.period-btn {
    padding: 6px 12px;
    border: 1px solid #ddd;
    background: white;
    border-radius: 4px;
    font-size: 12px;
    cursor: pointer;
    transition: all 0.2s;
}

.period-btn.active {
    background: #007bff;
    color: white;
    border-color: #007bff;
}

.chart-container {
    height: 300px;
    margin-bottom: 15px;
}

/* 告警和通知樣式 */
.eqc-alerts {
    background: white;
    border-radius: 8px;
    padding: 20px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.alert-item {
    display: flex;
    align-items: flex-start;
    padding: 15px;
    margin-bottom: 10px;
    border-radius: 8px;
    border-left: 4px solid #dee2e6;
}

.alert-item.critical {
    background-color: #f8d7da;
    border-left-color: #dc3545;
}

.alert-item.warning {
    background-color: #fff3cd;
    border-left-color: #ffc107;
}

.alert-item.info {
    background-color: #d1ecf1;
    border-left-color: #17a2b8;
}

.alert-icon {
    font-size: 20px;
    margin-right: 15px;
    margin-top: 2px;
}

.alert-content {
    flex: 1;
}

.alert-title {
    font-size: 14px;
    font-weight: 600;
    margin-bottom: 5px;
}

.alert-message {
    font-size: 13px;
    line-height: 1.4;
    margin-bottom: 8px;
}

.alert-actions {
    display: flex;
    gap: 10px;
    margin-top: 10px;
}

.alert-btn {
    padding: 4px 8px;
    border: none;
    border-radius: 4px;
    font-size: 12px;
    cursor: pointer;
    transition: background-color 0.2s;
}

.alert-btn.resolve {
    background-color: #28a745;
    color: white;
}

.alert-btn.dismiss {
    background-color: #6c757d;
    color: white;
}

/* 響應式設計 */
@media (max-width: 768px) {
    .eqc-dashboard {
        grid-template-columns: 1fr;
    }
    
    .quality-check-grid {
        grid-template-columns: 1fr;
    }
    
    .eqc-header {
        flex-direction: column;
        gap: 15px;
        text-align: center;
    }
    
    .compliance-overview {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .trends-header {
        flex-direction: column;
        gap: 15px;
    }
    
    .history-filters {
        flex-direction: column;
        gap: 10px;
    }
    
    .chart-container {
        height: 250px;
    }
    
    .eqc-container {
        padding: 10px;
    }
}