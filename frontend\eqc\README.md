# EQC Module - 設備品質控制功能模組

## 概述

EQC (Equipment Quality Control) 功能模組負責處理半導體設備品質控制相關功能，包括品質檢查、合規驗證和 EQC 數據分析。

**Vue.js 遷移準備**: 本模組已完成 Flask 藍圖重構，建立清晰的模組邊界和標準化 API 介面，與 FastAPI EQC 服務 (Port 8010) 完美整合，適合 Vue.js 的即時數據監控需求。

## 功能特性

### 核心功能
- **EQC 儀表板** - 顯示設備品質控制總覽和關鍵指標
- **品質檢查** - 執行自動化品質檢查流程
- **合規檢查** - 驗證數據是否符合行業標準和規範
- **數據分析** - 分析 EQC 相關的測試數據和趨勢

### 品質指標
- **良率 (Yield Rate)** - 產品良率統計和趨勢分析
- **缺陷率 (Defect Rate)** - 缺陷統計和分類
- **測試覆蓋率** - 測試項目覆蓋程度
- **設備效能** - 設備運行效能指標
- **合規狀態** - 法規合規性檢查結果

## 目錄結構

```
eqc/
├── templates/               # HTML 模板
│   ├── eqc_dashboard.html   # EQC 儀表板
│   ├── quality_check.html   # 品質檢查
│   └── compliance.html      # 合規檢查
├── static/                  # 靜態資源
│   ├── css/
│   │   └── eqc.css          # EQC 專用樣式
│   ├── js/
│   │   ├── eqc-dashboard.js # EQC 儀表板邏輯
│   │   ├── quality-check.js # 品質檢查邏輯
│   │   └── eqc-api.js       # EQC API
│   └── images/              # EQC 相關圖片
├── components/              # 可重用組件
│   ├── quality-meter.html   # 品質指標組件
│   ├── compliance-status.html # 合規狀態組件
│   └── eqc-chart.html       # EQC 圖表組件
├── routes/                  # 路由處理
│   └── eqc_routes.py        # EQC 路由
└── README.md                # 本檔案
```

## API 端點

### EQC 儀表板
- `GET /eqc/dashboard` - EQC 儀表板頁面
- `GET /api/eqc/metrics` - 獲取 EQC 關鍵指標
- `GET /api/eqc/charts/<type>` - 獲取 EQC 圖表數據
- `GET /api/eqc/alerts` - 獲取 EQC 警報

### 品質檢查
- `GET /eqc/quality-check` - 品質檢查頁面
- `GET /api/eqc/quality/tests` - 獲取品質測試列表
- `POST /api/eqc/quality/run` - 執行品質檢查
- `GET /api/eqc/quality/results/<job_id>` - 獲取檢查結果

### 合規檢查
- `GET /eqc/compliance` - 合規檢查頁面
- `GET /api/eqc/compliance/rules` - 獲取合規規則
- `POST /api/eqc/compliance/validate` - 執行合規驗證
- `GET /api/eqc/compliance/reports` - 獲取合規報告

### 數據分析
- `GET /api/eqc/analysis/yield-trends` - 良率趨勢分析
- `GET /api/eqc/analysis/defect-patterns` - 缺陷模式分析
- `GET /api/eqc/analysis/equipment-performance` - 設備效能分析

## 資料模型

### EQCMetrics
- `metric_id`: 指標識別碼
- `metric_name`: 指標名稱
- `metric_value`: 指標值
- `threshold_min`: 最小閾值
- `threshold_max`: 最大閾值
- `status`: 狀態 (normal, warning, critical)
- `timestamp`: 時間戳記
- `equipment_id`: 設備識別碼

### QualityTest
- `test_id`: 測試識別碼
- `test_name`: 測試名稱
- `test_type`: 測試類型 (functional, parametric, structural)
- `status`: 測試狀態 (pending, running, passed, failed)
- `result_data`: 測試結果數據
- `execution_time`: 執行時間
- `created_at`: 建立時間

### ComplianceRule
- `rule_id`: 規則識別碼
- `rule_name`: 規則名稱
- `rule_type`: 規則類型 (industry, internal, regulatory)
- `description`: 規則描述
- `validation_criteria`: 驗證標準
- `severity`: 嚴重程度 (low, medium, high, critical)
- `is_active`: 是否啟用

### EQCData
- `data_id`: 數據識別碼
- `lot_id`: 批次識別碼
- `mo_id`: 製造訂單識別碼
- `test_results`: 測試結果
- `yield_rate`: 良率
- `defect_count`: 缺陷數量
- `test_time`: 測試時間
- `equipment_id`: 設備識別碼
- `vendor_type`: 廠商類型

## EQC 處理流程

### 自動化流程
1. **數據接收** - 從郵件附件或檔案上傳接收 EQC 數據
2. **格式驗證** - 驗證數據格式和完整性
3. **品質檢查** - 執行預定義的品質檢查規則
4. **合規驗證** - 檢查是否符合行業標準
5. **結果分析** - 生成分析報告和趨勢圖表
6. **警報通知** - 發送異常警報和通知

### 廠商整合
- **FastAPI 服務整合** - 與 FastAPI EQC 服務 (Port 8010) 整合
- **數據標準化** - 統一不同廠商的數據格式
- **批次處理** - 支援大批量 EQC 數據處理
- **即時監控** - 即時監控 EQC 處理狀態

## 品質標準

### 行業標準
- **JEDEC 標準** - 半導體行業標準
- **IPC 標準** - 電子組裝標準
- **ISO 9001** - 品質管理系統標準
- **IATF 16949** - 汽車行業品質標準

### 內部標準
- **良率閾值** - 最小可接受良率標準
- **測試覆蓋率** - 必要測試項目覆蓋要求
- **數據完整性** - 數據欄位完整性要求
- **處理時效** - 數據處理時間要求

## Vue.js 遷移準備

### 已完成的準備工作 ✅
- **模組邊界清晰**: 獨立的 Flask 藍圖，URL 前綴 `/eqc/`
- **API 標準化**: 統一的 REST API 回應格式，與 FastAPI EQC 服務完美整合
- **即時監控準備**: WebSocket 支援，適合 Vue.js 的響應式即時更新
- **品質指標組件化**: 品質檢查組件，易於轉換為 Vue 響應式組件

### Vue.js 遷移優勢
- **即時監控**: Vue.js 的響應式特性完美適合 EQC 即時數據監控
- **服務整合**: 與 FastAPI EQC 服務 (Port 8010) 的 API 完全兼容
- **狀態管理**: 複雜的 EQC 數據狀態適合 Vuex/Pinia 集中管理
- **警報系統**: Vue.js 的事件系統支援即時警報通知

### 未來 Vue.js 架構
```
eqc/ (Vue.js 版本)
├── components/              # Vue 組件
│   ├── EQCDashboard.vue     # EQC 儀表板組件
│   ├── QualityMeter.vue     # 品質指標組件
│   ├── ComplianceStatus.vue # 合規狀態組件
│   └── EQCChart.vue         # EQC 圖表組件
├── views/                   # Vue 頁面
│   ├── DashboardView.vue    # EQC 儀表板
│   ├── QualityCheckView.vue # 品質檢查頁面
│   └── ComplianceView.vue   # 合規檢查頁面
├── store/                   # Vuex/Pinia 狀態管理
│   ├── eqc.js               # EQC 數據狀態
│   ├── quality.js           # 品質檢查狀態
│   └── compliance.js        # 合規檢查狀態
├── composables/             # Vue 3 Composition API
│   ├── useEQCMetrics.js     # EQC 指標邏輯複用
│   └── useQualityCheck.js   # 品質檢查邏輯複用
└── api/                     # API 服務
    └── eqcApi.js            # EQC API 服務
```

## 開發注意事項

### 當前階段 (Flask)
- 與現有 FastAPI EQC 服務 (Port 8010) 保持 API 相容性
- 實作即時數據更新和警報機制
- 支援大數據集的高效處理和分析
- 確保數據安全和存取控制
- 提供詳細的審計日誌和追蹤功能
- 支援多種數據格式和廠商標準

### Vue.js 遷移準備
- FastAPI EQC 服務的 API 與 Vue.js 整合考量
- 即時數據更新的響應式效能優化
- 大數據集的虛擬化渲染實現
- WebSocket 與 Vue.js 生命週期的整合