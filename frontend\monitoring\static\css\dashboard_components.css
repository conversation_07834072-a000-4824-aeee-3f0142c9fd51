/* 統一監控儀表板 - 元件樣式 */

/* 廠商統計元件 */
.vendor-stats {
    margin-bottom: var(--spacing-lg);
}

.vendor-stats h4 {
    font-size: var(--font-md);
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: var(--spacing-md);
    padding-bottom: var(--spacing-sm);
    border-bottom: 2px solid var(--border-color);
}

.vendor-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: var(--spacing-sm);
}

.vendor-item {
    background: var(--bg-primary);
    padding: var(--spacing-sm);
    border-radius: var(--border-radius-sm);
    text-align: center;
    transition: transform var(--transition-fast), box-shadow var(--transition-fast);
}

.vendor-item:hover {
    transform: translateY(-1px);
    box-shadow: var(--shadow-light);
}

.vendor-name {
    display: block;
    font-size: var(--font-xs);
    font-weight: 600;
    color: var(--text-secondary);
    margin-bottom: var(--spacing-xs);
    text-transform: uppercase;
}

.vendor-count {
    display: block;
    font-size: var(--font-lg);
    font-weight: 700;
    color: var(--text-primary);
}

.vendor-success-rate {
    display: block;
    font-size: var(--font-xs);
    color: var(--success-color);
    margin-top: var(--spacing-xs);
}

/* Code Comparison 統計元件 */
.code-comparison-stats {
    margin-bottom: var(--spacing-lg);
}

.code-comparison-stats h4 {
    font-size: var(--font-md);
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: var(--spacing-md);
    padding-bottom: var(--spacing-sm);
    border-bottom: 2px solid var(--border-color);
}

.comparison-metrics {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
    gap: var(--spacing-sm);
}

/* 任務類型統計元件 */
.task-type-stats {
    margin-bottom: var(--spacing-lg);
}

.task-type-stats h4 {
    font-size: var(--font-md);
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: var(--spacing-md);
    padding-bottom: var(--spacing-sm);
    border-bottom: 2px solid var(--border-color);
}

.task-type-grid {
    display: grid;
    gap: var(--spacing-sm);
}

/* Dramatiq 任務網格 */
.dramatiq-task-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: var(--spacing-md);
}

.task-type-item {
    background: var(--bg-primary);
    padding: var(--spacing-md);
    border-radius: var(--border-radius-sm);
    border-left: 4px solid var(--secondary-color);
}

.task-type-item.code-comparison {
    border-left-color: var(--info-color);
}

.task-type-item.csv-to-summary {
    border-left-color: var(--success-color);
}

.task-type-item.compression {
    border-left-color: var(--warning-color);
}

.task-type-item.decompression {
    border-left-color: var(--error-color);
}

.task-type-item.email-processing {
    border-left-color: var(--primary-color);
}

.task-type-item.data-analysis {
    border-left-color: #9b59b6;
}

.task-type-item.file-processing {
    border-left-color: #e67e22;
}

.task-type-item.batch-processing {
    border-left-color: #34495e;
}

.task-type-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-sm);
}

.task-type-name {
    font-size: var(--font-sm);
    font-weight: 600;
    color: var(--text-primary);
}

.task-type-status {
    font-size: var(--font-xs);
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--border-radius-sm);
    background: var(--success-color);
    color: var(--text-light);
}

.task-type-metrics {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: var(--spacing-sm);
    text-align: center;
}

.task-metric {
    display: flex;
    flex-direction: column;
}

.task-metric-value {
    font-size: var(--font-md);
    font-weight: 700;
    color: var(--text-primary);
}

.task-metric-label {
    font-size: var(--font-xs);
    color: var(--text-secondary);
    text-transform: uppercase;
}

/* 工作者狀態元件 */
.worker-stats {
    margin-bottom: var(--spacing-lg);
}

.worker-stats h4 {
    font-size: var(--font-md);
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: var(--spacing-md);
    padding-bottom: var(--spacing-sm);
    border-bottom: 2px solid var(--border-color);
}

.worker-grid {
    display: grid;
    gap: var(--spacing-sm);
}

.worker-item {
    background: var(--bg-primary);
    padding: var(--spacing-md);
    border-radius: var(--border-radius-sm);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.worker-info {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.worker-status-indicator {
    width: 10px;
    height: 10px;
    border-radius: 50%;
    background: var(--success-color);
}

.worker-status-indicator.offline {
    background: var(--error-color);
}

.worker-status-indicator.busy {
    background: var(--warning-color);
}

.worker-name {
    font-size: var(--font-sm);
    font-weight: 600;
    color: var(--text-primary);
}

.worker-load {
    font-size: var(--font-sm);
    color: var(--text-secondary);
}

/* 資源圖表元件 */
.resource-charts {
    margin-bottom: var(--spacing-lg);
}

.chart-container {
    margin-bottom: var(--spacing-md);
}

.chart-container h4 {
    font-size: var(--font-sm);
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: var(--spacing-sm);
}

.progress-bar {
    position: relative;
    height: 24px;
    background: var(--bg-primary);
    border-radius: var(--border-radius-sm);
    overflow: hidden;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, var(--success-color), var(--info-color));
    border-radius: var(--border-radius-sm);
    transition: width var(--transition-normal);
    width: 0%;
}

.progress-fill.warning {
    background: linear-gradient(90deg, var(--warning-color), #f1c40f);
}

.progress-fill.error {
    background: linear-gradient(90deg, var(--error-color), #e67e22);
}

.progress-text {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    font-size: var(--font-xs);
    font-weight: 600;
    color: var(--text-primary);
    text-shadow: 0 1px 2px rgba(255, 255, 255, 0.8);
}

/* 服務健康狀態元件 */
.service-health h4 {
    font-size: var(--font-md);
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: var(--spacing-md);
    padding-bottom: var(--spacing-sm);
    border-bottom: 2px solid var(--border-color);
}

.service-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: var(--spacing-sm);
}

.service-item {
    background: var(--bg-primary);
    padding: var(--spacing-sm);
    border-radius: var(--border-radius-sm);
    text-align: center;
    transition: transform var(--transition-fast);
}

.service-item:hover {
    transform: translateY(-1px);
}

.service-name {
    display: block;
    font-size: var(--font-xs);
    font-weight: 600;
    color: var(--text-secondary);
    margin-bottom: var(--spacing-xs);
    text-transform: uppercase;
}

.service-status {
    display: inline-block;
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--border-radius-sm);
    font-size: var(--font-xs);
    font-weight: 600;
    background: var(--success-color);
    color: var(--text-light);
}

.service-status.warning {
    background: var(--warning-color);
}

.service-status.error {
    background: var(--error-color);
}

/* 檔案統計元件 */
.file-stats {
    display: grid;
    gap: var(--spacing-lg);
}

.file-type-stats h4, .storage-stats h4 {
    font-size: var(--font-md);
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: var(--spacing-md);
    padding-bottom: var(--spacing-sm);
    border-bottom: 2px solid var(--border-color);
}

.file-type-grid, .storage-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: var(--spacing-sm);
}

.file-type-item, .storage-item {
    background: var(--bg-primary);
    padding: var(--spacing-sm);
    border-radius: var(--border-radius-sm);
    text-align: center;
}

.file-type-name, .storage-name {
    display: block;
    font-size: var(--font-xs);
    font-weight: 600;
    color: var(--text-secondary);
    margin-bottom: var(--spacing-xs);
    text-transform: uppercase;
}

.file-type-count, .storage-size {
    display: block;
    font-size: var(--font-md);
    font-weight: 700;
    color: var(--text-primary);
}

/* 告警元件 */
.alert-summary {
    margin-bottom: var(--spacing-lg);
}

.alert-count-grid {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: var(--spacing-sm);
}

.alert-count-item {
    background: var(--bg-primary);
    padding: var(--spacing-md);
    border-radius: var(--border-radius-sm);
    text-align: center;
    border-left: 4px solid var(--info-color);
}

.alert-count-item.critical {
    border-left-color: var(--critical-color);
}

.alert-count-item.error {
    border-left-color: var(--error-color);
}

.alert-count-item.warning {
    border-left-color: var(--warning-color);
}

.alert-count-item.info {
    border-left-color: var(--info-color);
}

.alert-count-item .count {
    display: block;
    font-size: var(--font-xl);
    font-weight: 700;
    color: var(--text-primary);
    margin-bottom: var(--spacing-xs);
}

.alert-count-item .label {
    display: block;
    font-size: var(--font-xs);
    color: var(--text-secondary);
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.alert-list {
    max-height: 300px;
    overflow-y: auto;
}

.alert-item {
    background: var(--bg-primary);
    padding: var(--spacing-md);
    border-radius: var(--border-radius-sm);
    margin-bottom: var(--spacing-sm);
    border-left: 4px solid var(--info-color);
    transition: transform var(--transition-fast);
}

.alert-item:hover {
    transform: translateX(2px);
}

.alert-item.critical {
    border-left-color: var(--critical-color);
    background: rgba(192, 57, 43, 0.05);
}

.alert-item.error {
    border-left-color: var(--error-color);
    background: rgba(231, 76, 60, 0.05);
}

.alert-item.warning {
    border-left-color: var(--warning-color);
    background: rgba(243, 156, 18, 0.05);
}

.alert-item.info {
    border-left-color: var(--info-color);
    background: rgba(23, 162, 184, 0.05);
}

.alert-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: var(--spacing-sm);
}

.alert-title {
    font-size: var(--font-sm);
    font-weight: 600;
    color: var(--text-primary);
    margin: 0;
}

.alert-time {
    font-size: var(--font-xs);
    color: var(--text-secondary);
    white-space: nowrap;
}

.alert-message {
    font-size: var(--font-sm);
    color: var(--text-secondary);
    line-height: 1.4;
    margin-bottom: var(--spacing-sm);
}

.alert-actions {
    display: flex;
    gap: var(--spacing-sm);
}

.alert-action-btn {
    background: none;
    border: 1px solid var(--border-color);
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--border-radius-sm);
    font-size: var(--font-xs);
    cursor: pointer;
    transition: all var(--transition-fast);
}

.alert-action-btn:hover {
    background: var(--secondary-color);
    color: var(--text-light);
    border-color: var(--secondary-color);
}

.alert-action-btn.acknowledge {
    background: var(--success-color);
    color: var(--text-light);
    border-color: var(--success-color);
}

.alert-action-btn.dismiss {
    background: var(--text-secondary);
    color: var(--text-light);
    border-color: var(--text-secondary);
}

/* 響應式調整 */
@media (max-width: 768px) {
    .vendor-grid, .task-type-metrics, .service-grid, .file-type-grid, .storage-grid {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .alert-count-grid {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .comparison-metrics {
        grid-template-columns: repeat(3, 1fr);
    }
    
    /* Dramatiq 任務網格響應式 */
    .dramatiq-task-grid {
        grid-template-columns: 1fr;
    }
    
    .task-type-metrics {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .worker-item {
        flex-direction: column;
        gap: var(--spacing-sm);
        text-align: center;
    }
    
    .alert-header {
        flex-direction: column;
        gap: var(--spacing-xs);
    }
    
    .alert-actions {
        justify-content: center;
    }
}

@media (max-width: 480px) {
    .vendor-grid, .service-grid, .file-type-grid, .storage-grid, .alert-count-grid {
        grid-template-columns: 1fr;
    }
    
    .task-type-metrics {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .comparison-metrics {
        grid-template-columns: 1fr;
    }
    
    /* Dramatiq 任務網格小螢幕優化 */
    .dramatiq-task-grid {
        grid-template-columns: 1fr;
        gap: var(--spacing-sm);
    }
    
    .task-type-item {
        padding: var(--spacing-sm);
    }
    
    .task-type-header {
        flex-direction: column;
        gap: var(--spacing-xs);
        text-align: center;
    }
    
    .task-type-name {
        font-size: var(--font-xs);
    }
    
    .progress-text {
        font-size: var(--font-xs);
    }
}

/* 動畫效果 */
.fade-in {
    animation: fadeIn var(--transition-normal) ease-in;
}

.slide-up {
    animation: slideUp var(--transition-normal) ease-out;
}

@keyframes fadeIn {
    from {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
}

@keyframes slideUp {
    from {
        transform: translateY(20px);
        opacity: 0;
    }
    to {
        transform: translateY(0);
        opacity: 1;
    }
}

/* ===== 圖表和視覺化元件 ===== */
.chart-wrapper {
    position: relative;
    width: 100%;
    height: 200px;
    margin-bottom: var(--spacing-lg);
}

.chart-canvas {
    width: 100%;
    height: 100%;
    border-radius: var(--border-radius-sm);
}

.chart-legend {
    display: flex;
    justify-content: center;
    gap: var(--spacing-md);
    margin-top: var(--spacing-sm);
    flex-wrap: wrap;
}

.legend-item {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    font-size: var(--font-xs);
    color: var(--text-secondary);
}

.legend-color {
    width: 12px;
    height: 12px;
    border-radius: var(--border-radius-sm);
}

/* ===== 數據表格元件 ===== */
.data-table {
    width: 100%;
    border-collapse: collapse;
    background: var(--bg-secondary);
    border-radius: var(--border-radius-md);
    overflow: hidden;
    box-shadow: var(--shadow-light);
}

.data-table th,
.data-table td {
    padding: var(--spacing-sm) var(--spacing-md);
    text-align: left;
    border-bottom: 1px solid var(--border-color);
}

.data-table th {
    background: var(--bg-tertiary);
    font-weight: var(--font-weight-semibold);
    font-size: var(--font-sm);
    color: var(--text-primary);
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.data-table tr:hover {
    background: var(--bg-primary);
}

.data-table tr:last-child td {
    border-bottom: none;
}

/* ===== 狀態徽章元件 ===== */
.status-badge {
    display: inline-flex;
    align-items: center;
    gap: var(--spacing-xs);
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--border-radius-full);
    font-size: var(--font-xs);
    font-weight: var(--font-weight-medium);
    text-transform: uppercase;
    letter-spacing: 0.5px;
    white-space: nowrap;
}

.status-badge.success {
    background: var(--success-light);
    color: var(--success-color);
}

.status-badge.warning {
    background: var(--warning-light);
    color: var(--warning-color);
}

.status-badge.error {
    background: var(--error-light);
    color: var(--error-color);
}

.status-badge.info {
    background: var(--info-light);
    color: var(--info-color);
}

.status-badge-icon {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: currentColor;
}

/* ===== 載入狀態元件 ===== */
.loading-skeleton {
    background: linear-gradient(90deg, var(--bg-tertiary) 25%, var(--bg-quaternary) 50%, var(--bg-tertiary) 75%);
    background-size: 200% 100%;
    animation: shimmer 1.5s infinite;
    border-radius: var(--border-radius-sm);
}

.skeleton-text {
    height: 1em;
    margin-bottom: var(--spacing-xs);
}

.skeleton-text.wide {
    width: 100%;
}

.skeleton-text.medium {
    width: 75%;
}

.skeleton-text.narrow {
    width: 50%;
}

.skeleton-card {
    height: 120px;
    width: 100%;
}

@keyframes shimmer {
    0% {
        background-position: -200% 0;
    }
    100% {
        background-position: 200% 0;
    }
}

/* ===== 工具提示元件 ===== */
.tooltip {
    position: relative;
    display: inline-block;
}

.tooltip-content {
    position: absolute;
    bottom: 125%;
    left: 50%;
    transform: translateX(-50%);
    background: var(--bg-dark);
    color: var(--text-light);
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--border-radius-sm);
    font-size: var(--font-xs);
    white-space: nowrap;
    opacity: 0;
    visibility: hidden;
    transition: all var(--transition-fast);
    z-index: var(--z-tooltip);
    pointer-events: none;
}

.tooltip-content::after {
    content: '';
    position: absolute;
    top: 100%;
    left: 50%;
    transform: translateX(-50%);
    border: 4px solid transparent;
    border-top-color: var(--bg-dark);
}

.tooltip:hover .tooltip-content {
    opacity: 1;
    visibility: visible;
}

/* ===== 通知元件 ===== */
.notification {
    position: fixed;
    top: var(--spacing-lg);
    right: var(--spacing-lg);
    background: var(--bg-secondary);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-md);
    padding: var(--spacing-md);
    box-shadow: var(--shadow-heavy);
    z-index: var(--z-modal);
    min-width: 300px;
    max-width: 400px;
    transform: translateX(100%);
    transition: transform var(--transition-normal);
}

.notification.show {
    transform: translateX(0);
}

.notification.success {
    border-left: 4px solid var(--success-color);
}

.notification.warning {
    border-left: 4px solid var(--warning-color);
}

.notification.error {
    border-left: 4px solid var(--error-color);
}

.notification.info {
    border-left: 4px solid var(--info-color);
}

.notification-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: var(--spacing-sm);
}

.notification-title {
    font-size: var(--font-sm);
    font-weight: var(--font-weight-semibold);
    color: var(--text-primary);
    margin: 0;
}

.notification-close {
    background: none;
    border: none;
    color: var(--text-secondary);
    cursor: pointer;
    padding: 0;
    font-size: var(--font-lg);
    line-height: 1;
    transition: color var(--transition-fast);
}

.notification-close:hover {
    color: var(--text-primary);
}

.notification-message {
    font-size: var(--font-sm);
    color: var(--text-secondary);
    line-height: var(--line-height-normal);
}

/* ===== 模態框元件 ===== */
.modal-backdrop {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    z-index: var(--z-modal-backdrop);
    opacity: 0;
    visibility: hidden;
    transition: all var(--transition-normal);
}

.modal-backdrop.show {
    opacity: 1;
    visibility: visible;
}

.modal {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%) scale(0.9);
    background: var(--bg-secondary);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-heavy);
    z-index: var(--z-modal);
    max-width: 90vw;
    max-height: 90vh;
    overflow: hidden;
    transition: transform var(--transition-normal);
}

.modal.show {
    transform: translate(-50%, -50%) scale(1);
}

.modal-header {
    padding: var(--spacing-lg);
    border-bottom: 1px solid var(--border-color);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.modal-title {
    font-size: var(--font-lg);
    font-weight: var(--font-weight-semibold);
    color: var(--text-primary);
    margin: 0;
}

.modal-body {
    padding: var(--spacing-lg);
    overflow-y: auto;
    max-height: 60vh;
}

.modal-footer {
    padding: var(--spacing-lg);
    border-top: 1px solid var(--border-color);
    display: flex;
    justify-content: flex-end;
    gap: var(--spacing-sm);
}

/* ===== 按鈕元件增強 ===== */
.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-xs);
    padding: var(--spacing-sm) var(--spacing-md);
    border: 1px solid transparent;
    border-radius: var(--border-radius-md);
    font-size: var(--font-sm);
    font-weight: var(--font-weight-medium);
    text-decoration: none;
    cursor: pointer;
    transition: all var(--transition-fast);
    white-space: nowrap;
    user-select: none;
}

.btn:focus {
    outline: none;
    box-shadow: var(--shadow-focus);
}

.btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    pointer-events: none;
}

.btn-primary {
    background: var(--primary-color);
    color: var(--text-light);
    border-color: var(--primary-color);
}

.btn-primary:hover {
    background: #34495e;
    border-color: #34495e;
}

.btn-secondary {
    background: var(--secondary-color);
    color: var(--text-light);
    border-color: var(--secondary-color);
}

.btn-secondary:hover {
    background: #2980b9;
    border-color: #2980b9;
}

.btn-success {
    background: var(--success-color);
    color: var(--text-light);
    border-color: var(--success-color);
}

.btn-success:hover {
    background: #229954;
    border-color: #229954;
}

.btn-warning {
    background: var(--warning-color);
    color: var(--text-light);
    border-color: var(--warning-color);
}

.btn-warning:hover {
    background: #e67e22;
    border-color: #e67e22;
}

.btn-error {
    background: var(--error-color);
    color: var(--text-light);
    border-color: var(--error-color);
}

.btn-error:hover {
    background: #c0392b;
    border-color: #c0392b;
}

.btn-outline {
    background: transparent;
    color: var(--text-primary);
    border-color: var(--border-color);
}

.btn-outline:hover {
    background: var(--bg-primary);
    border-color: var(--text-secondary);
}

.btn-sm {
    padding: var(--spacing-xs) var(--spacing-sm);
    font-size: var(--font-xs);
}

.btn-lg {
    padding: var(--spacing-md) var(--spacing-lg);
    font-size: var(--font-md);
}

/* ===== 表單元件 ===== */
.form-group {
    margin-bottom: var(--spacing-md);
}

.form-label {
    display: block;
    font-size: var(--font-sm);
    font-weight: var(--font-weight-medium);
    color: var(--text-primary);
    margin-bottom: var(--spacing-xs);
}

.form-control {
    width: 100%;
    padding: var(--spacing-sm) var(--spacing-md);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-md);
    font-size: var(--font-sm);
    color: var(--text-primary);
    background: var(--bg-secondary);
    transition: all var(--transition-fast);
}

.form-control:focus {
    outline: none;
    border-color: var(--secondary-color);
    box-shadow: var(--shadow-focus);
}

.form-control:disabled {
    background: var(--bg-tertiary);
    color: var(--text-secondary);
    cursor: not-allowed;
}

.form-select {
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
    background-position: right var(--spacing-sm) center;
    background-repeat: no-repeat;
    background-size: 16px 12px;
    padding-right: var(--spacing-xl);
    appearance: none;
}

/* ===== 工具類別 ===== */
.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }

.d-none { display: none; }
.d-block { display: block; }
.d-inline { display: inline; }
.d-inline-block { display: inline-block; }
.d-flex { display: flex; }
.d-grid { display: grid; }

.justify-start { justify-content: flex-start; }
.justify-center { justify-content: center; }
.justify-end { justify-content: flex-end; }
.justify-between { justify-content: space-between; }

.align-start { align-items: flex-start; }
.align-center { align-items: center; }
.align-end { align-items: flex-end; }

.flex-wrap { flex-wrap: wrap; }
.flex-nowrap { flex-wrap: nowrap; }

.gap-xs { gap: var(--spacing-xs); }
.gap-sm { gap: var(--spacing-sm); }
.gap-md { gap: var(--spacing-md); }
.gap-lg { gap: var(--spacing-lg); }

.m-0 { margin: 0; }
.mt-0 { margin-top: 0; }
.mb-0 { margin-bottom: 0; }
.ml-0 { margin-left: 0; }
.mr-0 { margin-right: 0; }

.p-0 { padding: 0; }
.pt-0 { padding-top: 0; }
.pb-0 { padding-bottom: 0; }
.pl-0 { padding-left: 0; }
.pr-0 { padding-right: 0; }

.w-full { width: 100%; }
.h-full { height: 100%; }

.text-primary { color: var(--text-primary); }
.text-secondary { color: var(--text-secondary); }
.text-success { color: var(--success-color); }
.text-warning { color: var(--warning-color); }
.text-error { color: var(--error-color); }

.bg-primary { background-color: var(--bg-primary); }
.bg-secondary { background-color: var(--bg-secondary); }
.bg-success { background-color: var(--success-color); }
.bg-warning { background-color: var(--warning-color); }
.bg-error { background-color: var(--error-color); }

.border { border: 1px solid var(--border-color); }
.border-0 { border: none; }
.border-top { border-top: 1px solid var(--border-color); }
.border-bottom { border-bottom: 1px solid var(--border-color); }

.rounded { border-radius: var(--border-radius-md); }
.rounded-sm { border-radius: var(--border-radius-sm); }
.rounded-lg { border-radius: var(--border-radius-lg); }
.rounded-full { border-radius: var(--border-radius-full); }

.shadow { box-shadow: var(--shadow-light); }
.shadow-md { box-shadow: var(--shadow-medium); }
.shadow-lg { box-shadow: var(--shadow-heavy); }

/* ===== 滾動條樣式 ===== */
.scrollable::-webkit-scrollbar,
.alert-list::-webkit-scrollbar,
.modal-body::-webkit-scrollbar {
    width: 6px;
    height: 6px;
}

.scrollable::-webkit-scrollbar-track,
.alert-list::-webkit-scrollbar-track,
.modal-body::-webkit-scrollbar-track {
    background: var(--bg-primary);
    border-radius: var(--border-radius-sm);
}

.scrollable::-webkit-scrollbar-thumb,
.alert-list::-webkit-scrollbar-thumb,
.modal-body::-webkit-scrollbar-thumb {
    background: var(--text-secondary);
    border-radius: var(--border-radius-sm);
}

.scrollable::-webkit-scrollbar-thumb:hover,
.alert-list::-webkit-scrollbar-thumb:hover,
.modal-body::-webkit-scrollbar-thumb:hover {
    background: var(--secondary-color);
}

/* ===== 無障礙功能增強 ===== */
.sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
}

.focus-visible:focus {
    outline: 2px solid var(--secondary-color);
    outline-offset: 2px;
}

/* ===== 列印樣式 ===== */
@media print {
    .no-print {
        display: none !important;
    }
    
    .print-break {
        page-break-after: always;
    }
    
    .print-avoid-break {
        page-break-inside: avoid;
    }
}