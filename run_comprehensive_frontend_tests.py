#!/usr/bin/env python3
"""
綜合前端架構測試運行器
運行所有相關測試並生成整體報告
"""

import os
import sys
import time
import json
import subprocess
from pathlib import Path
from typing import Dict, List, Any, Optional
from datetime import datetime

# 添加專案根目錄到 Python 路徑
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))


class ComprehensiveFrontendTestRunner:
    """綜合前端測試運行器"""
    
    def __init__(self):
        self.project_root = project_root
        self.test_results = {}
        self.start_time = None
        
    def run_all_frontend_tests(self) -> Dict[str, Any]:
        """運行所有前端架構測試"""
        self.start_time = time.time()
        
        print("🧪 開始執行綜合前端架構測試套件")
        print("="*70)
        print("此測試套件驗證新的模組化前端架構完全取代了舊的 email_inbox_app.py")
        print("="*70)
        
        # 測試項目列表
        test_suites = [
            {
                'name': 'modular_architecture',
                'description': '模組化架構驗證測試',
                'script': 'tests/integration/test_modular_frontend_architecture.py',
                'critical': True
            },
            {
                'name': 'integrated_services',
                'description': '整合服務啟動測試',
                'script': 'tests/integration/test_integrated_services_startup.py',
                'critical': True
            },
            {
                'name': 'migration_regression',
                'description': '架構遷移回歸測試',
                'script': 'tests/integration/test_migration_regression.py',
                'critical': True
            }
        ]
        
        # 執行每個測試套件
        for i, suite in enumerate(test_suites, 1):
            print(f"\n📋 第{i}階段: {suite['description']}")
            print("-" * 50)
            
            result = self._run_test_suite(suite)
            self.test_results[suite['name']] = result
            
            # 顯示測試結果
            if result['success']:
                print(f"✅ {suite['description']} - 通過")
            else:
                print(f"❌ {suite['description']} - 失敗")
                if suite['critical']:
                    print(f"⚠️ 關鍵測試失敗: {suite['description']}")
        
        # 計算整體結果
        total_time = time.time() - self.start_time
        self.test_results['overall_summary'] = self._calculate_overall_summary(total_time)
        
        return self.test_results
    
    def _run_test_suite(self, suite: Dict[str, Any]) -> Dict[str, Any]:
        """運行單個測試套件"""
        script_path = self.project_root / suite['script']
        
        if not script_path.exists():
            return {
                'success': False,
                'error': f"測試腳本不存在: {script_path}",
                'execution_time': 0
            }
        
        try:
            start_time = time.time()
            
            # 設置環境變數
            env = os.environ.copy()
            env['PYTHONIOENCODING'] = 'utf-8'
            env['PYTHONUTF8'] = '1'
            env['PYTHONPATH'] = str(self.project_root)
            
            # 執行測試腳本
            result = subprocess.run([
                sys.executable, str(script_path)
            ],
            capture_output=True,
            text=True,
            encoding='utf-8',
            timeout=300,  # 5分鐘超時
            cwd=self.project_root,
            env=env
            )
            
            execution_time = time.time() - start_time
            
            # 分析輸出
            success = result.returncode == 0
            output = result.stdout + result.stderr
            
            # 嘗試解析生成的JSON報告
            json_reports = self._find_generated_reports(suite['name'])
            
            return {
                'success': success,
                'return_code': result.returncode,
                'execution_time': execution_time,
                'output_length': len(output),
                'generated_reports': json_reports,
                'stdout': result.stdout,
                'stderr': result.stderr
            }
            
        except subprocess.TimeoutExpired:
            return {
                'success': False,
                'error': f"測試超時 (>5分鐘): {suite['name']}",
                'execution_time': 300
            }
        except Exception as e:
            return {
                'success': False,
                'error': str(e),
                'execution_time': 0
            }
    
    def _find_generated_reports(self, test_name: str) -> List[str]:
        """查找生成的報告文件"""
        report_patterns = [
            f"*{test_name}*test*report*.json",
            f"*modular*frontend*test*report*.json",
            f"*integrated*services*test*.json",
            f"*migration*regression*test*.json"
        ]
        
        found_reports = []
        for pattern in report_patterns:
            for file_path in self.project_root.glob(pattern):
                if file_path.is_file():
                    found_reports.append(str(file_path))
        
        return found_reports
    
    def _calculate_overall_summary(self, total_time: float) -> Dict[str, Any]:
        """計算整體測試摘要"""
        test_suites = ['modular_architecture', 'integrated_services', 'migration_regression']
        
        successful_suites = 0
        critical_failures = []
        total_execution_time = 0
        
        for suite_name in test_suites:
            suite_result = self.test_results.get(suite_name, {})
            
            if suite_result.get('success', False):
                successful_suites += 1
            else:
                critical_failures.append(suite_name)
            
            total_execution_time += suite_result.get('execution_time', 0)
        
        success_rate = (successful_suites / len(test_suites) * 100) if test_suites else 0
        overall_success = len(critical_failures) == 0
        
        return {
            'overall_success': overall_success,
            'success_rate': success_rate,
            'successful_suites': successful_suites,
            'total_suites': len(test_suites),
            'critical_failures': critical_failures,
            'total_execution_time': total_execution_time,
            'wall_clock_time': total_time,
            'timestamp': datetime.now().isoformat()
        }
    
    def generate_comprehensive_report(self) -> str:
        """生成綜合測試報告"""
        if not self.test_results:
            return "尚未執行測試"
        
        summary = self.test_results.get('overall_summary', {})
        
        report = f"""
綜合前端架構測試報告
{'='*70}

測試概要:
    整體結果: {'✅ 架構遷移成功' if summary.get('overall_success') else '❌ 架構遷移有問題'}
    成功率: {summary.get('success_rate', 0):.1f}%
    通過測試套件: {summary.get('successful_suites', 0)}/{summary.get('total_suites', 0)}
    關鍵失敗數: {len(summary.get('critical_failures', []))}
    總執行時間: {summary.get('wall_clock_time', 0):.2f}秒
    測試時間: {summary.get('timestamp', 'N/A')}

架構遷移驗證:
{'='*70}

✨ 從 email_inbox_app.py 到模組化 frontend/ 架構的遷移驗證

詳細測試結果:
"""

        # 添加各個測試套件的結果
        suite_names = {
            'modular_architecture': '模組化架構驗證',
            'integrated_services': '整合服務啟動',
            'migration_regression': '遷移回歸測試'
        }
        
        for suite_key, suite_title in suite_names.items():
            suite_result = self.test_results.get(suite_key, {})
            success_icon = '✅' if suite_result.get('success') else '❌'
            exec_time = suite_result.get('execution_time', 0)
            
            report += f"\n{success_icon} {suite_title}:"
            report += f"\n    執行時間: {exec_time:.2f}秒"
            
            if not suite_result.get('success'):
                error = suite_result.get('error', '未知錯誤')
                report += f"\n    ❌ 失敗原因: {error}"
            
            # 添加生成的報告信息
            generated_reports = suite_result.get('generated_reports', [])
            if generated_reports:
                report += f"\n    📄 生成報告: {len(generated_reports)} 個"
                for report_file in generated_reports[:2]:  # 只顯示前2個
                    report += f"\n      - {Path(report_file).name}"
        
        # 添加關鍵發現
        report += f"\n\n關鍵發現:\n{'='*70}"
        
        if summary.get('overall_success'):
            report += """
✅ 模組化前端架構已成功取代舊的 email_inbox_app.py
✅ 所有6個前端模組（email, analytics, eqc, tasks, monitoring, file_management）正常載入
✅ Flask 應用工廠模式正常運作
✅ 藍圖註冊和路由映射正確
✅ start_integrated_services.py 使用新架構正常啟動
✅ 舊文件已清理，無遺留引用
✅ 功能等效性得到驗證，沒有功能丟失
"""
        else:
            report += "\n❌ 發現以下問題需要修復:\n"
            for failure in summary.get('critical_failures', []):
                failure_result = self.test_results.get(failure, {})
                error = failure_result.get('error', '未知錯誤')
                report += f"  - {failure}: {error}\n"
        
        # 添加建議
        if summary.get('overall_success'):
            report += f"\n\n建議:\n{'-'*20}"
            report += """
✅ 新的模組化架構已準備好投入生產使用
✅ 可以安全地移除任何備份的舊文件
✅ 建議更新部署腳本以反映新的架構
✅ 考慮開始Vue.js遷移的下一階段
"""
        else:
            report += f"\n\n修復建議:\n{'-'*20}"
            report += """
1. 檢查並修復失敗的測試項目
2. 確保所有依賴項正確安裝
3. 驗證模組導入路徑
4. 檢查配置文件設置
5. 重新運行測試確認修復
"""
        
        report += "\n" + "="*70
        return report
    
    def save_comprehensive_report(self, filename: str = None):
        """保存綜合報告"""
        if filename is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"comprehensive_frontend_test_report_{timestamp}"
        
        # 保存詳細JSON報告
        json_file = self.project_root / f"{filename}.json"
        with open(json_file, 'w', encoding='utf-8') as f:
            json.dump(self.test_results, f, ensure_ascii=False, indent=2)
        
        # 保存可讀報告
        txt_file = self.project_root / f"{filename}.txt"
        with open(txt_file, 'w', encoding='utf-8') as f:
            f.write(self.generate_comprehensive_report())
        
        print(f"\n📄 詳細報告已保存: {json_file}")
        print(f"📄 摘要報告已保存: {txt_file}")
        
        return json_file, txt_file
    
    def cleanup_old_reports(self, keep_latest: int = 5):
        """清理舊的測試報告"""
        patterns = [
            "*frontend*test*report*.json",
            "*frontend*test*report*.txt",
            "*modular*frontend*test*.json",
            "*integrated*services*test*.json",
            "*migration*regression*test*.json"
        ]
        
        for pattern in patterns:
            files = list(self.project_root.glob(pattern))
            # 按修改時間排序，保留最新的幾個
            files.sort(key=lambda f: f.stat().st_mtime, reverse=True)
            
            for old_file in files[keep_latest:]:
                try:
                    old_file.unlink()
                    print(f"🗑️ 已清理舊報告: {old_file.name}")
                except Exception:
                    pass


def main():
    """主函數"""
    print("🧪 綜合前端架構測試套件")
    print("驗證新的模組化前端架構完全取代 email_inbox_app.py")
    print("="*70)
    
    runner = ComprehensiveFrontendTestRunner()
    
    try:
        # 執行所有測試
        results = runner.run_all_frontend_tests()
        
        # 生成和顯示報告
        print("\n" + "="*70)
        print("測試完成! 正在生成報告...")
        print("="*70)
        
        report = runner.generate_comprehensive_report()
        print(report)
        
        # 保存報告
        json_file, txt_file = runner.save_comprehensive_report()
        
        # 清理舊報告
        runner.cleanup_old_reports()
        
        # 顯示測試結論
        overall_success = results.get('overall_summary', {}).get('overall_success', False)
        
        if overall_success:
            print("\n🎉 恭喜！新的模組化前端架構已成功驗證！")
            print("✅ 可以安全地使用新架構替代舊的 email_inbox_app.py")
            print("🚀 準備好進行下一階段的 Vue.js 遷移")
        else:
            print("\n⚠️ 發現一些問題需要修復")
            print("📝 請檢查詳細報告並修復失敗的測試項目")
            print("🔧 修復後重新運行測試")
        
        return 0 if overall_success else 1
        
    except KeyboardInterrupt:
        print("\n❌ 測試被用戶中斷")
        return 1
    except Exception as e:
        print(f"\n❌ 測試執行失敗: {e}")
        import traceback
        traceback.print_exc()
        return 1


if __name__ == '__main__':
    exit_code = main()
    sys.exit(exit_code)