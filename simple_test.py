#!/usr/bin/env python3
"""
簡單的功能驗證測試
"""

import requests
import json
import sys

def test_basic_functionality():
    """測試基本功能"""
    base_url = "http://localhost:8000"
    
    print("🔍 測試 Flask 應用程式基本功能...")
    
    try:
        # 測試健康檢查端點
        print("1. 測試健康檢查端點...")
        response = requests.get(f"{base_url}/health", timeout=10)
        if response.status_code == 200:
            health_data = response.json()
            print(f"✅ 健康檢查成功: {health_data.get('status', 'unknown')}")
            print(f"   模組狀態: {health_data.get('modules', {})}")
        else:
            print(f"❌ 健康檢查失敗: {response.status_code}")
            return False
        
        # 測試主頁重定向
        print("2. 測試主頁重定向...")
        response = requests.get(f"{base_url}/", timeout=10, allow_redirects=False)
        if response.status_code == 302:
            print("✅ 主頁重定向正常")
        else:
            print(f"❌ 主頁重定向失敗: {response.status_code}")
        
        # 測試一些基本頁面
        test_pages = [
            "/email/",
            "/analytics/",
            "/files/",
            "/eqc/",
            "/tasks/",
            "/monitoring/"
        ]
        
        print("3. 測試模組頁面...")
        for page in test_pages:
            try:
                response = requests.get(f"{base_url}{page}", timeout=10)
                if response.status_code == 200:
                    print(f"✅ {page} - 載入成功")
                elif response.status_code == 404:
                    print(f"⚠️  {page} - 頁面不存在 (404)")
                else:
                    print(f"❌ {page} - 載入失敗 ({response.status_code})")
            except Exception as e:
                print(f"❌ {page} - 請求異常: {str(e)}")
        
        print("\n✅ 基本功能測試完成")
        return True
        
    except Exception as e:
        print(f"❌ 測試過程中發生異常: {str(e)}")
        return False

if __name__ == "__main__":
    success = test_basic_functionality()
    sys.exit(0 if success else 1)