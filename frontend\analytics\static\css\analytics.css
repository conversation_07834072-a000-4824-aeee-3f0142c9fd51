/* Analytics Module CSS - 分析模組樣式 */

/* 分析儀表板容器 */
.analytics-container {
    padding: 20px;
    background: #f8f9fa;
    min-height: 100vh;
}

.analytics-header {
    background: white;
    padding: 20px;
    border-radius: 8px;
    margin-bottom: 20px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.analytics-title {
    font-size: 24px;
    font-weight: 600;
    color: #333;
    margin-bottom: 10px;
}

.analytics-subtitle {
    color: #666;
    font-size: 14px;
}

/* 分析卡片網格 */
.analytics-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.analytics-card {
    background: white;
    border-radius: 8px;
    padding: 20px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.analytics-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.card-title {
    font-size: 18px;
    font-weight: 600;
    color: #333;
    margin-bottom: 15px;
    display: flex;
    align-items: center;
}

.card-icon {
    width: 24px;
    height: 24px;
    margin-right: 10px;
    color: #007bff;
}

/* 統計數字顯示 */
.stat-number {
    font-size: 32px;
    font-weight: 700;
    color: #007bff;
    margin-bottom: 5px;
}

.stat-label {
    font-size: 14px;
    color: #666;
}

.stat-change {
    font-size: 12px;
    margin-top: 5px;
}

.stat-change.positive {
    color: #28a745;
}

.stat-change.negative {
    color: #dc3545;
}

/* 圖表容器 */
.chart-container {
    background: white;
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 20px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.chart-title {
    font-size: 18px;
    font-weight: 600;
    margin-bottom: 15px;
    color: #333;
}

.chart-wrapper {
    position: relative;
    height: 300px;
    margin-bottom: 10px;
}

/* CSV 處理器樣式 */
.csv-processor {
    background: white;
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 20px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.csv-upload-area {
    border: 2px dashed #ddd;
    border-radius: 8px;
    padding: 40px;
    text-align: center;
    margin-bottom: 20px;
    cursor: pointer;
    transition: border-color 0.2s ease;
}

.csv-upload-area:hover {
    border-color: #007bff;
}

.csv-upload-area.dragover {
    border-color: #28a745;
    background-color: #f8fff9;
}

.upload-icon {
    font-size: 48px;
    color: #ddd;
    margin-bottom: 15px;
}

.upload-text {
    font-size: 16px;
    color: #666;
    margin-bottom: 10px;
}

.upload-hint {
    font-size: 14px;
    color: #999;
}

/* 資料表格樣式 */
.data-table-container {
    background: white;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.data-table {
    width: 100%;
    border-collapse: collapse;
}

.data-table th {
    background-color: #f8f9fa;
    padding: 15px 12px;
    text-align: left;
    font-weight: 600;
    border-bottom: 2px solid #e9ecef;
    position: sticky;
    top: 0;
}

.data-table td {
    padding: 12px;
    border-bottom: 1px solid #e9ecef;
}

.data-table tr:hover {
    background-color: #f8f9fa;
}

/* 供應商分析樣式 */
.vendor-analysis {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
    margin-bottom: 20px;
}

.vendor-card {
    background: white;
    border-radius: 8px;
    padding: 20px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.vendor-name {
    font-size: 18px;
    font-weight: 600;
    margin-bottom: 10px;
    color: #333;
}

.vendor-stats {
    display: flex;
    justify-content: space-between;
    margin-bottom: 15px;
}

.vendor-stat {
    text-align: center;
}

.vendor-stat-value {
    font-size: 20px;
    font-weight: 600;
    color: #007bff;
}

.vendor-stat-label {
    font-size: 12px;
    color: #666;
}

/* 篩選器樣式 */
.analytics-filters {
    background: white;
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 20px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.filter-row {
    display: flex;
    gap: 15px;
    flex-wrap: wrap;
    align-items: end;
}

.filter-group {
    flex: 1;
    min-width: 200px;
}

.filter-group label {
    display: block;
    margin-bottom: 5px;
    font-weight: 500;
}

.filter-group select,
.filter-group input {
    width: 100%;
    padding: 8px 12px;
    border: 1px solid #ddd;
    border-radius: 4px;
}

/* 響應式設計 */
@media (max-width: 768px) {
    .analytics-grid {
        grid-template-columns: 1fr;
    }
    
    .vendor-analysis {
        grid-template-columns: 1fr;
    }
    
    .filter-row {
        flex-direction: column;
    }
    
    .filter-group {
        width: 100%;
    }
    
    .chart-wrapper {
        height: 250px;
    }
    
    .analytics-container {
        padding: 10px;
    }
}