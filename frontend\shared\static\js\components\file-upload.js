/**
 * 檔案上傳組件模組
 * 處理檔案拖放上傳、檔案驗證、進度顯示和重複檢測
 */

class FileUploadComponent {
    constructor() {
        this.isSetup = false;
        this.isUploading = false;
        this.maxSizeMB = 1000; // 預設最大檔案大小
        this.allowedTypes = ['.zip', '.7z', '.rar', '.tar', '.gz'];
        
        this.elements = {
            uploadZone: null,
            fileInput: null,
            uploadStatus: null,
            maxSizeDisplay: null
        };
    }
    
    /**
     * 初始化檔案上傳功能
     */
    async init() {
        if (this.isSetup) return;
        
        // 獲取 DOM 元素
        this.elements.uploadZone = DOMManager.get('uploadZone');
        this.elements.fileInput = DOMManager.get('fileInput');
        this.elements.uploadStatus = DOMManager.get('uploadStatus');
        this.elements.maxSizeDisplay = DOMManager.get('maxSizeDisplay');
        
        if (!this.elements.uploadZone || !this.elements.fileInput || !this.elements.uploadStatus) {
            console.error('檔案上傳元素未找到，跳過初始化');
            return;
        }
        
        await this.loadUploadConfig();
        this.setupEventListeners();
        this.isSetup = true;
    }
    
    /**
     * 載入上傳配置
     */
    async loadUploadConfig() {
        try {
            const config = await ApiClient.getUploadConfig();
            this.maxSizeMB = config.max_upload_size_mb;
            if (this.elements.maxSizeDisplay) {
                DOMManager.setText('maxSizeDisplay', this.maxSizeMB.toString());
            }
        } catch (error) {
            console.warn('無法載入上傳配置，使用預設設定');
        }
    }
    
    /**
     * 設置事件監聽器
     */
    setupEventListeners() {
        const { uploadZone, fileInput } = this.elements;
        
        // 拖放事件
        uploadZone.addEventListener('dragover', (e) => {
            e.preventDefault();
            uploadZone.classList.add('dragover');
        });
        
        uploadZone.addEventListener('dragleave', (e) => {
            e.preventDefault();
            uploadZone.classList.remove('dragover');
        });
        
        uploadZone.addEventListener('drop', (e) => {
            e.preventDefault();
            uploadZone.classList.remove('dragover');
            const files = e.dataTransfer.files;
            if (files.length > 0) {
                this.handleFileUpload(files[0]);
            }
        });
        
        // 點擊上傳
        uploadZone.addEventListener('click', () => {
            fileInput.click();
        });
        
        fileInput.addEventListener('change', (e) => {
            if (e.target.files.length > 0) {
                this.handleFileUpload(e.target.files[0]);
            }
        });
    }
    
    /**
     * 處理檔案上傳
     * @param {File} file - 要上傳的檔案
     */
    async handleFileUpload(file) {
        if (this.isUploading) {
            StatusManager.showToast('已有檔案正在上傳中，請等待完成後再試！', 'warning');
            return;
        }
        
        this.isUploading = true;
        const { uploadStatus, uploadZone } = this.elements;
        
        try {
            if (!this.validateFile(file)) return;
            
            uploadZone.classList.add('uploading');
            DOMManager.show('uploadStatus');
            uploadStatus.innerHTML = `<i class="fas fa-spinner fa-spin"></i> 正在上傳 ${file.name}...`;
            uploadStatus.style.color = '#ffc107';
            
            const result = await ApiClient.uploadArchive(file);
            
            if (result.status === 'success') {
                await this.handleUploadSuccess(result, file);
            } else {
                throw new Error(result.message || '上傳失敗');
            }
            
        } catch (error) {
            await this.handleUploadError(error, file);
        } finally {
            this.isUploading = false;
            uploadZone.classList.remove('uploading');
        }
    }
    
    /**
     * 驗證檔案
     * @param {File} file - 要驗證的檔案
     * @returns {boolean} 是否通過驗證
     */
    validateFile(file) {
        // 檢查檔案格式
        if (!Utils.validateFileType(file, this.allowedTypes)) {
            StatusManager.showToast(
                `不支援的檔案格式！請上傳 ${this.allowedTypes.join('、')} 檔案。`,
                'error'
            );
            this.isUploading = false;
            return false;
        }
        
        // 檢查檔案大小
        if (!Utils.validateFileSize(file, this.maxSizeMB)) {
            const fileSizeMB = (file.size / (1024 * 1024)).toFixed(1);
            StatusManager.showToast(
                `檔案太大！最大支援 ${this.maxSizeMB}MB，您的檔案為 ${fileSizeMB}MB`,
                'error'
            );
            this.isUploading = false;
            return false;
        }
        
        return true;
    }
    
    /**
     * 處理上傳成功
     * @param {Object} result - 上傳結果
     * @param {File} file - 上傳的檔案
     */
    async handleUploadSuccess(result, file) {
        const { uploadStatus } = this.elements;
        
        // 上傳和解壓縮成功
        const extractDir = result.extraction_result?.extract_dir || result.extracted_path;
        
        // 轉換為Windows路徑格式
        const windowsPath = Utils.convertToWindowsPath(extractDir);
        
        uploadStatus.innerHTML = `<i class="fas fa-check"></i> 檔案上傳和解壓縮完成！解壓到: ${windowsPath}`;
        uploadStatus.style.color = '#28a745';
        
        // 自動設定資料夾路徑（Windows格式）
        DOMManager.setValue('folderPath', windowsPath);

        // 重置之前的處理結果
        if (typeof mainController !== 'undefined' && mainController.resetProcessingResults) {
            mainController.resetProcessingResults();
        }

        // 清空檔案選擇器
        this.elements.fileInput.value = '';

        // 🎯 啟動倒數計時功能（對應 REF 版本的 startCountdownTimer）
        this.tryStartCountdown(windowsPath);
    }

    /**
     * 嘗試啟動倒數計時功能
     * @param {string} windowsPath - Windows格式的資料夾路徑
     */
    tryStartCountdown(windowsPath) {
        // 直接檢查並啟動
        if (this.attemptCountdownStart(windowsPath)) return;
        
        // 延遲重試一次
        setTimeout(() => {
            if (!this.attemptCountdownStart(windowsPath)) {
                StatusManager.showToast('檔案上傳成功！請手動執行處理流程', 'success');
                setTimeout(() => DOMManager.hide('uploadStatus'), 5000);
            }
        }, 1000);
    }

    /**
     * 嘗試啟動倒數計時
     * @param {string} windowsPath - Windows格式的資料夾路徑
     * @returns {boolean} 是否成功啟動
     */
    attemptCountdownStart(windowsPath) {
        if (typeof countdownModal !== 'undefined' && countdownModal && typeof countdownModal.startCountdown === 'function') {
            countdownModal.startCountdown(windowsPath);
            return true;
        }
        
        if (typeof CountdownModal !== 'undefined') {
            window.countdownModal = new CountdownModal();
            countdownModal.startCountdown(windowsPath);
            return true;
        }
        
        return false;
    }
    
    /**
     * 處理上傳錯誤
     * @param {Error} error - 錯誤對象
     * @param {File} file - 上傳的檔案
     */
    async handleUploadError(error, file) {
        const { uploadStatus } = this.elements;
        
        console.error('檔案上傳失敗:', error);
        
        if (error.message && error.message.includes('duplicate_upload')) {
            await this.handleDuplicateUploadError(error, file);
            return;
        }
        
        uploadStatus.innerHTML = `<i class="fas fa-times"></i> 上傳失敗: ${error.message}`;
        uploadStatus.style.color = '#e74c3c';
        StatusManager.showToast(`上傳失敗: ${error.message}`, 'error');
    }
    
    /**
     * 處理重複上傳錯誤
     * @param {Error} error - 錯誤對象
     * @param {File} file - 上傳的檔案
     */
    async handleDuplicateUploadError(error, file) {
        const { uploadStatus, uploadZone } = this.elements;
        
        uploadZone.classList.remove('uploading');
        
        const errorData = JSON.parse(error.message);
        const duplicateInfo = errorData.duplicate_info || {};
        const remainingTime = Math.ceil(duplicateInfo.remaining_wait_time || 0);
        
        // 顯示友好的重複上傳訊息
        uploadStatus.innerHTML = `
            <div style="text-align: left;">
                <div style="margin-bottom: 10px;">
                    <i class="fas fa-exclamation-triangle" style="color: #ffc107;"></i>
                    <strong>檔案重複上傳</strong>
                </div>
                <div style="font-size: 11px; line-height: 1.4; margin-bottom: 10px;">
                    檔案 <strong>${file.name}</strong> 在 ${remainingTime} 秒內已上傳過。<br>
                    為避免重複處理，請等待 ${remainingTime} 秒後重試，或清除快取後立即重新上傳。
                </div>
                <div style="display: flex; gap: 8px;">
                    <button onclick="fileUpload.clearDuplicateCache('${file.name}')" 
                            style="padding: 4px 8px; font-size: 10px; background: #dc3545; color: white; border: none; border-radius: 3px; cursor: pointer;">
                        清除快取並重試
                    </button>
                    <button onclick="fileUpload.hideDuplicateError()" 
                            style="padding: 4px 8px; font-size: 10px; background: #6c757d; color: white; border: none; border-radius: 3px; cursor: pointer;">
                        知道了
                    </button>
                </div>
            </div>
        `;
        uploadStatus.style.color = '#856404';
        uploadStatus.style.background = '#fff3cd';
        uploadStatus.style.border = '1px solid #ffeaa7';
        uploadStatus.style.padding = '12px';
        uploadStatus.style.borderRadius = '6px';
        
    }
    
    /**
     * 清除重複快取並重試上傳
     * @param {string} fileName - 檔案名稱
     */
    async clearDuplicateCache(fileName) {
        try {
            const { uploadStatus } = this.elements;
            uploadStatus.innerHTML = `<i class="fas fa-spinner fa-spin"></i> 正在清除快取...`;
            uploadStatus.style.color = '#ffc107';
            
            const result = await ApiClient.clearDuplicateCache();
            
            uploadStatus.innerHTML = `<i class="fas fa-check"></i> 快取已清除，請重新選擇檔案上傳`;
            uploadStatus.style.color = '#28a745';
            
            // 清空檔案選擇器
            this.elements.fileInput.value = '';
            
            setTimeout(() => {
                this.hideDuplicateError();
            }, 3000);
            
        } catch (error) {
            console.error('清除快取失敗:', error);
            const { uploadStatus } = this.elements;
            uploadStatus.innerHTML = `<i class="fas fa-times"></i> 清除失敗: ${error.message}`;
            uploadStatus.style.color = '#e74c3c';
        }
    }
    
    /**
     * 隱藏重複上傳錯誤
     */
    hideDuplicateError() {
        const { uploadStatus } = this.elements;
        DOMManager.hide('uploadStatus');
        uploadStatus.style.background = '';
        uploadStatus.style.border = '';
        uploadStatus.style.padding = '';
        
        this.isUploading = false;
    }
}

// 創建全局實例
const fileUpload = new FileUploadComponent();

// 導出模組
if (typeof module !== 'undefined' && module.exports) {
    module.exports = FileUploadComponent;
} else if (typeof window !== 'undefined') {
    window.FileUploadComponent = FileUploadComponent;
    window.fileUpload = fileUpload;
}
