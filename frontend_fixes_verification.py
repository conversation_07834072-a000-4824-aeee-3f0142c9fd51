#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
前端修復驗證腳本
驗證所有前端頁面問題的修復效果
"""

import os
import sys
import json
import requests
import time
from pathlib import Path
from datetime import datetime

def check_file_exists(file_path, description):
    """檢查檔案是否存在"""
    if os.path.exists(file_path):
        print(f"[OK] {description}: 檔案存在 - {file_path}")
        return True
    else:
        print(f"[NG] {description}: 檔案不存在 - {file_path}")
        return False

def check_url_response(url, description, timeout=5):
    """檢查 URL 回應狀態"""
    try:
        response = requests.get(url, timeout=timeout)
        if response.status_code == 200:
            print(f"[OK] {description}: 回應正常 ({response.status_code}) - {url}")
            return True, response
        else:
            print(f"[WARN] {description}: 回應異常 ({response.status_code}) - {url}")
            return False, response
    except requests.exceptions.RequestException as e:
        print(f"[NG] {description}: 連線失敗 - {url} - {str(e)}")
        return False, None

def main():
    print("=" * 60)
    print("前端修復驗證報告")
    print(f"時間: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 60)
    
    verification_results = {}
    
    # 1. 資料庫管理頁面修復驗證
    print("\n1. 資料庫管理頁面修復驗證")
    print("-" * 40)
    
    # 檢查資料庫檔案
    db_files = [
        ("data/email_inbox.db", "主要資料庫檔案"),
        ("data/eqc_task_status.db", "EQC 任務狀態資料庫"),
    ]
    
    db_check_results = []
    for db_file, desc in db_files:
        result = check_file_exists(db_file, desc)
        db_check_results.append(result)
    
    verification_results['database_files'] = all(db_check_results)
    
    # 檢查資料庫管理頁面
    db_urls = [
        ("http://localhost:5000/monitoring/database-manager", "資料庫管理頁面"),
        ("http://localhost:5000/api/database/info", "資料庫資訊 API"),
    ]
    
    db_url_results = []
    for url, desc in db_urls:
        success, _ = check_url_response(url, desc)
        db_url_results.append(success)
    
    verification_results['database_urls'] = any(db_url_results)  # 至少一個成功即可
    
    # 2. 監控儀表板修復驗證
    print("\n2. 監控儀表板修復驗證")
    print("-" * 40)
    
    monitoring_urls = [
        ("http://localhost:5000/monitoring/dashboard", "監控儀表板"),
        ("http://localhost:5000/monitoring/health", "健康檢查頁面"),
        ("http://localhost:5000/api/status", "系統狀態 API"),
    ]
    
    monitoring_results = []
    for url, desc in monitoring_urls:
        success, _ = check_url_response(url, desc)
        monitoring_results.append(success)
    
    verification_results['monitoring'] = any(monitoring_results)
    
    # 3. FastAPI UI 介面修復驗證
    print("\n3. FastAPI UI 介面修復驗證")
    print("-" * 40)
    
    # 檢查模板檔案
    ui_files = [
        ("src/presentation/web/templates/ft_eqc_grouping_ui_modular.html", "FastAPI UI 模板"),
        ("frontend/shared/static/css/variables.css", "CSS 變數檔案"),
        ("frontend/shared/static/css/base.css", "基礎 CSS 檔案"),
    ]
    
    ui_file_results = []
    for file_path, desc in ui_files:
        result = check_file_exists(file_path, desc)
        ui_file_results.append(result)
    
    verification_results['ui_files'] = all(ui_file_results)
    
    # 檢查 FastAPI UI URL
    fastapi_urls = [
        ("http://localhost:8010/ui", "FastAPI UI 頁面"),
        ("http://localhost:8010/", "FastAPI 根路由"),
        ("http://localhost:8010/health", "FastAPI 健康檢查"),
    ]
    
    fastapi_results = []
    for url, desc in fastapi_urls:
        success, _ = check_url_response(url, desc)
        fastapi_results.append(success)
    
    verification_results['fastapi_ui'] = any(fastapi_results)
    
    # 4. FT-Summary UI 按鈕修復驗證
    print("\n4. FT-Summary UI 按鈕修復驗證")
    print("-" * 40)
    
    # 檢查 URL 配置檔案
    config_files = [
        ("frontend/shared/static/js/utils/url-config.js", "URL 配置檔案"),
        ("frontend/shared/templates/components/sidebar.html", "側邊欄模板"),
    ]
    
    config_file_results = []
    for file_path, desc in config_files:
        result = check_file_exists(file_path, desc)
        config_file_results.append(result)
    
    verification_results['config_files'] = all(config_file_results)
    
    # 檢查 FT-Summary 相關檔案
    ft_summary_files = [
        ("frontend/analytics/static/js/ft-summary-processor.js", "FT Summary 處理器"),
        ("frontend/analytics/templates/dashboard.html", "Analytics 儀表板"),
    ]
    
    ft_summary_results = []
    for file_path, desc in ft_summary_files:
        result = check_file_exists(file_path, desc)
        ft_summary_results.append(result)
    
    verification_results['ft_summary_files'] = all(ft_summary_results)
    
    # 5. 整體連通性測試
    print("\n5. 整體連通性測試")
    print("-" * 40)
    
    all_urls = [
        ("http://localhost:5000/", "Flask 主服務"),
        ("http://localhost:8010/", "FastAPI 主服務"),
        ("http://localhost:5000/email/inbox", "郵件收件匣"),
        ("http://localhost:5000/monitoring/dashboard", "監控儀表板"),
    ]
    
    connectivity_results = []
    for url, desc in all_urls:
        success, _ = check_url_response(url, desc)
        connectivity_results.append(success)
    
    verification_results['connectivity'] = connectivity_results
    
    # 總結報告
    print("\n" + "=" * 60)
    print("修復驗證總結")
    print("=" * 60)
    
    total_checks = 0
    passed_checks = 0
    
    for category, result in verification_results.items():
        if category == 'connectivity':
            total_checks += len(result)
            passed_checks += sum(result)
            success_rate = f"{sum(result)}/{len(result)}"
        else:
            total_checks += 1
            if result:
                passed_checks += 1
            success_rate = "[OK]" if result else "[NG]"
        
        print(f"{category:20}: {success_rate}")
    
    overall_success_rate = (passed_checks / total_checks) * 100
    print(f"\n總體成功率: {passed_checks}/{total_checks} ({overall_success_rate:.1f}%)")
    
    # 生成 JSON 報告
    report = {
        "timestamp": datetime.now().isoformat(),
        "verification_results": verification_results,
        "summary": {
            "total_checks": total_checks,
            "passed_checks": passed_checks,
            "success_rate": overall_success_rate
        }
    }
    
    report_file = "frontend_fixes_verification_report.json"
    with open(report_file, 'w', encoding='utf-8') as f:
        json.dump(report, f, ensure_ascii=False, indent=2)
    
    print(f"\n詳細報告已儲存至: {report_file}")
    
    # 修復建議
    print("\n修復建議:")
    print("-" * 40)
    
    if not verification_results.get('database_files', False):
        print("• 資料庫檔案: 請確認資料庫檔案在 data/ 目錄中")
    
    if not verification_results.get('ui_files', False):
        print("• UI 檔案: 請確認前端靜態檔案完整")
    
    if not any(verification_results.get('connectivity', [])):
        print("• 服務連接: 請啟動 Flask (5000) 和 FastAPI (8010) 服務")
    else:
        print("• 大部分修復已生效，建議進行手動測試確認")
    
    return overall_success_rate > 70

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)