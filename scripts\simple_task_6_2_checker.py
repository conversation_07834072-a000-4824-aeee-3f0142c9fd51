#!/usr/bin/env python3
"""
Task 6.2 - Simple Path and Link Checker (Windows Compatible)

基於 .kiro 配置中的 Task 6.2 要求，執行路徑和連結檢查驗證
Windows 兼容版本，避免 Unicode 輸出問題
"""

import os
import sys
import re
import json
import requests
import threading
import time
from pathlib import Path
from datetime import datetime
from urllib.parse import urljoin

# 設定編碼
if sys.platform.startswith('win'):
    os.environ['PYTHONIOENCODING'] = 'utf-8'

# 添加專案根目錄到 Python 路徑
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

class SimpleTask62Checker:
    """Task 6.2 簡化版路徑和連結檢查工具"""
    
    def __init__(self, project_root):
        self.project_root = Path(project_root)
        self.frontend_path = self.project_root / 'frontend'
        self.base_url = 'http://localhost:5000'
        self.session = requests.Session()
        self.session.timeout = 10
        
        self.results = {
            'task_info': {
                'task_id': '6.2',
                'task_name': 'Path and Link Check',
                'timestamp': datetime.now().isoformat(),
                'status': 'in_progress'
            },
            'checks': {
                'file_system': {'status': 'pending', 'details': []},
                'static_resources': {'status': 'pending', 'details': []},
                'routes': {'status': 'pending', 'details': []},
                'links': {'status': 'pending', 'details': []}
            },
            'summary': {
                'total_issues': 0,
                'critical_issues': 0,
                'warnings': 0
            }
        }
        
        self.modules = ['email', 'analytics', 'eqc', 'tasks', 'monitoring', 'file_management']
        self.critical_routes = [
            '/',
            '/email',
            '/analytics',
            '/eqc', 
            '/tasks',
            '/monitoring',
            '/files'
        ]

    def run_checks(self):
        """執行所有檢查"""
        print("="*60)
        print("Task 6.2 - Path and Link Checker")
        print("="*60)
        
        try:
            # 1. 檢查檔案系統
            print("\n1. Checking file system integrity...")
            self.check_file_system()
            
            # 2. 檢查靜態資源
            print("\n2. Checking static resources...")
            self.check_static_resources()
            
            # 3. 啟動Flask測試路由
            print("\n3. Testing Flask routes...")
            app_thread = self.start_flask_app()
            time.sleep(3)
            
            try:
                self.test_routes()
                self.check_links()
            finally:
                print("   Stopping test server...")
            
            # 4. 生成報告
            print("\n4. Generating report...")
            self.generate_report()
            
            print("\nTask 6.2 check completed!")
            return self.results
            
        except Exception as e:
            print(f"Error during check: {e}")
            self.results['task_info']['status'] = 'failed'
            return self.results

    def check_file_system(self):
        """檢查檔案系統完整性"""
        issues = []
        
        # 檢查關鍵目錄
        required_dirs = [
            'frontend',
            'frontend/shared',
            'frontend/shared/templates',
            'frontend/shared/static'
        ]
        
        for module in self.modules:
            required_dirs.extend([
                f'frontend/{module}',
                f'frontend/{module}/routes',
                f'frontend/{module}/static',
                f'frontend/{module}/templates'
            ])
        
        for dir_path in required_dirs:
            full_path = self.project_root / dir_path
            if not full_path.exists():
                issues.append(f"Missing directory: {dir_path}")
        
        # 檢查關鍵檔案
        required_files = [
            'frontend/app.py',
            'frontend/config.py',
            'frontend/shared/templates/base.html'
        ]
        
        # 檢查實際的路由檔案名稱
        route_files = {
            'tasks': 'task_routes.py',
            'file_management': 'file_routes.py'  
        }
        
        for module in self.modules:
            route_filename = route_files.get(module, f'{module}_routes.py')
            required_files.append(f'frontend/{module}/routes/{route_filename}')
        
        for file_path in required_files:
            full_path = self.project_root / file_path
            if not full_path.exists():
                issues.append(f"Missing file: {file_path}")
        
        self.results['checks']['file_system']['details'] = issues
        self.results['checks']['file_system']['status'] = 'pass' if not issues else 'issues'
        
        print(f"   Checked directories and files")
        if issues:
            print(f"   Found {len(issues)} issues")
            for issue in issues[:5]:  # 只顯示前5個
                print(f"     - {issue}")

    def check_static_resources(self):
        """檢查靜態資源"""
        issues = []
        
        # 檢查每個模組的靜態資源目錄
        for module in self.modules:
            static_dir = self.frontend_path / module / 'static'
            if static_dir.exists():
                css_dir = static_dir / 'css'
                js_dir = static_dir / 'js'
                
                if not css_dir.exists():
                    issues.append(f"Missing CSS directory: {module}/static/css")
                
                if not js_dir.exists():
                    issues.append(f"Missing JS directory: {module}/static/js")
        
        # 檢查共享靜態資源
        shared_static = self.frontend_path / 'shared' / 'static'
        if shared_static.exists():
            critical_shared = [
                'css/base.css',
                'css/global.css',
                'js/main.js'
            ]
            
            for resource in critical_shared:
                if not (shared_static / resource).exists():
                    issues.append(f"Missing shared resource: {resource}")
        
        self.results['checks']['static_resources']['details'] = issues
        self.results['checks']['static_resources']['status'] = 'pass' if not issues else 'issues'
        
        print(f"   Checked static resource structure")
        if issues:
            print(f"   Found {len(issues)} issues")

    def start_flask_app(self):
        """啟動Flask應用"""
        def run_app():
            try:
                from frontend.app import create_app
                app = create_app('development')
                app.run(host='127.0.0.1', port=5000, debug=False, use_reloader=False, threaded=True)
            except Exception as e:
                print(f"   Failed to start Flask app: {e}")
        
        app_thread = threading.Thread(target=run_app, daemon=True)
        app_thread.start()
        
        # 等待服務器啟動
        for i in range(10):
            try:
                response = self.session.get(f"{self.base_url}/", timeout=2)
                if response.status_code in [200, 302, 404]:
                    print("   Test server started successfully")
                    return app_thread
            except:
                time.sleep(1)
        
        print("   Warning: Test server may not have started properly")
        return app_thread

    def test_routes(self):
        """測試關鍵路由"""
        issues = []
        working_routes = 0
        
        for route in self.critical_routes:
            try:
                url = urljoin(self.base_url, route)
                response = self.session.get(url, timeout=5)
                
                if response.status_code == 200:
                    working_routes += 1
                    print(f"   OK: {route} -> {response.status_code}")
                elif response.status_code in [301, 302]:
                    working_routes += 1
                    print(f"   REDIRECT: {route} -> {response.status_code}")
                elif response.status_code == 404:
                    issues.append(f"404 Error: {route}")
                    print(f"   ERROR: {route} -> {response.status_code} (404)")
                else:
                    issues.append(f"HTTP {response.status_code}: {route}")
                    print(f"   WARNING: {route} -> {response.status_code}")
                    
            except Exception as e:
                issues.append(f"Connection error: {route} - {str(e)}")
                print(f"   ERROR: {route} -> Connection failed")
        
        self.results['checks']['routes']['details'] = issues
        self.results['checks']['routes']['status'] = 'pass' if not issues else 'issues'
        
        print(f"   Tested {len(self.critical_routes)} routes")
        print(f"   Working: {working_routes}, Issues: {len(issues)}")

    def check_links(self):
        """檢查內部連結"""
        issues = []
        
        try:
            # 獲取主頁並檢查連結
            response = self.session.get(self.base_url, timeout=5)
            if response.status_code in [200, 302]:
                # 簡單的連結檢查邏輯
                content = response.text
                
                # 查找可能的內部連結模式
                link_patterns = [
                    r'href=["\']([^"\']*)["\']',
                    r'action=["\']([^"\']*)["\']'
                ]
                
                found_links = []
                for pattern in link_patterns:
                    matches = re.findall(pattern, content)
                    found_links.extend(matches)
                
                # 測試內部連結
                internal_links = [link for link in found_links if link.startswith('/') and len(link) > 1]
                
                for link in internal_links[:10]:  # 限制檢查數量
                    try:
                        link_url = urljoin(self.base_url, link)
                        link_response = self.session.get(link_url, timeout=3)
                        if link_response.status_code == 404:
                            issues.append(f"Broken internal link: {link}")
                    except:
                        issues.append(f"Failed to check link: {link}")
                        
        except Exception as e:
            issues.append(f"Failed to check internal links: {str(e)}")
        
        self.results['checks']['links']['details'] = issues
        self.results['checks']['links']['status'] = 'pass' if not issues else 'issues'
        
        print(f"   Checked internal links")
        if issues:
            print(f"   Found {len(issues)} link issues")

    def generate_report(self):
        """生成報告"""
        # 計算統計
        total_issues = 0
        critical_issues = 0
        
        for check_name, check_data in self.results['checks'].items():
            issues_count = len(check_data['details'])
            total_issues += issues_count
            
            # 404錯誤和missing files視為critical
            critical_count = len([issue for issue in check_data['details'] 
                                if '404' in issue or 'Missing file' in issue or 'Missing directory' in issue])
            critical_issues += critical_count
        
        self.results['summary']['total_issues'] = total_issues
        self.results['summary']['critical_issues'] = critical_issues
        self.results['summary']['warnings'] = total_issues - critical_issues
        
        # 確定整體狀態
        if critical_issues == 0 and total_issues == 0:
            self.results['task_info']['status'] = 'completed'
        elif critical_issues == 0:
            self.results['task_info']['status'] = 'completed_with_warnings'
        else:
            self.results['task_info']['status'] = 'issues_found'
        
        # 保存報告
        report_file = self.project_root / 'task_6_2_simple_check_report.json'
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(self.results, f, indent=2, ensure_ascii=False)
        
        print(f"   Report saved to: {report_file}")
        
        # 輸出摘要
        print("\n" + "="*60)
        print("TASK 6.2 CHECK SUMMARY")
        print("="*60)
        print(f"Status: {self.results['task_info']['status']}")
        print(f"Total Issues: {total_issues}")
        print(f"Critical Issues: {critical_issues}")
        print(f"Warnings: {total_issues - critical_issues}")
        
        print(f"\nDETAILS:")
        for check_name, check_data in self.results['checks'].items():
            status = check_data['status']
            issue_count = len(check_data['details'])
            print(f"  {check_name}: {status} ({issue_count} issues)")
            
            if issue_count > 0 and issue_count <= 3:
                for issue in check_data['details']:
                    print(f"    - {issue}")
        
        if self.results['task_info']['status'] == 'completed':
            print(f"\nSUCCESS: Task 6.2 requirements met!")
            print(f"- All internal links working")
            print(f"- Static resources validated")
            print(f"- No 404 errors found")
            print(f"- Functionality maintained (Requirement 1.2)")
        else:
            print(f"\nACTION REQUIRED: Issues found that need attention")
            print(f"Please review the report and fix critical issues before proceeding.")
        
        print("="*60)


def main():
    """主函數"""
    try:
        project_root = Path(__file__).parent.parent
        checker = SimpleTask62Checker(project_root)
        results = checker.run_checks()
        
        # 返回檢查是否成功
        return results['task_info']['status'] in ['completed', 'completed_with_warnings']
        
    except Exception as e:
        print(f"Checker failed: {e}")
        return False


if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)