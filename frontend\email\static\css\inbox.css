/* 郵件收件夾專用樣式 */

/* 主容器 */
.inbox-container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 20px;
    background-color: #f8f9fa;
    min-height: 100vh;
}

/* 頂部導航 */
.inbox-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: white;
    padding: 20px;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    margin-bottom: 20px;
}

.header-left h1 {
    margin: 0 0 10px 0;
    color: #333;
    font-size: 24px;
}

.stats-summary {
    display: flex;
    gap: 20px;
    font-size: 14px;
    color: #666;
}

.stat-item {
    display: flex;
    align-items: center;
    gap: 5px;
}

.stat-item span {
    font-weight: bold;
    color: #007bff;
}

.header-right {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
    align-items: center;
}

/* 按鈕樣式 */
.btn {
    display: inline-flex;
    align-items: center;
    gap: 5px;
    padding: 8px 16px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
    transition: all 0.2s;
    text-decoration: none;
}

.btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0,0,0,0.2);
}

.btn-primary {
    background-color: #007bff;
    color: white;
}

.btn-primary:hover {
    background-color: #0056b3;
}

.btn-secondary {
    background-color: #6c757d;
    color: white;
}

.btn-secondary:hover {
    background-color: #545b62;
}

.btn-danger {
    background-color: #dc3545;
    color: white;
}

.btn-danger:hover {
    background-color: #c82333;
}

.btn-success {
    background-color: #28a745;
    color: white;
}

.btn-success:hover {
    background-color: #218838;
}

.btn-info {
    background-color: #17a2b8;
    color: white;
}

.btn-info:hover {
    background-color: #138496;
}

.btn-warning {
    background-color: #ffc107;
    color: #212529;
}

.btn-warning:hover {
    background-color: #e0a800;
}

.btn-close {
    background: none;
    border: none;
    font-size: 16px;
    cursor: pointer;
    color: #666;
}

.btn-sm {
    padding: 4px 8px;
    font-size: 12px;
}

/* 同步狀態 */
.sync-status {
    background: #e3f2fd;
    border: 1px solid #2196f3;
    border-radius: 4px;
    padding: 10px;
    margin-bottom: 20px;
}

.sync-status.hidden {
    display: none;
}

.status-content {
    display: flex;
    align-items: center;
    gap: 10px;
}

.status-progress {
    flex: 1;
    height: 4px;
    background: #ddd;
    border-radius: 2px;
    overflow: hidden;
}

.progress-bar {
    height: 100%;
    background: #2196f3;
    border-radius: 2px;
    animation: progress-animation 2s infinite;
}

@keyframes progress-animation {
    0% { width: 0%; }
    50% { width: 100%; }
    100% { width: 0%; }
}

/* 寄件者分頁 */
.sender-tabs {
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    margin-bottom: 20px;
    overflow: hidden;
}

.tabs-container {
    display: flex;
    overflow-x: auto;
    padding: 10px;
    gap: 5px;
}

.tab-btn {
    padding: 10px 16px;
    border: none;
    background: #f8f9fa;
    cursor: pointer;
    border-radius: 4px;
    white-space: nowrap;
    transition: all 0.2s;
    font-size: 14px;
}

.tab-btn:hover {
    background: #e9ecef;
}

.tab-btn.active {
    background: #007bff;
    color: white;
}

.sender-count {
    font-weight: bold;
}

/* 搜尋和篩選 */
.search-filter-bar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: white;
    padding: 15px;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    margin-bottom: 20px;
    flex-wrap: wrap;
    gap: 15px;
}

.search-container {
    display: flex;
    gap: 10px;
    flex: 1;
    max-width: 400px;
}

.search-container input {
    flex: 1;
    padding: 8px 12px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 14px;
}

.filter-container {
    display: flex;
    gap: 15px;
    align-items: center;
}

.filter-container select {
    padding: 8px 12px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 14px;
}

.checkbox-label {
    display: flex;
    align-items: center;
    gap: 5px;
    cursor: pointer;
}

/* 郵件列表 */
.email-list-container {
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    overflow: hidden;
    margin-bottom: 20px;
}

.email-list-header {
    display: flex;
    align-items: center;
    gap: 15px;
    padding: 15px;
    background: #f8f9fa;
    border-bottom: 1px solid #dee2e6;
    font-weight: bold;
    color: #495057;
    font-size: 14px;
}

.email-list-header .header-checkbox {
    width: 40px;
    text-align: center;
    flex-shrink: 0;
}

.email-list-header .header-sender {
    width: 200px;
    flex-shrink: 0;
}

.email-list-header .header-subject {
    flex: 1;
    min-width: 200px;
}

.email-list-header .header-time {
    width: 120px;
    flex-shrink: 0;
}

.email-list-header .header-status {
    width: 80px;
    flex-shrink: 0;
}

.email-list-header .header-actions {
    width: 100px;
    flex-shrink: 0;
}

.email-list {
    max-height: 600px;
    overflow-y: auto;
    display: flex;
    flex-direction: column;
}

.email-item {
    display: flex !important;
    align-items: center;
    gap: 15px;
    padding: 15px;
    border-bottom: 1px solid #f1f3f5;
    cursor: pointer;
    transition: background-color 0.2s;
    min-height: 60px;
    width: 100% !important;
    flex-shrink: 0;
    float: none !important;
    clear: both !important;
    position: static !important;
    left: auto !important;
    margin-left: 0 !important;
    margin-right: 0 !important;
}

.email-item:hover {
    background-color: #f8f9fa;
}

.email-item.unread {
    background-color: #fff3cd;
    border-left: 4px solid #ffc107;
}

.email-item.selected {
    background-color: #e3f2fd;
    border-left: 4px solid #2196f3;
}

.email-checkbox {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    flex-shrink: 0;
}

.email-sender {
    font-weight: 500;
    color: #007bff;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    width: 200px;
    flex-shrink: 0;
}

.email-subject {
    font-weight: 500;
    color: #333;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    flex: 1;
    min-width: 200px;
}

.email-time {
    color: #666;
    font-size: 12px;
    width: 120px;
    flex-shrink: 0;
    text-align: right;
}

.email-attachment {
    display: flex;
    align-items: center;
    justify-content: center;
    color: #666;
    width: 80px;
    flex-shrink: 0;
    text-align: center;
}

.email-actions {
    display: flex;
    gap: 5px;
    opacity: 0;
    transition: opacity 0.2s;
    width: 100px;
    flex-shrink: 0;
    justify-content: flex-end;
}

.email-item:hover .email-actions {
    opacity: 1;
}

.action-btn {
    padding: 4px 8px;
    font-size: 12px;
    border: none;
    border-radius: 3px;
    cursor: pointer;
    transition: all 0.2s;
}

.action-btn:hover {
    transform: scale(1.05);
}


.action-btn.delete {
    background: #dc3545;
    color: white;
}

.action-btn.process {
    background: #28a745;
    color: white;
}

/* 載入中狀態 */
.loading-placeholder {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 60px;
    color: #666;
}

.loading-spinner {
    width: 40px;
    height: 40px;
    border: 4px solid #f3f3f3;
    border-top: 4px solid #007bff;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 20px;
}

.loading-spinner.large {
    width: 60px;
    height: 60px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 郵件詳情 */
.email-detail {
    position: fixed;
    top: 0;
    right: 0;
    width: 500px;
    height: 100vh;
    background: white;
    box-shadow: -2px 0 10px rgba(0,0,0,0.1);
    z-index: 1000;
    overflow-y: auto;
    transform: translateX(100%);
    transition: transform 0.3s ease;
}

.email-detail.show {
    transform: translateX(0);
}

.email-detail.hidden {
    display: none;
}

.detail-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px;
    border-bottom: 1px solid #dee2e6;
    background: #f8f9fa;
}

.detail-header h3 {
    margin: 0;
    font-size: 18px;
    color: #333;
}

.detail-meta {
    padding: 20px;
    background: #fff;
}

.meta-item {
    display: flex;
    margin-bottom: 10px;
    gap: 10px;
}

.meta-item label {
    font-weight: bold;
    color: #666;
    min-width: 80px;
}

.meta-item span {
    color: #333;
}

.detail-content {
    padding: 20px;
}

.email-body {
    white-space: pre-wrap;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    line-height: 1.6;
    color: #333;
    background: #f8f9fa;
    padding: 15px;
    border-radius: 4px;
    border: 1px solid #dee2e6;
}

/* 批量操作 */
.batch-actions {
    position: fixed;
    bottom: 20px;
    left: 50%;
    transform: translateX(-50%);
    background: white;
    padding: 15px;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0,0,0,0.2);
    display: flex;
    align-items: center;
    gap: 15px;
    z-index: 1000;
}

.batch-actions.hidden {
    display: none;
}

.batch-info {
    color: #666;
    font-size: 14px;
}

.batch-buttons {
    display: flex;
    gap: 10px;
}

/* 分頁 */
.pagination-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: white;
    padding: 15px;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.pagination-info {
    color: #666;
    font-size: 14px;
}

.pagination-controls {
    display: flex;
    align-items: center;
    gap: 10px;
}

.page-numbers {
    display: flex;
    gap: 5px;
}

.page-number {
    padding: 8px 12px;
    border: 1px solid #ddd;
    background: white;
    cursor: pointer;
    border-radius: 4px;
    transition: all 0.2s;
}

.page-number:hover {
    background: #f8f9fa;
}

.page-number.active {
    background: #007bff;
    color: white;
    border-color: #007bff;
}

/* 載入中遮罩 */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0,0,0,0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 2000;
}

.loading-overlay.hidden {
    display: none;
}

.loading-content {
    background: white;
    padding: 40px;
    border-radius: 8px;
    text-align: center;
    box-shadow: 0 4px 12px rgba(0,0,0,0.2);
}

/* 對話框 */
.dialog-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0,0,0,0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 2000;
}

.dialog-overlay.hidden {
    display: none;
}

.dialog-content {
    background: white;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0,0,0,0.2);
    max-width: 500px;
    width: 90%;
}

.dialog-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px;
    border-bottom: 1px solid #dee2e6;
}

.dialog-header h3 {
    margin: 0;
    font-size: 18px;
    color: #333;
}

.dialog-body {
    padding: 20px;
}

.dialog-footer {
    display: flex;
    justify-content: flex-end;
    gap: 10px;
    padding: 20px;
    border-top: 1px solid #dee2e6;
}

/* 通知 */
.notification {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 3000;
    animation: slideInRight 0.3s ease;
}

.notification.hidden {
    display: none;
}

.notification-content {
    background: white;
    border: 1px solid #dee2e6;
    border-radius: 4px;
    padding: 15px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    display: flex;
    align-items: center;
    gap: 10px;
    max-width: 400px;
}

.notification.success .notification-content {
    border-left: 4px solid #28a745;
}

.notification.error .notification-content {
    border-left: 4px solid #dc3545;
}

.notification.warning .notification-content {
    border-left: 4px solid #ffc107;
}

.notification-close {
    background: none;
    border: none;
    font-size: 16px;
    cursor: pointer;
    color: #666;
    margin-left: auto;
}

@keyframes slideInRight {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

/* 郵件詳情頁面 */
.email-detail-page {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
    background-color: #f8f9fa;
    min-height: 100vh;
}

.email-info-card {
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    margin-bottom: 20px;
    overflow: hidden;
}

.email-header {
    padding: 20px;
    border-bottom: 1px solid #dee2e6;
}

.email-subject {
    margin: 0 0 15px 0;
    font-size: 24px;
    color: #333;
    word-wrap: break-word;
}

.email-meta {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 10px;
}

.meta-row {
    display: flex;
    gap: 10px;
}

.meta-label {
    font-weight: bold;
    color: #666;
    min-width: 80px;
}

.meta-value {
    color: #333;
    word-wrap: break-word;
}

/* 附件區域 */
.attachments-section {
    padding: 20px;
    border-bottom: 1px solid #dee2e6;
}

.attachments-section h3 {
    margin: 0 0 15px 0;
    color: #333;
}

.attachments-list {
    display: grid;
    gap: 10px;
}

.attachment-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px;
    background: #f8f9fa;
    border-radius: 4px;
    border: 1px solid #dee2e6;
}

.attachment-info {
    display: flex;
    flex-direction: column;
    gap: 5px;
}

.attachment-name {
    font-weight: 500;
    color: #333;
}

.attachment-size,
.attachment-type {
    font-size: 12px;
    color: #666;
}

.attachment-actions {
    display: flex;
    gap: 5px;
}

/* 郵件內容區域 */
.email-content-section {
    padding: 20px;
}

.email-content-section h3 {
    margin: 0 0 15px 0;
    color: #333;
}

.email-content {
    background: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 4px;
    padding: 15px;
    max-height: 400px;
    overflow-y: auto;
}

/* 處理狀態區域 */
.process-status-section {
    padding: 20px;
    border-top: 1px solid #dee2e6;
}

.process-status-section h3 {
    margin: 0 0 15px 0;
    color: #333;
}

.process-status-list {
    display: grid;
    gap: 15px;
}

.process-step {
    padding: 15px;
    border-radius: 4px;
    border: 1px solid #dee2e6;
}

.process-step.completed {
    background: #d4edda;
    border-color: #c3e6cb;
}

.process-step.failed {
    background: #f8d7da;
    border-color: #f5c6cb;
}

.process-step.processing {
    background: #fff3cd;
    border-color: #ffeaa7;
}

.step-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
}

.step-name {
    font-weight: 500;
    color: #333;
}

.step-status {
    font-size: 12px;
    padding: 2px 8px;
    border-radius: 12px;
    text-transform: uppercase;
}

.step-status {
    background: #6c757d;
    color: white;
}

.completed .step-status {
    background: #28a745;
}

.failed .step-status {
    background: #dc3545;
}

.processing .step-status {
    background: #ffc107;
    color: #212529;
}

.step-details {
    display: flex;
    gap: 15px;
    font-size: 12px;
    color: #666;
    margin-bottom: 10px;
}

.step-error {
    color: #dc3545;
}

.step-progress {
    display: flex;
    align-items: center;
    gap: 10px;
}

.progress-bar {
    flex: 1;
    height: 6px;
    background: #e9ecef;
    border-radius: 3px;
    overflow: hidden;
}

.progress-fill {
    height: 100%;
    background: #007bff;
    transition: width 0.3s ease;
}

.progress-text {
    font-size: 12px;
    color: #666;
    min-width: 40px;
}

/* 操作面板 */
.action-panel {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
}

.action-group {
    background: white;
    padding: 20px;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.action-group h4 {
    margin: 0 0 15px 0;
    color: #333;
    font-size: 16px;
}

.action-group .btn {
    display: block;
    width: 100%;
    margin-bottom: 10px;
    text-align: left;
}

.action-group .btn:last-child {
    margin-bottom: 0;
}

/* 響應式設計 */
@media (max-width: 768px) {
    .inbox-container {
        padding: 10px;
    }
    
    .inbox-header {
        flex-direction: column;
        gap: 15px;
        align-items: flex-start;
    }
    
    .header-right {
        width: 100%;
        justify-content: flex-end;
    }
    
    .search-filter-bar {
        flex-direction: column;
        align-items: stretch;
    }
    
    .search-container {
        max-width: none;
    }
    
    .email-list-header .header-sender,
    .email-sender {
        display: none;
    }
    
    .email-list-header .header-actions,
    .email-actions {
        width: 60px;
    }
    
    .sender-cell,
    .attachment-cell {
        display: none;
    }
    
    .email-detail {
        width: 100%;
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
    }
    
    .pagination-container {
        flex-direction: column;
        gap: 15px;
    }
}

@media (max-width: 480px) {
    .email-list-header .header-time,
    .email-time {
        display: none;
    }
    
    .email-list-header .header-attachment,
    .email-attachment {
        display: none;
    }
    
    .tabs-container {
        flex-wrap: wrap;
    }
    
    .tab-btn {
        flex: 1;
        min-width: 120px;
    }
}