# Tasks Module - 任務管理功能模組

## 概述

任務管理功能模組負責處理系統中的所有任務相關功能，包括任務儀表板、任務隊列管理、任務調度和任務監控。

**Vue.js 遷移準備**: 本模組已完成 Flask 藍圖重構，建立清晰的模組邊界和標準化 API 介面，與 Dramatiq/Celery 任務隊列系統完美整合，特別適合 Vue.js 的即時任務狀態更新需求。

## 功能特性

### 核心功能
- **任務儀表板** - 顯示任務執行狀態和統計資訊
- **任務隊列** - 管理和監控任務隊列狀態
- **任務調度** - 配置和管理定時任務
- **任務監控** - 即時監控任務執行進度和結果

### 任務類型
- **郵件處理任務** - 自動處理收到的郵件
- **數據分析任務** - 執行數據分析和報表生成
- **檔案處理任務** - 處理上傳的檔案和附件
- **EQC 處理任務** - 執行品質控制檢查
- **系統維護任務** - 執行系統清理和維護

## 目錄結構

```
tasks/
├── templates/               # HTML 模板
│   ├── task_dashboard.html  # 任務儀表板
│   ├── task_queue.html      # 任務隊列
│   └── task_scheduler.html  # 任務調度
├── static/                  # 靜態資源
│   ├── css/
│   │   └── tasks.css        # 任務管理樣式
│   ├── js/
│   │   ├── task-monitor.js  # 任務監控邏輯
│   │   ├── task-queue.js    # 任務隊列邏輯
│   │   └── tasks-api.js     # 任務 API
│   └── images/              # 任務相關圖片
├── components/              # 可重用組件
│   ├── task-card.html       # 任務卡片組件
│   ├── progress-bar.html    # 進度條組件
│   └── task-status.html     # 任務狀態組件
├── routes/                  # 路由處理
│   └── task_routes.py       # 任務路由
└── README.md                # 本檔案
```

## API 端點

### 任務儀表板
- `GET /tasks/dashboard` - 任務儀表板頁面
- `GET /api/tasks/stats` - 獲取任務統計資訊
- `GET /api/tasks/recent` - 獲取最近任務列表
- `GET /api/tasks/alerts` - 獲取任務警報

### 任務管理
- `GET /tasks/queue` - 任務隊列頁面
- `GET /api/tasks/list` - 獲取任務列表
- `GET /api/tasks/<id>` - 獲取任務詳情
- `POST /api/tasks/create` - 建立新任務
- `PUT /api/tasks/<id>/cancel` - 取消任務
- `PUT /api/tasks/<id>/retry` - 重試任務

### 任務調度
- `GET /tasks/scheduler` - 任務調度頁面
- `GET /api/tasks/schedules` - 獲取調度任務列表
- `POST /api/tasks/schedules` - 建立調度任務
- `PUT /api/tasks/schedules/<id>` - 更新調度任務
- `DELETE /api/tasks/schedules/<id>` - 刪除調度任務

### 任務監控
- `GET /api/tasks/monitor/status` - 獲取系統狀態
- `GET /api/tasks/monitor/queues` - 獲取隊列狀態
- `GET /api/tasks/monitor/workers` - 獲取工作者狀態
- `WebSocket /ws/tasks/monitor` - 即時任務監控

## 資料模型

### Task
- `id`: 任務唯一識別碼
- `name`: 任務名稱
- `type`: 任務類型 (email_processing, data_analysis, file_processing, eqc_processing, maintenance)
- `status`: 任務狀態 (pending, running, completed, failed, cancelled)
- `priority`: 優先級 (low, normal, high, urgent)
- `progress`: 執行進度 (0-100)
- `parameters`: 任務參數
- `result`: 執行結果
- `error_message`: 錯誤訊息
- `created_at`: 建立時間
- `started_at`: 開始時間
- `completed_at`: 完成時間
- `estimated_duration`: 預估執行時間

### TaskSchedule
- `id`: 調度識別碼
- `name`: 調度名稱
- `task_type`: 任務類型
- `cron_expression`: Cron 表達式
- `parameters`: 任務參數
- `is_active`: 是否啟用
- `last_run`: 最後執行時間
- `next_run`: 下次執行時間
- `created_at`: 建立時間

### TaskQueue
- `queue_name`: 隊列名稱
- `pending_count`: 待處理任務數
- `running_count`: 執行中任務數
- `completed_count`: 已完成任務數
- `failed_count`: 失敗任務數
- `worker_count`: 工作者數量
- `last_updated`: 最後更新時間

### Worker
- `worker_id`: 工作者識別碼
- `worker_name`: 工作者名稱
- `status`: 工作者狀態 (idle, busy, offline)
- `current_task`: 當前執行任務
- `processed_count`: 已處理任務數
- `last_heartbeat`: 最後心跳時間

## 任務隊列系統

### Dramatiq 整合
- **Redis 後端** - 使用 Redis 作為訊息代理
- **任務分發** - 自動分發任務到可用工作者
- **錯誤處理** - 自動重試和錯誤恢復機制
- **監控支援** - 提供詳細的執行統計和監控

### Celery 整合
- **分散式處理** - 支援多機器分散式任務處理
- **任務路由** - 根據任務類型路由到專用隊列
- **結果存儲** - 任務結果持久化存儲
- **定時任務** - 支援 Cron 風格的定時任務

## 任務類型詳細說明

### 郵件處理任務
- **郵件同步** - 從郵件伺服器同步新郵件
- **附件解析** - 解析和處理郵件附件
- **廠商分類** - 根據關鍵字自動分類郵件
- **數據提取** - 從附件中提取關鍵數據

### 數據分析任務
- **統計計算** - 計算各種業務統計指標
- **報表生成** - 生成 Excel 格式的分析報表
- **趨勢分析** - 分析數據趨勢和模式
- **異常檢測** - 檢測數據異常和品質問題

### 檔案處理任務
- **格式轉換** - CSV 轉 Excel，數據格式標準化
- **壓縮解壓** - 自動解壓縮上傳的檔案
- **數據驗證** - 驗證檔案格式和數據完整性
- **檔案清理** - 定期清理過期檔案

### EQC 處理任務
- **品質檢查** - 執行自動化品質檢查流程
- **合規驗證** - 驗證數據是否符合標準
- **結果分析** - 分析 EQC 測試結果
- **警報通知** - 發送品質異常警報

## 監控和警報

### 系統監控
- **隊列長度監控** - 監控各隊列的任務積壓情況
- **工作者狀態監控** - 監控工作者健康狀態
- **執行時間監控** - 監控任務執行時間和效能
- **錯誤率監控** - 監控任務失敗率和錯誤模式

### 警報機制
- **隊列積壓警報** - 隊列任務過多時發送警報
- **任務失敗警報** - 重要任務失敗時發送警報
- **工作者離線警報** - 工作者離線時發送警報
- **效能異常警報** - 執行時間異常時發送警報

## Vue.js 遷移準備

### 已完成的準備工作 ✅
- **模組邊界清晰**: 獨立的 Flask 藍圖，URL 前綴 `/tasks/`
- **API 標準化**: 統一的 REST API 回應格式，支援任務狀態和進度追蹤
- **即時監控準備**: WebSocket 支援，適合 Vue.js 的響應式即時更新
- **任務隊列整合**: 與 Dramatiq/Celery 系統完美整合，API 完全兼容

### Vue.js 遷移優勢
- **即時狀態更新**: Vue.js 的響應式特性完美適合任務狀態即時監控
- **隊列系統整合**: 與現有 Dramatiq/Celery 系統的 API 完全兼容
- **狀態管理**: 複雜的任務狀態適合 Vuex/Pinia 集中管理
- **進度顯示**: Vue.js 的響應式更新支援流暢的進度條動畫

### 未來 Vue.js 架構
```
tasks/ (Vue.js 版本)
├── components/              # Vue 組件
│   ├── TaskDashboard.vue    # 任務儀表板組件
│   ├── TaskCard.vue         # 任務卡片組件
│   ├── ProgressBar.vue      # 進度條組件
│   ├── TaskQueue.vue        # 任務隊列組件
│   └── TaskScheduler.vue    # 任務調度組件
├── views/                   # Vue 頁面
│   ├── DashboardView.vue    # 任務儀表板
│   ├── QueueView.vue        # 任務隊列頁面
│   └── SchedulerView.vue    # 任務調度頁面
├── store/                   # Vuex/Pinia 狀態管理
│   ├── tasks.js             # 任務狀態管理
│   ├── queues.js            # 隊列狀態管理
│   └── scheduler.js         # 調度狀態管理
├── composables/             # Vue 3 Composition API
│   ├── useTasks.js          # 任務邏輯複用
│   ├── useTaskMonitor.js    # 任務監控邏輯複用
│   └── useWebSocket.js      # WebSocket 邏輯複用
└── api/                     # API 服務
    └── tasksApi.js          # 任務 API 服務
```

## 開發注意事項

### 當前階段 (Flask)
- 與現有 Dramatiq 和 Celery 系統保持相容性
- 實作即時任務狀態更新 (WebSocket)
- 支援任務優先級和資源分配
- 確保任務執行的可靠性和容錯性
- 提供詳細的任務執行日誌和審計追蹤
- 支援任務的暫停、恢復和取消操作

### Vue.js 遷移準備
- 任務隊列 API 與 Vue.js 的整合考量
- 即時狀態更新的響應式效能優化
- WebSocket 與 Vue.js 生命週期的整合
- 大量任務數據的虛擬化渲染實現