/* 
 * 響應式設計模組
 * 包含各種螢幕尺寸的適配樣式
 */

/* ==================== 斷點定義 ==================== */
/* 
 * xs: < 576px (手機直向)
 * sm: >= 576px (手機橫向)
 * md: >= 768px (平板直向)
 * lg: >= 992px (平板橫向/小筆電)
 * xl: >= 1200px (桌面)
 * xxl: >= 1400px (大桌面)
 */

/* ==================== 大桌面 (≥ 1400px) ==================== */
@media (min-width: 1400px) {
    .container {
        max-width: 1320px;
    }
    
    .main-content {
        padding: var(--spacing-xl);
    }
    
    .page-content {
        padding: var(--spacing-xl);
    }
    
    .sidebar {
        width: 320px;
    }
    
    .main-content {
        margin-left: 320px;
    }
    
    .main-content.sidebar-collapsed {
        margin-left: 60px;
    }
}

/* ==================== 桌面 (≥ 1200px) ==================== */
@media (min-width: 1200px) and (max-width: 1399px) {
    .container {
        max-width: 1140px;
    }
    
    .navbar-nav {
        gap: var(--spacing-md);
    }
    
    .dropdown-menu {
        min-width: 220px;
    }
}

/* ==================== 平板橫向/小筆電 (≥ 992px) ==================== */
@media (min-width: 992px) and (max-width: 1199px) {
    .container {
        max-width: 960px;
    }
    
    .sidebar {
        width: 280px;
    }
    
    .main-content {
        margin-left: 280px;
    }
    
    .main-content.sidebar-collapsed {
        margin-left: 60px;
    }
    
    .navbar-brand .brand-text {
        font-size: var(--font-size-md);
    }
    
    .page-title {
        font-size: 1.6em;
    }
}

/* ==================== 平板直向 (≥ 768px) ==================== */
@media (min-width: 768px) and (max-width: 991px) {
    .container {
        max-width: 720px;
    }
    
    /* 側邊欄在平板上隱藏 */
    .sidebar {
        transform: translateX(-100%);
        transition: transform 0.3s ease;
    }
    
    .sidebar.show {
        transform: translateX(0);
    }
    
    .main-content {
        margin-left: 0;
        padding: var(--spacing-md);
    }
    
    /* 導航列調整 */
    .navbar-container {
        padding: 0 var(--spacing-sm);
    }
    
    .navbar-toggle {
        display: block;
    }
    
    .navbar-nav {
        position: absolute;
        top: 100%;
        left: 0;
        right: 0;
        background: white;
        border-top: 1px solid var(--border-color);
        box-shadow: var(--shadow-lg);
        flex-direction: column;
        align-items: stretch;
        padding: var(--spacing-md);
        opacity: 0;
        visibility: hidden;
        transform: translateY(-10px);
        transition: var(--transition);
        z-index: var(--z-dropdown);
    }
    
    .navbar-nav.show {
        opacity: 1;
        visibility: visible;
        transform: translateY(0);
    }
    
    .nav-item {
        width: 100%;
        margin-bottom: var(--spacing-xxs);
    }
    
    .nav-link {
        justify-content: flex-start;
        width: 100%;
        padding: var(--spacing-sm) var(--spacing-md);
    }
    
    .dropdown-menu {
        position: static;
        opacity: 1;
        visibility: visible;
        transform: none;
        box-shadow: none;
        border: none;
        margin-left: var(--spacing-lg);
        background: var(--bg-light);
    }
    
    /* 頁面標題區調整 */
    .page-header {
        flex-direction: column;
        align-items: flex-start;
        gap: var(--spacing-md);
    }
    
    .page-actions {
        width: 100%;
        justify-content: flex-start;
        flex-wrap: wrap;
        gap: var(--spacing-sm);
    }
    
    .page-title {
        font-size: 1.4em;
    }
    
    /* 卡片調整 */
    .card-body {
        padding: var(--spacing-md);
    }
    
    /* 表格調整 */
    .table {
        font-size: var(--font-size-sm);
    }
    
    .table th,
    .table td {
        padding: var(--spacing-sm);
    }
    
    /* 按鈕調整 */
    .btn {
        font-size: var(--font-size-sm);
        padding: var(--spacing-sm) var(--spacing-md);
    }
    
    .btn-sm {
        font-size: var(--font-size-xs);
        padding: var(--spacing-xxs) var(--spacing-sm);
    }
}

/* ==================== 手機橫向 (≥ 576px) ==================== */
@media (min-width: 576px) and (max-width: 767px) {
    .container {
        max-width: 540px;
    }
    
    /* 導航列品牌縮短 */
    .navbar-brand .brand-text {
        display: none;
    }
    
    .navbar-brand::after {
        content: "半導體系統";
        font-size: var(--font-size-sm);
    }
    
    /* 主要內容調整 */
    .main-content {
        padding: var(--spacing-sm);
    }
    
    .page-content {
        padding: var(--spacing-sm);
    }
    
    /* 頁面標題調整 */
    .page-title {
        font-size: 1.3em;
    }
    
    /* 表單調整 */
    .form-control {
        font-size: var(--font-size-sm);
    }
    
    /* 模態框調整 */
    .modal-container {
        margin: var(--spacing-sm);
        max-width: calc(100vw - var(--spacing-lg));
    }
    
    /* 通知調整 */
    .notification-container {
        top: var(--spacing-sm);
        right: var(--spacing-sm);
        left: var(--spacing-sm);
        max-width: none;
    }
    
    .notification {
        margin-bottom: var(--spacing-sm);
        padding: var(--spacing-sm);
    }
    
    /* 側邊欄完全隱藏 */
    .sidebar {
        display: none;
    }
    
    /* 快速操作按鈕調整 */
    .quick-actions {
        grid-template-columns: repeat(3, 1fr);
        gap: var(--spacing-xxs);
    }
    
    .quick-action-btn {
        padding: var(--spacing-xxs);
        font-size: var(--font-size-xxs);
    }
    
    .quick-action-btn i {
        font-size: 1em;
    }
}

/* ==================== 手機直向 (< 576px) ==================== */
@media (max-width: 575px) {
    .container {
        width: 100%;
        padding: 0 var(--spacing-sm);
    }
    
    /* 導航列調整 */
    .navbar {
        height: 60px;
    }
    
    .navbar-container {
        padding: 0 var(--spacing-sm);
    }
    
    .navbar-brand {
        font-size: var(--font-size-md);
    }
    
    .navbar-brand .brand-text {
        display: none;
    }
    
    .navbar-brand i {
        font-size: 1.2em;
    }
    
    .navbar-tools {
        gap: var(--spacing-xxs);
    }
    
    .system-status .status-text {
        display: none;
    }
    
    /* 主要內容調整 */
    .main-content {
        padding: var(--spacing-sm);
        margin-left: 0;
        min-height: calc(100vh - 60px);
    }
    
    .page-content {
        padding: var(--spacing-sm);
        border-radius: var(--radius-sm);
    }
    
    /* 頁面標題調整 */
    .page-header {
        margin-bottom: var(--spacing-md);
        padding-bottom: var(--spacing-sm);
    }
    
    .page-title {
        font-size: 1.2em;
        line-height: 1.2;
    }
    
    .page-actions {
        flex-direction: column;
        align-items: stretch;
        gap: var(--spacing-xxs);
    }
    
    .page-actions .btn {
        width: 100%;
        justify-content: center;
    }
    
    /* 表單調整 */
    .form-group {
        margin-bottom: var(--spacing-sm);
    }
    
    .form-control {
        padding: var(--spacing-sm);
        font-size: var(--font-size-sm);
    }
    
    .btn {
        width: 100%;
        margin-bottom: var(--spacing-xxs);
        padding: var(--spacing-sm);
        font-size: var(--font-size-sm);
    }
    
    .btn:last-child {
        margin-bottom: 0;
    }
    
    /* 表格調整 */
    .table {
        font-size: var(--font-size-xs);
        display: block;
        overflow-x: auto;
        white-space: nowrap;
    }
    
    .table th,
    .table td {
        padding: var(--spacing-xxs) var(--spacing-xs);
        min-width: 80px;
    }
    
    /* 卡片調整 */
    .card {
        border-radius: var(--radius-sm);
        margin-bottom: var(--spacing-sm);
    }
    
    .card-header,
    .card-body,
    .card-footer {
        padding: var(--spacing-sm);
    }
    
    .card-title {
        font-size: var(--font-size-md);
        margin-bottom: var(--spacing-xxs);
    }
    
    /* 模態框調整 */
    .modal-overlay {
        padding: var(--spacing-sm);
    }
    
    .modal-container {
        width: 100%;
        max-width: none;
        margin: 0;
        max-height: calc(100vh - var(--spacing-lg));
        overflow-y: auto;
    }
    
    .modal-header,
    .modal-body,
    .modal-footer {
        padding: var(--spacing-sm);
    }
    
    .modal-title {
        font-size: var(--font-size-md);
    }
    
    /* 對話框調整 */
    .dialog-container {
        width: 100%;
        max-width: none;
        margin: var(--spacing-sm);
    }
    
    /* 通知調整 */
    .notification-container {
        top: var(--spacing-xxs);
        right: var(--spacing-xxs);
        left: var(--spacing-xxs);
        max-width: none;
    }
    
    .notification {
        padding: var(--spacing-sm);
        margin-bottom: var(--spacing-xxs);
        font-size: var(--font-size-xs);
    }
    
    .notification-title {
        font-size: var(--font-size-sm);
    }
    
    .notification-message {
        font-size: var(--font-size-xs);
    }
    
    /* 載入動畫調整 */
    .loading-content {
        padding: var(--spacing-md);
        margin: var(--spacing-sm);
    }
    
    .loading-spinner.large {
        width: 40px;
        height: 40px;
    }
    
    /* 側邊欄完全隱藏 */
    .sidebar {
        display: none;
    }
    
    /* 統計卡片調整 */
    .stats-grid-new {
        grid-template-columns: 1fr;
        gap: var(--spacing-xxs);
    }
    
    .stat-card {
        padding: var(--spacing-sm);
    }
    
    .stat-card .value {
        font-size: 1.2em;
    }
    
    .stat-card .label {
        font-size: var(--font-size-xxs);
    }
    
    /* 進度條調整 */
    .progress-display {
        padding: var(--spacing-sm);
    }
    
    .progress-step {
        font-size: var(--font-size-xs);
    }
    
    .progress-detail {
        font-size: var(--font-size-xxs);
    }
}

/* ==================== 超小螢幕 (< 400px) ==================== */
@media (max-width: 399px) {
    .container {
        padding: 0 var(--spacing-xxs);
    }
    
    .main-content {
        padding: var(--spacing-xxs);
    }
    
    .page-content {
        padding: var(--spacing-xxs);
    }
    
    .navbar-container {
        padding: 0 var(--spacing-xxs);
    }
    
    .page-title {
        font-size: 1.1em;
    }
    
    .btn {
        padding: var(--spacing-xxs) var(--spacing-sm);
        font-size: var(--font-size-xs);
    }
    
    .form-control {
        padding: var(--spacing-xxs) var(--spacing-sm);
    }
    
    .notification {
        padding: var(--spacing-xxs) var(--spacing-sm);
    }
    
    .modal-container {
        margin: var(--spacing-xxs);
    }
}

/* ==================== 橫向模式調整 ==================== */
@media (orientation: landscape) and (max-height: 500px) {
    .navbar {
        height: 50px;
    }
    
    .main-content {
        min-height: calc(100vh - 50px);
        padding: var(--spacing-sm);
    }
    
    .modal-container {
        max-height: calc(100vh - var(--spacing-md));
        overflow-y: auto;
    }
    
    .sidebar {
        display: none;
    }
}

/* ==================== 高解析度螢幕調整 ==================== */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
    .loading-spinner {
        border-width: 2px;
    }
    
    .notification {
        border-width: 1px;
    }
    
    .btn {
        border-width: 1px;
    }
}

/* ==================== 觸控裝置調整 ==================== */
@media (hover: none) and (pointer: coarse) {
    .btn {
        min-height: 44px; /* 觸控友好的最小尺寸 */
        padding: var(--spacing-sm) var(--spacing-md);
    }
    
    .form-control {
        min-height: 44px;
        padding: var(--spacing-sm) var(--spacing-md);
    }
    
    .nav-link {
        min-height: 44px;
        padding: var(--spacing-sm) var(--spacing-md);
    }
    
    .dropdown-item {
        min-height: 44px;
        padding: var(--spacing-sm) var(--spacing-md);
    }
    
    .quick-action-btn {
        min-height: 60px;
        padding: var(--spacing-sm);
    }
    
    /* 移除 hover 效果 */
    .btn:hover,
    .nav-link:hover,
    .dropdown-item:hover {
        transform: none;
    }
    
    /* 增強點擊反饋 */
    .btn:active,
    .nav-link:active,
    .dropdown-item:active {
        transform: scale(0.98);
        transition: transform 0.1s ease;
    }
}

/* ==================== 減少動畫偏好 ==================== */
@media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
        scroll-behavior: auto !important;
    }
    
    .loading-spinner {
        animation: none;
    }
    
    .notification {
        transition: none;
        transform: none;
        opacity: 1;
    }
    
    .modal-overlay,
    .dialog-overlay {
        transition: none;
    }
}

/* ==================== 高對比度模式 ==================== */
@media (prefers-contrast: high) {
    .btn {
        border-width: 2px;
    }
    
    .form-control {
        border-width: 2px;
    }
    
    .card {
        border-width: 2px;
    }
    
    .notification {
        border-width: 2px;
    }
    
    .nav-link:focus,
    .btn:focus,
    .form-control:focus {
        outline-width: 3px;
        outline-offset: 2px;
    }
}

/* ==================== 深色模式偏好 ==================== */
@media (prefers-color-scheme: dark) {
    /* 這裡可以添加深色模式的響應式調整 */
    .notification {
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
    }
    
    .modal-container,
    .dialog-container {
        box-shadow: 0 8px 24px rgba(0, 0, 0, 0.4);
    }
}