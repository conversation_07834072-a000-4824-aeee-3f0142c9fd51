// 網路連接管理模組
// 處理網路共享連接、認證和用戶資訊

export class NetworkConnection {
    constructor(api) {
        this.api = api;
        this.isConnected = false;
        this.currentCredentials = null;
    }

    // 連接到網路共享
    async connectToShare(currentPath, statusCallback) {
        statusCallback('正在自動連接網路共享...', 'loading');

        try {
            // 首先嘗試使用當前 Windows 用戶認證
            statusCallback('嘗試使用當前 Windows 用戶認證連接...', 'loading');
            let response = await fetch(`${this.api}/connect`, {
                method: 'POST',
                headers: {'Content-Type': 'application/json'},
                body: JSON.stringify({
                    path: currentPath,
                    username: 'current_user',
                    password: '',
                    domain: 'gmt'
                })
            });

            let result = await response.json();

            // 如果當前用戶認證失敗，嘗試使用 .env 檔案中的認證
            if (result.status !== 'success' || !result.connected) {
                statusCallback('當前用戶認證失敗，嘗試使用配置的認證資訊...', 'loading');

                // 獲取 .env 檔案中的認證資訊
                const credResponse = await fetch(`${this.api}/credentials`);
                const credResult = await credResponse.json();

                if (credResult.status !== 'success') {
                    throw new Error('無法獲取認證資訊');
                }

                const credentials = credResult.credentials;

                // 使用從 .env 獲取的認證資訊連接
                response = await fetch(`${this.api}/connect`, {
                    method: 'POST',
                    headers: {'Content-Type': 'application/json'},
                    body: JSON.stringify({
                        path: currentPath,
                        username: credentials.username,
                        password: credentials.password,
                        domain: credentials.domain
                    })
                });

                result = await response.json();
            }

            // 處理最終連接結果
            if (result.status === 'success' && result.connected) {
                this.isConnected = true;
                statusCallback('網路共享連接成功！', 'success');
                
                // 顯示UI元素
                document.getElementById('navigationBar').style.display = 'block';
                document.getElementById('filterBar').style.display = 'block';
                document.getElementById('currentPath').textContent = currentPath;

                return {
                    success: true,
                    message: '連接成功'
                };
            } else {
                return {
                    success: false,
                    message: result.message || '未知錯誤'
                };
            }
        } catch (error) {
            return {
                success: false,
                message: error.message
            };
        }
    }

    // 顯示歡迎訊息
    async showWelcomeMessage() {
        try {
            const response = await fetch(`${this.api}/current-user`);
            const result = await response.json();

            if (result.status === 'success') {
                const userInfo = result.user_info;
                const welcomeElement = document.getElementById('welcomeMessage');
                const welcomeText = document.getElementById('welcomeText');

                welcomeText.innerHTML = `歡迎，${userInfo.username}！已使用當前 Windows 用戶認證成功連接網路共享`;
                welcomeElement.style.display = 'block';

                // 3秒後淡出歡迎訊息
                setTimeout(() => {
                    welcomeElement.style.transition = 'opacity 1s ease-out';
                    welcomeElement.style.opacity = '0.7';
                }, 3000);
            }
        } catch (error) {
            console.log('無法獲取用戶資訊:', error);
        }
    }

    // 獲取連接狀態
    getConnectionStatus() {
        return this.isConnected;
    }

    // 重置連接狀態
    resetConnection() {
        this.isConnected = false;
        this.currentCredentials = null;
    }
}