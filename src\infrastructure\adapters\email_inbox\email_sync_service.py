"""
郵件同步服務
整合 POP3 郵件讀取器和資料庫存儲
"""

import asyncio
from datetime import datetime
from typing import Dict, Any, Optional
import threading
import time
import os
from pathlib import Path
from dotenv import load_dotenv

# 確保 .env 文件被載入
load_dotenv()

from src.infrastructure.adapters.email_reader_factory import EmailReaderFactory, EmailReaderType
from src.infrastructure.adapters.database.email_database import EmailDatabase
from src.infrastructure.adapters.notification.line_notification_service import LineNotificationService
from src.infrastructure.logging.logger_manager import LoggerManager
from src.data_models.email_models import EmailData
from src.infrastructure.adapters.email_inbox.sync_attachment_handler import SyncAttachmentHandler
from src.infrastructure.parsers.base_parser import ParserFactory, ParserRegistry


class EmailSyncService:
    """
    郵件同步服務
    負責從 POP3 服務器同步郵件到本地資料庫
    """
    
    def __init__(self, database: EmailDatabase = None):
        """
        初始化郵件同步服務
        
        Args:
            database: 郵件資料庫實例
        """
        self.logger = LoggerManager().get_logger("EmailSyncService")
        self.database = database or EmailDatabase()
        self.email_reader = None
        
        # 初始化附件處理器和解析服務
        self.attachment_handler = SyncAttachmentHandler(self.database)
        # 🔧 修復：使用統一的 ParserFactory（自動註冊所有解析器）
        self.parser_factory = ParserFactory()

        # 初始化 LINE 通知服務（優雅降級）
        try:
            self.line_notification_service = LineNotificationService(auto_load_env=True)
            
            # 檢查服務狀態
            status = self.line_notification_service.get_service_status()
            if status['enabled']:
                self.logger.info("LINE 通知服務已成功啟用")
            else:
                self.logger.warning(f"LINE 通知服務已初始化但未啟用: {status['config_error']}")
                
        except Exception as e:
            self.logger.warning(f"LINE 通知服務初始化失敗: {e}")
            # 建立一個禁用的服務實例，避免 None 檢查
            self.line_notification_service = LineNotificationService(auto_load_env=False)
        
        # 同步狀態
        self.is_syncing = False
        self.last_sync_time = None
        self.sync_stats = {
            'total_synced': 0,
            'last_sync_count': 0,
            'sync_errors': 0,
            'last_error': None,
            'last_error_details': []
        }
        
        # 自動同步設定
        self.auto_sync_enabled = False
        self.auto_sync_interval = 60  # 1分鐘
        self.auto_sync_thread = None
        
        self.logger.info("郵件同步服務已初始化")
    
    # 🗑️ 已刪除：_validate_parsing_result_with_env 方法
    # 此方法未被使用，已清理以減少代碼冗餘
        
    # 🗑️ 已刪除：_register_parsers 方法（第1部分）
    # 現在使用 ParserFactory 的自動註冊功能，避免重複註冊
    # 🗑️ 已刪除：_register_parsers 方法（第2部分）
    # 現在使用 ParserFactory 的自動註冊功能，避免重複註冊
    
    async def initialize_email_reader(self) -> bool:
        """
        初始化郵件讀取器
        
        Returns:
            初始化成功與否
        """
        try:
            if not self.email_reader:
                self.email_reader = EmailReaderFactory.create_from_env_config(EmailReaderType.POP3)
                self.logger.info("郵件讀取器已初始化")
            
            return True
            
        except Exception as e:
            self.logger.error(f"初始化郵件讀取器失敗: {e}")
            return False
    
    async def sync_emails_once(self, max_emails: int = 100) -> Dict[str, Any]:
        """
        執行一次郵件同步
        
        Args:
            max_emails: 最大同步郵件數量
            
        Returns:
            同步結果
        """
        if self.is_syncing:
            return {
                'success': False,
                'message': '正在同步中，請稍後再試',
                'data': {
                    'sync_status': 'already_running'
                }
            }
        
        self.is_syncing = True
        sync_start_time = datetime.now()
        
        try:
            self.logger.info(f"開始同步郵件 (最多 {max_emails} 封)")
            
            # 初始化郵件讀取器
            if not await self.initialize_email_reader():
                raise Exception("無法初始化郵件讀取器")
            
            # 連接到 POP3 服務器
            if not await self.email_reader.connect():
                raise Exception("無法連接到 POP3 服務器")
            
            try:
                # 讀取郵件
                emails = await self.email_reader.read_emails(count=max_emails)
                
                if not emails:
                    self.logger.info("沒有新郵件可同步")
                    return {
                        'success': True,
                        'message': '沒有新郵件',
                        'data': {
                            'sync_count': 0,
                            'sync_time': sync_start_time.isoformat(),
                            'sync_duration': (datetime.now() - sync_start_time).total_seconds()
                        }
                    }
                
                # 儲存郵件到資料庫
                sync_count = 0
                sync_errors = 0
                error_details = []
                
                for i, email in enumerate(emails):
                    try:
                        email_id = self.database.save_email(email)
                        if email_id:
                            # 檢查郵件是否已經處理過
                            if self._is_email_already_processed(email_id):
                                self.logger.debug(f"郵件已處理過，跳過: {email.subject} (ID: {email_id})")
                                continue

                            sync_count += 1
                            self.logger.debug(f"郵件已同步: {email.subject} (ID: {email_id})")

                            # 🔧 修復：使用統一的 ALL IN ONE 郵件處理服務
                            try:
                                from src.application.services.unified_email_processor import UnifiedEmailProcessor

                                # 創建統一處理器
                                if not hasattr(self, 'unified_processor'):
                                    self.unified_processor = UnifiedEmailProcessor()

                                # 使用 ALL IN ONE 完整流程處理郵件
                                processing_result = await self.unified_processor.process_email_complete(
                                    email_data=email,
                                    email_id=email_id,
                                    process_attachments=True,
                                    process_vendor_files=True,
                                    send_notifications=True,
                                    update_database=True
                                )

                                # 記錄處理結果
                                if processing_result.is_success:
                                    self.logger.info(
                                        f"✅ ALL IN ONE 處理成功: {email_id} - "
                                        f"廠商: {processing_result.vendor_code}, "
                                        f"解析方法: {processing_result.parsing_result.extraction_method}, "
                                        f"處理時間: {processing_result.processing_time:.2f}s"
                                    )
                                else:
                                    self.logger.warning(
                                        f"❌ ALL IN ONE 處理失敗: {email_id} - "
                                        f"錯誤: {processing_result.error_message}"
                                    )

                            except Exception as e:
                                self.logger.error(f"ALL IN ONE 處理發生異常: {e}")

                                # 異常情況發送通知
                                try:
                                    if self.line_notification_service:
                                        notification_data = {
                                            'id': email.message_id,
                                            'subject': email.subject,
                                            'sender': email.sender,
                                            'received_time': email.received_time.isoformat() if email.received_time else None,
                                            'vendor_code': 'ERROR',
                                            'extraction_method': 'error',
                                            'error_message': str(e),
                                            'pd': 'N/A',
                                            'lot': 'N/A',
                                            'mo_number': 'N/A',
                                            'yield_value': 'N/A'
                                        }

                                        self.line_notification_service.send_parsing_failure_notification(notification_data)
                                except Exception as notification_error:
                                    self.logger.debug(f"發送異常通知失敗: {notification_error}")

                            # 標記郵件為已處理（如果設定為刪除，會從伺服器刪除）
                            try:
                                if hasattr(self.email_reader, 'mark_as_processed'):
                                    await self.email_reader.mark_as_processed(email)
                                    self.logger.debug(f"郵件已標記為已處理: {email.subject}")
                            except Exception as e:
                                self.logger.error(f"標記郵件為已處理失敗: {e}")

                        else:
                            sync_errors += 1
                            error_msg = f"郵件同步失敗 ({i+1}/{len(emails)}): subject='{email.subject[:50]}...', sender='{email.sender}'"
                            self.logger.warning(error_msg)
                            error_details.append({
                                'index': i+1,
                                'subject': email.subject[:100],
                                'sender': email.sender,
                                'message_id': email.message_id,
                                'error': 'save_email returned None'
                            })
                    except Exception as e:
                        sync_errors += 1
                        error_msg = f"儲存郵件失敗 ({i+1}/{len(emails)}): subject='{email.subject[:50]}...', sender='{email.sender}', error={e}"
                        self.logger.error(error_msg)
                        error_details.append({
                            'index': i+1,
                            'subject': email.subject[:100] if hasattr(email, 'subject') else 'Unknown',
                            'sender': email.sender if hasattr(email, 'sender') else 'Unknown',
                            'message_id': email.message_id if hasattr(email, 'message_id') else 'Unknown',
                            'error': str(e)
                        })
                
                # 更新統計
                self.sync_stats['total_synced'] += sync_count
                self.sync_stats['last_sync_count'] = sync_count
                self.sync_stats['sync_errors'] += sync_errors
                self.sync_stats['last_error_details'] = error_details
                self.last_sync_time = datetime.now()
                
                # 記錄詳細的同步結果
                if sync_errors > 0:
                    self.logger.warning(f"郵件同步完成: {sync_count} 封成功, {sync_errors} 封失敗")
                    self.logger.warning(f"失敗郵件詳情: {error_details}")
                else:
                    self.logger.info(f"郵件同步完成: {sync_count} 封成功, {sync_errors} 封失敗")
                
                # 🔧 修復：新郵件已經在上面的循環中通過 UnifiedEmailProcessor 處理了
                # 不再需要額外的自動解析步驟，因為每封郵件都已經完整處理
                if sync_count > 0:
                    self.logger.info(f"✅ {sync_count} 封新郵件已通過 ALL IN ONE 流程完整處理")
                
                return {
                    'success': True,
                    'message': f'成功同步 {sync_count} 封郵件' + (f', {sync_errors} 封失敗' if sync_errors > 0 else ''),
                    'data': {
                        'sync_count': sync_count,
                        'sync_errors': sync_errors,
                        'error_details': error_details if sync_errors > 0 else [],
                        'sync_time': sync_start_time.isoformat(),
                        'sync_duration': (datetime.now() - sync_start_time).total_seconds()
                    }
                }
                
            finally:
                # 斷開連接
                await self.email_reader.disconnect()
                
        except Exception as e:
            self.logger.error(f"郵件同步失敗: {e}")
            self.sync_stats['sync_errors'] += 1
            self.sync_stats['last_error'] = str(e)
            
            return {
                'success': False,
                'message': f'郵件同步失敗: {str(e)}',
                'data': {
                    'error': str(e),
                    'sync_time': sync_start_time.isoformat(),
                    'sync_duration': (datetime.now() - sync_start_time).total_seconds()
                }
            }
            
        finally:
            self.is_syncing = False
    
    def sync_emails_background(self, max_emails: int = 100) -> Dict[str, Any]:
        """
        在背景執行郵件同步
        
        Args:
            max_emails: 最大同步郵件數量
            
        Returns:
            同步請求結果
        """
        if self.is_syncing:
            return {
                'success': False,
                'message': '正在同步中，請稍後再試',
                'data': {
                    'sync_status': 'already_running'
                }
            }
        
        self.logger.info(f"開始背景同步，最大郵件數: {max_emails}")
        
        # 在背景執行同步
        def run_sync():
            try:
                self.logger.info("背景同步線程已啟動")
                
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)
                result = loop.run_until_complete(self.sync_emails_once(max_emails))
                loop.close()
                
                if result['success']:
                    self.logger.info(f"背景同步完成: 成功同步 {result['data']['sync_count']} 封郵件")
                else:
                    self.logger.warning(f"背景同步失敗: {result['message']}")
                
            except Exception as e:
                self.logger.error(f"背景同步失敗: {e}")
        
        sync_thread = threading.Thread(target=run_sync, daemon=True)
        sync_thread.start()
        
        self.logger.info("背景同步線程已啟動")
        
        return {
            'success': True,
            'message': '郵件同步已在背景啟動',
            'data': {
                'sync_status': 'started',
                'sync_time': datetime.now().isoformat()
            }
        }
    
    def start_auto_sync(self, interval_seconds: int = 60) -> Dict[str, Any]:
        """
        啟動自動同步
        
        Args:
            interval_seconds: 同步間隔（秒）
            
        Returns:
            啟動結果
        """
        if self.auto_sync_enabled:
            return {
                'success': False,
                'message': '自動同步已啟動',
                'data': {
                    'auto_sync_enabled': True,
                    'interval_seconds': self.auto_sync_interval
                }
            }
        
        self.auto_sync_enabled = True
        self.auto_sync_interval = interval_seconds
        
        def auto_sync_loop():
            """自動同步循環"""
            try:
                # 首次立即執行同步
                if not self.is_syncing:
                    self.logger.info("開始首次自動同步...")
                    
                    # 使用同步方式執行異步函數
                    loop = asyncio.new_event_loop()
                    asyncio.set_event_loop(loop)
                    result = loop.run_until_complete(self.sync_emails_once(50))
                    loop.close()
                    
                    if result['success']:
                        self.logger.info(f"首次自動同步完成: {result['data']['sync_count']} 封郵件")
                    else:
                        self.logger.warning(f"首次自動同步失敗: {result['message']}")
                else:
                    self.logger.info("跳過首次同步（已有同步進行中）")
                    
            except Exception as e:
                self.logger.error(f"首次自動同步錯誤: {e}")
            
            # 定期同步循環
            while self.auto_sync_enabled:
                try:
                    # 等待下次同步
                    self.logger.info(f"等待 {self.auto_sync_interval} 秒後進行下次同步...")
                    time.sleep(self.auto_sync_interval)
                    
                    if not self.auto_sync_enabled:
                        break
                    
                    if not self.is_syncing:
                        self.logger.info("執行定期自動同步...")
                        
                        # 使用同步方式執行異步函數
                        loop = asyncio.new_event_loop()
                        asyncio.set_event_loop(loop)
                        result = loop.run_until_complete(self.sync_emails_once(50))
                        loop.close()
                        
                        if result['success']:
                            self.logger.info(f"定期自動同步完成: {result['data']['sync_count']} 封郵件")
                        else:
                            self.logger.warning(f"定期自動同步失敗: {result['message']}")
                    else:
                        self.logger.info("跳過定期同步（已有同步進行中）")
                    
                except Exception as e:
                    self.logger.error(f"定期自動同步循環錯誤: {e}")
                    time.sleep(60)  # 錯誤後等待較短時間
        
        self.auto_sync_thread = threading.Thread(target=auto_sync_loop, daemon=True)
        self.auto_sync_thread.start()
        
        self.logger.info(f"自動同步已啟動，間隔 {interval_seconds} 秒（首次立即執行）")
        
        return {
            'success': True,
            'message': f'自動同步已啟動，間隔 {interval_seconds} 秒（首次立即執行）',
            'data': {
                'auto_sync_enabled': True,
                'interval_seconds': interval_seconds
            }
        }
    
    def stop_auto_sync(self) -> Dict[str, Any]:
        """
        停止自動同步
        
        Returns:
            停止結果
        """
        if not self.auto_sync_enabled:
            self.logger.info("自動同步未啟動，無需停止")
            return {
                'success': False,
                'message': '自動同步未啟動',
                'data': {
                    'auto_sync_enabled': False
                }
            }
        
        self.logger.info("正在停止自動同步...")
        self.auto_sync_enabled = False
        
        # 等待線程結束
        if self.auto_sync_thread and self.auto_sync_thread.is_alive():
            self.logger.info("等待自動同步線程結束...")
            self.auto_sync_thread.join(timeout=5)
            if self.auto_sync_thread.is_alive():
                self.logger.warning("自動同步線程未能在時限內結束")
            else:
                self.logger.info("自動同步線程已成功結束")
        
        self.logger.info("自動同步已停止")
        
        return {
            'success': True,
            'message': '自動同步已停止',
            'data': {
                'auto_sync_enabled': False
            }
        }
    
    def get_sync_status(self) -> Dict[str, Any]:
        """
        取得同步狀態
        
        Returns:
            同步狀態資訊
        """
        return {
            'is_syncing': self.is_syncing,
            'auto_sync_enabled': self.auto_sync_enabled,
            'auto_sync_interval': self.auto_sync_interval,
            'last_sync_time': self.last_sync_time.isoformat() if self.last_sync_time else None,
            'sync_stats': self.sync_stats.copy()
        }
    
    def get_connection_status(self) -> Dict[str, Any]:
        """
        取得連接狀態
        
        Returns:
            連接狀態資訊
        """
        try:
            if self.email_reader:
                stats = self.email_reader.get_statistics()
                return {
                    'connected': self.email_reader.is_connected(),
                    'connection_info': stats.get('connection_info', {}),
                    'reader_stats': stats
                }
            else:
                return {
                    'connected': False,
                    'connection_info': {},
                    'reader_stats': {}
                }
                
        except Exception as e:
            self.logger.error(f"取得連接狀態失敗: {e}")
            return {
                'connected': False,
                'error': str(e)
            }
    
    async def test_connection(self) -> Dict[str, Any]:
        """
        測試郵件伺服器連接
        
        Returns:
            測試結果
        """
        try:
            if not await self.initialize_email_reader():
                raise Exception("無法初始化郵件讀取器")
            
            if await self.email_reader.connect():
                try:
                    unread_count = self.email_reader.get_unread_count()
                    stats = self.email_reader.get_statistics()
                    
                    return {
                        'success': True,
                        'message': '連接測試成功',
                        'data': {
                            'connected': True,
                            'unread_count': unread_count,
                            'server_info': stats.get('connection_info', {})
                        }
                    }
                finally:
                    await self.email_reader.disconnect()
            else:
                return {
                    'success': False,
                    'message': '無法連接到郵件伺服器',
                    'data': {
                        'connected': False
                    }
                }
                
        except Exception as e:
            self.logger.error(f"連接測試失敗: {e}")
            return {
                'success': False,
                'message': f'連接測試失敗: {str(e)}',
                'data': {
                    'connected': False,
                    'error': str(e)
                }
            }
    
    def cleanup(self):
        """清理資源"""
        try:
            # 停止自動同步
            if self.auto_sync_enabled:
                self.stop_auto_sync()
            
            # 清理郵件讀取器
            if self.email_reader:
                if hasattr(self.email_reader, 'disconnect'):
                    try:
                        # 檢查是否已經在事件循環中
                        loop = asyncio.get_running_loop()
                        # 如果在事件循環中，創建任務
                        asyncio.create_task(self.email_reader.disconnect())
                    except RuntimeError:
                        # 沒有運行的事件循環，可以使用 asyncio.run
                        asyncio.run(self.email_reader.disconnect())
                self.email_reader = None
            
            self.logger.info("郵件同步服務已清理")
            
        except Exception as e:
            self.logger.error(f"清理郵件同步服務失敗: {e}")
    
    def __del__(self):
        """析構函數"""
        self.cleanup()
        
    # 🗑️ 已刪除：_update_email_parse_result 方法
    # 此方法未被使用，現在使用 UnifiedEmailProcessor 統一處理

    # 🗑️ 已刪除：_process_vendor_files 方法
    # 現在使用 UnifiedEmailProcessor._process_vendor_files

    # 🗑️ 已刪除：_update_vendor_files_status 方法
    # 此方法未被使用，現在使用 UnifiedEmailProcessor 統一處理

    # 🗑️ 已刪除：_send_line_notification 方法
    # 現在使用 UnifiedEmailProcessor._send_success_notification / _send_failure_notification

    # 🗑️ 已刪除：_build_attachment_path 方法
    # 現在使用 UnifiedEmailProcessor._build_attachment_path

    def _is_email_already_processed(self, email_id: int) -> bool:
        """
        檢查郵件是否已經處理過

        Args:
            email_id: 郵件 ID

        Returns:
            是否已處理
        """
        try:
            from src.infrastructure.adapters.database.models import EmailDB

            with self.database.get_session() as session:
                email = session.query(EmailDB).filter_by(id=email_id).first()
                if email:
                    return email.is_processed
                return False

        except Exception as e:
            self.logger.error(f"檢查郵件處理狀態失敗: {e}")
            return False  # 發生錯誤時假設未處理，避免跳過郵件