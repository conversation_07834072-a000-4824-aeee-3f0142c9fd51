#!/usr/bin/env python3
"""
配置安全檢查腳本
檢查 Flask 配置和部署配置的安全性
"""

import os
import sys
from pathlib import Path

# 添加專案根目錄到 Python 路徑
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

def check_flask_config():
    """檢查 Flask 配置安全性（內網環境）"""
    print("🔍 檢查 Flask 配置安全性（內網環境）...")

    issues = []

    try:
        from frontend.config import Config, ProductionConfig

        # 檢查 SECRET_KEY
        if not os.environ.get('SECRET_KEY'):
            if os.environ.get('FLASK_ENV') == 'production':
                issues.append("❌ 生產環境未設定 SECRET_KEY 環境變數")
            else:
                print("✅ 開發環境 SECRET_KEY 配置正確")
        else:
            print("✅ SECRET_KEY 環境變數已設定")

        # 檢查生產環境配置
        prod_config = ProductionConfig()

        # 內網環境檢查網路綁定
        if hasattr(prod_config, 'HOST'):
            if prod_config.HOST == '0.0.0.0':
                print("✅ 內網環境網路綁定配置正確 (0.0.0.0)")
            else:
                print(f"ℹ️ 網路綁定設定為: {prod_config.HOST}")

        # 內網環境 Cookie 安全性檢查
        if hasattr(prod_config, 'SESSION_COOKIE_HTTPONLY') and prod_config.SESSION_COOKIE_HTTPONLY:
            print("✅ Cookie HttpOnly 設定正確")
        else:
            issues.append("⚠️ Cookie HttpOnly 設定可能有問題")

        # 內網環境不強制 HTTPS
        print("ℹ️ 內網環境不強制 HTTPS，由外部防火牆處理安全性")

    except Exception as e:
        issues.append(f"❌ Flask 配置檢查失敗: {e}")

    return issues

def check_deployment_config():
    """檢查部署配置（內網環境）"""
    print("\n🔍 檢查部署配置（內網環境）...")

    issues = []

    # 檢查 Systemd 服務檔案
    systemd_file = project_root / "deployment" / "systemd" / "outlook-summary.service"
    if systemd_file.exists():
        try:
            content = systemd_file.read_text(encoding='utf-8')
        except UnicodeDecodeError:
            content = systemd_file.read_text(encoding='utf-8', errors='ignore')

        # 檢查網路綁定一致性
        if "--bind 0.0.0.0:8000" in content:
            print("✅ Systemd 服務網路綁定配置正確 (0.0.0.0:8000)")
        elif "--bind 127.0.0.1:8000" in content:
            issues.append("⚠️ Systemd 服務綁定到本地，可能與內網需求不符")

        # 檢查資源限制
        if "MemoryMax=2G" in content:
            print("✅ Systemd 記憶體限制設定合理")
        elif "MemoryMax=4G" in content:
            issues.append("⚠️ Systemd 記憶體限制可能過高 (4GB)")

        if "CPUQuota=400%" in content:
            print("✅ Systemd CPU 限制設定合理")
        elif "CPUQuota=800%" in content:
            issues.append("⚠️ Systemd CPU 限制可能過高 (800%)")

        # 檢查安全設定
        if "NoNewPrivileges=true" in content:
            print("✅ Systemd 安全設定正確")
        else:
            issues.append("⚠️ Systemd 缺少安全設定")

        # 檢查 IP 地址限制與內網需求的一致性
        if "IPAddressDeny=any" in content:
            if ("IPAddressAllow=10.0.0.0/8" in content or
                "IPAddressAllow=**********/12" in content or
                "IPAddressAllow=***********/16" in content):
                print("✅ Systemd IP 限制配置支援內網訪問")
            else:
                issues.append("❌ 關鍵衝突: Systemd 僅允許本地連接，但服務綁定 0.0.0.0 - 將阻斷內網用戶")
        else:
            print("ℹ️ Systemd 未設定 IP 地址限制")
    else:
        issues.append("❌ 找不到 Systemd 服務檔案")
    
    # 檢查 Docker 配置
    dockerfile = project_root / "deployment" / "docker" / "Dockerfile"
    if dockerfile.exists():
        try:
            content = dockerfile.read_text(encoding='utf-8')
        except UnicodeDecodeError:
            content = dockerfile.read_text(encoding='utf-8', errors='ignore')
        
        if "USER outlook" in content:
            print("✅ Docker 使用非 root 用戶")
        else:
            issues.append("⚠️ Docker 可能使用 root 用戶")
            
        if "HEALTHCHECK" in content:
            print("✅ Docker 健康檢查已配置")
        else:
            issues.append("⚠️ Docker 缺少健康檢查")
    else:
        issues.append("❌ 找不到 Dockerfile")
    
    return issues

def check_environment_variables():
    """檢查環境變數配置"""
    print("\n🔍 檢查環境變數配置...")
    
    issues = []
    
    # 檢查 .env.example 檔案
    env_example = project_root / ".env.example"
    if env_example.exists():
        try:
            content = env_example.read_text(encoding='utf-8')
        except UnicodeDecodeError:
            content = env_example.read_text(encoding='utf-8', errors='ignore')
        
        if "SECRET_KEY=" in content:
            print("✅ .env.example 包含 SECRET_KEY 範例")
        else:
            issues.append("⚠️ .env.example 缺少 SECRET_KEY 範例")
            
        if "FLASK_ENV=" in content:
            print("✅ .env.example 包含 FLASK_ENV 範例")
        else:
            issues.append("⚠️ .env.example 缺少 FLASK_ENV 範例")
    else:
        issues.append("❌ 找不到 .env.example 檔案")
    
    return issues

def main():
    """主要檢查函數"""
    print("🛡️ 開始配置安全檢查...\n")
    
    all_issues = []
    
    # 執行各項檢查
    all_issues.extend(check_flask_config())
    all_issues.extend(check_deployment_config())
    all_issues.extend(check_environment_variables())
    
    # 輸出結果
    print("\n" + "="*50)
    print("📋 安全檢查結果摘要")
    print("="*50)
    
    if not all_issues:
        print("🎉 所有安全檢查都通過！")
        return 0
    else:
        print(f"⚠️ 發現 {len(all_issues)} 個問題：\n")
        for issue in all_issues:
            print(f"  {issue}")
        
        print(f"\n💡 建議：")
        print("  1. 修正上述安全問題")
        print("  2. 在生產環境部署前設定所有必要的環境變數")
        print("  3. 定期執行此安全檢查")
        
        return 1

if __name__ == "__main__":
    sys.exit(main())
