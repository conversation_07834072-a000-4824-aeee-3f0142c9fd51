<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>郵件設定 - Outlook Summary</title>
    <link rel="stylesheet" href="{{ url_for('email.static', filename='css/inbox.css') }}?v={{ range(1000, 9999) | random }}">
    <link rel="stylesheet" href="{{ url_for('shared.static', filename='css/global.css') }}">
</head>
<body>
    <div class="settings-container">
        <header class="settings-header">
            <h1>郵件設定</h1>
            <div class="header-actions">
                <button id="save-settings-btn" class="btn btn-primary">
                    <span class="btn-icon">💾</span>
                    <span class="btn-text">儲存設定</span>
                </button>
                <a href="{{ url_for('email.inbox') }}" class="btn btn-outline">
                    <span class="btn-icon">↩</span>
                    <span class="btn-text">返回收件匣</span>
                </a>
            </div>
        </header>

        <div class="settings-content">
            <div class="settings-tabs">
                <button class="tab-btn active" data-tab="sync">同步設定</button>
                <button class="tab-btn" data-tab="parser">解析設定</button>
                <button class="tab-btn" data-tab="notification">通知設定</button>
                <button class="tab-btn" data-tab="advanced">進階設定</button>
            </div>

            <div class="tab-content active" id="sync-tab">
                <h3>郵件同步設定</h3>
                <div class="form-group">
                    <label>
                        <input type="checkbox" id="auto-sync" {{ 'checked' if settings.auto_sync else '' }}>
                        啟用自動同步
                    </label>
                </div>
                <div class="form-group">
                    <label for="sync-interval">同步間隔 (分鐘):</label>
                    <input type="number" id="sync-interval" value="{{ settings.sync_interval or 10 }}" min="1" max="60">
                </div>
                <div class="form-group">
                    <label for="max-emails">最大同步郵件數:</label>
                    <input type="number" id="max-emails" value="{{ settings.max_emails or 100 }}" min="10" max="1000">
                </div>
            </div>

            <div class="tab-content" id="parser-tab">
                <h3>郵件解析設定</h3>
                <div class="form-group">
                    <label>
                        <input type="checkbox" id="auto-parse" {{ 'checked' if settings.auto_parse else '' }}>
                        自動解析新郵件
                    </label>
                </div>
                <div class="form-group">
                    <label for="parse-attachments">附件處理:</label>
                    <select id="parse-attachments">
                        <option value="none" {{ 'selected' if settings.parse_attachments == 'none' else '' }}>不處理</option>
                        <option value="download" {{ 'selected' if settings.parse_attachments == 'download' else '' }}>下載附件</option>
                        <option value="parse" {{ 'selected' if settings.parse_attachments == 'parse' else '' }}>解析附件內容</option>
                    </select>
                </div>
                <div class="form-group">
                    <label for="file-types">支援的檔案類型:</label>
                    <input type="text" id="file-types" value="{{ settings.file_types or '.pdf,.doc,.docx,.xlsx,.zip' }}" placeholder="例如: .pdf,.doc,.xlsx">
                </div>
            </div>

            <div class="tab-content" id="notification-tab">
                <h3>通知設定</h3>
                <div class="form-group">
                    <label>
                        <input type="checkbox" id="email-notifications" {{ 'checked' if settings.email_notifications else '' }}>
                        啟用郵件通知
                    </label>
                </div>
                <div class="form-group">
                    <label>
                        <input type="checkbox" id="desktop-notifications" {{ 'checked' if settings.desktop_notifications else '' }}>
                        啟用桌面通知
                    </label>
                </div>
                <div class="form-group">
                    <label for="notification-email">通知接收信箱:</label>
                    <input type="email" id="notification-email" value="{{ settings.notification_email or '' }}" placeholder="輸入接收通知的信箱">
                </div>
            </div>

            <div class="tab-content" id="advanced-tab">
                <h3>進階設定</h3>
                <div class="form-group">
                    <label for="log-level">日誌等級:</label>
                    <select id="log-level">
                        <option value="DEBUG" {{ 'selected' if settings.log_level == 'DEBUG' else '' }}>DEBUG</option>
                        <option value="INFO" {{ 'selected' if settings.log_level == 'INFO' else '' }}>INFO</option>
                        <option value="WARNING" {{ 'selected' if settings.log_level == 'WARNING' else '' }}>WARNING</option>
                        <option value="ERROR" {{ 'selected' if settings.log_level == 'ERROR' else '' }}>ERROR</option>
                    </select>
                </div>
                <div class="form-group">
                    <label for="temp-folder">臨時資料夾路徑:</label>
                    <input type="text" id="temp-folder" value="{{ settings.temp_folder or './temp' }}" placeholder="./temp">
                </div>
                <div class="form-group">
                    <label>
                        <input type="checkbox" id="cleanup-temp" {{ 'checked' if settings.cleanup_temp else '' }}>
                        自動清理臨時檔案
                    </label>
                </div>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="{{ url_for('shared.static', filename='js/main.js') }}"></script>
    <script src="{{ url_for('email.static', filename='js/email-settings.js') }}"></script>
</body>
</html>