# Backend Refactoring Guidelines

## Overview

This document provides comprehensive guidelines for the backend refactoring phase of the Vue.js frontend migration project. It ensures 100% coverage of all existing backend functionality while preparing for Vue.js integration.

## Architecture Principles

### Modular Backend Structure
- **Target Architecture**: Modular `backend/` structure corresponding to 6 frontend modules
- **Source Migration**: Complete migration from existing `src/` directory structure
- **Compatibility**: Maintain full compatibility with existing frontend during transition
- **Hexagonal Architecture**: Preserve existing architectural patterns and dependencies

### Service Organization
```
backend/
├── email/           # Email services (Outlook/POP3 adapters)
├── analytics/       # Analytics and reporting services  
├── file_management/ # File handling and attachment services
├── eqc/            # Equipment Quality Control services
├── tasks/          # Task management and Dramatiq services
├── monitoring/     # Dashboard monitoring system (Task 1-28)
└── shared/         # Shared resources (application, domain, infrastructure)
```

## Migration Strategy

### File Migration Rules
- **Move and Delete**: Use `git mv` to preserve file history, then immediately delete source files
- **Import Path Updates**: Update all import statements and dependencies after migration
- **Commit Messages**: Use standardized format: `move: [description] - Source: [old_path] - Target: [new_path] - Module: [module_name]`
- **Verification**: Test functionality after each migration step

### Service Mapping

#### Core Services Migration
| Source Location | Target Location | Task |
|----------------|-----------------|------|
| `src/infrastructure/adapters/outlook/` | `backend/email/services/` | 9.1 |
| `src/infrastructure/adapters/pop3/` | `backend/email/services/` | 9.1 |
| `src/infrastructure/adapters/attachments/` | `backend/file_management/services/` | 9.3 |
| `src/infrastructure/adapters/file_upload/` | `backend/file_management/services/` | 9.3 |
| `src/services/scheduler.py` | `backend/tasks/services/` | 9.5 |
| `src/services/concurrent_task_manager.py` | `backend/tasks/services/` | 9.5 |
| `dramatiq_tasks.py` | `backend/tasks/services/` | 9.8 |
| `batch_csv_to_excel_processor.py` | `backend/analytics/services/` | 9.8 |

#### Complete System Migration
| Source Location | Target Location | Task |
|----------------|-----------------|------|
| `src/dashboard_monitoring/` (complete) | `backend/monitoring/` | 9.6 |
| `src/application/` | `backend/shared/application/` | 9.7 |
| `src/domain/` | `backend/shared/domain/` | 9.7 |
| `src/infrastructure/` | `backend/shared/infrastructure/` | 9.7 |
| `src/tasks/` | `backend/tasks/pipeline/` | 9.8 |
| `src/utils/` | `backend/shared/utils/` | 9.8 |

## Implementation Standards

### API Standardization
- **Unified Response Format**: Implement consistent JSON response structure
- **Error Handling**: Standardized error codes and messages
- **Authentication**: Maintain existing authentication mechanisms
- **WebSocket Support**: Preserve real-time data streaming capabilities

### Database Layer
- **Repository Pattern**: Implement for each module
- **ORM Configuration**: Unified SQLAlchemy setup
- **Migration Support**: Database versioning and migration scripts
- **Performance**: Connection pooling and caching strategies

### Testing Requirements
- **Unit Tests**: 90% coverage for domain logic
- **Integration Tests**: Module interaction verification
- **Performance Tests**: API response time < 500ms
- **Compatibility Tests**: Ensure frontend compatibility throughout migration

## Quality Assurance

### Code Quality Standards
- **Type Hints**: All functions must have complete type annotations
- **Documentation**: Comprehensive docstrings for all public methods
- **Linting**: Black + Flake8 compliance required
- **Security**: Bandit security scanning

### Monitoring Integration
- **Dashboard Monitoring**: Preserve Task 1-28 completed functionality
- **Health Checks**: Service availability monitoring
- **Performance Metrics**: API and database performance tracking
- **Alerting**: Multi-channel notification system

## Security Considerations

### API Security
- **Authentication**: JWT token validation
- **Authorization**: Role-based access control
- **Data Encryption**: Sensitive data protection
- **Input Validation**: Request sanitization and validation

### Infrastructure Security
- **Environment Variables**: Secure configuration management
- **Database Security**: Connection encryption and access control
- **Logging**: Secure audit trail implementation

## Deployment Guidelines

### Environment Configuration
- **Multi-Environment**: Development, testing, production configs
- **Docker Support**: Containerized deployment preparation
- **Service Discovery**: Backend service registration and discovery
- **Load Balancing**: Horizontal scaling preparation

### Monitoring and Logging
- **Centralized Logging**: Unified log collection and analysis
- **Performance Monitoring**: Real-time performance metrics
- **Error Tracking**: Comprehensive error reporting and alerting
- **Health Checks**: Service availability and dependency monitoring

## Vue.js Preparation

### API Design
- **RESTful Endpoints**: Standard HTTP methods and status codes
- **JSON Schema**: Consistent data structures for frontend consumption
- **Pagination**: Efficient data loading for large datasets
- **Real-time Updates**: WebSocket integration for live data

### Frontend Integration
- **CORS Configuration**: Cross-origin request handling
- **API Versioning**: Backward compatibility support
- **Documentation**: OpenAPI/Swagger documentation generation
- **Testing**: API contract testing with frontend

## Success Criteria

### Functional Requirements
- ✅ 100% backend functionality coverage
- ✅ Zero data loss during migration
- ✅ Maintained system performance
- ✅ Full frontend compatibility
- ✅ Preserved monitoring capabilities (Task 1-28)

### Technical Requirements
- ✅ Modular architecture implementation
- ✅ Standardized API interfaces
- ✅ Comprehensive test coverage
- ✅ Production-ready deployment configuration
- ✅ Complete documentation

## References

- **Main Specification**: `.kiro/specs/vue-frontend-migration/`
- **Task List**: `.kiro/specs/vue-frontend-migration/tasks.md` (Tasks 8-13)
- **Requirements**: `.kiro/specs/vue-frontend-migration/requirements.md` (Requirements 9-12)
- **Design Document**: `.kiro/specs/vue-frontend-migration/design.md`
- **Technology Stack**: `.kiro/steering/tech.md`
- **Project Structure**: `.kiro/steering/structure.md`

## Usage Instructions

This document is automatically included in all backend refactoring tasks. Reference these guidelines when:
- Planning backend service migrations
- Implementing API standardization
- Setting up testing frameworks
- Configuring deployment environments
- Integrating with monitoring systems

For specific implementation details, refer to the task list in `.kiro/specs/vue-frontend-migration/tasks.md` starting from Task 8.