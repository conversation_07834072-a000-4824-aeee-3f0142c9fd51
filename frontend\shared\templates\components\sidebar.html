<!-- 側邊欄 -->
<aside class="sidebar" id="sidebar">
    <div class="sidebar-header">
        <div class="sidebar-brand">
            <i class="fas fa-microchip"></i>
            <span class="brand-text">系統導航</span>
        </div>
        <button class="sidebar-toggle" id="sidebar-toggle">
            <i class="fas fa-chevron-left"></i>
        </button>
    </div>
    
    <div class="sidebar-content">
        <!-- 快速操作區 -->
        <div class="sidebar-section">
            <h6 class="sidebar-section-title">快速操作</h6>
            <div class="quick-actions">
                <a href="/email/inbox" class="quick-action-btn" title="郵件收件夾">
                    <i class="fas fa-inbox"></i>
                    <span>收件夾</span>
                </a>
                <a href="/analytics/dashboard" class="quick-action-btn" title="統計儀表板">
                    <i class="fas fa-chart-line"></i>
                    <span>統計</span>
                </a>
                <a href="/files/network-browser" class="quick-action-btn" title="網路瀏覽器">
                    <i class="fas fa-network-wired"></i>
                    <span>瀏覽器</span>
                </a>
                <a href="/monitoring/dashboard" class="quick-action-btn" title="監控儀表板">
                    <i class="fas fa-monitor-heart-rate"></i>
                    <span>監控</span>
                </a>
            </div>
        </div>
        
        <!-- 系統狀態區 -->
        <div class="sidebar-section">
            <h6 class="sidebar-section-title">系統狀態</h6>
            <div class="system-stats">
                <div class="stat-item">
                    <div class="stat-icon">
                        <i class="fas fa-envelope"></i>
                    </div>
                    <div class="stat-info">
                        <div class="stat-label">未讀郵件</div>
                        <div class="stat-value" id="sidebar-unread-emails">-</div>
                    </div>
                </div>
                
                <div class="stat-item">
                    <div class="stat-icon">
                        <i class="fas fa-tasks"></i>
                    </div>
                    <div class="stat-info">
                        <div class="stat-label">執行中任務</div>
                        <div class="stat-value" id="sidebar-running-tasks">-</div>
                    </div>
                </div>
                
                <div class="stat-item">
                    <div class="stat-icon">
                        <i class="fas fa-database"></i>
                    </div>
                    <div class="stat-info">
                        <div class="stat-label">資料庫狀態</div>
                        <div class="stat-value" id="sidebar-db-status">
                            <span class="status-indicator status-normal"></span>
                            正常
                        </div>
                    </div>
                </div>
                
                <div class="stat-item">
                    <div class="stat-icon">
                        <i class="fas fa-server"></i>
                    </div>
                    <div class="stat-info">
                        <div class="stat-label">服務狀態</div>
                        <div class="stat-value" id="sidebar-service-status">
                            <span class="status-indicator status-normal"></span>
                            運行中
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 最近活動區 -->
        <div class="sidebar-section">
            <h6 class="sidebar-section-title">最近活動</h6>
            <div class="recent-activities" id="sidebar-recent-activities">
                <div class="activity-item">
                    <div class="activity-icon">
                        <i class="fas fa-envelope"></i>
                    </div>
                    <div class="activity-info">
                        <div class="activity-text">新郵件已同步</div>
                        <div class="activity-time">剛剛</div>
                    </div>
                </div>
                
                <div class="activity-item">
                    <div class="activity-icon">
                        <i class="fas fa-check-circle"></i>
                    </div>
                    <div class="activity-info">
                        <div class="activity-text">EQC 處理完成</div>
                        <div class="activity-time">5分鐘前</div>
                    </div>
                </div>
                
                <div class="activity-item">
                    <div class="activity-icon">
                        <i class="fas fa-file-csv"></i>
                    </div>
                    <div class="activity-info">
                        <div class="activity-text">報表生成完成</div>
                        <div class="activity-time">10分鐘前</div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 快速連結區 -->
        <div class="sidebar-section">
            <h6 class="sidebar-section-title">外部連結</h6>
            <div class="external-links">
                <a href="/eqc/ui" class="external-link" target="_blank">
                    <i class="fas fa-external-link-alt"></i>
                    <span>FT-EQC 處理</span>
                </a>
                <a href="#" class="external-link" id="ft-summary-sidebar-link" target="_blank">
                    <i class="fas fa-chart-bar"></i>
                    <span>FT-Summary UI</span>
                </a>
                <a href="/monitoring/realtime-dashboard" class="external-link" target="_blank">
                    <i class="fas fa-broadcast-tower"></i>
                    <span>即時監控</span>
                </a>
            </div>
        </div>
    </div>
    
    <!-- 側邊欄底部 -->
    <div class="sidebar-footer">
        <div class="system-info">
            <div class="info-item">
                <i class="fas fa-clock"></i>
                <span id="sidebar-current-time">--:--</span>
            </div>
            <div class="info-item">
                <i class="fas fa-memory"></i>
                <span id="sidebar-memory-usage">-- MB</span>
            </div>
        </div>
    </div>
</aside>

<!-- 側邊欄 JavaScript -->
<script>
document.addEventListener('DOMContentLoaded', function() {
    // 側邊欄切換功能
    const sidebarToggle = document.getElementById('sidebar-toggle');
    const sidebar = document.getElementById('sidebar');
    const mainContent = document.querySelector('.main-content');
    
    if (sidebarToggle && sidebar) {
        sidebarToggle.addEventListener('click', function() {
            sidebar.classList.toggle('collapsed');
            if (mainContent) {
                mainContent.classList.toggle('sidebar-collapsed');
            }
            
            // 更新切換按鈕圖示
            const icon = sidebarToggle.querySelector('i');
            if (icon) {
                if (sidebar.classList.contains('collapsed')) {
                    icon.className = 'fas fa-chevron-right';
                } else {
                    icon.className = 'fas fa-chevron-left';
                }
            }
        });
    }
    
    // 更新時間顯示
    function updateCurrentTime() {
        const timeElement = document.getElementById('sidebar-current-time');
        if (timeElement) {
            const now = new Date();
            const timeString = now.toLocaleTimeString('zh-TW', {
                hour: '2-digit',
                minute: '2-digit'
            });
            timeElement.textContent = timeString;
        }
    }
    
    // 初始化時間並每分鐘更新
    updateCurrentTime();
    setInterval(updateCurrentTime, 60000);
    
    // 更新系統統計
    function updateSidebarStats() {
        // 這裡可以添加實際的 API 調用來獲取統計數據
        // 目前使用模擬數據
        
        const unreadEmailsElement = document.getElementById('sidebar-unread-emails');
        const runningTasksElement = document.getElementById('sidebar-running-tasks');
        const memoryUsageElement = document.getElementById('sidebar-memory-usage');
        
        // 模擬數據更新
        if (unreadEmailsElement) {
            // 這裡應該從 API 獲取實際數據
            unreadEmailsElement.textContent = '0';
        }
        
        if (runningTasksElement) {
            runningTasksElement.textContent = '0';
        }
        
        if (memoryUsageElement) {
            // 模擬記憶體使用量
            const memoryUsage = Math.floor(Math.random() * 500 + 200);
            memoryUsageElement.textContent = memoryUsage + ' MB';
        }
    }
    
    // 初始化統計數據
    updateSidebarStats();
    
    // 每30秒更新一次統計數據
    setInterval(updateSidebarStats, 30000);
    
    // 設置 FT-Summary 連結
    function setupFTSummaryLink() {
        const ftSummaryLink = document.getElementById('ft-summary-sidebar-link');
        if (ftSummaryLink && typeof UrlConfig !== 'undefined') {
            try {
                const config = UrlConfig.getConfig();
                ftSummaryLink.href = config.ui.ftSummary;
            } catch (error) {
                console.warn('設置 FT-Summary 連結失敗:', error);
                ftSummaryLink.href = 'http://127.0.0.1:8010/ft-summary-ui#';
            }
        }
    }
    
    // 延遲設置連結，確保 UrlConfig 已載入
    setTimeout(setupFTSummaryLink, 1000);
    
    // 更新最近活動
    function updateRecentActivities() {
        // 這裡可以添加實際的活動數據獲取邏輯
        // 目前保持靜態內容
    }
    
    // 響應式處理
    function handleResponsive() {
        const screenWidth = window.innerWidth;
        
        if (screenWidth < 768) {
            // 在小螢幕上自動收合側邊欄
            sidebar.classList.add('collapsed');
            if (mainContent) {
                mainContent.classList.add('sidebar-collapsed');
            }
        }
    }
    
    // 初始化響應式處理
    handleResponsive();
    window.addEventListener('resize', handleResponsive);
});
</script>