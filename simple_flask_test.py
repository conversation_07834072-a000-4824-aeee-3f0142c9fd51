#!/usr/bin/env python3
"""
簡單的 Flask 配置測試
"""

import sys
from pathlib import Path

# 添加專案根目錄到 Python 路徑
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_configuration():
    """測試配置"""
    print("🧪 測試 Flask 配置...")
    
    try:
        from frontend.app import create_app
        
        # 測試開發環境
        dev_app = create_app('development')
        print(f"✅ 開發環境配置: DEBUG={dev_app.config.get('DEBUG')}")
        
        # 測試生產環境
        prod_app = create_app('production')
        print(f"✅ 生產環境配置: DEBUG={prod_app.config.get('DEBUG')}")
        
        # 測試模組配置
        static_mapping = dev_app.config.get('STATIC_FOLDER_MAPPING', {})
        print(f"✅ 靜態資源模組: {len(static_mapping)} 個")
        
        # 測試路由
        with dev_app.app_context():
            rules = list(dev_app.url_map.iter_rules())
            print(f"✅ 總路由數量: {len(rules)}")
        
        print("🎉 Flask 配置測試通過！")
        return True
        
    except Exception as e:
        print(f"❌ 測試失敗: {e}")
        return False

if __name__ == '__main__':
    success = test_configuration()
    sys.exit(0 if success else 1)