/**
 * Email Settings Module
 * 郵件設定模組 JavaScript
 */

class EmailSettingsManager {
    constructor() {
        this.settings = {};
        this.accounts = [];
        this.isTestingConnection = false;
        
        this.init();
    }
    
    init() {
        console.log('Email Settings Manager: Initializing...');
        this.bindEvents();
        this.loadEmailSettings();
        this.loadEmailAccounts();
    }
    
    bindEvents() {
        // 保存設定按鈕
        document.addEventListener('click', (e) => {
            if (e.target.matches('.save-settings-btn')) {
                this.handleSaveSettings(e);
            }
        });
        
        // 測試連接按鈕
        document.addEventListener('click', (e) => {
            if (e.target.matches('.test-connection-btn')) {
                this.handleTestConnection(e);
            }
        });
        
        // 添加帳戶按鈕
        document.addEventListener('click', (e) => {
            if (e.target.matches('.add-account-btn')) {
                this.handleAddAccount();
            }
        });
        
        // 編輯帳戶按鈕
        document.addEventListener('click', (e) => {
            if (e.target.matches('.edit-account-btn')) {
                this.handleEditAccount(e);
            }
        });
        
        // 刪除帳戶按鈕
        document.addEventListener('click', (e) => {
            if (e.target.matches('.delete-account-btn')) {
                this.handleDeleteAccount(e);
            }
        });
        
        // 設定帳戶為預設
        document.addEventListener('click', (e) => {
            if (e.target.matches('.set-default-btn')) {
                this.handleSetDefault(e);
            }
        });
        
        // 表單提交
        document.addEventListener('submit', (e) => {
            if (e.target.matches('#emailSettingsForm')) {
                e.preventDefault();
                this.handleFormSubmit(e);
            }
            if (e.target.matches('#emailAccountForm')) {
                e.preventDefault();
                this.handleAccountFormSubmit(e);
            }
        });
        
        // 連接類型變更
        document.addEventListener('change', (e) => {
            if (e.target.matches('#connectionType')) {
                this.handleConnectionTypeChange(e);
            }
        });
        
        // 自動檢測設定
        document.addEventListener('click', (e) => {
            if (e.target.matches('.auto-detect-btn')) {
                this.handleAutoDetect(e);
            }
        });
    }
    
    async loadEmailSettings() {
        try {
            const response = await fetch('/email/api/settings');
            
            if (response.ok) {
                this.settings = await response.json();
                this.populateSettingsForm();
            }
        } catch (error) {
            console.error('Failed to load email settings:', error);
            this.showError('載入郵件設定失敗');
        }
    }
    
    async loadEmailAccounts() {
        try {
            const response = await fetch('/email/api/accounts');
            
            if (response.ok) {
                this.accounts = await response.json();
                this.displayEmailAccounts();
            }
        } catch (error) {
            console.error('Failed to load email accounts:', error);
            this.showError('載入郵件帳戶失敗');
        }
    }
    
    populateSettingsForm() {
        const form = document.getElementById('emailSettingsForm');
        if (!form) return;
        
        // 填充一般設定
        this.setFormValue('checkInterval', this.settings.check_interval);
        this.setFormValue('maxEmails', this.settings.max_emails);
        this.setFormValue('autoProcess', this.settings.auto_process);
        this.setFormValue('saveAttachments', this.settings.save_attachments);
        this.setFormValue('attachmentPath', this.settings.attachment_path);
        
        // 填充通知設定
        this.setFormValue('enableNotifications', this.settings.enable_notifications);
        this.setFormValue('notificationSound', this.settings.notification_sound);
        this.setFormValue('desktopNotifications', this.settings.desktop_notifications);
        
        // 填充安全設定
        this.setFormValue('useSSL', this.settings.use_ssl);
        this.setFormValue('verifySSL', this.settings.verify_ssl);
        this.setFormValue('timeout', this.settings.timeout);
    }
    
    displayEmailAccounts() {
        const container = document.querySelector('.email-accounts-list');
        if (!container) return;
        
        container.innerHTML = '';
        
        if (this.accounts.length === 0) {
            container.innerHTML = `
                <div class="no-accounts">
                    <p>尚未設定任何郵件帳戶</p>
                    <button class="btn btn-primary add-account-btn">添加第一個帳戶</button>
                </div>
            `;
            return;
        }
        
        this.accounts.forEach(account => {
            const accountElement = this.createAccountElement(account);
            container.appendChild(accountElement);
        });
    }
    
    createAccountElement(account) {
        const element = document.createElement('div');
        element.className = `account-item ${account.is_default ? 'default-account' : ''}`;
        element.dataset.accountId = account.id;
        
        const statusClass = account.status === 'connected' ? 'success' : 
                          account.status === 'error' ? 'danger' : 'warning';
        
        element.innerHTML = `
            <div class="account-header">
                <div class="account-info">
                    <h5 class="account-email">${account.email}</h5>
                    <span class="account-name">${account.display_name || account.email}</span>
                    ${account.is_default ? '<span class="badge bg-primary">預設</span>' : ''}
                </div>
                <div class="account-status">
                    <span class="status-indicator status-${statusClass}">${this.getStatusText(account.status)}</span>
                    <span class="last-check">${account.last_check ? '最後檢查：' + this.formatDateTime(account.last_check) : ''}</span>
                </div>
            </div>
            
            <div class="account-details">
                <div class="detail-row">
                    <span class="label">伺服器：</span>
                    <span class="value">${account.server}:${account.port}</span>
                </div>
                <div class="detail-row">
                    <span class="label">類型：</span>
                    <span class="value">${account.connection_type?.toUpperCase() || 'IMAP'}</span>
                </div>
                <div class="detail-row">
                    <span class="label">SSL：</span>
                    <span class="value">${account.use_ssl ? '是' : '否'}</span>
                </div>
                <div class="detail-row">
                    <span class="label">郵件數量：</span>
                    <span class="value">${account.email_count || 0} 封</span>
                </div>
            </div>
            
            <div class="account-actions">
                <button class="btn btn-sm btn-success test-connection-btn" data-account-id="${account.id}" title="測試連接">
                    <i class="fas fa-plug"></i> 測試
                </button>
                ${!account.is_default ? `
                    <button class="btn btn-sm btn-info set-default-btn" data-account-id="${account.id}" title="設為預設">
                        <i class="fas fa-star"></i> 設為預設
                    </button>
                ` : ''}
                <button class="btn btn-sm btn-primary edit-account-btn" data-account-id="${account.id}" title="編輯">
                    <i class="fas fa-edit"></i> 編輯
                </button>
                <button class="btn btn-sm btn-danger delete-account-btn" data-account-id="${account.id}" title="刪除">
                    <i class="fas fa-trash"></i> 刪除
                </button>
            </div>
            
            ${account.error ? `
                <div class="account-error">
                    <i class="fas fa-exclamation-triangle"></i>
                    <span>${account.error}</span>
                </div>
            ` : ''}
        `;
        
        return element;
    }
    
    async handleSaveSettings(e) {
        const form = document.getElementById('emailSettingsForm');
        if (!form) return;
        
        const button = e.target;
        const originalText = button.textContent;
        
        button.textContent = '儲存中...';
        button.disabled = true;
        
        try {
            const formData = new FormData(form);
            const settings = this.formDataToObject(formData);
            
            const response = await fetch('/email/api/settings', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(settings)
            });
            
            if (response.ok) {
                this.settings = await response.json();
                this.showSuccess('設定已儲存');
            } else {
                const error = await response.json();
                throw new Error(error.message || '儲存失敗');
            }
        } catch (error) {
            console.error('Save settings error:', error);
            this.showError('儲存設定失敗：' + error.message);
        } finally {
            button.textContent = originalText;
            button.disabled = false;
        }
    }
    
    async handleTestConnection(e) {
        if (this.isTestingConnection) {
            this.showWarning('連接測試正在進行中，請稍候...');
            return;
        }
        
        const accountId = e.target.closest('button').dataset.accountId;
        const account = this.accounts.find(a => a.id === accountId);
        
        if (!account) return;
        
        const button = e.target.closest('button');
        const originalHTML = button.innerHTML;
        
        this.isTestingConnection = true;
        button.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 測試中...';
        button.disabled = true;
        
        try {
            const response = await fetch(`/email/api/accounts/${accountId}/test`, {
                method: 'POST'
            });
            
            const result = await response.json();
            
            if (response.ok && result.success) {
                this.showSuccess('連接測試成功！');
                
                // 更新帳戶狀態
                account.status = 'connected';
                account.last_check = new Date().toISOString();
                this.updateAccountElement(account);
                
            } else {
                throw new Error(result.error || '連接測試失敗');
            }
            
        } catch (error) {
            console.error('Connection test error:', error);
            this.showError('連接測試失敗：' + error.message);
            
            // 更新帳戶狀態為錯誤
            if (account) {
                account.status = 'error';
                account.error = error.message;
                this.updateAccountElement(account);
            }
        } finally {
            this.isTestingConnection = false;
            button.innerHTML = originalHTML;
            button.disabled = false;
        }
    }
    
    handleAddAccount() {
        this.resetAccountForm();
        this.showAccountModal('新增郵件帳戶');
    }
    
    handleEditAccount(e) {
        const accountId = e.target.closest('button').dataset.accountId;
        const account = this.accounts.find(a => a.id === accountId);
        
        if (!account) return;
        
        this.populateAccountForm(account);
        this.showAccountModal('編輯郵件帳戶');
    }
    
    async handleDeleteAccount(e) {
        const accountId = e.target.closest('button').dataset.accountId;
        const account = this.accounts.find(a => a.id === accountId);
        
        if (!account || !confirm(`確定要刪除帳戶「${account.email}」嗎？`)) return;
        
        try {
            const response = await fetch(`/email/api/accounts/${accountId}`, {
                method: 'DELETE'
            });
            
            if (response.ok) {
                this.showSuccess('帳戶已刪除');
                this.loadEmailAccounts();
            } else {
                throw new Error('刪除失敗');
            }
        } catch (error) {
            console.error('Delete account error:', error);
            this.showError('刪除帳戶失敗：' + error.message);
        }
    }
    
    async handleSetDefault(e) {
        const accountId = e.target.closest('button').dataset.accountId;
        
        try {
            const response = await fetch(`/email/api/accounts/${accountId}/set-default`, {
                method: 'POST'
            });
            
            if (response.ok) {
                this.showSuccess('已設為預設帳戶');
                this.loadEmailAccounts();
            } else {
                throw new Error('設定失敗');
            }
        } catch (error) {
            console.error('Set default error:', error);
            this.showError('設定預設帳戶失敗：' + error.message);
        }
    }
    
    async handleAccountFormSubmit(e) {
        const form = e.target;
        const formData = new FormData(form);
        const accountData = this.formDataToObject(formData);
        
        const isEdit = !!accountData.account_id;
        const url = isEdit ? `/email/api/accounts/${accountData.account_id}` : '/email/api/accounts';
        const method = isEdit ? 'PUT' : 'POST';
        
        try {
            const response = await fetch(url, {
                method: method,
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(accountData)
            });
            
            if (response.ok) {
                this.showSuccess(isEdit ? '帳戶已更新' : '帳戶已新增');
                this.hideAccountModal();
                this.loadEmailAccounts();
            } else {
                const error = await response.json();
                throw new Error(error.message || '保存失敗');
            }
        } catch (error) {
            console.error('Save account error:', error);
            this.showError('保存帳戶失敗：' + error.message);
        }
    }
    
    handleConnectionTypeChange(e) {
        const connectionType = e.target.value;
        const portField = document.getElementById('port');
        const useSslField = document.getElementById('useSSL');
        
        // 根據連接類型設定預設值
        if (connectionType === 'IMAP') {
            portField.value = useSslField.checked ? '993' : '143';
        } else if (connectionType === 'POP3') {
            portField.value = useSslField.checked ? '995' : '110';
        }
    }
    
    async handleAutoDetect(e) {
        const emailField = document.getElementById('email');
        if (!emailField || !emailField.value) {
            this.showError('請先輸入郵件地址');
            return;
        }
        
        const button = e.target;
        const originalText = button.textContent;
        
        button.textContent = '偵測中...';
        button.disabled = true;
        
        try {
            const response = await fetch('/email/api/auto-detect', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ email: emailField.value })
            });
            
            if (response.ok) {
                const settings = await response.json();
                this.applyDetectedSettings(settings);
                this.showSuccess('自動偵測完成');
            } else {
                const error = await response.json();
                throw new Error(error.message || '自動偵測失敗');
            }
        } catch (error) {
            console.error('Auto detect error:', error);
            this.showError('自動偵測失敗：' + error.message);
        } finally {
            button.textContent = originalText;
            button.disabled = false;
        }
    }
    
    applyDetectedSettings(settings) {
        this.setFormValue('server', settings.server);
        this.setFormValue('port', settings.port);
        this.setFormValue('connectionType', settings.connection_type);
        this.setFormValue('useSSL', settings.use_ssl);
        
        if (settings.display_name) {
            this.setFormValue('displayName', settings.display_name);
        }
    }
    
    showAccountModal(title) {
        const modal = document.getElementById('accountModal');
        if (!modal) return;
        
        const modalTitle = modal.querySelector('.modal-title');
        if (modalTitle) {
            modalTitle.textContent = title;
        }
        
        const modalInstance = new bootstrap.Modal(modal);
        modalInstance.show();
    }
    
    hideAccountModal() {
        const modal = document.getElementById('accountModal');
        if (modal) {
            const modalInstance = bootstrap.Modal.getInstance(modal);
            if (modalInstance) {
                modalInstance.hide();
            }
        }
    }
    
    resetAccountForm() {
        const form = document.getElementById('emailAccountForm');
        if (form) {
            form.reset();
            delete form.dataset.accountId;
        }
    }
    
    populateAccountForm(account) {
        const form = document.getElementById('emailAccountForm');
        if (!form) return;
        
        form.dataset.accountId = account.id;
        
        this.setFormValue('email', account.email);
        this.setFormValue('displayName', account.display_name);
        this.setFormValue('server', account.server);
        this.setFormValue('port', account.port);
        this.setFormValue('username', account.username);
        this.setFormValue('connectionType', account.connection_type);
        this.setFormValue('useSSL', account.use_ssl);
        this.setFormValue('verifySSL', account.verify_ssl);
        
        // 密碼欄位不填充（安全考量）
    }
    
    updateAccountElement(account) {
        const element = document.querySelector(`[data-account-id="${account.id}"]`);
        if (element) {
            const newElement = this.createAccountElement(account);
            element.replaceWith(newElement);
        }
    }
    
    setFormValue(fieldName, value) {
        const field = document.getElementById(fieldName) || document.querySelector(`[name="${fieldName}"]`);
        if (!field) return;
        
        if (field.type === 'checkbox') {
            field.checked = !!value;
        } else {
            field.value = value || '';
        }
    }
    
    formDataToObject(formData) {
        const obj = {};
        for (let [key, value] of formData.entries()) {
            // 處理複選框
            if (value === 'on') {
                obj[key] = true;
            } else if (key.includes('checkbox')) {
                obj[key] = false;
            } else {
                obj[key] = value;
            }
        }
        
        // 處理沒有被提交的複選框（預設為 false）
        const checkboxes = document.querySelectorAll('input[type="checkbox"]');
        checkboxes.forEach(checkbox => {
            if (!obj.hasOwnProperty(checkbox.name)) {
                obj[checkbox.name] = false;
            }
        });
        
        return obj;
    }
    
    getStatusText(status) {
        const statusMap = {
            'connected': '已連接',
            'disconnected': '已斷線',
            'error': '連接錯誤',
            'testing': '測試中'
        };
        
        return statusMap[status] || status;
    }
    
    formatDateTime(dateString) {
        const date = new Date(dateString);
        return date.toLocaleDateString('zh-TW') + ' ' + date.toLocaleTimeString('zh-TW');
    }
    
    showSuccess(message) {
        this.showNotification(message, 'success');
    }
    
    showError(message) {
        this.showNotification(message, 'danger');
    }
    
    showWarning(message) {
        this.showNotification(message, 'warning');
    }
    
    showNotification(message, type) {
        const alert = document.createElement('div');
        alert.className = `alert alert-${type} alert-dismissible`;
        alert.innerHTML = `
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;
        
        const container = document.querySelector('.email-settings-container');
        if (container) {
            container.insertBefore(alert, container.firstChild);
        }
        
        // 自動移除通知
        setTimeout(() => {
            if (alert.parentNode) {
                alert.remove();
            }
        }, 5000);
    }
}

// 頁面載入完成後初始化
document.addEventListener('DOMContentLoaded', function() {
    if (document.querySelector('.email-settings-container')) {
        new EmailSettingsManager();
    }
});