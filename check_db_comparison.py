#!/usr/bin/env python3
"""
Database content comparison tool
Compare email_inbox.db files in root and data directories
"""

import sqlite3
import os
from pathlib import Path

def check_database_info(db_path):
    """Check database information"""
    if not os.path.exists(db_path):
        return f"Database file not found: {db_path}"
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # Get file size
        file_size = os.path.getsize(db_path) / 1024  # KB
        
        info = {
            'path': db_path,
            'size_kb': file_size,
            'tables': {}
        }
        
        # Check record count for each table
        tables = ['emails', 'senders', 'attachments']
        
        for table in tables:
            try:
                cursor.execute(f"SELECT COUNT(*) FROM {table}")
                count = cursor.fetchone()[0]
                info['tables'][table] = count
            except sqlite3.OperationalError:
                info['tables'][table] = "Table not exists"
        
        # Get email samples
        try:
            cursor.execute("""
                SELECT id, subject, sender, datetime(received_time) 
                FROM emails 
                ORDER BY received_time DESC 
                LIMIT 3
            """)
            info['sample_emails'] = cursor.fetchall()
        except sqlite3.OperationalError:
            info['sample_emails'] = []
        
        # Get oldest and newest email times
        try:
            cursor.execute("""
                SELECT 
                    datetime(MIN(received_time)) as oldest,
                    datetime(MAX(received_time)) as newest
                FROM emails
            """)
            time_range = cursor.fetchone()
            info['time_range'] = {
                'oldest': time_range[0],
                'newest': time_range[1]
            }
        except sqlite3.OperationalError:
            info['time_range'] = None
        
        conn.close()
        return info
        
    except Exception as e:
        return f"Database check error: {str(e)}"

def print_database_comparison():
    """Print database comparison results"""
    project_root = Path(__file__).parent
    
    databases = [
        project_root / "email_inbox.db",  # root directory
        project_root / "data" / "email_inbox.db",  # data directory
        project_root / "frontend" / "email_inbox.db"  # frontend directory
    ]
    
    print("=== Database Content Comparison ===\n")
    
    for db_path in databases:
        print(f"Database: {db_path}")
        info = check_database_info(str(db_path))
        
        if isinstance(info, str):
            print(f"   ERROR: {info}")
        else:
            print(f"   File size: {info['size_kb']:.1f} KB")
            print(f"   Email count: {info['tables'].get('emails', 0)}")
            print(f"   Sender count: {info['tables'].get('senders', 0)}")
            print(f"   Attachment count: {info['tables'].get('attachments', 0)}")
            
            if info['time_range']:
                print(f"   Email time range: {info['time_range']['oldest']} to {info['time_range']['newest']}")
            
            if info['sample_emails']:
                print("   Email samples:")
                for email in info['sample_emails']:
                    subject = email[1][:50] + "..." if len(email[1]) > 50 else email[1]
                    print(f"      - ID:{email[0]} | {email[3]} | {email[2]} | {subject}")
        print()
    
    # Recommend which database to use
    print("=== Recommendation ===")
    
    # Check root and data databases
    root_info = check_database_info(str(project_root / "email_inbox.db"))
    data_info = check_database_info(str(project_root / "data" / "email_inbox.db"))
    
    if isinstance(root_info, dict) and isinstance(data_info, dict):
        root_emails = root_info['tables'].get('emails', 0)
        data_emails = data_info['tables'].get('emails', 0)
        
        if root_emails > data_emails:
            print("✅ RECOMMENDED: Use root directory database - email_inbox.db")
            print(f"   Reason: Contains more email records ({root_emails} vs {data_emails})")
            print("   Suggested config: sqlite:///email_inbox.db")
        elif data_emails > root_emails:
            print("✅ RECOMMENDED: Use data directory database - data/email_inbox.db")
            print(f"   Reason: Contains more email records ({data_emails} vs {root_emails})")
            print("   Suggested config: sqlite:///data/email_inbox.db")
        else:
            print("⚠️  Both databases have same number of emails, need further analysis")

if __name__ == "__main__":
    print_database_comparison()