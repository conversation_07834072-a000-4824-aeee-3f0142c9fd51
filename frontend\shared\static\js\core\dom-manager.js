/**
 * DOM 管理器模組
 * 統一管理 DOM 元素的獲取、操作和緩存
 */

class DOMManager {
    static elements = {};
    
    /**
     * 獲取 DOM 元素（帶緩存）
     * @param {string} id - 元素 ID
     * @returns {HTMLElement|null} DOM 元素
     */
    static get(id) {
        if (!this.elements[id]) {
            this.elements[id] = document.getElementById(id);
        }
        return this.elements[id];
    }
    
    /**
     * 獲取元素的值
     * @param {string} id - 元素 ID
     * @returns {string} 元素值
     */
    static getValue(id) {
        const element = this.get(id);
        return element ? element.value : '';
    }
    
    /**
     * 設置元素的文本內容
     * @param {string} id - 元素 ID
     * @param {string} text - 文本內容
     */
    static setText(id, text) {
        const element = this.get(id);
        if (element) element.textContent = text;
    }
    
    /**
     * 設置元素的 HTML 內容
     * @param {string} id - 元素 ID
     * @param {string} html - HTML 內容
     */
    static setHTML(id, html) {
        const element = this.get(id);
        if (element) element.innerHTML = html;
    }
    
    /**
     * 顯示元素
     * @param {string} id - 元素 ID
     * @param {string} display - 顯示方式，默認為 'block'
     */
    static show(id, display = 'block') {
        const element = this.get(id);
        if (element) element.style.display = display;
    }
    
    /**
     * 隱藏元素
     * @param {string} id - 元素 ID
     */
    static hide(id) {
        const element = this.get(id);
        if (element) element.style.display = 'none';
    }
    
    /**
     * 設置元素的值
     * @param {string} id - 元素 ID
     * @param {string} value - 值
     */
    static setValue(id, value) {
        const element = this.get(id);
        if (element) element.value = value;
    }
    
    /**
     * 添加 CSS 類
     * @param {string} id - 元素 ID
     * @param {string} className - CSS 類名
     */
    static addClass(id, className) {
        const element = this.get(id);
        if (element) element.classList.add(className);
    }
    
    /**
     * 移除 CSS 類
     * @param {string} id - 元素 ID
     * @param {string} className - CSS 類名
     */
    static removeClass(id, className) {
        const element = this.get(id);
        if (element) element.classList.remove(className);
    }
    
    /**
     * 切換 CSS 類
     * @param {string} id - 元素 ID
     * @param {string} className - CSS 類名
     */
    static toggleClass(id, className) {
        const element = this.get(id);
        if (element) element.classList.toggle(className);
    }
    
    /**
     * 設置元素樣式
     * @param {string} id - 元素 ID
     * @param {Object} styles - 樣式對象
     */
    static setStyle(id, styles) {
        const element = this.get(id);
        if (element) {
            Object.assign(element.style, styles);
        }
    }
    
    /**
     * 設置元素屬性
     * @param {string} id - 元素 ID
     * @param {string} attribute - 屬性名
     * @param {string} value - 屬性值
     */
    static setAttribute(id, attribute, value) {
        const element = this.get(id);
        if (element) element.setAttribute(attribute, value);
    }
    
    /**
     * 獲取元素屬性
     * @param {string} id - 元素 ID
     * @param {string} attribute - 屬性名
     * @returns {string|null} 屬性值
     */
    static getAttribute(id, attribute) {
        const element = this.get(id);
        return element ? element.getAttribute(attribute) : null;
    }
    
    /**
     * 移除元素屬性
     * @param {string} id - 元素 ID
     * @param {string} attribute - 屬性名
     */
    static removeAttribute(id, attribute) {
        const element = this.get(id);
        if (element) element.removeAttribute(attribute);
    }
    
    /**
     * 添加事件監聽器
     * @param {string} id - 元素 ID
     * @param {string} event - 事件類型
     * @param {Function} handler - 事件處理函數
     */
    static addEventListener(id, event, handler) {
        const element = this.get(id);
        if (element) element.addEventListener(event, handler);
    }
    
    /**
     * 移除事件監聽器
     * @param {string} id - 元素 ID
     * @param {string} event - 事件類型
     * @param {Function} handler - 事件處理函數
     */
    static removeEventListener(id, event, handler) {
        const element = this.get(id);
        if (element) element.removeEventListener(event, handler);
    }
    
    /**
     * 查詢選擇器（單個元素）
     * @param {string} selector - CSS 選擇器
     * @returns {HTMLElement|null} DOM 元素
     */
    static querySelector(selector) {
        return document.querySelector(selector);
    }
    
    /**
     * 查詢選擇器（多個元素）
     * @param {string} selector - CSS 選擇器
     * @returns {NodeList} DOM 元素列表
     */
    static querySelectorAll(selector) {
        return document.querySelectorAll(selector);
    }
    
    /**
     * 創建元素
     * @param {string} tagName - 標籤名
     * @param {Object} options - 選項
     * @returns {HTMLElement} 新創建的元素
     */
    static createElement(tagName, options = {}) {
        const element = document.createElement(tagName);
        
        if (options.id) element.id = options.id;
        if (options.className) element.className = options.className;
        if (options.textContent) element.textContent = options.textContent;
        if (options.innerHTML) element.innerHTML = options.innerHTML;
        if (options.attributes) {
            Object.entries(options.attributes).forEach(([key, value]) => {
                element.setAttribute(key, value);
            });
        }
        if (options.styles) {
            Object.assign(element.style, options.styles);
        }
        
        return element;
    }
    
    /**
     * 移除元素
     * @param {string} id - 元素 ID
     */
    static remove(id) {
        const element = this.get(id);
        if (element && element.parentNode) {
            element.parentNode.removeChild(element);
            delete this.elements[id]; // 清除緩存
        }
    }
    
    /**
     * 清除元素緩存
     * @param {string} id - 元素 ID（可選，不提供則清除所有緩存）
     */
    static clearCache(id = null) {
        if (id) {
            delete this.elements[id];
        } else {
            this.elements = {};
        }
    }
    
    /**
     * 檢查元素是否存在
     * @param {string} id - 元素 ID
     * @returns {boolean} 是否存在
     */
    static exists(id) {
        return this.get(id) !== null;
    }
    
    /**
     * 獲取元素的邊界矩形
     * @param {string} id - 元素 ID
     * @returns {DOMRect|null} 邊界矩形
     */
    static getBoundingClientRect(id) {
        const element = this.get(id);
        return element ? element.getBoundingClientRect() : null;
    }
    
    /**
     * 滾動到元素
     * @param {string} id - 元素 ID
     * @param {Object} options - 滾動選項
     */
    static scrollIntoView(id, options = { behavior: 'smooth' }) {
        const element = this.get(id);
        if (element) element.scrollIntoView(options);
    }
    
    /**
     * 聚焦到元素
     * @param {string} id - 元素 ID
     */
    static focus(id) {
        const element = this.get(id);
        if (element) element.focus();
    }
    
    /**
     * 失去焦點
     * @param {string} id - 元素 ID
     */
    static blur(id) {
        const element = this.get(id);
        if (element) element.blur();
    }
}

// 導出 DOMManager 類
if (typeof module !== 'undefined' && module.exports) {
    module.exports = DOMManager;
} else if (typeof window !== 'undefined') {
    window.DOMManager = DOMManager;
}
