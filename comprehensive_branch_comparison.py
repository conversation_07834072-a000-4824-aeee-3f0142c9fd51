#!/usr/bin/env python3
"""
全面分支對比測試執行器
統合所有測試模組並生成詳細對比報告
創建日期: 2025-08-13
作者: backend-architect agent
"""

import os
import sys
import json
import time
import subprocess
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Any
import concurrent.futures


class ComprehensiveBranchComparison:
    """全面分支對比測試執行器"""
    
    def __init__(self, base_url: str = "http://localhost:8000", 
                 current_branch: str = "task/6-testing-validation",
                 base_branch: str = "main"):
        self.base_url = base_url
        self.current_branch = current_branch
        self.base_branch = base_branch
        self.project_root = Path(__file__).parent
        
        self.results = {
            'test_metadata': {
                'test_start_time': datetime.now().isoformat(),
                'current_branch': current_branch,
                'base_branch': base_branch,
                'base_url': base_url,
                'tester': 'comprehensive-branch-comparison'
            },
            'test_modules': {},
            'execution_summary': {},
            'comparison_analysis': {},
            'recommendations': []
        }
        
        # 定義測試模組
        self.test_modules = [
            {
                'name': 'startup_performance',
                'script': 'startup_performance_test.py',
                'description': '啟動性能測試',
                'weight': 20,
                'critical': True
            },
            {
                'name': 'functional_integrity',
                'script': 'functional_integrity_test.py',
                'description': '功能完整性測試',
                'weight': 35,
                'critical': True
            },
            {
                'name': 'performance_metrics',
                'script': 'performance_metrics_test.py',
                'description': '性能指標測試',
                'weight': 25,
                'critical': False
            },
            {
                'name': 'error_handling',
                'script': 'error_handling_test.py',
                'description': '錯誤處理測試',
                'weight': 20,
                'critical': False
            }
        ]

    def log_execution_result(self, module_name: str, success: bool, 
                           execution_time: float, message: str = "",
                           report_file: str = None):
        """記錄執行結果"""
        self.results['test_modules'][module_name] = {
            'success': success,
            'execution_time': execution_time,
            'message': message,
            'report_file': report_file,
            'timestamp': datetime.now().isoformat()
        }
        
        status = "✅ 完成" if success else "❌ 失敗"
        print(f"{status} {module_name}: {execution_time:.2f}秒")
        if message:
            print(f"    {message}")
        if report_file:
            print(f"    報告: {report_file}")

    def check_service_availability(self) -> bool:
        """檢查服務可用性"""
        import requests
        try:
            response = requests.get(f"{self.base_url}/health", timeout=5)
            return response.status_code == 200
        except:
            return False

    def execute_test_module(self, module_config: Dict[str, Any]) -> Dict[str, Any]:
        """執行單個測試模組"""
        module_name = module_config['name']
        script_path = self.project_root / module_config['script']
        
        print(f"\n🔬 執行 {module_config['description']}...")
        
        if not script_path.exists():
            return {
                'success': False,
                'execution_time': 0,
                'error': f"測試腳本不存在: {script_path}",
                'report_file': None
            }
        
        start_time = time.time()
        
        try:
            # 構建命令
            cmd = [sys.executable, str(script_path), '--url', self.base_url]
            
            # 執行測試
            result = subprocess.run(
                cmd,
                capture_output=True,
                text=True,
                timeout=1800,  # 30分鐘超時
                encoding='utf-8',
                errors='replace'
            )
            
            execution_time = time.time() - start_time
            
            # 分析結果
            if result.returncode == 0:
                # 查找生成的報告文件
                report_pattern = f"{module_name}_report_*.json"
                report_files = list(self.project_root.glob(report_pattern))
                
                if report_files:
                    # 取最新的報告文件
                    latest_report = max(report_files, key=lambda p: p.stat().st_mtime)
                    report_file = str(latest_report)
                else:
                    report_file = None
                
                return {
                    'success': True,
                    'execution_time': execution_time,
                    'stdout': result.stdout,
                    'stderr': result.stderr,
                    'report_file': report_file
                }
            else:
                return {
                    'success': False,
                    'execution_time': execution_time,
                    'error': f"測試失敗 (退出碼: {result.returncode})",
                    'stdout': result.stdout,
                    'stderr': result.stderr,
                    'report_file': None
                }
                
        except subprocess.TimeoutExpired:
            return {
                'success': False,
                'execution_time': time.time() - start_time,
                'error': "測試超時（30分鐘）",
                'report_file': None
            }
        except Exception as e:
            return {
                'success': False,
                'execution_time': time.time() - start_time,
                'error': f"執行異常: {str(e)}",
                'report_file': None
            }

    def execute_all_tests_sequential(self) -> Dict[str, Any]:
        """順序執行所有測試"""
        print("🔄 順序執行所有測試模組...")
        
        execution_results = {}
        
        for module_config in self.test_modules:
            module_name = module_config['name']
            result = self.execute_test_module(module_config)
            
            execution_results[module_name] = result
            
            # 記錄結果
            self.log_execution_result(
                module_name,
                result['success'],
                result['execution_time'],
                result.get('error', '執行成功'),
                result.get('report_file')
            )
            
            # 如果是關鍵測試失敗，考慮是否繼續
            if module_config.get('critical', False) and not result['success']:
                print(f"⚠️ 關鍵測試 {module_name} 失敗，但繼續執行其他測試...")
        
        return execution_results

    def execute_all_tests_parallel(self) -> Dict[str, Any]:
        """並行執行所有測試（非關鍵測試）"""
        print("⚡ 並行執行非關鍵測試模組...")
        
        # 分離關鍵和非關鍵測試
        critical_tests = [m for m in self.test_modules if m.get('critical', False)]
        non_critical_tests = [m for m in self.test_modules if not m.get('critical', False)]
        
        execution_results = {}
        
        # 先順序執行關鍵測試
        print("🔴 執行關鍵測試...")
        for module_config in critical_tests:
            module_name = module_config['name']
            result = self.execute_test_module(module_config)
            execution_results[module_name] = result
            
            self.log_execution_result(
                module_name,
                result['success'],
                result['execution_time'],
                result.get('error', '執行成功'),
                result.get('report_file')
            )
        
        # 然後並行執行非關鍵測試
        if non_critical_tests:
            print("🟡 並行執行非關鍵測試...")
            
            with concurrent.futures.ThreadPoolExecutor(max_workers=2) as executor:
                future_to_module = {
                    executor.submit(self.execute_test_module, module_config): module_config
                    for module_config in non_critical_tests
                }
                
                for future in concurrent.futures.as_completed(future_to_module):
                    module_config = future_to_module[future]
                    module_name = module_config['name']
                    
                    try:
                        result = future.result()
                        execution_results[module_name] = result
                        
                        self.log_execution_result(
                            module_name,
                            result['success'],
                            result['execution_time'],
                            result.get('error', '執行成功'),
                            result.get('report_file')
                        )
                    except Exception as e:
                        execution_results[module_name] = {
                            'success': False,
                            'execution_time': 0,
                            'error': f"並行執行異常: {str(e)}",
                            'report_file': None
                        }
                        
                        self.log_execution_result(
                            module_name, False, 0, f"並行執行異常: {str(e)}")
        
        return execution_results

    def analyze_test_results(self) -> Dict[str, Any]:
        """分析測試結果"""
        print("\n📊 分析測試結果...")
        
        # 載入各個測試模組的詳細結果
        detailed_results = {}
        
        for module_name, module_result in self.results['test_modules'].items():
            if module_result['success'] and module_result.get('report_file'):
                try:
                    with open(module_result['report_file'], 'r', encoding='utf-8') as f:
                        detailed_results[module_name] = json.load(f)
                except Exception as e:
                    print(f"⚠️ 無法載入 {module_name} 的詳細結果: {str(e)}")
        
        # 分析總體性能
        analysis = {
            'overall_success_rate': 0,
            'critical_test_success_rate': 0,
            'performance_summary': {},
            'functional_summary': {},
            'error_handling_summary': {},
            'startup_summary': {},
            'weighted_score': 0,
            'detailed_analysis': detailed_results
        }
        
        # 計算成功率
        total_tests = len(self.results['test_modules'])
        successful_tests = sum(1 for r in self.results['test_modules'].values() if r['success'])
        analysis['overall_success_rate'] = (successful_tests / total_tests * 100) if total_tests > 0 else 0
        
        # 計算關鍵測試成功率
        critical_modules = [m for m in self.test_modules if m.get('critical', False)]
        critical_names = [m['name'] for m in critical_modules]
        successful_critical = sum(1 for name, result in self.results['test_modules'].items() 
                                if name in critical_names and result['success'])
        analysis['critical_test_success_rate'] = (successful_critical / len(critical_names) * 100) if critical_names else 0
        
        # 計算加權分數
        total_weight = 0
        achieved_weight = 0
        
        for module_config in self.test_modules:
            module_name = module_config['name']
            weight = module_config['weight']
            total_weight += weight
            
            if self.results['test_modules'][module_name]['success']:
                achieved_weight += weight
        
        analysis['weighted_score'] = (achieved_weight / total_weight * 100) if total_weight > 0 else 0
        
        # 詳細分析各個模組
        
        # 功能完整性分析
        if 'functional_integrity' in detailed_results:
            func_data = detailed_results['functional_integrity']
            frontend_modules = func_data.get('frontend_modules', {})
            api_endpoints = func_data.get('api_endpoints', {})
            
            total_frontend_endpoints = 0
            successful_frontend_endpoints = 0
            
            for module_name, module_data in frontend_modules.items():
                if 'summary' in module_data:
                    total_frontend_endpoints += module_data['summary']['total_endpoints']
                    successful_frontend_endpoints += module_data['summary']['successful_endpoints']
            
            total_api_endpoints = 0
            successful_api_endpoints = 0
            
            for api_group, group_data in api_endpoints.items():
                if 'summary' in group_data:
                    total_api_endpoints += group_data['summary']['total_endpoints']
                    successful_api_endpoints += group_data['summary']['successful_endpoints']
            
            analysis['functional_summary'] = {
                'frontend_success_rate': (successful_frontend_endpoints / total_frontend_endpoints * 100) if total_frontend_endpoints > 0 else 0,
                'api_success_rate': (successful_api_endpoints / total_api_endpoints * 100) if total_api_endpoints > 0 else 0,
                'total_endpoints_tested': total_frontend_endpoints + total_api_endpoints,
                'total_successful_endpoints': successful_frontend_endpoints + successful_api_endpoints
            }
        
        # 性能分析
        if 'performance_metrics' in detailed_results:
            perf_data = detailed_results['performance_metrics']
            
            # 頁面載入性能
            page_load_data = perf_data.get('page_load_performance', {})
            total_pages = 0
            good_performance_pages = 0
            
            for category, pages in page_load_data.items():
                for page_url, page_data in pages.items():
                    if 'performance_ok' in page_data:
                        total_pages += 1
                        if page_data['performance_ok']:
                            good_performance_pages += 1
            
            # 併發性能
            concurrent_data = perf_data.get('concurrent_performance', {})
            concurrent_scenarios = 0
            successful_scenarios = 0
            
            for scenario_name, scenario_data in concurrent_data.items():
                if 'success_rate' in scenario_data:
                    concurrent_scenarios += 1
                    if scenario_data['success_rate'] >= 95:
                        successful_scenarios += 1
            
            analysis['performance_summary'] = {
                'page_load_success_rate': (good_performance_pages / total_pages * 100) if total_pages > 0 else 0,
                'concurrent_success_rate': (successful_scenarios / concurrent_scenarios * 100) if concurrent_scenarios > 0 else 0,
                'total_pages_tested': total_pages,
                'total_concurrent_scenarios': concurrent_scenarios
            }
        
        # 啟動性能分析
        if 'startup_performance' in detailed_results:
            startup_data = detailed_results['startup_performance']
            startup_measurements = startup_data.get('startup_measurements', {})
            
            analysis['startup_summary'] = {
                'cold_start_available': 'cold_start' in startup_measurements,
                'warm_start_available': 'warm_start' in startup_measurements,
                'stability_test_available': 'stability_tests' in startup_data
            }
            
            if 'cold_start' in startup_measurements and startup_measurements['cold_start']['success']:
                analysis['startup_summary']['cold_start_time'] = startup_measurements['cold_start']['data']['startup_time']
        
        # 錯誤處理分析
        if 'error_handling' in detailed_results:
            error_data = detailed_results['error_handling']
            
            total_error_tests = 0
            successful_error_tests = 0
            
            for category, tests in error_data.items():
                if category == 'test_metadata':
                    continue
                    
                for test_name, test_result in tests.items():
                    if isinstance(test_result, dict) and 'success' in test_result:
                        total_error_tests += 1
                        if test_result['success']:
                            successful_error_tests += 1
            
            analysis['error_handling_summary'] = {
                'error_handling_success_rate': (successful_error_tests / total_error_tests * 100) if total_error_tests > 0 else 0,
                'total_error_tests': total_error_tests
            }
        
        return analysis

    def generate_recommendations(self, analysis: Dict[str, Any]) -> List[str]:
        """生成建議"""
        recommendations = []
        
        # 基於總體成功率的建議
        overall_success = analysis['overall_success_rate']
        critical_success = analysis['critical_test_success_rate']
        weighted_score = analysis['weighted_score']
        
        if overall_success >= 95:
            recommendations.append("✅ 所有測試表現優異，分支功能與原始版本高度一致")
        elif overall_success >= 85:
            recommendations.append("✅ 大部分測試通過，分支功能基本與原始版本一致")
        elif overall_success >= 70:
            recommendations.append("⚠️ 部分測試失敗，建議檢查失敗的測試項目")
        else:
            recommendations.append("❌ 多個測試失敗，建議進行全面檢查和修復")
        
        if critical_success < 100:
            recommendations.append("🔴 關鍵測試未全部通過，建議優先修復關鍵功能問題")
        
        # 基於功能完整性的建議
        if 'functional_summary' in analysis:
            func_summary = analysis['functional_summary']
            
            if func_summary['frontend_success_rate'] < 95:
                recommendations.append(f"🎨 前端模組成功率 {func_summary['frontend_success_rate']:.1f}%，建議檢查前端功能")
            
            if func_summary['api_success_rate'] < 95:
                recommendations.append(f"🔌 API端點成功率 {func_summary['api_success_rate']:.1f}%，建議檢查API功能")
        
        # 基於性能的建議
        if 'performance_summary' in analysis:
            perf_summary = analysis['performance_summary']
            
            if perf_summary['page_load_success_rate'] < 80:
                recommendations.append(f"⚡ 頁面載入性能需要改善 ({perf_summary['page_load_success_rate']:.1f}% 達標)")
            
            if perf_summary['concurrent_success_rate'] < 90:
                recommendations.append(f"🔄 併發性能需要優化 ({perf_summary['concurrent_success_rate']:.1f}% 達標)")
        
        # 基於錯誤處理的建議
        if 'error_handling_summary' in analysis:
            error_summary = analysis['error_handling_summary']
            
            if error_summary['error_handling_success_rate'] < 85:
                recommendations.append(f"🚫 錯誤處理一致性需要改善 ({error_summary['error_handling_success_rate']:.1f}% 達標)")
        
        # 基於啟動性能的建議
        if 'startup_summary' in analysis:
            startup_summary = analysis['startup_summary']
            
            if startup_summary.get('cold_start_time', 0) > 10:
                recommendations.append(f"🚀 啟動時間較長 ({startup_summary['cold_start_time']:.2f}秒)，建議優化啟動性能")
        
        # 總體建議
        if weighted_score >= 90:
            recommendations.append("🏆 整體表現優秀，可以考慮合併到主分支")
        elif weighted_score >= 80:
            recommendations.append("👍 整體表現良好，建議解決少數問題後合併")
        elif weighted_score >= 70:
            recommendations.append("⚠️ 需要修復一些問題才能考慮合併")
        else:
            recommendations.append("❌ 需要大量修復工作才能達到合併標準")
        
        return recommendations

    def run_comprehensive_comparison(self, parallel: bool = False) -> Dict[str, Any]:
        """執行全面對比測試"""
        print("=" * 100)
        print("🔬 全面分支對比測試系統")
        print(f"當前分支: {self.current_branch}")
        print(f"基準分支: {self.base_branch}")
        print(f"測試目標: {self.base_url}")
        print("=" * 100)
        
        # 檢查服務可用性
        if not self.check_service_availability():
            print("❌ 服務未運行或無法訪問，請先啟動服務")
            return None
        
        print("✅ 服務可用，開始全面測試...")
        
        try:
            # 執行所有測試
            if parallel:
                execution_results = self.execute_all_tests_parallel()
            else:
                execution_results = self.execute_all_tests_sequential()
            
            # 分析結果
            self.results['comparison_analysis'] = self.analyze_test_results()
            
            # 生成建議
            self.results['recommendations'] = self.generate_recommendations(
                self.results['comparison_analysis']
            )
            
            # 生成執行摘要
            self.results['execution_summary'] = {
                'total_modules': len(self.test_modules),
                'successful_modules': sum(1 for r in self.results['test_modules'].values() if r['success']),
                'failed_modules': sum(1 for r in self.results['test_modules'].values() if not r['success']),
                'total_execution_time': sum(r['execution_time'] for r in self.results['test_modules'].values()),
                'execution_mode': 'parallel' if parallel else 'sequential'
            }
            
            # 添加測試完成時間
            self.results['test_metadata']['test_end_time'] = datetime.now().isoformat()
            self.results['test_metadata']['total_duration'] = (
                datetime.fromisoformat(self.results['test_metadata']['test_end_time']) - 
                datetime.fromisoformat(self.results['test_metadata']['test_start_time'])
            ).total_seconds()
            
            return self.results
            
        except Exception as e:
            print(f"❌ 全面測試過程中發生錯誤: {str(e)}")
            return None

    def save_comprehensive_report(self, filename: str = None) -> str:
        """保存全面測試報告"""
        if filename is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"comprehensive_branch_comparison_{timestamp}.json"
        
        filepath = self.project_root / filename
        
        with open(filepath, 'w', encoding='utf-8') as f:
            json.dump(self.results, f, indent=2, ensure_ascii=False)
        
        print(f"\n📄 全面對比測試報告已保存: {filepath}")
        return str(filepath)

    def print_comprehensive_summary(self):
        """打印全面測試摘要"""
        print("\n" + "=" * 100)
        print("📋 全面分支對比測試摘要")
        print("=" * 100)
        
        # 執行摘要
        if 'execution_summary' in self.results:
            exec_summary = self.results['execution_summary']
            print(f"測試執行: {exec_summary['successful_modules']}/{exec_summary['total_modules']} 模組成功")
            print(f"總執行時間: {exec_summary['total_execution_time']:.2f}秒")
            print(f"執行模式: {exec_summary['execution_mode']}")
        
        # 分析摘要
        if 'comparison_analysis' in self.results:
            analysis = self.results['comparison_analysis']
            
            print(f"\n📊 測試結果分析:")
            print(f"  總體成功率: {analysis['overall_success_rate']:.1f}%")
            print(f"  關鍵測試成功率: {analysis['critical_test_success_rate']:.1f}%")
            print(f"  加權得分: {analysis['weighted_score']:.1f}/100")
            
            if 'functional_summary' in analysis:
                func = analysis['functional_summary']
                print(f"  前端功能: {func['frontend_success_rate']:.1f}% ({func['total_successful_endpoints']}/{func['total_endpoints_tested']})")
                print(f"  API功能: {func['api_success_rate']:.1f}%")
            
            if 'performance_summary' in analysis:
                perf = analysis['performance_summary']
                print(f"  頁面載入性能: {perf['page_load_success_rate']:.1f}%")
                print(f"  併發性能: {perf['concurrent_success_rate']:.1f}%")
            
            if 'error_handling_summary' in analysis:
                error = analysis['error_handling_summary']
                print(f"  錯誤處理: {error['error_handling_success_rate']:.1f}%")
        
        # 建議摘要
        if 'recommendations' in self.results:
            print(f"\n💡 主要建議:")
            for i, recommendation in enumerate(self.results['recommendations'][:5], 1):
                print(f"  {i}. {recommendation}")
            
            if len(self.results['recommendations']) > 5:
                print(f"  ... 還有 {len(self.results['recommendations']) - 5} 個建議")
        
        print("=" * 100)


def main():
    """主函數"""
    if len(sys.argv) > 1 and sys.argv[1] == '--help':
        print("""
全面分支對比測試系統
用法: python comprehensive_branch_comparison.py [選項]

選項:
  --help              顯示此幫助信息
  --url URL           指定測試URL (默認: http://localhost:8000)
  --current-branch    當前分支名稱 (默認: task/6-testing-validation)
  --base-branch       基準分支名稱 (默認: main)
  --parallel          並行執行非關鍵測試 (默認: 順序執行)

測試模組:
  1. 啟動性能測試 (關鍵)
  2. 功能完整性測試 (關鍵) 
  3. 性能指標測試
  4. 錯誤處理測試

範例:
  python comprehensive_branch_comparison.py
  python comprehensive_branch_comparison.py --parallel
  python comprehensive_branch_comparison.py --url http://localhost:5000
        """)
        return
    
    # 解析命令行參數
    base_url = "http://localhost:8000"
    current_branch = "task/6-testing-validation"
    base_branch = "main"
    parallel = False
    
    for i, arg in enumerate(sys.argv):
        if arg == '--url' and i + 1 < len(sys.argv):
            base_url = sys.argv[i + 1]
        elif arg == '--current-branch' and i + 1 < len(sys.argv):
            current_branch = sys.argv[i + 1]
        elif arg == '--base-branch' and i + 1 < len(sys.argv):
            base_branch = sys.argv[i + 1]
        elif arg == '--parallel':
            parallel = True
    
    # 創建測試器並運行全面測試
    tester = ComprehensiveBranchComparison(base_url, current_branch, base_branch)
    results = tester.run_comprehensive_comparison(parallel=parallel)
    
    if results:
        # 保存結果
        report_file = tester.save_comprehensive_report()
        
        # 打印摘要
        tester.print_comprehensive_summary()
        
        print(f"\n✅ 全面分支對比測試完成！")
        print(f"📄 詳細報告: {report_file}")
        
        # 根據結果設置退出碼
        if results['comparison_analysis']['critical_test_success_rate'] == 100:
            print("🎉 所有關鍵測試通過，分支準備就緒！")
            sys.exit(0)
        else:
            print("⚠️ 部分關鍵測試未通過，需要進一步檢查")
            sys.exit(1)
    else:
        print("\n❌ 全面分支對比測試失敗")
        sys.exit(2)


if __name__ == "__main__":
    main()