#!/usr/bin/env python3
"""
分支對比測試系統
對比當前分支 (task/6-testing-validation) 與 main 分支的功能表現
創建日期: 2025-08-13
作者: backend-architect agent
"""

import os
import sys
import json
import time
import psutil
import requests
import subprocess
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Tuple, Any
import concurrent.futures


class BranchComparisonTester:
    """分支對比測試器"""
    
    def __init__(self, current_branch: str = "task/6-testing-validation", base_branch: str = "main"):
        self.current_branch = current_branch
        self.base_branch = base_branch
        self.base_url = "http://localhost:8000"
        self.project_root = Path(__file__).parent
        self.results = {
            'test_metadata': {
                'test_start_time': datetime.now().isoformat(),
                'current_branch': current_branch,
                'base_branch': base_branch,
                'base_url': self.base_url,
                'tester': 'backend-architect-agent'
            },
            'startup_performance': {},
            'functional_integrity': {},
            'api_response': {},
            'performance_metrics': {},
            'error_handling': {},
            'comparison_summary': {}
        }
        
        # 定義6個前端模組的測試端點
        self.frontend_modules = {
            'analytics': [
                '/analytics',
                '/analytics/dashboard',
                '/analytics/reports',
                '/analytics/vendor_analysis'
            ],
            'email': [
                '/email',
                '/email/inbox',
                '/email/settings',
                '/email/compose'
            ],
            'eqc': [
                '/eqc',
                '/eqc/dashboard',
                '/eqc/quality_check',
                '/eqc/compliance'
            ],
            'file_management': [
                '/files',
                '/files/upload',
                '/files/browser',
                '/files/manager'
            ],
            'monitoring': [
                '/monitoring',
                '/monitoring/health',
                '/monitoring/system',
                '/monitoring/realtime'
            ],
            'tasks': [
                '/tasks',
                '/tasks/queue',
                '/tasks/scheduler',
                '/tasks/manager'
            ]
        }
        
        # API端點測試
        self.api_endpoints = {
            'health_check': '/health',
            'api_status': '/api/status',
            'analytics_api': '/api/analytics/status',
            'email_api': '/api/email/status',
            'eqc_api': '/api/eqc/status',
            'files_api': '/api/files/status',
            'monitoring_api': '/api/monitoring/status',
            'tasks_api': '/api/tasks/status'
        }

    def log_test_result(self, category: str, test_name: str, success: bool, 
                       message: str = "", data: Any = None, duration: float = 0):
        """記錄測試結果"""
        if category not in self.results:
            self.results[category] = {}
            
        self.results[category][test_name] = {
            'success': success,
            'message': message,
            'data': data,
            'duration_ms': round(duration * 1000, 2),
            'timestamp': datetime.now().isoformat()
        }
        
        status = "✅ PASS" if success else "❌ FAIL"
        print(f"{status} [{category}] {test_name}")
        if message:
            print(f"    {message}")
        if duration > 0:
            print(f"    執行時間: {round(duration * 1000, 2)}ms")

    def check_service_running(self) -> bool:
        """檢查服務是否運行"""
        try:
            response = requests.get(f"{self.base_url}/health", timeout=5)
            return response.status_code == 200
        except:
            return False

    def measure_startup_performance(self) -> Dict[str, Any]:
        """測量啟動性能"""
        print("\n🚀 測試啟動性能...")
        
        startup_metrics = {}
        
        # 1. 測量冷啟動時間
        if self.check_service_running():
            print("服務已運行，進行重啟測試...")
            # 這裡可以添加重啟邏輯，但為了安全起見先跳過
            
        start_time = time.time()
        max_attempts = 30
        attempt = 0
        
        while attempt < max_attempts:
            if self.check_service_running():
                startup_time = time.time() - start_time
                startup_metrics['cold_start_time'] = startup_time
                self.log_test_result('startup_performance', 'cold_start', True, 
                                   f"冷啟動時間: {startup_time:.2f}秒", startup_time)
                break
            time.sleep(1)
            attempt += 1
        else:
            self.log_test_result('startup_performance', 'cold_start', False, 
                               "服務啟動超時（30秒）")
            return startup_metrics
        
        # 2. 測量首次響應時間
        start_time = time.time()
        try:
            response = requests.get(f"{self.base_url}/", timeout=10)
            first_response_time = time.time() - start_time
            startup_metrics['first_response_time'] = first_response_time
            startup_metrics['first_response_status'] = response.status_code
            
            self.log_test_result('startup_performance', 'first_response', True,
                               f"首次響應時間: {first_response_time:.2f}秒, 狀態碼: {response.status_code}",
                               first_response_time)
        except Exception as e:
            self.log_test_result('startup_performance', 'first_response', False, 
                               f"首次響應失敗: {str(e)}")
        
        # 3. 測量系統資源使用
        try:
            # 查找 Python 進程（假設是我們的服務）
            python_processes = []
            for proc in psutil.process_iter(['pid', 'name', 'memory_info', 'cpu_percent']):
                if 'python' in proc.info['name'].lower():
                    python_processes.append(proc.info)
            
            if python_processes:
                startup_metrics['memory_usage_mb'] = sum(p['memory_info'].rss for p in python_processes) / 1024 / 1024
                startup_metrics['process_count'] = len(python_processes)
                
                self.log_test_result('startup_performance', 'resource_usage', True,
                                   f"記憶體使用: {startup_metrics['memory_usage_mb']:.2f}MB, 進程數: {startup_metrics['process_count']}")
        except Exception as e:
            self.log_test_result('startup_performance', 'resource_usage', False,
                               f"資源使用測量失敗: {str(e)}")
        
        return startup_metrics

    def test_functional_integrity(self) -> Dict[str, Any]:
        """測試功能完整性"""
        print("\n🔍 測試功能完整性...")
        
        integrity_results = {}
        session = requests.Session()
        session.timeout = 10
        
        # 測試所有前端模組
        for module_name, endpoints in self.frontend_modules.items():
            print(f"\n測試模組: {module_name}")
            module_results = {}
            
            for endpoint in endpoints:
                start_time = time.time()
                try:
                    response = session.get(f"{self.base_url}{endpoint}")
                    duration = time.time() - start_time
                    
                    success = response.status_code == 200
                    module_results[endpoint] = {
                        'status_code': response.status_code,
                        'response_time': duration,
                        'content_length': len(response.content),
                        'success': success
                    }
                    
                    self.log_test_result('functional_integrity', f"{module_name}{endpoint}", 
                                       success, f"狀態碼: {response.status_code}, 內容長度: {len(response.content)}",
                                       module_results[endpoint], duration)
                    
                except Exception as e:
                    module_results[endpoint] = {
                        'error': str(e),
                        'success': False
                    }
                    self.log_test_result('functional_integrity', f"{module_name}{endpoint}", 
                                       False, f"請求失敗: {str(e)}")
            
            integrity_results[module_name] = module_results
        
        return integrity_results

    def test_api_responses(self) -> Dict[str, Any]:
        """測試API響應"""
        print("\n🌐 測試API響應...")
        
        api_results = {}
        session = requests.Session()
        session.timeout = 10
        
        for api_name, endpoint in self.api_endpoints.items():
            start_time = time.time()
            try:
                response = session.get(f"{self.base_url}{endpoint}")
                duration = time.time() - start_time
                
                # 嘗試解析JSON響應
                response_data = None
                try:
                    response_data = response.json()
                except:
                    response_data = response.text[:200]  # 前200字符
                
                success = response.status_code == 200
                api_results[api_name] = {
                    'status_code': response.status_code,
                    'response_time': duration,
                    'response_data': response_data,
                    'success': success
                }
                
                self.log_test_result('api_response', api_name, success,
                                   f"狀態碼: {response.status_code}, 響應時間: {duration:.3f}秒",
                                   api_results[api_name], duration)
                
            except Exception as e:
                api_results[api_name] = {
                    'error': str(e),
                    'success': False
                }
                self.log_test_result('api_response', api_name, False, f"API請求失敗: {str(e)}")
        
        return api_results

    def test_performance_metrics(self) -> Dict[str, Any]:
        """測試性能指標"""
        print("\n⚡ 測試性能指標...")
        
        performance_results = {}
        
        # 1. 併發負載測試
        print("執行併發負載測試...")
        concurrent_results = self._run_concurrent_test()
        performance_results['concurrent_load'] = concurrent_results
        
        # 2. 頁面載入時間測試
        print("測試頁面載入時間...")
        page_load_results = self._test_page_load_times()
        performance_results['page_load_times'] = page_load_results
        
        # 3. 記憶體使用監控
        print("監控記憶體使用...")
        memory_results = self._monitor_memory_usage()
        performance_results['memory_monitoring'] = memory_results
        
        return performance_results

    def _run_concurrent_test(self, num_workers: int = 10, requests_per_worker: int = 5) -> Dict[str, Any]:
        """執行併發測試"""
        
        def make_request(url):
            start_time = time.time()
            try:
                response = requests.get(url, timeout=10)
                return {
                    'success': True,
                    'status_code': response.status_code,
                    'response_time': time.time() - start_time,
                    'content_length': len(response.content)
                }
            except Exception as e:
                return {
                    'success': False,
                    'error': str(e),
                    'response_time': time.time() - start_time
                }
        
        # 測試主頁的併發性能
        test_url = f"{self.base_url}/"
        total_requests = num_workers * requests_per_worker
        
        start_time = time.time()
        
        with concurrent.futures.ThreadPoolExecutor(max_workers=num_workers) as executor:
            futures = [executor.submit(make_request, test_url) for _ in range(total_requests)]
            results = [f.result() for f in concurrent.futures.as_completed(futures)]
        
        total_time = time.time() - start_time
        
        successful_requests = sum(1 for r in results if r['success'])
        avg_response_time = sum(r['response_time'] for r in results) / len(results)
        
        concurrent_results = {
            'total_requests': total_requests,
            'successful_requests': successful_requests,
            'failed_requests': total_requests - successful_requests,
            'success_rate': successful_requests / total_requests * 100,
            'total_time': total_time,
            'requests_per_second': total_requests / total_time,
            'avg_response_time': avg_response_time
        }
        
        self.log_test_result('performance_metrics', 'concurrent_load', True,
                           f"成功率: {concurrent_results['success_rate']:.1f}%, "
                           f"RPS: {concurrent_results['requests_per_second']:.1f}, "
                           f"平均響應時間: {avg_response_time:.3f}秒",
                           concurrent_results, total_time)
        
        return concurrent_results

    def _test_page_load_times(self) -> Dict[str, Any]:
        """測試頁面載入時間"""
        page_results = {}
        
        # 測試主要頁面的載入時間
        test_pages = [
            '/',
            '/analytics',
            '/email',
            '/eqc',
            '/files',
            '/monitoring',
            '/tasks'
        ]
        
        for page in test_pages:
            times = []
            for i in range(3):  # 測試3次取平均
                start_time = time.time()
                try:
                    response = requests.get(f"{self.base_url}{page}", timeout=10)
                    load_time = time.time() - start_time
                    if response.status_code == 200:
                        times.append(load_time)
                except:
                    pass
            
            if times:
                avg_time = sum(times) / len(times)
                page_results[page] = {
                    'avg_load_time': avg_time,
                    'min_load_time': min(times),
                    'max_load_time': max(times),
                    'successful_loads': len(times)
                }
                
                self.log_test_result('performance_metrics', f'page_load{page}', True,
                                   f"平均載入時間: {avg_time:.3f}秒", avg_time)
            else:
                page_results[page] = {'error': '所有載入嘗試失敗'}
                self.log_test_result('performance_metrics', f'page_load{page}', False,
                                   "頁面載入失敗")
        
        return page_results

    def _monitor_memory_usage(self) -> Dict[str, Any]:
        """監控記憶體使用"""
        try:
            system_memory = psutil.virtual_memory()
            
            # 查找相關的Python進程
            python_processes = []
            for proc in psutil.process_iter(['pid', 'name', 'memory_info', 'cpu_percent']):
                try:
                    if 'python' in proc.info['name'].lower():
                        memory_mb = proc.info['memory_info'].rss / 1024 / 1024
                        python_processes.append({
                            'pid': proc.info['pid'],
                            'memory_mb': memory_mb,
                            'cpu_percent': proc.info['cpu_percent']
                        })
                except:
                    continue
            
            memory_results = {
                'system_memory_total_gb': system_memory.total / 1024 / 1024 / 1024,
                'system_memory_used_percent': system_memory.percent,
                'python_processes': python_processes,
                'total_python_memory_mb': sum(p['memory_mb'] for p in python_processes)
            }
            
            self.log_test_result('performance_metrics', 'memory_monitoring', True,
                               f"系統記憶體使用: {system_memory.percent:.1f}%, "
                               f"Python進程總記憶體: {memory_results['total_python_memory_mb']:.2f}MB",
                               memory_results)
            
            return memory_results
            
        except Exception as e:
            self.log_test_result('performance_metrics', 'memory_monitoring', False,
                               f"記憶體監控失敗: {str(e)}")
            return {'error': str(e)}

    def test_error_handling(self) -> Dict[str, Any]:
        """測試錯誤處理一致性"""
        print("\n🚨 測試錯誤處理...")
        
        error_tests = {
            'invalid_route': '/nonexistent/route',
            'invalid_api': '/api/nonexistent',
            'malformed_request': '/api/files/upload?invalid=param'
        }
        
        error_results = {}
        
        for test_name, endpoint in error_tests.items():
            try:
                response = requests.get(f"{self.base_url}{endpoint}", timeout=5)
                
                error_results[test_name] = {
                    'status_code': response.status_code,
                    'has_error_page': 'error' in response.text.lower() or response.status_code in [404, 500],
                    'response_size': len(response.content)
                }
                
                # 檢查是否有適當的錯誤處理
                appropriate_error = response.status_code in [404, 400, 500]
                
                self.log_test_result('error_handling', test_name, appropriate_error,
                                   f"狀態碼: {response.status_code}, 錯誤頁面: {error_results[test_name]['has_error_page']}")
                
            except Exception as e:
                error_results[test_name] = {'error': str(e)}
                self.log_test_result('error_handling', test_name, False, f"錯誤測試失敗: {str(e)}")
        
        return error_results

    def generate_comparison_summary(self) -> Dict[str, Any]:
        """生成對比摘要"""
        print("\n📊 生成對比摘要...")
        
        summary = {
            'overall_health': True,
            'critical_issues': [],
            'warnings': [],
            'performance_summary': {},
            'functional_summary': {},
            'recommendations': []
        }
        
        # 分析功能完整性結果
        functional_issues = 0
        total_endpoints = 0
        
        if 'functional_integrity' in self.results:
            for module, endpoints in self.results['functional_integrity'].items():
                for endpoint, result in endpoints.items():
                    total_endpoints += 1
                    if not result.get('success', False):
                        functional_issues += 1
                        summary['critical_issues'].append(f"功能問題: {module}{endpoint}")
        
        summary['functional_summary'] = {
            'total_endpoints': total_endpoints,
            'failed_endpoints': functional_issues,
            'success_rate': ((total_endpoints - functional_issues) / total_endpoints * 100) if total_endpoints > 0 else 0
        }
        
        # 分析性能結果
        if 'performance_metrics' in self.results:
            perf_data = self.results['performance_metrics']
            
            # 檢查併發性能
            if 'concurrent_load' in perf_data and 'data' in perf_data['concurrent_load']:
                concurrent_data = perf_data['concurrent_load']['data']
                if concurrent_data.get('success_rate', 0) < 95:
                    summary['warnings'].append(f"併發成功率較低: {concurrent_data.get('success_rate', 0):.1f}%")
                
                summary['performance_summary']['concurrent_success_rate'] = concurrent_data.get('success_rate', 0)
                summary['performance_summary']['requests_per_second'] = concurrent_data.get('requests_per_second', 0)
        
        # 總體健康檢查
        if functional_issues > 0:
            summary['overall_health'] = False
            summary['critical_issues'].append(f"發現 {functional_issues} 個功能問題")
        
        # 建議
        if functional_issues == 0:
            summary['recommendations'].append("✅ 所有功能測試通過，與原始版本功能一致")
        else:
            summary['recommendations'].append(f"❌ 需要修復 {functional_issues} 個功能問題")
        
        if summary['performance_summary'].get('concurrent_success_rate', 0) >= 95:
            summary['recommendations'].append("✅ 併發性能表現良好")
        
        summary['recommendations'].append("建議進行更深入的業務邏輯測試")
        summary['recommendations'].append("建議與main分支進行實際對比測試")
        
        return summary

    def run_complete_comparison(self) -> Dict[str, Any]:
        """執行完整的對比測試"""
        print("=" * 80)
        print("🔬 分支對比測試系統")
        print(f"當前分支: {self.current_branch}")
        print(f"基準分支: {self.base_branch}")
        print(f"測試目標: {self.base_url}")
        print("=" * 80)
        
        # 檢查服務是否運行
        if not self.check_service_running():
            print("❌ 服務未運行，請先啟動服務")
            return None
        
        try:
            # 1. 啟動性能測試
            self.results['startup_performance'] = self.measure_startup_performance()
            
            # 2. 功能完整性測試
            self.results['functional_integrity'] = self.test_functional_integrity()
            
            # 3. API響應測試
            self.results['api_response'] = self.test_api_responses()
            
            # 4. 性能指標測試
            self.results['performance_metrics'] = self.test_performance_metrics()
            
            # 5. 錯誤處理測試
            self.results['error_handling'] = self.test_error_handling()
            
            # 6. 生成對比摘要
            self.results['comparison_summary'] = self.generate_comparison_summary()
            
            # 添加測試完成時間
            self.results['test_metadata']['test_end_time'] = datetime.now().isoformat()
            self.results['test_metadata']['total_duration'] = (
                datetime.fromisoformat(self.results['test_metadata']['test_end_time']) - 
                datetime.fromisoformat(self.results['test_metadata']['test_start_time'])
            ).total_seconds()
            
            return self.results
            
        except Exception as e:
            print(f"❌ 測試執行過程中發生錯誤: {str(e)}")
            return None

    def save_results(self, filename: str = None) -> str:
        """保存測試結果"""
        if filename is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"branch_comparison_report_{timestamp}.json"
        
        filepath = self.project_root / filename
        
        with open(filepath, 'w', encoding='utf-8') as f:
            json.dump(self.results, f, indent=2, ensure_ascii=False)
        
        print(f"\n📄 測試報告已保存: {filepath}")
        return str(filepath)

    def print_summary(self):
        """打印測試摘要"""
        if 'comparison_summary' not in self.results:
            return
        
        summary = self.results['comparison_summary']
        
        print("\n" + "=" * 80)
        print("📋 測試摘要")
        print("=" * 80)
        
        print(f"總體健康狀態: {'✅ 良好' if summary['overall_health'] else '❌ 有問題'}")
        
        if summary['functional_summary']:
            func_summary = summary['functional_summary']
            print(f"功能完整性: {func_summary['success_rate']:.1f}% ({func_summary['total_endpoints'] - func_summary['failed_endpoints']}/{func_summary['total_endpoints']})")
        
        if summary['performance_summary']:
            perf_summary = summary['performance_summary']
            if 'concurrent_success_rate' in perf_summary:
                print(f"併發性能: {perf_summary['concurrent_success_rate']:.1f}%")
            if 'requests_per_second' in perf_summary:
                print(f"每秒請求數: {perf_summary['requests_per_second']:.1f}")
        
        if summary['critical_issues']:
            print(f"\n🚨 關鍵問題 ({len(summary['critical_issues'])}):")
            for issue in summary['critical_issues']:
                print(f"  • {issue}")
        
        if summary['warnings']:
            print(f"\n⚠️ 警告 ({len(summary['warnings'])}):")
            for warning in summary['warnings']:
                print(f"  • {warning}")
        
        if summary['recommendations']:
            print(f"\n💡 建議:")
            for rec in summary['recommendations']:
                print(f"  • {rec}")
        
        print("=" * 80)


def main():
    """主函數"""
    if len(sys.argv) > 1 and sys.argv[1] == '--help':
        print("""
分支對比測試系統
用法: python branch_comparison_test.py [選項]

選項:
  --help          顯示此幫助信息
  --current-branch BRANCH  指定當前分支名稱 (默認: task/6-testing-validation)
  --base-branch BRANCH     指定基準分支名稱 (默認: main)
  --url URL               指定測試URL (默認: http://localhost:8000)

範例:
  python branch_comparison_test.py
  python branch_comparison_test.py --current-branch feature/new-api --base-branch develop
        """)
        return
    
    # 解析命令行參數
    current_branch = "task/6-testing-validation"
    base_branch = "main"
    
    for i, arg in enumerate(sys.argv):
        if arg == '--current-branch' and i + 1 < len(sys.argv):
            current_branch = sys.argv[i + 1]
        elif arg == '--base-branch' and i + 1 < len(sys.argv):
            base_branch = sys.argv[i + 1]
    
    # 創建測試器並運行測試
    tester = BranchComparisonTester(current_branch, base_branch)
    results = tester.run_complete_comparison()
    
    if results:
        # 保存結果
        report_file = tester.save_results()
        
        # 打印摘要
        tester.print_summary()
        
        print(f"\n✅ 對比測試完成！詳細報告: {report_file}")
    else:
        print("\n❌ 對比測試失敗")
        sys.exit(1)


if __name__ == "__main__":
    main()