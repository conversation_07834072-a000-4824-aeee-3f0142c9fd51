// 網路共享瀏覽器 - 主要入口點和模組協調器
// 使用 ES6 模組化架構，整合所有功能模組

// 導入模組
import { NetworkConnection } from './modules/network-connection.js';
import { FileManager } from './modules/file-manager.js';
import { SearchEngine } from './modules/search-engine.js';
import { FileProcessor } from './modules/file-processor.js';
import { UIComponents } from './modules/ui-components.js';

// API 配置
const API = '/api';

// 全域狀態變數
let currentPath = '\\\\192.168.1.60\\test_log';
let isConnected = false;
let isAuthenticated = false;

// 模組實例
let networkConnection;
let fileManager;
let searchEngine;
let fileProcessor;
let uiComponents;

// 初始化所有模組
function initializeModules() {
    networkConnection = new NetworkConnection(API);
    fileManager = new FileManager(API);
    searchEngine = new SearchEngine(API);
    fileProcessor = new FileProcessor(API);
    uiComponents = new UIComponents();
}

// 狀態管理包裝函數（保持原有介面）
function showStatus(msg, type = 'loading') {
    uiComponents.showStatus(msg, type);
}

function debugLog(msg) {
    uiComponents.debugLog(msg);
}

// 網路連接功能
async function connectToShare() {
    const result = await networkConnection.connectToShare(currentPath, showStatus);
    
    if (result.success) {
        isConnected = true;
        // 顯示當前用戶歡迎訊息
        await networkConnection.showWelcomeMessage();
        await loadFiles();
    } else {
        showStatus(`連接失敗：${result.message}`, 'error');
    }
}

// 檔案載入功能
async function loadFiles(subpath = '') {
    if (!isConnected) {
        showStatus('請先連接網路共享', 'error');
        return;
    }

    const result = await fileManager.loadFiles(currentPath, subpath, showStatus);
    if (result.success && subpath) {
        currentPath = fileManager.normalizePath(currentPath + '\\' + subpath);
        document.getElementById('currentPath').textContent = currentPath;
    }
}

// 導航功能
function navigateToFolder(folderName) {
    if (!isConnected) {
        debugLog('未連接，無法導航');
        return;
    }

    const newPath = fileManager.navigateToFolder(folderName, currentPath, loadFiles, debugLog);
    currentPath = newPath;
    loadFiles();
}

function navigateUp() {
    if (!isConnected) return;

    const newPath = fileManager.navigateUp(currentPath, debugLog);
    if (newPath !== currentPath) {
        currentPath = newPath;
        loadFiles();
    }
}

// 檔案下載功能
async function downloadFile(filename) {
    await fileManager.downloadFile(filename, currentPath, showStatus);
}

// 檔案過濾功能
function filterFiles() {
    const searchTerm = uiComponents.getInputValue('searchInput');
    const startDate = uiComponents.getInputValue('startDate');
    const endDate = uiComponents.getInputValue('endDate');

    fileManager.filterFiles(searchTerm, startDate, endDate);
}

function clearFilters() {
    fileManager.clearFilters();
}

// 檔案選擇功能
function updateFileSelection() {
    return fileProcessor.updateFileSelection();
}

function selectAllFiles() {
    return fileProcessor.selectAllFiles();
}

function clearSelection() {
    return fileProcessor.clearSelection();
}

// 檔案處理功能
async function processSelectedFiles(toolType) {
    await fileProcessor.processSelectedFiles(toolType, currentPath, showStatus);
}

// 產品搜尋功能
async function performProductSearch() {
    const productName = uiComponents.getInputValue('productNameInput');
    const searchParams = {
        searchDirectory: uiComponents.getSelectValue('searchDirectorySelect'),
        timeRangeType: uiComponents.getSelectValue('timeRangeSelect'),
        selectedFileTypes: uiComponents.getMultiSelectValues('fileTypeSelect'),
        minSize: parseFloat(uiComponents.getInputValue('minSizeInput')) || null,
        maxSize: parseFloat(uiComponents.getInputValue('maxSizeInput')) || null,
        includeDirs: uiComponents.getCheckboxState('includeDirsCheckbox'),
        maxResults: parseInt(uiComponents.getInputValue('maxResultsInput')) || 1000,
        startDate: uiComponents.getInputValue('customStartDate'),
        endDate: uiComponents.getInputValue('customEndDate')
    };

    await searchEngine.performProductSearch(
        productName, 
        currentPath, 
        searchParams, 
        (msg, type) => uiComponents.showProductSearchStatus(msg, type),
        handleProductSearchResult
    );
}

function handleProductSearchResult(result) {
    if (result.error) {
        uiComponents.showProductSearchStatus(`❌ ${result.message}`, 'error');
        return;
    }

    if (result.emptyResult) {
        // 處理空結果
        if (result.totalFilesInDirectory && result.totalFilesInDirectory > 0) {
            const statusMsg = `⚠️ 找到產品資料夾，但沒有符合時間範圍的檔案\n📁 該目錄共有 ${result.totalFilesInDirectory} 個檔案，但都不符合所選時間範圍\n⏱️ 搜尋時間: ${result.searchDuration}秒 (伺服器: ${result.serverDuration}秒)`;
            uiComponents.showProductSearchStatus(statusMsg, 'warning');
            showStatus(`找到產品資料夾，但沒有符合條件的檔案`, 'warning');

            // 顯示特殊的空狀態
            document.getElementById('fileList').innerHTML =
                '<div class="empty-state">' +
                '<i class="fas fa-clock" style="font-size: 3em; color: #ffc107; margin-bottom: 15px;"></i>' +
                '<h4>找到產品資料夾，但沒有符合時間範圍的檔案</h4>' +
                `<p class="text-muted">該目錄共有 <strong>${result.totalFilesInDirectory}</strong> 個檔案</p>` +
                '<p class="text-muted">請嘗試調整時間範圍或檔案篩選條件</p>' +
                '</div>';
        } else {
            const statusMsg = `⚠️ 沒有找到符合條件的檔案\n⏱️ 搜尋時間: ${result.searchDuration}秒 (伺服器: ${result.serverDuration}秒)`;
            uiComponents.showProductSearchStatus(statusMsg, 'warning');
            showStatus('沒有找到符合條件的檔案', 'warning');

            document.getElementById('fileList').innerHTML =
                '<div class="empty-state">' +
                '<i class="fas fa-folder-open"></i>' +
                '<p>此目錄為空或無法存取</p>' +
                '</div>';
        }
    } else {
        // 顯示搜尋結果
        fileManager.setFiles(result.files);
        
        const statusMsg = `🎉 產品搜尋完成！找到 ${result.totalFiles} 個檔案，總大小 ${result.totalSizeMB.toFixed(2)} MB\n⏱️ 搜尋時間: ${result.searchDuration}秒 (伺服器: ${result.serverDuration}秒)`;
        uiComponents.showProductSearchStatus(statusMsg, 'success');
        showStatus(`搜尋完成: 找到 ${result.totalFiles} 個檔案`, 'success');

        // 更新路徑顯示
        if (result.productFolder) {
            document.getElementById('currentPath').textContent = `產品搜尋結果: ${result.productFolder}`;
        }
    }
}

function cancelProductSearch() {
    searchEngine.cancelProductSearch((msg, type) => uiComponents.showProductSearchStatus(msg, type));
    showStatus('產品搜尋已取消', 'error');
}

function clearProductSearch() {
    uiComponents.clearProductSearchForm();
    
    // 重新載入原始檔案列表
    if (isConnected) {
        loadFiles();
    }
}

// 智慧搜尋功能
async function performSmartSearch() {
    const query = uiComponents.getInputValue('smartSearchInput');
    const maxResults = parseInt(uiComponents.getInputValue('smartMaxResultsInput')) || 100;

    await searchEngine.performSmartSearch(
        query,
        currentPath,
        maxResults,
        (msg, type) => uiComponents.showSmartSearchStatus(msg, type),
        handleSmartSearchResult
    );
}

function handleSmartSearchResult(result) {
    if (!result.success) {
        if (result.interpretation) {
            uiComponents.displaySmartSearchInterpretation(result.interpretation);
        }
        if (result.analysis) {
            uiComponents.displaySmartSearchAnalysis(result.analysis);
        }
        if (result.suggestions) {
            uiComponents.displaySmartSearchSuggestions(result.suggestions);
        }
        uiComponents.showElement('smartSearchResults');
        return;
    }

    // 顯示成功結果
    fileManager.setFiles(result.files);
    
    // 顯示分析結果
    uiComponents.displaySmartSearchInterpretation(result.interpretation);
    uiComponents.displaySmartSearchAnalysis(result.analysis);
    uiComponents.displaySmartSearchSuggestions(result.suggestions);

    // 更新路徑顯示
    document.getElementById('currentPath').textContent = `智慧搜尋結果: "${result.query}"`;

    // 顯示結果面板
    uiComponents.showElement('smartSearchResults');
}

function clearSmartSearch() {
    uiComponents.clearSmartSearchForm();
    
    // 重新載入原始檔案列表
    if (isConnected) {
        loadFiles();
    }
}

// UI 輔助功能
function showProcessingStatus() {
    fileProcessor.showProcessingStatus();
}

function hideProcessingStatus() {
    fileProcessor.hideProcessingStatus();
}

function toggleCustomDateRange() {
    uiComponents.toggleCustomDateRange();
}

function logout() {
    uiComponents.logout();
}

// 登入功能（佔位符，因為應用程序跳過登入檢查）
function performLogin() {
    // 這是一個佔位符函數，因為應用程序配置為跳過登入檢查
    console.log('登入功能被調用，但應用程序配置為自動連接');
    document.getElementById('loginModal').style.display = 'none';
    document.getElementById('mainContainer').style.display = 'block';
}

// 事件監聽器和初始化
window.addEventListener('load', function() {
    // 初始化所有模組
    initializeModules();
    
    // 直接顯示主要內容，跳過登入檢查
    isAuthenticated = true;
    document.getElementById('loginModal').style.display = 'none';
    document.getElementById('mainContainer').style.display = 'block';

    // 自動連接
    showStatus('網路共享瀏覽器正在自動連接...', 'loading');
    setTimeout(() => {
        connectToShare();
    }, 1000);
});

// DOM 載入完成後的初始化
document.addEventListener('DOMContentLoaded', function() {
    // 時間範圍選擇器事件監聽
    const timeRangeSelect = document.getElementById('timeRangeSelect');
    if (timeRangeSelect) {
        timeRangeSelect.addEventListener('change', toggleCustomDateRange);
    }
});

// 將函數暴露到全域範圍（為了與現有 HTML 的 onclick 事件相容）
window.connectToShare = connectToShare;
window.loadFiles = loadFiles;
window.navigateToFolder = navigateToFolder;
window.navigateUp = navigateUp;
window.downloadFile = downloadFile;
window.filterFiles = filterFiles;
window.clearFilters = clearFilters;
window.updateFileSelection = updateFileSelection;
window.selectAllFiles = selectAllFiles;
window.clearSelection = clearSelection;
window.processSelectedFiles = processSelectedFiles;
window.performProductSearch = performProductSearch;
window.cancelProductSearch = cancelProductSearch;
window.clearProductSearch = clearProductSearch;
window.performSmartSearch = performSmartSearch;
window.clearSmartSearch = clearSmartSearch;
window.showProcessingStatus = showProcessingStatus;
window.hideProcessingStatus = hideProcessingStatus;
window.toggleCustomDateRange = toggleCustomDateRange;
window.logout = logout;
window.performLogin = performLogin;