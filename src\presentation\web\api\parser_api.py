"""
解析相關 API
提供郵件解析的 RESTful API
"""

from flask import Blueprint, jsonify, request
from datetime import datetime
from functools import wraps
import re
import os
from typing import Dict, Any, Optional

from src.infrastructure.parsers.base_parser import ParserFactory
from src.infrastructure.adapters.database.email_database import EmailDatabase
from src.infrastructure.adapters.database.models import EmailDB
from src.data_models.email_models import EmailData, VendorIdentificationResult, EmailParsingResult
from src.infrastructure.logging.logger_manager import LoggerManager


parser_bp = Blueprint('parser', __name__, url_prefix='/api/parser')
logger = LoggerManager().get_logger("ParserAPI")

# Add CORS headers to all responses
@parser_bp.after_request
def after_request(response):
    """Add security headers to all responses"""
    response.headers.add('Access-Control-Allow-Origin', '*')
    response.headers.add('Access-Control-Allow-Headers', 'Content-Type,Authorization,X-API-Key')
    response.headers.add('Access-Control-Allow-Methods', 'GET,PUT,POST,DELETE,OPTIONS')
    response.headers.add('X-Content-Type-Options', 'nosniff')
    response.headers.add('X-Frame-Options', 'DENY')
    response.headers.add('X-XSS-Protection', '1; mode=block')
    return response

# Handle OPTIONS requests for CORS
@parser_bp.route('/<path:path>', methods=['OPTIONS'])
def handle_options(path):
    """Handle CORS preflight requests"""
    return '', 200

# Global error handlers
@parser_bp.errorhandler(400)
def bad_request(error):
    """Handle bad request errors"""
    logger.warning(f"Bad request: {error}")
    return jsonify({
        'success': False,
        'error': 'Bad request',
        'code': 'BAD_REQUEST'
    }), 400

@parser_bp.errorhandler(401)
def unauthorized(error):
    """Handle unauthorized errors"""
    logger.warning(f"Unauthorized access: {error}")
    return jsonify({
        'success': False,
        'error': 'Unauthorized',
        'code': 'UNAUTHORIZED'
    }), 401

@parser_bp.errorhandler(404)
def not_found(error):
    """Handle not found errors"""
    logger.warning(f"Resource not found: {error}")
    return jsonify({
        'success': False,
        'error': 'Resource not found',
        'code': 'NOT_FOUND'
    }), 404

@parser_bp.errorhandler(429)
def rate_limit_exceeded(error):
    """Handle rate limit exceeded errors"""
    logger.warning(f"Rate limit exceeded: {error}")
    return jsonify({
        'success': False,
        'error': 'Rate limit exceeded',
        'code': 'RATE_LIMIT_EXCEEDED'
    }), 429

@parser_bp.errorhandler(500)
def internal_error(error):
    """Handle internal server errors"""
    logger.error(f"Internal server error: {error}")
    return jsonify({
        'success': False,
        'error': 'Internal server error',
        'code': 'INTERNAL_ERROR'
    }), 500

# Security Configuration
API_KEY_HEADER = 'X-API-Key'
ALLOWED_API_KEYS = set([
    os.environ.get('PARSER_API_KEY', 'dev-parser-key-12345'),
    os.environ.get('ADMIN_API_KEY', 'dev-admin-key-67890')
])

# Rate limiting storage (in production, use Redis or similar)
_rate_limit_storage = {}
RATE_LIMIT_REQUESTS = 100  # requests per hour per IP
RATE_LIMIT_WINDOW = 3600   # 1 hour in seconds


def require_api_key(f):
    """API key authentication decorator"""
    @wraps(f)
    def decorated_function(*args, **kwargs):
        # Skip authentication in development if explicitly disabled
        if os.environ.get('SKIP_API_AUTH', 'False').lower() == 'true':
            logger.warning("API authentication is disabled - development mode only!")
            return f(*args, **kwargs)
            
        api_key = request.headers.get(API_KEY_HEADER)
        if not api_key:
            logger.warning(f"Missing API key for endpoint: {request.endpoint}")
            return jsonify({
                'success': False,
                'error': 'API key required',
                'code': 'MISSING_API_KEY'
            }), 401
            
        if api_key not in ALLOWED_API_KEYS:
            logger.warning(f"Invalid API key attempted for endpoint: {request.endpoint}")
            return jsonify({
                'success': False,
                'error': 'Invalid API key',
                'code': 'INVALID_API_KEY'
            }), 401
            
        return f(*args, **kwargs)
    return decorated_function


def validate_email_id(email_id: int) -> bool:
    """Validate email ID parameter"""
    return isinstance(email_id, int) and email_id > 0


def validate_json_payload(required_fields: list = None, optional_fields: list = None) -> Dict[str, Any]:
    """Validate JSON payload with required and optional fields"""
    try:
        data = request.get_json()
        if not data:
            raise ValueError("No JSON payload provided")
            
        # Check required fields
        if required_fields:
            missing_fields = [field for field in required_fields if field not in data]
            if missing_fields:
                raise ValueError(f"Missing required fields: {', '.join(missing_fields)}")
        
        # Sanitize string inputs
        for key, value in data.items():
            if isinstance(value, str):
                # Basic XSS prevention
                data[key] = re.sub(r'[<>"\']', '', value.strip())
        
        return data
    except Exception as e:
        logger.error(f"JSON payload validation failed: {e}")
        raise


def validate_vendor_code(vendor_code: str) -> bool:
    """Validate vendor code format"""
    if not vendor_code or not isinstance(vendor_code, str):
        return False
    # Allow alphanumeric and common separators
    return bool(re.match(r'^[A-Za-z0-9_-]+$', vendor_code.strip()))


def validate_product_code(product_code: str) -> bool:
    """Validate product code format"""
    if not product_code or not isinstance(product_code, str):
        return False
    # Allow alphanumeric, parentheses, and common separators
    return bool(re.match(r'^[A-Za-z0-9()._-]+$', product_code.strip()))


def validate_lot_number(lot_number: str) -> bool:
    """Validate lot number format"""
    if not lot_number or not isinstance(lot_number, str):
        return False
    # Allow alphanumeric and common separators
    return bool(re.match(r'^[A-Za-z0-9._-]+$', lot_number.strip()))


def validate_yield_value(yield_value: float) -> bool:
    """Validate yield value"""
    try:
        value = float(yield_value)
        return 0.0 <= value <= 100.0
    except (TypeError, ValueError):
        return False

# 🔧 修復：使用統一的 ParserFactory（自動註冊所有解析器）
# 不再需要手動註冊解析器，ParserFactory 會自動處理
parser_factory = ParserFactory()
database = EmailDatabase()


@parser_bp.route('/emails/<int:email_id>/reparse', methods=['POST'])
@require_api_key
def reparse_email(email_id: int):
    """重新解析郵件"""
    try:
        # Input validation
        if not validate_email_id(email_id):
            return jsonify({
                'success': False,
                'error': '無效的郵件ID',
                'code': 'INVALID_EMAIL_ID'
            }), 400
        # 取得郵件資料
        email = database.get_email_by_id(email_id)
        if not email:
            return jsonify({
                'success': False, 
                'error': '郵件不存在'
            }), 404
            
        # 轉換為 EmailData
        email_data = EmailData(
            message_id=email['message_id'],
            subject=email['subject'],
            sender=email['sender'],
            body=email.get('body', ''),
            received_time=datetime.fromisoformat(email['received_time'])
        )
        
        # 檢查是否為 ETD ANF 格式，如果是則強制使用傳統解析器
        use_traditional_parser = False
        if 'anf' in email_data.subject.lower():
            use_traditional_parser = True
            logger.info(f"檢測到 ANF 格式郵件，強制使用傳統解析器: {email_data.subject}")
        
        if use_traditional_parser:
            # 強制使用傳統解析器處理 ETD ANF 格式
            raise Exception("ANF 格式郵件使用傳統解析器")
        
        # 🔧 修復：使用統一的 ALL IN ONE 郵件處理服務
        try:
            from src.application.services.unified_email_processor import UnifiedEmailProcessor

            # 創建統一處理器
            unified_processor = UnifiedEmailProcessor()

            # 使用 ALL IN ONE 完整流程處理郵件
            import asyncio
            processing_result = asyncio.run(unified_processor.process_email_complete(
                email_data=email_data,
                email_id=None,  # Web API 通常沒有 email_id
                process_attachments=True,   # ✅ 處理附件
                process_vendor_files=True,  # ✅ 處理廠商檔案
                send_notifications=True,    # ✅ 發送LINE通知
                update_database=True        # ✅ 更新資料庫
            ))

            # 轉換為 API 回應格式
            result = {
                'success': processing_result.is_success,
                'vendor': processing_result.vendor_code,
                'vendor_name': processing_result.vendor_name,
                'pd': processing_result.parsing_result.product_code,
                'lot': processing_result.parsing_result.lot_number,
                'mo': processing_result.parsing_result.mo_number,
                'yield': processing_result.parsing_result.extracted_data.get('yield_value') if processing_result.parsing_result.extracted_data else None,
                'confidence': processing_result.vendor_result.confidence_score,
                'error': processing_result.error_message,
                'extraction_method': processing_result.parsing_result.extraction_method,
                'processing_time': processing_result.processing_time,
                'attachment_processed': processing_result.attachment_result is not None,
                'vendor_files_processed': processing_result.vendor_files_result is not None,
                'notification_sent': processing_result.notification_sent,
                'database_updated': processing_result.database_updated
            }

            logger.info(f"Web API ALL IN ONE 處理完成: {email_data.message_id}")
            logger.info(f"  成功: {processing_result.is_success}")
            logger.info(f"  廠商: {processing_result.vendor_code}")
            logger.info(f"  解析方法: {processing_result.parsing_result.extraction_method}")
            logger.info(f"  處理時間: {processing_result.processing_time:.2f}s")
            
        except Exception as llm_error:
            logger.info(f"降級到傳統解析器: {llm_error}")
            # 降級到傳統解析器
            result = parser_factory.parse_email(email_data)
            if isinstance(result, tuple) and len(result) == 2:
                vendor_result, parsing_result = result
            else:
                vendor_result = VendorIdentificationResult(
                    vendor_code=None,
                    vendor_name=None,
                    confidence_score=0.0,
                    matching_patterns=[],
                    is_identified=False
                )
                parsing_result = result if result else EmailParsingResult(
                    is_success=False,
                    error_message="解析器返回無效結果",
                    validation_errors=["解析器返回格式錯誤"]
                )
            
            result = {
                'success': parsing_result.is_success,
                'vendor': vendor_result.vendor_code,
                'vendor_name': vendor_result.vendor_name,
                'pd': parsing_result.extracted_data.get('product') or parsing_result.extracted_data.get('product_name'),
                'lot': parsing_result.lot_number,
                'mo': parsing_result.mo_number,  # 添加 MO 編號欄位
                'yield': parsing_result.extracted_data.get('yield_value'),
                'confidence': vendor_result.confidence_score,
                'error': parsing_result.error_message,
                'extraction_method': parsing_result.extraction_method
            }
        
        # 更新資料庫 - 無論成功或失敗都要更新時間戳
        from src.infrastructure.adapters.database.models import EmailDB
        import json
        
        with database.get_session() as session:
            email_db = session.query(EmailDB).filter_by(id=email_id).first()
            if email_db:
                # 總是更新解析時間，記錄嘗試
                email_db.parsed_at = datetime.now()
                
                # 如果使用了 unified_llm，更新 LLM 分析相關欄位
                extraction_method = result.get('extraction_method')
                logger.info(f"重新解析 - 解析方法: {extraction_method}")
                
                if extraction_method == 'unified_llm':
                    try:
                        current_time = datetime.now()
                        logger.info(f"更新 LLM 分析欄位 - 時間: {current_time}")
                        
                        # 儲存 LLM 分析結果
                        llm_analysis_data = {
                            'success': result.get('success'),
                            'vendor_code': result.get('vendor'),
                            'vendor_name': result.get('vendor_name'),
                            'product_code': result.get('pd'),
                            'lot_number': result.get('lot'),
                            'yield_rate': result.get('yield'),
                            'confidence_score': result.get('confidence'),
                            'error_message': result.get('error'),
                            'llm_provider': result.get('llm_provider'),
                            'llm_model': result.get('llm_model'),
                            'analysis_reasoning': result.get('analysis_reasoning'),
                            'analysis_timestamp': current_time.isoformat()
                        }
                        email_db.llm_analysis_result = json.dumps(llm_analysis_data, ensure_ascii=False)
                        email_db.llm_analysis_timestamp = current_time
                        email_db.llm_service_used = result.get('llm_provider', 'grok')
                        
                        logger.info(f"LLM 分析欄位更新完成 - timestamp: {email_db.llm_analysis_timestamp}")
                    except Exception as e:
                        logger.error(f"更新 LLM 分析欄位失敗: {e}")
                        import traceback
                        traceback.print_exc()
                
                if result['success']:
                    # 成功時更新所有欄位
                    email_db.vendor_code = result.get('vendor')
                    email_db.pd = result.get('pd')
                    email_db.lot = result.get('lot')
                    email_db.mo = result.get('mo')  # 添加 MO 欄位更新
                    email_db.yield_value = result.get('yield')
                    email_db.extraction_method = result.get('extraction_method')
                    email_db.parse_status = 'parsed'
                    email_db.parse_error = None
                else:
                    # 失敗時記錄錯誤但保持時間戳
                    email_db.parse_status = 'failed'
                    email_db.parse_error = result.get('error', '解析失敗')
                    email_db.extraction_method = result.get('extraction_method', 'unified_llm')
                
                session.commit()

                # 🗑️ 已刪除：重複的通知和處理邏輯
                # 現在統一使用 UnifiedEmailProcessor 處理所有後續步驟

        return jsonify({
            'success': True,
            'data': result
        })
        
    except Exception as e:
        logger.error(f"重新解析郵件失敗: {e}")
        
        # 如果是 ANF 格式郵件的處理錯誤，嘗試降級到傳統解析器
        if "ANF 格式郵件使用傳統解析器" in str(e):
            try:
                logger.info("ANF 格式郵件降級到傳統解析器")
                # 重新構建 email_data
                email = database.get_email_by_id(email_id)
                if email:
                    email_data = EmailData(
                        message_id=email['message_id'],
                        subject=email['subject'],
                        sender=email['sender'],
                        body=email.get('body', ''),
                        received_time=datetime.fromisoformat(email['received_time'])
                    )
                    
                    # 使用傳統解析器
                    vendor_result, parsing_result = parser_factory.parse_email(email_data)
                    
                    result = {
                        'success': parsing_result.is_success,
                        'vendor': vendor_result.vendor_code,
                        'vendor_name': vendor_result.vendor_name,
                        'pd': parsing_result.extracted_data.get('product') or parsing_result.extracted_data.get('product_name'),
                        'mo': parsing_result.mo_number,
                        'lot': parsing_result.lot_number,
                        'yield': parsing_result.extracted_data.get('yield_value'),
                        'confidence': vendor_result.confidence_score,
                        'error': parsing_result.error_message,
                        'extraction_method': parsing_result.extraction_method or 'traditional'
                    }
                    
                    # 更新資料庫
                    from src.infrastructure.adapters.database.models import EmailDB
                    with database.get_session() as session:
                        email_db = session.query(EmailDB).filter_by(id=email_id).first()
                        if email_db:
                            email_db.parsed_at = datetime.now()
                            
                            if result['success']:
                                email_db.vendor_code = result.get('vendor')
                                email_db.pd = result.get('pd')
                                email_db.lot = result.get('lot')
                                email_db.mo = result.get('mo')
                                email_db.yield_value = result.get('yield')
                                email_db.extraction_method = result.get('extraction_method')
                                email_db.parse_status = 'parsed'
                                email_db.parse_error = None
                            else:
                                email_db.parse_status = 'failed'
                                email_db.parse_error = result.get('error', '傳統解析失敗')
                                email_db.extraction_method = result.get('extraction_method', 'traditional')
                            
                            session.commit()
                    
                    return jsonify({
                        'success': True,
                        'data': result
                    })
            except Exception as fallback_error:
                logger.error(f"傳統解析器降級失敗: {fallback_error}")
        
        return jsonify({
            'success': False, 
            'error': str(e)
        }), 500


@parser_bp.route('/emails/batch-parse', methods=['POST'])
@require_api_key
def batch_parse_emails():
    """批次解析郵件 - 使用統一的批次解析邏輯"""
    try:
        # Input validation
        try:
            data = validate_json_payload()
        except ValueError as e:
            return jsonify({
                'success': False,
                'error': str(e),
                'code': 'INVALID_PAYLOAD'
            }), 400
        
        # Validate limit parameter
        if 'limit' in data:
            try:
                limit = int(data['limit'])
                if limit <= 0 or limit > 1000:  # Prevent resource exhaustion
                    return jsonify({
                        'success': False,
                        'error': 'Limit must be between 1 and 1000',
                        'code': 'INVALID_LIMIT'
                    }), 400
            except (TypeError, ValueError):
                return jsonify({
                    'success': False,
                    'error': 'Invalid limit value',
                    'code': 'INVALID_LIMIT'
                }), 400
        limit = data.get('limit', 100)
        parse_failed_only = data.get('failed_only', False)

        # 查詢待解析郵件
        from src.infrastructure.adapters.database.models import EmailDB

        with database.get_session() as session:
            query = session.query(EmailDB)

            if parse_failed_only:
                query = query.filter_by(parse_status='failed')
            else:
                query = query.filter_by(parse_status='pending')

            emails_db = query.limit(limit).all()

            if not emails_db:
                return jsonify({
                    'success': True,
                    'message': '沒有待解析的郵件',
                    'parsed_count': 0
                })

            # 🔧 修復：使用統一的批次解析邏輯
            # 轉換為 EmailData 格式
            emails_data = []
            for email_db in emails_db:
                email_data = EmailData(
                    message_id=email_db.message_id,
                    subject=email_db.subject,
                    sender=email_db.sender,
                    body=email_db.body or '',
                    received_time=email_db.received_time
                )
                emails_data.append(email_data)

            # 使用 ParserFactory 的統一批次解析
            batch_results = parser_factory.batch_parse_emails(emails_data)

            # 處理批次解析結果
            results = []
            for i, (email_data, vendor_result, parsing_result) in enumerate(batch_results):
                try:
                    email_db = emails_db[i]  # 對應的資料庫記錄

                    # 轉換結果格式
                    result = {
                        'success': parsing_result.is_success,
                        'vendor': vendor_result.vendor_code,
                        'vendor_name': vendor_result.vendor_name,
                        'pd': parsing_result.extracted_data.get('product') or parsing_result.extracted_data.get('product_name'),
                        'mo': parsing_result.mo_number,
                        'lot': parsing_result.lot_number,
                        'yield': parsing_result.extracted_data.get('yield_value'),
                        'confidence': vendor_result.confidence_score,
                        'error': parsing_result.error_message,
                        'extraction_method': parsing_result.extraction_method,
                        'email_id': email_db.id
                    }

                    # 更新資料庫
                    if result['success']:
                        email_db.vendor_code = result.get('vendor')
                        email_db.pd = result.get('pd')
                        email_db.lot = result.get('lot')
                        email_db.yield_value = result.get('yield')
                        email_db.extraction_method = result.get('extraction_method')
                        email_db.parsed_at = datetime.now()
                        email_db.parse_status = 'parsed'
                        email_db.parse_error = None
                    else:
                        email_db.parse_status = 'failed'
                        email_db.parse_error = result.get('error')

                    results.append(result)

                except Exception as e:
                    logger.error(f"批次解析郵件 {email_db.id if i < len(emails_db) else i} 失敗: {e}")
                    results.append({
                        'success': False,
                        'email_id': emails_db[i].id if i < len(emails_db) else None,
                        'subject': emails_db[i].subject if i < len(emails_db) else 'Unknown',
                        'error': str(e)
                    })
                    
            # 提交所有更新
            session.commit()
            
        success_count = sum(1 for r in results if r['success'])
        
        return jsonify({
            'success': True,
            'parsed_count': len(results),
            'success_count': success_count,
            'failed_count': len(results) - success_count,
            'results': results
        })
        
    except Exception as e:
        logger.error(f"批次解析失敗: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500


@parser_bp.route('/statistics', methods=['GET'])
@require_api_key
def get_parser_statistics():
    """取得解析統計"""
    try:
        # 使用 ParserFactory 取得基本資訊
        supported_vendors = parser_factory.get_supported_vendors()
        
        stats = {
            'supported_vendors': supported_vendors,
            'registered_parsers': len(supported_vendors)
        }
        
        # 添加資料庫統計
        from src.infrastructure.adapters.database.models import EmailDB
        
        with database.get_session() as session:
            total_emails = session.query(EmailDB).count()
            parsed_emails = session.query(EmailDB).filter_by(parse_status='parsed').count()
            failed_emails = session.query(EmailDB).filter_by(parse_status='failed').count()
            pending_emails = session.query(EmailDB).filter_by(parse_status='pending').count()
            
            # 廠商分布
            vendor_stats = session.query(
                EmailDB.vendor_code, 
                session.query(EmailDB).filter(EmailDB.vendor_code.isnot(None)).count()
            ).group_by(EmailDB.vendor_code).all()
            
        stats['database_stats'] = {
            'total_emails': total_emails,
            'parsed_emails': parsed_emails,
            'failed_emails': failed_emails,
            'pending_emails': pending_emails,
            'vendor_distribution': dict(vendor_stats) if vendor_stats else {}
        }
        
        return jsonify({
            'success': True,
            'data': stats
        })
        
    except Exception as e:
        logger.error(f"取得統計失敗: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500


@parser_bp.route('/test', methods=['POST'])
@require_api_key
def test_parse_email():
    """測試解析（用於開發和調試）"""
    try:
        # Input validation
        try:
            data = validate_json_payload(['subject'], ['message_id', 'sender', 'body'])
        except ValueError as e:
            return jsonify({
                'success': False,
                'error': str(e),
                'code': 'INVALID_PAYLOAD'
            }), 400
            
        # 創建測試郵件資料
        email_data = EmailData(
            message_id=data.get('message_id', f'test-{datetime.now().timestamp()}'),
            subject=data.get('subject', ''),
            sender=data.get('sender', '<EMAIL>'),
            body=data.get('body', ''),
            received_time=datetime.now()
        )
        
        # 測試解析
        result = parser_factory.parse_email(email_data)
        if isinstance(result, tuple) and len(result) == 2:
            vendor_result, parsing_result = result
        else:
            vendor_result = VendorIdentificationResult(
                vendor_code=None,
                vendor_name=None,
                confidence_score=0.0,
                matching_patterns=[],
                is_identified=False
            )
            parsing_result = result if result else EmailParsingResult(
                is_success=False,
                error_message="解析器返回無效結果",
                validation_errors=["解析器返回格式錯誤"]
            )
        
        result = {
            'vendor_identification': {
                'is_identified': vendor_result.is_identified,
                'vendor_code': vendor_result.vendor_code,
                'vendor_name': vendor_result.vendor_name,
                'confidence_score': vendor_result.confidence_score,
                'matching_patterns': vendor_result.matching_patterns
            },
            'parse_result': {
                'success': parsing_result.is_success,
                'vendor': vendor_result.vendor_code,
                'pd': parsing_result.extracted_data.get('product') or parsing_result.extracted_data.get('product_name'),
                'mo': parsing_result.mo_number,
                'lot': parsing_result.lot_number,
                'yield': parsing_result.extracted_data.get('yield_value'),
                'error': parsing_result.error_message
            },
            'email_info': {
                'subject': email_data.subject,
                'sender': email_data.sender,
                'has_body': bool(email_data.body),
                'attachment_count': len(email_data.attachments)
            }
        }
        
        return jsonify({
            'success': True,
            'data': result
        })
        
    except Exception as e:
        logger.error(f"測試解析失敗: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500


@parser_bp.route('/emails/batch-process', methods=['POST'])
@require_api_key
def batch_process_files():
    """批次處理已解析郵件的檔案"""
    try:
        # 查詢已解析但未處理檔案的郵件
        from src.infrastructure.adapters.database.models import EmailDB
        import os
        import shutil
        from pathlib import Path
        
        with database.get_session() as session:
            # 查詢已解析且有解析結果的郵件
            emails = session.query(EmailDB).filter(
                EmailDB.parse_status == 'parsed',
                EmailDB.vendor_code.isnot(None),
                EmailDB.pd.isnot(None),
                EmailDB.lot.isnot(None)
            ).all()
            
            if not emails:
                return jsonify({
                    'success': True,
                    'message': '沒有已解析的郵件需要處理',
                    'processed_count': 0,
                    'success_count': 0,
                    'failed_count': 0
                })
            
            # 取得設定
            temp_base_path = os.getenv('FILE_TEMP_BASE_PATH', 'D:\\temp')
            source_base_path = os.getenv('FILE_SOURCE_BASE_PATH', '\\\\192.168.1.60\\test_log')
            
            processed_count = 0
            success_count = 0
            failed_count = 0
            
            for email in emails:
                try:
                    # 檢查是否已經處理過
                    if email.is_processed:
                        logger.debug(f"郵件 {email.id} 已處理過，跳過")
                        continue
                    
                    processed_count += 1
                    vendor_code = email.vendor_code
                    pd = email.pd or 'default'
                    lot = email.lot or 'default'
                    
                    # 建立目標路徑 D:\temp\{pd}\{lot}
                    target_path = Path(temp_base_path) / pd / lot
                    target_path.mkdir(parents=True, exist_ok=True)
                    
                    # 檢查是否有廠商檔案處理路徑（暫時跳過，直接處理附件）
                    logger.info(f"開始處理郵件 {email.id}: {vendor_code} -> {target_path}")
                    
                    # 直接處理附件（簡化流程）
                    files_copied = 0
                    
                    # 嘗試直接從來源路徑處理廠商檔案
                    try:
                        from src.infrastructure.adapters.file_handlers import FileHandlerFactory
                        
                        factory = FileHandlerFactory(source_base_path)
                        mo = lot  # 使用 lot 作為 mo
                        
                        result = factory.process_vendor_files(
                            vendor_code=vendor_code,
                            mo=mo,
                            temp_path=str(target_path),
                            pd=pd,
                            lot=lot
                        )
                        
                        if result['success']:
                            logger.info(f"成功處理廠商檔案 {email.id}: {vendor_code} -> {target_path}")
                            files_copied += 1
                        else:
                            logger.warning(f"處理廠商檔案失敗 {email.id}: {result.get('error')}")
                    except Exception as e:
                        logger.warning(f"無法處理廠商檔案 {email.id}: {e}")
                    
                    # 處理附件
                    from src.infrastructure.adapters.database.models import AttachmentDB
                    attachments = session.query(AttachmentDB).filter_by(email_id=email.id).all()
                    
                    if attachments:
                        logger.info(f"處理郵件 {email.id} 的 {len(attachments)} 個附件")
                        for attachment in attachments:
                            try:
                                if attachment.file_path and Path(attachment.file_path).exists():
                                    # 複製附件到目標路徑
                                    source_file = Path(attachment.file_path)
                                    target_file = target_path / attachment.filename
                                    shutil.copy2(source_file, target_file)
                                    logger.debug(f"複製附件: {attachment.filename} -> {target_file}")
                                    files_copied += 1
                                else:
                                    logger.warning(f"附件檔案不存在: {attachment.filename} ({attachment.file_path})")
                            except Exception as e:
                                logger.error(f"複製附件 {attachment.filename} 失敗: {e}")
                    
                    # 更新郵件處理狀態
                    if files_copied > 0:
                        email.is_processed = True
                        logger.info(f"成功處理郵件 {email.id}，複製了 {files_copied} 個檔案到 {target_path}")
                        success_count += 1
                    else:
                        logger.warning(f"郵件 {email.id} 沒有可處理的檔案")
                        failed_count += 1
                    
                except Exception as e:
                    logger.error(f"處理郵件 {email.id} 時發生錯誤: {e}")
                    failed_count += 1
            
            # 提交資料庫變更
            session.commit()
            
            return jsonify({
                'success': True,
                'message': f'批次處理完成',
                'processed_count': processed_count,
                'success_count': success_count,
                'failed_count': failed_count
            })
        
    except Exception as e:
        logger.error(f"批次處理檔案失敗: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500


@parser_bp.route('/emails/<int:email_id>/llm-analysis', methods=['GET'])
@require_api_key
def get_llm_analysis(email_id):
    """獲取郵件的 LLM 分析結果"""
    try:
        # Input validation
        if not validate_email_id(email_id):
            return jsonify({
                'success': False,
                'error': '無效的郵件ID',
                'code': 'INVALID_EMAIL_ID'
            }), 400
            
        logger.info(f"請求 LLM 分析結果: email_id={email_id}")
        
        # 獲取郵件資料
        with database.get_session() as session:
            email = session.query(EmailDB).filter(EmailDB.id == email_id).first()
            
            if not email:
                return jsonify({
                    'success': False,
                    'error': '郵件不存在'
                }), 404
            
            # 檢查是否為 LLM 解析
            if email.extraction_method != 'llm':
                return jsonify({
                    'success': False,
                    'error': '此郵件非 LLM 解析'
                }), 400
            
            # 在會話內獲取所有需要的資料
            email_data = {
                'email_id': email_id,
                'subject': email.subject,
                'sender': email.sender,
                'body': email.body,
                'received_time': email.received_time.isoformat() if email.received_time else None,
                'extraction_method': email.extraction_method,
                'parse_status': email.parse_status,
                'parsed_at': email.parsed_at.isoformat() if email.parsed_at else None,
                'database_results': {
                    'vendor_code': email.vendor_code,
                    'pd': email.pd,
                    'lot': email.lot,
                    'yield_value': email.yield_value,
                    'mo_number': None  # EmailDB 模型沒有 mo_number 欄位
                }
            }
        
        # Check if LLM analysis is available
        llm_analysis_data = None
        if hasattr(email, 'llm_analysis_result') and email.llm_analysis_result:
            try:
                import json
                llm_analysis_data = json.loads(email.llm_analysis_result)
            except (json.JSONDecodeError, TypeError) as e:
                logger.warning(f"Failed to parse LLM analysis result: {e}")
        
        # If no real LLM data available, return error
        if not llm_analysis_data:
            return jsonify({
                'success': False,
                'error': 'LLM analysis not available for this email',
                'code': 'LLM_ANALYSIS_UNAVAILABLE'
            }), 404
        
        # 組織分析結果
        analysis_data = email_data.copy()
        analysis_data['llm_analysis'] = llm_analysis_data
        
        return jsonify({
            'success': True,
            'data': analysis_data
        })
        
    except Exception as e:
        logger.error(f"獲取 LLM 分析結果失敗: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500


@parser_bp.route('/emails/<int:email_id>/manual-input', methods=['POST'])
@require_api_key
def save_manual_input(email_id):
    """儲存手動輸入的解析資料"""
    try:
        # Input validation
        if not validate_email_id(email_id):
            return jsonify({
                'success': False,
                'error': '無效的郵件ID',
                'code': 'INVALID_EMAIL_ID'
            }), 400
        
        # JSON payload validation
        try:
            data = validate_json_payload(['vendor_code', 'pd', 'lot'], ['mo', 'yield_value'])
        except ValueError as e:
            return jsonify({
                'success': False,
                'error': str(e),
                'code': 'INVALID_PAYLOAD'
            }), 400
        
        # 驗證必填欄位格式
        vendor_code = data.get('vendor_code', '').strip()
        pd = data.get('pd', '').strip()
        lot = data.get('lot', '').strip()
        
        if not validate_vendor_code(vendor_code):
            return jsonify({
                'success': False,
                'error': '廠家代碼格式無效',
                'code': 'INVALID_VENDOR_CODE'
            }), 400
            
        if not validate_product_code(pd):
            return jsonify({
                'success': False,
                'error': '產品編號格式無效',
                'code': 'INVALID_PRODUCT_CODE'
            }), 400
            
        if not validate_lot_number(lot):
            return jsonify({
                'success': False,
                'error': '批次編號格式無效',
                'code': 'INVALID_LOT_NUMBER'
            }), 400
        
        # 取得選填欄位並驗證
        mo = data.get('mo', '').strip() or None
        if mo and not validate_lot_number(mo):  # MO uses same validation as lot
            return jsonify({
                'success': False,
                'error': 'MO編號格式無效',
                'code': 'INVALID_MO_NUMBER'
            }), 400
            
        yield_value = data.get('yield_value')
        if yield_value is not None:
            if not validate_yield_value(yield_value):
                return jsonify({
                    'success': False,
                    'error': '良率必須在 0-100 之間',
                    'code': 'INVALID_YIELD_VALUE'
                }), 400
            yield_value = float(yield_value)
        
        logger.info(f"手動輸入解析資料: email_id={email_id}, vendor={vendor_code}, pd={pd}, lot={lot}")
        
        # 更新資料庫
        with database.get_session() as session:
            email = session.query(EmailDB).filter_by(id=email_id).first()
            
            if not email:
                return jsonify({
                    'success': False,
                    'error': '郵件不存在'
                }), 404
            
            # 更新解析結果
            email.vendor_code = vendor_code
            email.pd = pd
            email.lot = lot
            email.mo = mo
            email.yield_value = yield_value
            email.extraction_method = 'manual'
            email.parse_status = 'parsed'
            email.parse_error = None
            email.parsed_at = datetime.now()
            
            # 記錄手動輸入的資訊
            import json
            manual_input_data = {
                'manual_input': True,
                'input_time': datetime.now().isoformat(),
                'vendor_code': vendor_code,
                'pd': pd,
                'lot': lot,
                'mo': mo,
                'yield_value': yield_value,
                'source': 'manual_input'
            }
            
            # 如果有 LLM 分析欄位，也一併更新
            try:
                email.llm_analysis_result = json.dumps(manual_input_data, ensure_ascii=False)
                email.llm_analysis_timestamp = datetime.now()
                email.llm_service_used = 'manual_input'
            except Exception as e:
                logger.warning(f"更新 LLM 分析欄位失敗: {e}")
            
            session.commit()
            
            logger.info(f"手動輸入資料已儲存: email_id={email_id}")
        
        return jsonify({
            'success': True,
            'message': '手動輸入資料已儲存',
            'data': {
                'vendor_code': vendor_code,
                'pd': pd,
                'lot': lot,
                'mo': mo,
                'yield_value': yield_value,
                'extraction_method': 'manual'
            }
        })
        
    except Exception as e:
        logger.error(f"儲存手動輸入資料失敗: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500