#!/usr/bin/env python3
"""
半導體郵件處理系統 - 環境驗證腳本
驗證開發環境是否正確設定，支援新的模組化架構
"""

import os
import sys
import subprocess
import importlib.util
from pathlib import Path
from typing import List, Tuple, Dict
import json

# Windows UTF-8 編碼修復
if sys.platform.startswith('win'):
    import io
    sys.stdout = io.TextIOWrapper(sys.stdout.buffer, encoding='utf-8')
    sys.stderr = io.TextIOWrapper(sys.stderr.buffer, encoding='utf-8')
    os.environ['PYTHONIOENCODING'] = 'utf-8'
    os.environ['PYTHONUTF8'] = '1'

class Colors:
    """終端顏色定義"""
    GREEN = '\033[92m'
    RED = '\033[91m'
    YELLOW = '\033[93m'
    BLUE = '\033[94m'
    MAGENTA = '\033[95m'
    CYAN = '\033[96m'
    WHITE = '\033[97m'
    BOLD = '\033[1m'
    END = '\033[0m'

class EnvironmentVerifier:
    """環境驗證器"""
    
    def __init__(self):
        self.project_root = Path(__file__).parent.parent
        self.results = []
        self.errors = []
        self.warnings = []
        
    def print_header(self):
        """列印標題"""
        print(f"{Colors.MAGENTA}{Colors.BOLD}")
        print("=" * 60)
        print("🚀 半導體郵件處理系統 - 環境驗證")
        print("   模組化 Flask 前端架構")
        print("=" * 60)
        print(f"{Colors.END}")
        
    def check_python_version(self) -> bool:
        """檢查 Python 版本"""
        print(f"{Colors.CYAN}🐍 檢查 Python 版本...{Colors.END}")
        
        version = sys.version_info
        required_major, required_minor = 3, 9
        
        if version.major >= required_major and version.minor >= required_minor:
            print(f"  ✅ Python {version.major}.{version.minor}.{version.micro} (符合需求 >= {required_major}.{required_minor})")
            return True
        else:
            print(f"  ❌ Python {version.major}.{version.minor}.{version.micro} (需要 >= {required_major}.{required_minor})")
            self.errors.append(f"Python 版本過舊: {version.major}.{version.minor}.{version.micro}")
            return False
            
    def check_virtual_environment(self) -> bool:
        """檢查虛擬環境"""
        print(f"{Colors.CYAN}🔧 檢查虛擬環境...{Colors.END}")
        
        venv_path = self.project_root / "venv_win_3_11_12"
        
        if venv_path.exists():
            print(f"  ✅ 虛擬環境目錄存在: {venv_path}")
            
            # 檢查啟動腳本
            activate_script = venv_path / "Scripts" / "Activate.ps1"
            if activate_script.exists():
                print(f"  ✅ PowerShell 啟動腳本存在")
                return True
            else:
                print(f"  ❌ PowerShell 啟動腳本不存在")
                self.errors.append("虛擬環境啟動腳本不存在")
                return False
        else:
            print(f"  ❌ 虛擬環境目錄不存在: {venv_path}")
            self.errors.append("虛擬環境不存在")
            return False
            
    def check_environment_variables(self) -> bool:
        """檢查環境變數"""
        print(f"{Colors.CYAN}⚙️  檢查環境變數...{Colors.END}")
        
        required_vars = {
            'PYTHONIOENCODING': 'utf-8',
            'PYTHONUTF8': '1',
        }
        
        flask_vars = {
            'FLASK_APP': 'frontend.app:create_app',
            'FLASK_ENV': 'development',
            'FLASK_DEBUG': 'True'
        }
        
        all_good = True
        
        # 檢查必要環境變數
        for var, expected in required_vars.items():
            value = os.environ.get(var)
            if value == expected:
                print(f"  ✅ {var} = {value}")
            else:
                print(f"  ⚠️  {var} = {value} (建議: {expected})")
                self.warnings.append(f"環境變數 {var} 未正確設定")
                
        # 檢查 Flask 環境變數
        for var, expected in flask_vars.items():
            value = os.environ.get(var)
            if value:
                print(f"  ✅ {var} = {value}")
            else:
                print(f"  ⚠️  {var} 未設定 (建議: {expected})")
                self.warnings.append(f"Flask 環境變數 {var} 未設定")
                
        return all_good
        
    def check_frontend_structure(self) -> bool:
        """檢查前端模組化結構"""
        print(f"{Colors.CYAN}🏗️  檢查前端模組化結構...{Colors.END}")
        
        frontend_path = self.project_root / "frontend"
        
        if not frontend_path.exists():
            print(f"  ❌ frontend/ 目錄不存在")
            self.errors.append("前端目錄不存在")
            return False
            
        print(f"  ✅ frontend/ 目錄存在")
        
        # 檢查主要檔案
        main_files = ['app.py', 'config.py', 'cli.py']
        for file in main_files:
            file_path = frontend_path / file
            if file_path.exists():
                print(f"  ✅ {file} 存在")
            else:
                print(f"  ❌ {file} 不存在")
                self.errors.append(f"主要檔案 {file} 不存在")
                
        # 檢查模組目錄
        modules = ['email', 'analytics', 'file_management', 'eqc', 'tasks', 'monitoring', 'shared']
        module_status = {}
        
        for module in modules:
            module_path = frontend_path / module
            if module_path.exists():
                print(f"  ✅ {module}/ 模組存在")
                module_status[module] = True
                
                # 檢查模組子目錄
                subdirs = ['templates', 'static']
                if module != 'shared':
                    subdirs.append('routes')
                    
                for subdir in subdirs:
                    subdir_path = module_path / subdir
                    if subdir_path.exists():
                        print(f"    ✅ {module}/{subdir}/ 存在")
                    else:
                        print(f"    ⚠️  {module}/{subdir}/ 不存在")
                        self.warnings.append(f"模組子目錄 {module}/{subdir} 不存在")
            else:
                print(f"  ❌ {module}/ 模組不存在")
                self.errors.append(f"模組 {module} 不存在")
                module_status[module] = False
                
        return all(module_status.values())
        
    def check_dependencies(self) -> bool:
        """檢查 Python 依賴套件"""
        print(f"{Colors.CYAN}📦 檢查 Python 依賴套件...{Colors.END}")
        
        requirements_file = self.project_root / "requirements.txt"
        
        if not requirements_file.exists():
            print(f"  ❌ requirements.txt 不存在")
            self.errors.append("requirements.txt 檔案不存在")
            return False
            
        print(f"  ✅ requirements.txt 存在")
        
        # 檢查關鍵套件
        key_packages = ['flask', 'jinja2', 'werkzeug']
        
        for package in key_packages:
            try:
                spec = importlib.util.find_spec(package)
                if spec is not None:
                    print(f"  ✅ {package} 已安裝")
                else:
                    print(f"  ❌ {package} 未安裝")
                    self.errors.append(f"套件 {package} 未安裝")
            except ImportError:
                print(f"  ❌ {package} 未安裝")
                self.errors.append(f"套件 {package} 未安裝")
                
        return True
        
    def check_flask_app(self) -> bool:
        """檢查 Flask 應用程式"""
        print(f"{Colors.CYAN}🌐 檢查 Flask 應用程式...{Colors.END}")
        
        app_file = self.project_root / "frontend" / "app.py"
        
        if not app_file.exists():
            print(f"  ❌ frontend/app.py 不存在")
            self.errors.append("Flask 主應用程式檔案不存在")
            return False
            
        print(f"  ✅ frontend/app.py 存在")
        
        # 嘗試導入 Flask 應用程式
        try:
            sys.path.insert(0, str(self.project_root))
            from frontend.app import create_app
            app = create_app()
            print(f"  ✅ Flask 應用程式可以正常建立")
            
            # 檢查藍圖註冊
            blueprints = list(app.blueprints.keys())
            expected_blueprints = ['email', 'analytics', 'file_management', 'eqc', 'tasks', 'monitoring', 'shared']
            
            for bp in expected_blueprints:
                if bp in blueprints:
                    print(f"    ✅ {bp} 藍圖已註冊")
                else:
                    print(f"    ⚠️  {bp} 藍圖未註冊")
                    self.warnings.append(f"藍圖 {bp} 未註冊")
                    
            return True
            
        except Exception as e:
            print(f"  ❌ Flask 應用程式建立失敗: {e}")
            self.errors.append(f"Flask 應用程式建立失敗: {e}")
            return False
            
    def check_development_scripts(self) -> bool:
        """檢查開發腳本"""
        print(f"{Colors.CYAN}📜 檢查開發腳本...{Colors.END}")
        
        scripts = {
            'dev_env.ps1': '開發環境啟動腳本',
            'Makefile': '開發命令集合',
        }
        
        all_good = True
        
        for script, description in scripts.items():
            script_path = self.project_root / script
            if script_path.exists():
                print(f"  ✅ {script} 存在 ({description})")
            else:
                print(f"  ❌ {script} 不存在 ({description})")
                self.errors.append(f"開發腳本 {script} 不存在")
                all_good = False
                
        return all_good
        
    def generate_report(self) -> Dict:
        """生成驗證報告"""
        total_checks = len(self.results)
        passed_checks = sum(self.results)
        
        report = {
            'timestamp': str(Path(__file__).stat().st_mtime),
            'total_checks': total_checks,
            'passed_checks': passed_checks,
            'success_rate': (passed_checks / total_checks * 100) if total_checks > 0 else 0,
            'errors': self.errors,
            'warnings': self.warnings,
            'status': 'PASS' if len(self.errors) == 0 else 'FAIL'
        }
        
        return report
        
    def print_summary(self, report: Dict):
        """列印摘要"""
        print(f"\n{Colors.BOLD}📊 驗證摘要{Colors.END}")
        print("=" * 40)
        
        status_color = Colors.GREEN if report['status'] == 'PASS' else Colors.RED
        print(f"狀態: {status_color}{report['status']}{Colors.END}")
        print(f"通過檢查: {report['passed_checks']}/{report['total_checks']}")
        print(f"成功率: {report['success_rate']:.1f}%")
        
        if self.errors:
            print(f"\n{Colors.RED}❌ 錯誤 ({len(self.errors)}):{Colors.END}")
            for i, error in enumerate(self.errors, 1):
                print(f"  {i}. {error}")
                
        if self.warnings:
            print(f"\n{Colors.YELLOW}⚠️  警告 ({len(self.warnings)}):{Colors.END}")
            for i, warning in enumerate(self.warnings, 1):
                print(f"  {i}. {warning}")
                
        if report['status'] == 'PASS':
            print(f"\n{Colors.GREEN}🎉 環境驗證通過！可以開始開發了。{Colors.END}")
            print(f"{Colors.CYAN}💡 執行 'python frontend/app.py' 啟動應用程式{Colors.END}")
        else:
            print(f"\n{Colors.RED}🚨 環境驗證失敗！請修復上述問題。{Colors.END}")
            print(f"{Colors.CYAN}💡 參考開發環境設定指南: docs/development/DEVELOPMENT_SETUP_GUIDE.md{Colors.END}")
            
    def run_all_checks(self) -> Dict:
        """執行所有檢查"""
        self.print_header()
        
        checks = [
            ('Python 版本', self.check_python_version),
            ('虛擬環境', self.check_virtual_environment),
            ('環境變數', self.check_environment_variables),
            ('前端結構', self.check_frontend_structure),
            ('Python 依賴', self.check_dependencies),
            ('Flask 應用程式', self.check_flask_app),
            ('開發腳本', self.check_development_scripts),
        ]
        
        for name, check_func in checks:
            print()  # 空行分隔
            try:
                result = check_func()
                self.results.append(result)
            except Exception as e:
                print(f"  ❌ 檢查 {name} 時發生錯誤: {e}")
                self.errors.append(f"檢查 {name} 時發生錯誤: {e}")
                self.results.append(False)
                
        report = self.generate_report()
        self.print_summary(report)
        
        return report

def main():
    """主函數"""
    verifier = EnvironmentVerifier()
    report = verifier.run_all_checks()
    
    # 儲存報告
    report_file = Path(__file__).parent.parent / "environment_verification_report.json"
    with open(report_file, 'w', encoding='utf-8') as f:
        json.dump(report, f, indent=2, ensure_ascii=False)
        
    print(f"\n📄 詳細報告已儲存至: {report_file}")
    
    # 返回適當的退出碼
    sys.exit(0 if report['status'] == 'PASS' else 1)

if __name__ == "__main__":
    main()