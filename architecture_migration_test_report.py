#!/usr/bin/env python3
"""
架構遷移測試報告生成器
生成詳細的架構遷移驗證報告
"""

import os
import sys
import json
import time
from pathlib import Path
from datetime import datetime
from typing import Dict, Any, List

# 添加專案根目錄到 Python 路徑
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))


def run_architecture_verification() -> Dict[str, Any]:
    """執行架構驗證並收集結果"""
    results = {
        'timestamp': datetime.now().isoformat(),
        'test_results': {},
        'summary': {}
    }
    
    print("🔍 執行架構遷移驗證...")
    
    # 1. 檢查舊文件移除
    print("\n📋 檢查舊文件移除...")
    legacy_files = ['email_inbox_app.py', 'email_inbox_app.py.backup']
    legacy_check = {}
    
    for filename in legacy_files:
        file_path = project_root / filename
        exists = file_path.exists()
        legacy_check[filename] = {
            'exists': exists,
            'removed': not exists
        }
        
        if exists:
            print(f"❌ 舊文件仍存在: {filename}")
        else:
            print(f"✅ 舊文件已移除: {filename}")
    
    results['test_results']['legacy_cleanup'] = {
        'success': all(not check['exists'] for check in legacy_check.values()),
        'details': legacy_check
    }
    
    # 2. 檢查新架構目錄結構
    print("\n📁 檢查新架構目錄結構...")
    required_paths = {
        'frontend_app': 'frontend/app.py',
        'frontend_config': 'frontend/config.py',
        'shared_templates': 'frontend/shared/templates',
        'shared_static': 'frontend/shared/static',
        'email_module': 'frontend/email',
        'analytics_module': 'frontend/analytics',
        'eqc_module': 'frontend/eqc',
        'tasks_module': 'frontend/tasks',
        'monitoring_module': 'frontend/monitoring',
        'file_management_module': 'frontend/file_management'
    }
    
    structure_check = {}
    for name, path in required_paths.items():
        full_path = project_root / path
        exists = full_path.exists()
        structure_check[name] = {
            'path': path,
            'exists': exists
        }
        
        if exists:
            print(f"✅ {name}: {path}")
        else:
            print(f"❌ {name}: {path} (缺失)")
    
    results['test_results']['directory_structure'] = {
        'success': all(check['exists'] for check in structure_check.values()),
        'details': structure_check
    }
    
    # 3. 測試Flask應用創建
    print("\n🏗️ 測試Flask應用創建...")
    try:
        from frontend.app import create_app
        
        app = create_app('testing')
        blueprints_count = len(app.blueprints)
        
        # 檢查預期的藍圖
        expected_blueprints = {'shared', 'email', 'analytics', 'eqc', 'task', 'monitoring', 'file'}
        registered_blueprints = set(app.blueprints.keys())
        
        app_creation_result = {
            'success': True,
            'blueprints_count': blueprints_count,
            'expected_blueprints': list(expected_blueprints),
            'registered_blueprints': list(registered_blueprints),
            'missing_blueprints': list(expected_blueprints - registered_blueprints),
            'extra_blueprints': list(registered_blueprints - expected_blueprints)
        }
        
        print(f"✅ Flask應用創建成功")
        print(f"✅ 註冊藍圖數: {blueprints_count}")
        print(f"✅ 藍圖列表: {', '.join(registered_blueprints)}")
        
    except Exception as e:
        app_creation_result = {
            'success': False,
            'error': str(e)
        }
        print(f"❌ Flask應用創建失敗: {e}")
    
    results['test_results']['flask_app_creation'] = app_creation_result
    
    # 4. 測試模組導入
    print("\n📦 測試模組導入...")
    modules_to_test = [
        'frontend.email.routes.email_routes',
        'frontend.analytics.routes.analytics_routes',
        'frontend.eqc.routes.eqc_routes', 
        'frontend.tasks.routes.task_routes',
        'frontend.monitoring.routes.monitoring_routes',
        'frontend.file_management.routes.file_routes'
    ]
    
    module_imports = {}
    successful_imports = 0
    
    for module_name in modules_to_test:
        try:
            __import__(module_name)
            module_imports[module_name] = {
                'success': True,
                'error': None
            }
            successful_imports += 1
            print(f"✅ {module_name}")
        except Exception as e:
            module_imports[module_name] = {
                'success': False,
                'error': str(e)
            }
            print(f"❌ {module_name}: {e}")
    
    results['test_results']['module_imports'] = {
        'success': successful_imports == len(modules_to_test),
        'successful_imports': successful_imports,
        'total_modules': len(modules_to_test),
        'details': module_imports
    }
    
    # 5. 測試start_integrated_services.py配置
    print("\n⚙️ 檢查start_integrated_services.py...")
    start_script_path = project_root / 'start_integrated_services.py'
    
    if start_script_path.exists():
        try:
            # 檢查文件內容是否使用新架構
            content = start_script_path.read_text(encoding='utf-8')
            uses_new_architecture = 'frontend.app' in content and 'create_app' in content
            no_old_references = 'email_inbox_app' not in content
            
            start_script_result = {
                'success': uses_new_architecture and no_old_references,
                'file_exists': True,
                'uses_new_architecture': uses_new_architecture,
                'no_old_references': no_old_references
            }
            
            if uses_new_architecture:
                print("✅ start_integrated_services.py 使用新架構")
            if no_old_references:
                print("✅ start_integrated_services.py 無舊架構引用")
                
        except Exception as e:
            start_script_result = {
                'success': False,
                'error': str(e)
            }
            print(f"❌ start_integrated_services.py 檢查失敗: {e}")
    else:
        start_script_result = {
            'success': False,
            'file_exists': False,
            'error': 'start_integrated_services.py 不存在'
        }
        print("❌ start_integrated_services.py 不存在")
    
    results['test_results']['start_script_check'] = start_script_result
    
    # 計算總體結果
    test_categories = ['legacy_cleanup', 'directory_structure', 'flask_app_creation', 
                      'module_imports', 'start_script_check']
    
    successful_tests = sum(1 for cat in test_categories 
                          if results['test_results'][cat]['success'])
    total_tests = len(test_categories)
    success_rate = (successful_tests / total_tests * 100) if total_tests > 0 else 0
    
    results['summary'] = {
        'overall_success': successful_tests == total_tests,
        'success_rate': success_rate,
        'successful_tests': successful_tests,
        'total_tests': total_tests,
        'critical_issues': []
    }
    
    # 收集關鍵問題
    if not results['test_results']['legacy_cleanup']['success']:
        results['summary']['critical_issues'].append('舊文件未完全移除')
    
    if not results['test_results']['flask_app_creation']['success']:
        results['summary']['critical_issues'].append('Flask應用創建失敗')
    
    if results['test_results']['module_imports']['successful_imports'] < 5:
        results['summary']['critical_issues'].append('模組導入失敗過多')
    
    return results


def generate_detailed_report(results: Dict[str, Any]) -> str:
    """生成詳細報告"""
    summary = results['summary']
    
    report = f"""
架構遷移驗證報告
{'='*60}

測試時間: {results['timestamp']}

總體結果: {'✅ 遷移成功' if summary['overall_success'] else '❌ 遷移不完整'}
成功率: {summary['success_rate']:.1f}%
通過測試: {summary['successful_tests']}/{summary['total_tests']}

關鍵發現:
{'='*60}

🔄 架構遷移狀態: {'完成' if summary['overall_success'] else '需要修復'}

詳細測試結果:
{'='*60}

1. 舊文件清理: {'✅ 完成' if results['test_results']['legacy_cleanup']['success'] else '❌ 未完成'}
   - email_inbox_app.py: {'已移除' if not results['test_results']['legacy_cleanup']['details']['email_inbox_app.py']['exists'] else '仍存在'}
   
2. 目錄結構: {'✅ 正確' if results['test_results']['directory_structure']['success'] else '❌ 不完整'}
   - frontend/app.py: {'存在' if results['test_results']['directory_structure']['details']['frontend_app']['exists'] else '缺失'}
   - 前端模組: {'完整' if results['test_results']['directory_structure']['success'] else '不完整'}

3. Flask應用: {'✅ 正常' if results['test_results']['flask_app_creation']['success'] else '❌ 失敗'}
"""
    
    if results['test_results']['flask_app_creation']['success']:
        flask_result = results['test_results']['flask_app_creation']
        report += f"   - 註冊藍圖: {flask_result['blueprints_count']} 個\n"
        report += f"   - 藍圖列表: {', '.join(flask_result['registered_blueprints'])}\n"
    
    module_result = results['test_results']['module_imports']
    report += f"""
4. 模組導入: {'✅ 成功' if module_result['success'] else '❌ 部分失敗'}
   - 成功導入: {module_result['successful_imports']}/{module_result['total_modules']} 個模組
   
5. 啟動腳本: {'✅ 已更新' if results['test_results']['start_script_check']['success'] else '❌ 需要更新'}
"""
    
    if results['test_results']['start_script_check']['success']:
        report += "   - 使用新架構: ✅\n   - 無舊引用: ✅\n"
    
    # 添加結論和建議
    if summary['overall_success']:
        report += f"""

結論:
{'='*60}

✅ 架構遷移完全成功！

新的模組化前端架構已經：
• 完全替代了舊的 email_inbox_app.py
• 所有 6 個前端模組正常載入 
• Flask 應用工廠模式正常運作
• start_integrated_services.py 已更新使用新架構
• 無遺留的舊架構引用

下一步建議:
• ✅ 可以開始使用新架構
• ✅ 可以移除任何備份文件
• ✅ 準備進行 Vue.js 遷移
• ✅ 更新部署文檔
"""
    else:
        report += f"""

需要修復的問題:
{'='*60}

"""
        for issue in summary['critical_issues']:
            report += f"❌ {issue}\n"
        
        report += """
建議修復步驟:
1. 檢查並修復上述問題
2. 重新運行驗證測試
3. 確保所有測試通過後再繼續
"""
    
    report += "\n" + "="*60
    return report


def main():
    """主函數"""
    print("🧪 架構遷移驗證報告生成器")
    print("驗證從 email_inbox_app.py 到模組化 frontend/ 的遷移")
    print("="*60)
    
    try:
        # 執行驗證
        results = run_architecture_verification()
        
        # 生成報告
        report = generate_detailed_report(results)
        
        # 顯示報告
        print("\n" + "="*60)
        print("驗證完成！")
        print("="*60)
        print(report)
        
        # 保存報告
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        # JSON 詳細報告
        json_file = project_root / f"architecture_migration_verification_{timestamp}.json"
        with open(json_file, 'w', encoding='utf-8') as f:
            json.dump(results, f, ensure_ascii=False, indent=2)
        
        # 文本報告
        txt_file = project_root / f"architecture_migration_verification_{timestamp}.txt"
        with open(txt_file, 'w', encoding='utf-8') as f:
            f.write(report)
        
        print(f"\n📄 詳細報告已保存: {json_file}")
        print(f"📄 摘要報告已保存: {txt_file}")
        
        # 返回結果
        return 0 if results['summary']['overall_success'] else 1
        
    except Exception as e:
        print(f"\n❌ 驗證執行失敗: {e}")
        import traceback
        traceback.print_exc()
        return 1


if __name__ == '__main__':
    exit_code = main()
    sys.exit(exit_code)