#!/usr/bin/env python3
"""
簡化版整合服務測試腳本
避免 Unicode 編碼問題，專注於功能驗證
"""

import os
import sys
import subprocess
import time
import requests
import json
from pathlib import Path

# 添加專案根目錄到 Python 路徑
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_service_availability():
    """測試服務可用性"""
    print("正在測試服務可用性...")
    
    # 測試端點
    endpoints = [
        ("首頁", "http://localhost:5000/"),
        ("健康檢查", "http://localhost:5000/health"),
        ("郵件模組", "http://localhost:5000/email"),
        ("分析模組", "http://localhost:5000/analytics"),
        ("檔案管理", "http://localhost:5000/files"),
        ("EQC模組", "http://localhost:5000/eqc"),
        ("任務管理", "http://localhost:5000/tasks"),
        ("監控模組", "http://localhost:5000/monitoring"),
    ]
    
    results = []
    
    for name, url in endpoints:
        try:
            print(f"測試 {name}: {url}")
            response = requests.get(url, timeout=10)
            
            if response.status_code < 400:
                print(f"  OK - 狀態碼: {response.status_code}")
                results.append((name, True, response.status_code, ""))
            else:
                print(f"  FAIL - 狀態碼: {response.status_code}")
                results.append((name, False, response.status_code, "HTTP錯誤"))
                
        except requests.exceptions.ConnectionError:
            print(f"  FAIL - 無法連接")
            results.append((name, False, 0, "連接失敗"))
        except requests.exceptions.Timeout:
            print(f"  FAIL - 請求超時")
            results.append((name, False, 0, "超時"))
        except Exception as e:
            print(f"  FAIL - 錯誤: {e}")
            results.append((name, False, 0, str(e)))
    
    return results

def start_service():
    """啟動整合服務"""
    print("正在啟動整合服務...")
    
    try:
        # 設置環境變數
        env = os.environ.copy()
        env['PYTHONIOENCODING'] = 'utf-8'
        env['PYTHONUTF8'] = '1'
        env['FLASK_ENV'] = 'testing'
        
        # 啟動服務
        start_script = project_root / "start_integrated_services.py"
        process = subprocess.Popen([
            sys.executable, str(start_script),
            "--mode", "integrated",
            "--flask-port", "5000",
            "--fastapi-port", "8010",
            "--no-monitor"
        ], env=env, stdout=subprocess.PIPE, stderr=subprocess.PIPE, text=True)
        
        print(f"服務已啟動，PID: {process.pid}")
        
        # 等待服務啟動
        print("等待服務啟動中...")
        for i in range(30):  # 最多等待60秒
            try:
                response = requests.get("http://localhost:5000/health", timeout=5)
                if response.status_code == 200:
                    print("服務啟動成功！")
                    return process
            except:
                pass
            time.sleep(2)
            print(f"等待中... ({i+1}/30)")
        
        print("服務啟動超時")
        process.terminate()
        return None
        
    except Exception as e:
        print(f"啟動服務失敗: {e}")
        return None

def stop_service(process):
    """停止服務"""
    if process:
        try:
            process.terminate()
            process.wait(timeout=10)
            print("服務已停止")
        except:
            process.kill()
            print("強制停止服務")

def main():
    """主函數"""
    print("整合服務測試工具")
    print("=" * 50)
    
    # 檢查是否有現有服務在運行
    try:
        response = requests.get("http://localhost:5000/health", timeout=5)
        if response.status_code == 200:
            print("檢測到現有服務正在運行，開始測試...")
            results = test_service_availability()
            process = None
        else:
            # 啟動新服務
            process = start_service()
            if not process:
                print("無法啟動服務，測試終止")
                return
            results = test_service_availability()
    except:
        # 啟動新服務
        process = start_service()
        if not process:
            print("無法啟動服務，測試終止")
            return
        results = test_service_availability()
    
    # 顯示結果
    print("\n" + "=" * 50)
    print("測試結果摘要:")
    print("=" * 50)
    
    passed = 0
    total = len(results)
    
    for name, success, status_code, error in results:
        if success:
            print(f"PASS: {name} (狀態碼: {status_code})")
            passed += 1
        else:
            print(f"FAIL: {name} - {error}")
    
    print(f"\n總計: {passed}/{total} 個測試通過")
    success_rate = (passed / total * 100) if total > 0 else 0
    print(f"成功率: {success_rate:.1f}%")
    
    # 保存結果
    result_data = {
        "timestamp": time.strftime("%Y-%m-%d %H:%M:%S"),
        "total_tests": total,
        "passed_tests": passed,
        "success_rate": success_rate,
        "results": [
            {
                "name": name,
                "success": success,
                "status_code": status_code,
                "error": error
            }
            for name, success, status_code, error in results
        ]
    }
    
    results_file = project_root / "simple_web_test_results.json"
    with open(results_file, 'w', encoding='utf-8') as f:
        json.dump(result_data, f, ensure_ascii=False, indent=2)
    
    print(f"\n測試結果已保存到: {results_file}")
    
    # 停止服務（如果是我們啟動的）
    if process:
        stop_service(process)
    
    # 顯示建議
    if passed == total:
        print("\n所有測試通過！整合服務運行正常。")
    else:
        print(f"\n有 {total - passed} 個測試失敗，請檢查:")
        print("1. 前端模組是否正確配置")
        print("2. 路由是否正確設置")
        print("3. 依賴是否正確安裝")
        print("4. 環境變數是否正確設置")

if __name__ == "__main__":
    main()