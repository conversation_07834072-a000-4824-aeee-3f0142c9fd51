<!-- 通用模態框 -->
<div class="modal-overlay" id="global-modal" style="display: none;">
    <div class="modal-container">
        <div class="modal-header">
            <h3 class="modal-title" id="modal-title">標題</h3>
            <button class="modal-close" id="modal-close" aria-label="關閉">
                <i class="fas fa-times"></i>
            </button>
        </div>
        
        <div class="modal-body" id="modal-body">
            <p>內容</p>
        </div>
        
        <div class="modal-footer" id="modal-footer">
            <button class="btn btn-secondary" id="modal-cancel">取消</button>
            <button class="btn btn-primary" id="modal-confirm">確認</button>
        </div>
    </div>
</div>

<!-- 模態框 JavaScript -->
<script>
class ModalManager {
    constructor() {
        this.modal = document.getElementById('global-modal');
        this.modalTitle = document.getElementById('modal-title');
        this.modalBody = document.getElementById('modal-body');
        this.modalFooter = document.getElementById('modal-footer');
        this.modalClose = document.getElementById('modal-close');
        this.modalCancel = document.getElementById('modal-cancel');
        this.modalConfirm = document.getElementById('modal-confirm');
        
        this.currentCallback = null;
        this.currentCancelCallback = null;
        
        this.init();
    }
    
    init() {
        if (!this.modal) return;
        
        // 關閉按鈕事件
        if (this.modalClose) {
            this.modalClose.addEventListener('click', () => this.hide());
        }
        
        // 取消按鈕事件
        if (this.modalCancel) {
            this.modalCancel.addEventListener('click', () => {
                if (this.currentCancelCallback) {
                    this.currentCancelCallback();
                }
                this.hide();
            });
        }
        
        // 確認按鈕事件
        if (this.modalConfirm) {
            this.modalConfirm.addEventListener('click', () => {
                if (this.currentCallback) {
                    this.currentCallback();
                }
                this.hide();
            });
        }
        
        // 點擊背景關閉
        this.modal.addEventListener('click', (e) => {
            if (e.target === this.modal) {
                this.hide();
            }
        });
        
        // ESC 鍵關閉
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape' && this.isVisible()) {
                this.hide();
            }
        });
    }
    
    show(options = {}) {
        const {
            title = '提示',
            body = '',
            confirmText = '確認',
            cancelText = '取消',
            showCancel = true,
            onConfirm = null,
            onCancel = null,
            size = 'medium'
        } = options;
        
        // 設置標題
        if (this.modalTitle) {
            this.modalTitle.textContent = title;
        }
        
        // 設置內容
        if (this.modalBody) {
            if (typeof body === 'string') {
                this.modalBody.innerHTML = body;
            } else if (body instanceof HTMLElement) {
                this.modalBody.innerHTML = '';
                this.modalBody.appendChild(body);
            }
        }
        
        // 設置按鈕
        if (this.modalConfirm) {
            this.modalConfirm.textContent = confirmText;
        }
        
        if (this.modalCancel) {
            this.modalCancel.textContent = cancelText;
            this.modalCancel.style.display = showCancel ? 'inline-block' : 'none';
        }
        
        // 設置回調函數
        this.currentCallback = onConfirm;
        this.currentCancelCallback = onCancel;
        
        // 設置大小
        const container = this.modal.querySelector('.modal-container');
        if (container) {
            container.className = `modal-container modal-${size}`;
        }
        
        // 顯示模態框
        this.modal.style.display = 'flex';
        document.body.classList.add('modal-open');
        
        // 聚焦到確認按鈕
        setTimeout(() => {
            if (this.modalConfirm) {
                this.modalConfirm.focus();
            }
        }, 100);
    }
    
    hide() {
        if (this.modal) {
            this.modal.style.display = 'none';
            document.body.classList.remove('modal-open');
        }
        
        // 清除回調函數
        this.currentCallback = null;
        this.currentCancelCallback = null;
    }
    
    isVisible() {
        return this.modal && this.modal.style.display !== 'none';
    }
    
    // 便捷方法：顯示確認對話框
    confirm(message, onConfirm, options = {}) {
        this.show({
            title: options.title || '確認操作',
            body: message,
            confirmText: options.confirmText || '確認',
            cancelText: options.cancelText || '取消',
            showCancel: true,
            onConfirm: onConfirm,
            ...options
        });
    }
    
    // 便捷方法：顯示警告對話框
    alert(message, onConfirm, options = {}) {
        this.show({
            title: options.title || '提示',
            body: message,
            confirmText: options.confirmText || '確定',
            showCancel: false,
            onConfirm: onConfirm,
            ...options
        });
    }
    
    // 便捷方法：顯示輸入對話框
    prompt(message, defaultValue = '', onConfirm, options = {}) {
        const inputId = 'modal-prompt-input';
        const inputHtml = `
            <div class="form-group">
                <label for="${inputId}">${message}</label>
                <input type="text" id="${inputId}" class="form-control" value="${defaultValue}" />
            </div>
        `;
        
        this.show({
            title: options.title || '輸入',
            body: inputHtml,
            confirmText: options.confirmText || '確認',
            cancelText: options.cancelText || '取消',
            showCancel: true,
            onConfirm: () => {
                const input = document.getElementById(inputId);
                if (input && onConfirm) {
                    onConfirm(input.value);
                }
            },
            ...options
        });
        
        // 聚焦到輸入框
        setTimeout(() => {
            const input = document.getElementById(inputId);
            if (input) {
                input.focus();
                input.select();
            }
        }, 100);
    }
    
    // 便捷方法：顯示載入對話框
    loading(message = '處理中...', options = {}) {
        const loadingHtml = `
            <div class="loading-content">
                <div class="loading-spinner"></div>
                <p>${message}</p>
            </div>
        `;
        
        this.show({
            title: options.title || '請稍候',
            body: loadingHtml,
            showCancel: false,
            ...options
        });
        
        // 隱藏底部按鈕
        if (this.modalFooter) {
            this.modalFooter.style.display = 'none';
        }
    }
    
    // 便捷方法：顯示成功訊息
    success(message, onConfirm, options = {}) {
        const successHtml = `
            <div class="success-content">
                <i class="fas fa-check-circle success-icon"></i>
                <p>${message}</p>
            </div>
        `;
        
        this.show({
            title: options.title || '成功',
            body: successHtml,
            confirmText: options.confirmText || '確定',
            showCancel: false,
            onConfirm: onConfirm,
            ...options
        });
    }
    
    // 便捷方法：顯示錯誤訊息
    error(message, onConfirm, options = {}) {
        const errorHtml = `
            <div class="error-content">
                <i class="fas fa-exclamation-triangle error-icon"></i>
                <p>${message}</p>
            </div>
        `;
        
        this.show({
            title: options.title || '錯誤',
            body: errorHtml,
            confirmText: options.confirmText || '確定',
            showCancel: false,
            onConfirm: onConfirm,
            ...options
        });
    }
}

// 全域模態框管理器
window.modalManager = new ModalManager();
</script>