/* 
 * 特殊組件樣式模組
 * 包含模態框、對話框、通知等高級 UI 組件樣式
 */

/* ==================== 模態框樣式 ==================== */
.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: var(--z-modal);
    opacity: 0;
    visibility: hidden;
    transition: var(--transition);
}

.modal-overlay.show {
    opacity: 1;
    visibility: visible;
}

.modal-container {
    background: white;
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-xl);
    max-width: 90vw;
    max-height: 90vh;
    overflow: hidden;
    transform: scale(0.9);
    transition: var(--transition);
}

.modal-overlay.show .modal-container {
    transform: scale(1);
}

.modal-small { max-width: 400px; }
.modal-medium { max-width: 600px; }
.modal-large { max-width: 800px; }
.modal-xl { max-width: 1000px; }

.modal-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: var(--spacing-lg);
    border-bottom: 1px solid var(--border-color);
    background-color: var(--bg-light);
}

.modal-title {
    margin: 0;
    font-size: var(--font-size-lg);
    font-weight: 600;
    color: var(--secondary-color);
}

.modal-body {
    padding: var(--spacing-lg);
    max-height: 60vh;
    overflow-y: auto;
}

.modal-footer {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    gap: var(--spacing-sm);
    padding: var(--spacing-lg);
    border-top: 1px solid var(--border-color);
    background-color: var(--bg-light);
}

/* ==================== 對話框樣式 ==================== */
.dialog-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.6);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: var(--z-modal);
    opacity: 0;
    visibility: hidden;
    transition: var(--transition);
}

.dialog-overlay.show {
    opacity: 1;
    visibility: visible;
}

.dialog-container {
    background: white;
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-xl);
    max-width: 500px;
    width: 90vw;
    transform: scale(0.9) translateY(-20px);
    transition: var(--transition);
}

.dialog-overlay.show .dialog-container {
    transform: scale(1) translateY(0);
}

.dialog-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: var(--spacing-lg);
    border-bottom: 1px solid var(--border-color);
}

.dialog-title {
    margin: 0;
    font-size: var(--font-size-md);
    font-weight: 600;
    color: var(--secondary-color);
}

.dialog-close {
    background: none;
    border: none;
    color: var(--text-muted);
    cursor: pointer;
    padding: var(--spacing-xxs);
    border-radius: var(--radius-sm);
    transition: var(--transition);
    font-size: 1.2em;
}

.dialog-close:hover {
    color: var(--error-color);
    background-color: rgba(220, 53, 69, 0.1);
}

.dialog-body {
    padding: var(--spacing-lg);
    display: flex;
    align-items: flex-start;
    gap: var(--spacing-md);
}

.dialog-icon {
    flex-shrink: 0;
    width: 48px;
    height: 48px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    font-size: 1.5em;
}

.dialog-icon .question-icon {
    color: var(--info-color);
    background-color: rgba(23, 162, 184, 0.1);
}

.dialog-icon .warning-icon {
    color: var(--warning-color);
    background-color: rgba(255, 193, 7, 0.1);
}

.dialog-icon .danger-icon {
    color: var(--error-color);
    background-color: rgba(220, 53, 69, 0.1);
}

.dialog-icon .info-icon {
    color: var(--primary-color);
    background-color: rgba(102, 126, 234, 0.1);
}

.dialog-content {
    flex: 1;
}

.dialog-message {
    margin: 0 0 var(--spacing-sm) 0;
    font-size: var(--font-size-md);
    color: var(--secondary-color);
    line-height: 1.5;
}

.dialog-details {
    font-size: var(--font-size-sm);
    color: var(--text-muted);
    line-height: 1.4;
    background-color: var(--bg-light);
    padding: var(--spacing-sm);
    border-radius: var(--radius-sm);
    border-left: 3px solid var(--warning-color);
}

.dialog-footer {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    gap: var(--spacing-sm);
    padding: var(--spacing-lg);
    border-top: 1px solid var(--border-color);
}

/* ==================== 通知樣式 ==================== */
.notification-container {
    position: fixed;
    top: var(--spacing-lg);
    right: var(--spacing-lg);
    z-index: var(--z-toast);
    pointer-events: none;
    max-width: 400px;
}

.notification {
    display: flex;
    align-items: flex-start;
    gap: var(--spacing-sm);
    background: white;
    border: 1px solid var(--border-color);
    border-radius: var(--radius-md);
    box-shadow: var(--shadow-lg);
    padding: var(--spacing-md);
    margin-bottom: var(--spacing-sm);
    pointer-events: auto;
    transform: translateX(100%);
    opacity: 0;
    transition: var(--transition);
    position: relative;
    overflow: hidden;
}

.notification.show {
    transform: translateX(0);
    opacity: 1;
}

.notification.hide {
    transform: translateX(100%);
    opacity: 0;
}

.notification-success {
    border-left: 4px solid var(--success-color);
}

.notification-error {
    border-left: 4px solid var(--error-color);
}

.notification-warning {
    border-left: 4px solid var(--warning-color);
}

.notification-info {
    border-left: 4px solid var(--info-color);
}

.notification-icon {
    flex-shrink: 0;
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    font-size: var(--font-size-sm);
}

.notification-success .notification-icon {
    background: var(--success-color);
    color: white;
}

.notification-error .notification-icon {
    background: var(--error-color);
    color: white;
}

.notification-warning .notification-icon {
    background: var(--warning-color);
    color: var(--secondary-color);
}

.notification-info .notification-icon {
    background: var(--info-color);
    color: white;
}

.notification-content {
    flex: 1;
    min-width: 0;
}

.notification-title {
    font-weight: 600;
    font-size: var(--font-size-sm);
    color: var(--secondary-color);
    margin-bottom: var(--spacing-xxs);
    line-height: 1.3;
}

.notification-message {
    font-size: var(--font-size-sm);
    color: var(--text-muted);
    line-height: 1.4;
    word-wrap: break-word;
}

.notification-actions {
    flex-shrink: 0;
    display: flex;
    align-items: flex-start;
    gap: var(--spacing-xxs);
}

.notification-close {
    background: none;
    border: none;
    color: var(--text-muted);
    cursor: pointer;
    padding: var(--spacing-xxs);
    border-radius: var(--radius-xs);
    transition: var(--transition);
    font-size: var(--font-size-xs);
}

.notification-close:hover {
    background: rgba(0, 0, 0, 0.1);
    color: var(--error-color);
}

.notification-progress {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: rgba(0, 0, 0, 0.1);
    display: none;
}

.notification-progress .progress-bar {
    height: 100%;
    background: var(--primary-color);
    width: 0%;
    transition: width 0.3s ease;
}

.notification-loading .notification-progress {
    display: block;
}

.notification-loading .notification-icon {
    animation: spin 1s linear infinite;
}

/* ==================== 載入覆蓋層樣式 ==================== */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: var(--z-modal);
    opacity: 0;
    visibility: hidden;
    transition: var(--transition);
}

.loading-overlay.show {
    opacity: 1;
    visibility: visible;
}

.loading-content {
    background: white;
    padding: var(--spacing-xl);
    border-radius: var(--radius-lg);
    text-align: center;
    box-shadow: var(--shadow-lg);
    max-width: 300px;
}

.loading-text {
    margin-top: var(--spacing-md);
    font-size: var(--font-size-md);
    color: var(--secondary-color);
    font-weight: 500;
}

.loading-progress {
    margin-top: var(--spacing-md);
}

.loading-progress .progress-bar {
    height: 6px;
    background: var(--border-color);
    border-radius: var(--radius-xs);
    overflow: hidden;
    margin-bottom: var(--spacing-xxs);
}

.loading-progress .progress-fill {
    height: 100%;
    background: var(--gradient-primary);
    width: 0%;
    transition: width 0.3s ease;
}

.loading-progress .progress-text {
    font-size: var(--font-size-sm);
    color: var(--text-muted);
}

/* ==================== 元素載入狀態 ==================== */
.element-loading-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.9);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    z-index: 10;
    border-radius: inherit;
}

.inline-loading {
    display: flex;
    align-items: center;
    gap: var(--spacing-xxs);
    justify-content: center;
}

.loading-state {
    position: relative;
    pointer-events: none;
    opacity: 0.7;
}

/* ==================== 工具提示樣式 ==================== */
.tooltip {
    position: absolute;
    z-index: var(--z-tooltip);
    background: var(--secondary-color);
    color: white;
    padding: var(--spacing-xxs) var(--spacing-sm);
    border-radius: var(--radius-sm);
    font-size: var(--font-size-xs);
    white-space: nowrap;
    opacity: 0;
    visibility: hidden;
    transform: translateY(-5px);
    transition: var(--transition);
    pointer-events: none;
}

.tooltip.show {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.tooltip::after {
    content: '';
    position: absolute;
    top: 100%;
    left: 50%;
    transform: translateX(-50%);
    border: 4px solid transparent;
    border-top-color: var(--secondary-color);
}

/* ==================== 下拉選單增強樣式 ==================== */
.dropdown-menu {
    animation: dropdownFadeIn 0.2s ease-out;
}

@keyframes dropdownFadeIn {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.dropdown-item {
    position: relative;
    overflow: hidden;
}

.dropdown-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(102, 126, 234, 0.1), transparent);
    transition: left 0.5s ease;
}

.dropdown-item:hover::before {
    left: 100%;
}

/* ==================== 按鈕載入狀態 ==================== */
.btn.loading {
    position: relative;
    color: transparent !important;
    pointer-events: none;
}

.btn.loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 16px;
    height: 16px;
    margin: -8px 0 0 -8px;
    border: 2px solid transparent;
    border-top-color: currentColor;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

.btn-primary.loading::after {
    border-top-color: white;
}

.btn-secondary.loading::after {
    border-top-color: white;
}

.btn-outline-primary.loading::after {
    border-top-color: var(--primary-color);
}

/* ==================== 進度指示器 ==================== */
.progress-indicator {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-md);
    background: var(--bg-light);
    border-radius: var(--radius-md);
    border: 1px solid var(--border-color);
}

.progress-steps {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    flex: 1;
}

.progress-step {
    display: flex;
    align-items: center;
    gap: var(--spacing-xxs);
    font-size: var(--font-size-sm);
    color: var(--text-muted);
}

.progress-step.active {
    color: var(--primary-color);
    font-weight: 600;
}

.progress-step.completed {
    color: var(--success-color);
}

.progress-step.error {
    color: var(--error-color);
}

.progress-step-icon {
    width: 20px;
    height: 20px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: var(--font-size-xs);
    background: var(--border-color);
    color: white;
}

.progress-step.active .progress-step-icon {
    background: var(--primary-color);
    animation: pulse 2s infinite;
}

.progress-step.completed .progress-step-icon {
    background: var(--success-color);
}

.progress-step.error .progress-step-icon {
    background: var(--error-color);
}

@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.7; }
}

/* ==================== 摺疊面板樣式 ==================== */
.collapse-panel {
    border: 1px solid var(--border-color);
    border-radius: var(--radius-md);
    overflow: hidden;
}

.collapse-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: var(--spacing-md);
    background: var(--bg-light);
    cursor: pointer;
    transition: var(--transition);
    user-select: none;
}

.collapse-header:hover {
    background: rgba(102, 126, 234, 0.05);
}

.collapse-title {
    margin: 0;
    font-size: var(--font-size-md);
    font-weight: 600;
    color: var(--secondary-color);
}

.collapse-icon {
    transition: transform 0.3s ease;
    color: var(--text-muted);
}

.collapse-panel.expanded .collapse-icon {
    transform: rotate(180deg);
}

.collapse-content {
    max-height: 0;
    overflow: hidden;
    transition: max-height 0.3s ease;
}

.collapse-panel.expanded .collapse-content {
    max-height: 1000px; /* 足夠大的值 */
}

.collapse-body {
    padding: var(--spacing-md);
    border-top: 1px solid var(--border-color);
}

/* ==================== 標籤頁樣式 ==================== */
.tabs {
    border-bottom: 1px solid var(--border-color);
}

.tab-list {
    display: flex;
    list-style: none;
    margin: 0;
    padding: 0;
    gap: var(--spacing-sm);
}

.tab-item {
    margin: 0;
}

.tab-link {
    display: block;
    padding: var(--spacing-sm) var(--spacing-md);
    color: var(--text-muted);
    text-decoration: none;
    border-bottom: 2px solid transparent;
    transition: var(--transition);
    font-weight: 500;
}

.tab-link:hover {
    color: var(--primary-color);
    background: rgba(102, 126, 234, 0.05);
}

.tab-link.active {
    color: var(--primary-color);
    border-bottom-color: var(--primary-color);
}

.tab-content {
    padding: var(--spacing-lg) 0;
}

.tab-pane {
    display: none;
}

.tab-pane.active {
    display: block;
    animation: fadeIn 0.3s ease-in-out;
}

/* ==================== 動畫關鍵幀 ==================== */
@keyframes spin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideInDown {
    from {
        opacity: 0;
        transform: translateY(-20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes bounceIn {
    0% {
        opacity: 0;
        transform: scale(0.3);
    }
    50% {
        opacity: 1;
        transform: scale(1.05);
    }
    70% {
        transform: scale(0.9);
    }
    100% {
        opacity: 1;
        transform: scale(1);
    }
}

/* ==================== 無障礙性增強 ==================== */
.sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
}

/* 焦點可見性增強 */
.modal-container:focus,
.dialog-container:focus,
.notification:focus {
    outline: 2px solid var(--primary-color);
    outline-offset: 2px;
}

/* 減少動畫偏好支援 */
@media (prefers-reduced-motion: reduce) {
    .notification,
    .modal-container,
    .dialog-container,
    .loading-spinner,
    .progress-step-icon {
        animation: none;
        transition: none;
    }
    
    .collapse-content {
        transition: none;
    }
    
    .tab-pane.active {
        animation: none;
    }
}