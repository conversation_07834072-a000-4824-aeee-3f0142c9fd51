// 檔案處理模組
// 處理選中檔案的批次處理操作

export class FileProcessor {
    constructor(api) {
        this.api = api;
        this.selectedFiles = [];
    }

    // 更新檔案選擇
    updateFileSelection() {
        const checkboxes = document.querySelectorAll('.file-checkbox:checked');
        this.selectedFiles = Array.from(checkboxes).map(cb => cb.value);

        const count = this.selectedFiles.length;
        const countElement = document.getElementById('selectedCount');
        const csvBtn = document.getElementById('csvSummaryBtn');
        const codeBtn = document.getElementById('codeComparisonBtn');

        if (count > 0) {
            countElement.textContent = `已選擇 ${count} 個項目`;
            if (csvBtn) csvBtn.disabled = false;
            if (codeBtn) codeBtn.disabled = false;
        } else {
            countElement.textContent = '未選擇檔案';
            if (csvBtn) csvBtn.disabled = true;
            if (codeBtn) codeBtn.disabled = true;
        }

        return this.selectedFiles;
    }

    // 選擇所有檔案
    selectAllFiles() {
        const checkboxes = document.querySelectorAll('.file-checkbox');
        checkboxes.forEach(cb => cb.checked = true);
        return this.updateFileSelection();
    }

    // 清除選擇
    clearSelection() {
        const checkboxes = document.querySelectorAll('.file-checkbox');
        checkboxes.forEach(cb => cb.checked = false);
        return this.updateFileSelection();
    }

    // 處理選中的檔案
    async processSelectedFiles(toolType, currentPath, statusCallback) {
        if (this.selectedFiles.length === 0) {
            statusCallback('請先選擇要處理的檔案', 'error');
            return { success: false, message: '沒有選擇檔案' };
        }

        const toolNames = {
            'csv_summary': 'CSV 摘要生成',
            'code_comparison': '程式碼比較'
        };

        const toolName = toolNames[toolType];
        if (!toolName) {
            statusCallback(`不支援的工具類型: ${toolType}`, 'error');
            return { success: false, message: '不支援的工具類型' };
        }

        try {
            statusCallback(`正在啟動 ${toolName}...`, 'loading');
            this.showProcessingStatus();

            const results = [];
            const errors = [];

            // 對每個選擇的檔案執行處理
            for (let i = 0; i < this.selectedFiles.length; i++) {
                const filename = this.selectedFiles[i];
                const filePath = `${currentPath}\\${filename}`;

                this.updateProcessingStatus(toolName, `BATCH_${Date.now()}`,
                                         (i / this.selectedFiles.length) * 100,
                                         [`正在處理檔案 ${i + 1}/${this.selectedFiles.length}: ${filename}`]);

                try {
                    // 呼叫對應的 API 端點
                    const endpoint = toolType === 'csv_summary' ?
                        '/api/process/csv-summary' :
                        '/api/process/code-comparison';

                    const response = await fetch(`${endpoint}?input_path=${encodeURIComponent(filePath)}`, {
                        method: 'POST'
                    });

                    const result = await response.json();

                    if (result.status === 'success') {
                        console.log(`✅ ${filename} 處理成功`);
                        if (result.output_files && result.output_files.length > 0) {
                            console.log(`   輸出檔案: ${result.output_files.join(', ')}`);
                        }
                        results.push({
                            filename: filename,
                            success: true,
                            outputFiles: result.output_files
                        });
                    } else {
                        const errorMsg = result.error_message || '未知錯誤';
                        console.log(`❌ ${filename} 處理失敗: ${errorMsg}`);
                        errors.push({
                            filename: filename,
                            error: errorMsg
                        });
                    }
                } catch (error) {
                    console.log(`❌ ${filename} 處理失敗: ${error.message}`);
                    errors.push({
                        filename: filename,
                        error: error.message
                    });
                }
            }

            this.updateProcessingStatus(toolName, `BATCH_${Date.now()}`, 100,
                                     [`${toolName} 完成！`]);

            const successCount = results.length;
            const errorCount = errors.length;
            const totalCount = this.selectedFiles.length;

            if (errorCount === 0) {
                statusCallback(`${toolName} 完成，共處理 ${totalCount} 個檔案`, 'success');
                return {
                    success: true,
                    results: results,
                    message: `全部 ${totalCount} 個檔案處理成功`
                };
            } else if (successCount > 0) {
                statusCallback(`${toolName} 部分完成，成功 ${successCount} 個，失敗 ${errorCount} 個`, 'success');
                return {
                    success: true,
                    results: results,
                    errors: errors,
                    message: `處理完成：成功 ${successCount} 個，失敗 ${errorCount} 個`
                };
            } else {
                statusCallback(`${toolName} 失敗，全部 ${totalCount} 個檔案都處理失敗`, 'error');
                return {
                    success: false,
                    errors: errors,
                    message: `全部檔案處理失敗`
                };
            }

        } catch (error) {
            console.log(`❌ 處理過程中發生錯誤: ${error.message}`);
            statusCallback(`${toolName} 失敗: ${error.message}`, 'error');
            return {
                success: false,
                message: error.message
            };
        } finally {
            // 隱藏處理狀態（延遲幾秒讓用戶看到完成訊息）
            setTimeout(() => {
                this.hideProcessingStatus();
            }, 3000);
        }
    }

    // 顯示處理狀態
    showProcessingStatus() {
        const statusElement = document.getElementById('processingStatus');
        if (statusElement) {
            statusElement.style.display = 'block';
        }
    }

    // 隱藏處理狀態
    hideProcessingStatus() {
        const statusElement = document.getElementById('processingStatus');
        if (statusElement) {
            statusElement.style.display = 'none';
        }
    }

    // 更新處理狀態
    updateProcessingStatus(tool, taskId, progress, logs) {
        const currentToolElement = document.getElementById('currentTool');
        const currentTaskIdElement = document.getElementById('currentTaskId');
        const progressPercentElement = document.getElementById('progressPercent');
        const progressBarElement = document.getElementById('progressBar');
        const logsElement = document.getElementById('processingLogs');

        if (currentToolElement) {
            currentToolElement.textContent = tool;
        }
        if (currentTaskIdElement) {
            currentTaskIdElement.textContent = taskId;
        }
        if (progressPercentElement) {
            progressPercentElement.textContent = `${progress.toFixed(0)}%`;
        }
        if (progressBarElement) {
            progressBarElement.style.width = `${progress}%`;
        }

        if (logs && logsElement) {
            logsElement.innerHTML = logs.map(log => `<div>${log}</div>`).join('');
            logsElement.scrollTop = logsElement.scrollHeight;
        }
    }

    // 取得選中的檔案列表
    getSelectedFiles() {
        return [...this.selectedFiles];
    }

    // 取得選中檔案數量
    getSelectedFileCount() {
        return this.selectedFiles.length;
    }

    // 檢查是否有選中檔案
    hasSelectedFiles() {
        return this.selectedFiles.length > 0;
    }

    // 重置選擇狀態
    resetSelection() {
        this.selectedFiles = [];
        this.clearSelection();
    }
}