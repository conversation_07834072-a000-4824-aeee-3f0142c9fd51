/**
 * 通知組件
 * 提供統一的通知訊息顯示功能
 */

class NotificationComponent {
    constructor() {
        this.container = null;
        this.template = null;
        this.notifications = new Map();
        this.nextId = 1;
        this.maxNotifications = 5;
        
        this.init();
    }
    
    init() {
        this.createContainer();
        this.createTemplate();
        this.setupStyles();
    }
    
    createContainer() {
        // 檢查是否已存在容器
        this.container = document.getElementById('notification-container');
        
        if (!this.container) {
            this.container = document.createElement('div');
            this.container.id = 'notification-container';
            this.container.className = 'notification-container';
            document.body.appendChild(this.container);
        }
    }
    
    createTemplate() {
        // 檢查是否已存在模板
        this.template = document.getElementById('notification-template');
        
        if (!this.template) {
            this.template = document.createElement('template');
            this.template.id = 'notification-template';
            this.template.innerHTML = `
                <div class="notification">
                    <div class="notification-icon">
                        <i class="fas fa-info-circle"></i>
                    </div>
                    <div class="notification-content">
                        <div class="notification-title"></div>
                        <div class="notification-message"></div>
                    </div>
                    <div class="notification-actions">
                        <button class="notification-close" aria-label="關閉通知">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                    <div class="notification-progress">
                        <div class="progress-bar"></div>
                    </div>
                </div>
            `;
            document.head.appendChild(this.template);
        }
    }
    
    setupStyles() {
        // 檢查是否已存在樣式
        if (document.getElementById('notification-styles')) {
            return;
        }
        
        const style = document.createElement('style');
        style.id = 'notification-styles';
        style.textContent = `
            .notification-container {
                position: fixed;
                top: 20px;
                right: 20px;
                z-index: 10000;
                pointer-events: none;
                max-width: 400px;
            }
            
            .notification {
                display: flex;
                align-items: flex-start;
                gap: 12px;
                background: white;
                border: 1px solid var(--border-color, #e9ecef);
                border-radius: 8px;
                box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
                padding: 16px;
                margin-bottom: 12px;
                pointer-events: auto;
                transform: translateX(100%);
                opacity: 0;
                transition: all 0.3s ease;
                position: relative;
                overflow: hidden;
            }
            
            .notification.show {
                transform: translateX(0);
                opacity: 1;
            }
            
            .notification.hide {
                transform: translateX(100%);
                opacity: 0;
            }
            
            .notification-icon {
                flex-shrink: 0;
                width: 24px;
                height: 24px;
                display: flex;
                align-items: center;
                justify-content: center;
                border-radius: 50%;
                font-size: 14px;
            }
            
            .notification-success .notification-icon {
                background: var(--success-color, #28a745);
                color: white;
            }
            
            .notification-error .notification-icon {
                background: var(--error-color, #dc3545);
                color: white;
            }
            
            .notification-warning .notification-icon {
                background: var(--warning-color, #ffc107);
                color: var(--secondary-color, #2c3e50);
            }
            
            .notification-info .notification-icon {
                background: var(--info-color, #17a2b8);
                color: white;
            }
            
            .notification-content {
                flex: 1;
                min-width: 0;
            }
            
            .notification-title {
                font-weight: 600;
                font-size: 14px;
                color: var(--secondary-color, #2c3e50);
                margin-bottom: 4px;
                line-height: 1.3;
            }
            
            .notification-message {
                font-size: 13px;
                color: var(--text-muted, #6c757d);
                line-height: 1.4;
                word-wrap: break-word;
            }
            
            .notification-actions {
                flex-shrink: 0;
                display: flex;
                align-items: flex-start;
                gap: 8px;
            }
            
            .notification-close {
                background: none;
                border: none;
                color: var(--text-muted, #6c757d);
                cursor: pointer;
                padding: 4px;
                border-radius: 4px;
                transition: all 0.2s ease;
                font-size: 12px;
            }
            
            .notification-close:hover {
                background: rgba(0, 0, 0, 0.1);
                color: var(--error-color, #dc3545);
            }
            
            .notification-progress {
                position: absolute;
                bottom: 0;
                left: 0;
                right: 0;
                height: 3px;
                background: rgba(0, 0, 0, 0.1);
                display: none;
            }
            
            .notification-progress .progress-bar {
                height: 100%;
                background: var(--primary-color, #667eea);
                width: 0%;
                transition: width 0.3s ease;
            }
            
            .notification-loading .notification-progress {
                display: block;
            }
            
            .notification-loading .notification-icon {
                animation: spin 1s linear infinite;
            }
            
            @keyframes spin {
                from { transform: rotate(0deg); }
                to { transform: rotate(360deg); }
            }
            
            @media (max-width: 768px) {
                .notification-container {
                    top: 10px;
                    right: 10px;
                    left: 10px;
                    max-width: none;
                }
                
                .notification {
                    margin-bottom: 8px;
                    padding: 12px;
                }
            }
        `;
        document.head.appendChild(style);
    }
    
    show(options = {}) {
        const {
            title = '',
            message = '',
            type = 'info',
            duration = 5000,
            persistent = false,
            actions = [],
            onClose = null
        } = options;
        
        // 限制通知數量
        if (this.notifications.size >= this.maxNotifications) {
            const oldestId = this.notifications.keys().next().value;
            this.hide(oldestId);
        }
        
        const id = this.nextId++;
        const notification = this.createNotification(id, {
            title,
            message,
            type,
            duration,
            persistent,
            actions,
            onClose
        });
        
        this.container.appendChild(notification);
        this.notifications.set(id, notification);
        
        // 觸發顯示動畫
        requestAnimationFrame(() => {
            notification.classList.add('show');
        });
        
        // 設置自動關閉
        if (duration > 0 && !persistent) {
            setTimeout(() => {
                this.hide(id);
            }, duration);
        }
        
        return id;
    }
    
    createNotification(id, options) {
        const { title, message, type, actions, onClose } = options;
        
        const notification = this.template.content.cloneNode(true).querySelector('.notification');
        notification.dataset.id = id;
        notification.classList.add(`notification-${type}`);
        
        // 設置圖示
        const icon = notification.querySelector('.notification-icon i');
        if (icon) {
            switch (type) {
                case 'success':
                    icon.className = 'fas fa-check-circle';
                    break;
                case 'error':
                    icon.className = 'fas fa-exclamation-circle';
                    break;
                case 'warning':
                    icon.className = 'fas fa-exclamation-triangle';
                    break;
                default:
                    icon.className = 'fas fa-info-circle';
            }
        }
        
        // 設置標題
        const titleElement = notification.querySelector('.notification-title');
        if (titleElement) {
            if (title) {
                titleElement.textContent = title;
                titleElement.style.display = 'block';
            } else {
                titleElement.style.display = 'none';
            }
        }
        
        // 設置訊息
        const messageElement = notification.querySelector('.notification-message');
        if (messageElement) {
            messageElement.textContent = message;
        }
        
        // 設置關閉按鈕
        const closeButton = notification.querySelector('.notification-close');
        if (closeButton) {
            closeButton.addEventListener('click', () => {
                this.hide(id);
                if (onClose) onClose();
            });
        }
        
        // 設置自定義操作
        if (actions && actions.length > 0) {
            const actionsContainer = notification.querySelector('.notification-actions');
            actions.forEach(action => {
                const button = document.createElement('button');
                button.className = `btn btn-sm btn-${action.type || 'secondary'}`;
                button.textContent = action.text;
                button.addEventListener('click', () => {
                    if (action.handler) action.handler();
                    if (action.closeOnClick !== false) this.hide(id);
                });
                actionsContainer.insertBefore(button, closeButton);
            });
        }
        
        return notification;
    }
    
    hide(id) {
        const notification = this.notifications.get(id);
        if (!notification) return;
        
        notification.classList.add('hide');
        
        setTimeout(() => {
            if (notification.parentNode) {
                notification.parentNode.removeChild(notification);
            }
            this.notifications.delete(id);
        }, 300);
    }
    
    hideAll() {
        this.notifications.forEach((notification, id) => {
            this.hide(id);
        });
    }
    
    // 便捷方法
    success(message, title = '成功', options = {}) {
        return this.show({
            title,
            message,
            type: 'success',
            duration: 3000,
            ...options
        });
    }
    
    error(message, title = '錯誤', options = {}) {
        return this.show({
            title,
            message,
            type: 'error',
            duration: 0,
            persistent: true,
            ...options
        });
    }
    
    warning(message, title = '警告', options = {}) {
        return this.show({
            title,
            message,
            type: 'warning',
            duration: 5000,
            ...options
        });
    }
    
    info(message, title = '提示', options = {}) {
        return this.show({
            title,
            message,
            type: 'info',
            duration: 4000,
            ...options
        });
    }
    
    loading(message, title = '處理中', options = {}) {
        const notification = this.show({
            title,
            message,
            type: 'info',
            duration: 0,
            persistent: true,
            ...options
        });
        
        const notificationElement = this.notifications.get(notification);
        if (notificationElement) {
            notificationElement.classList.add('notification-loading');
            
            // 隱藏關閉按鈕
            const closeButton = notificationElement.querySelector('.notification-close');
            if (closeButton) {
                closeButton.style.display = 'none';
            }
        }
        
        return notification;
    }
    
    progress(message, progress = 0, title = '處理中', options = {}) {
        const id = this.show({
            title,
            message,
            type: 'info',
            duration: 0,
            persistent: true,
            ...options
        });
        
        this.updateProgress(id, progress);
        return id;
    }
    
    updateProgress(id, progress, message = null) {
        const notification = this.notifications.get(id);
        if (!notification) return;
        
        const progressBar = notification.querySelector('.progress-bar');
        if (progressBar) {
            progressBar.style.width = `${Math.max(0, Math.min(100, progress))}%`;
        }
        
        if (message) {
            const messageElement = notification.querySelector('.notification-message');
            if (messageElement) {
                messageElement.textContent = message;
            }
        }
        
        const progressContainer = notification.querySelector('.notification-progress');
        if (progressContainer) {
            progressContainer.style.display = 'block';
        }
    }
    
    completeProgress(id, message = '完成', options = {}) {
        this.updateProgress(id, 100, message);
        
        setTimeout(() => {
            this.hide(id);
            
            if (options.showComplete !== false) {
                this.success(message, '完成');
            }
        }, 1000);
    }
}

// 全域實例
if (typeof window !== 'undefined') {
    window.NotificationComponent = NotificationComponent;
    
    // 自動初始化
    document.addEventListener('DOMContentLoaded', function() {
        if (!window.notificationComponent) {
            window.notificationComponent = new NotificationComponent();
        }
    });
}