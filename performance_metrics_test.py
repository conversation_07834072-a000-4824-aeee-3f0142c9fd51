#!/usr/bin/env python3
"""
性能指標對比測試
測試頁面載入時間、記憶體使用量等性能指標
創建日期: 2025-08-13
作者: backend-architect agent
"""

import os
import sys
import json
import time
import psutil
import requests
import statistics
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Any, Tuple
import concurrent.futures
import threading
import gc


class PerformanceMetricsTester:
    """性能指標測試器"""
    
    def __init__(self, base_url: str = "http://localhost:8000"):
        self.base_url = base_url
        self.project_root = Path(__file__).parent
        
        self.results = {
            'test_metadata': {
                'test_start_time': datetime.now().isoformat(),
                'base_url': base_url,
                'tester': 'performance-metrics-tester',
                'system_info': self._get_system_info()
            },
            'page_load_performance': {},
            'memory_usage_analysis': {},
            'concurrent_performance': {},
            'sustained_load_test': {},
            'resource_efficiency': {},
            'performance_baselines': {}
        }
        
        # 定義測試頁面
        self.test_pages = {
            'core_pages': [
                {'url': '/', 'name': '主頁', 'expected_load_time': 2.0},
                {'url': '/health', 'name': '健康檢查', 'expected_load_time': 0.5}
            ],
            'frontend_modules': [
                {'url': '/analytics', 'name': '分析模組', 'expected_load_time': 3.0},
                {'url': '/analytics/dashboard', 'name': '分析儀表板', 'expected_load_time': 4.0},
                {'url': '/email', 'name': '郵件模組', 'expected_load_time': 3.0},
                {'url': '/email/inbox', 'name': '郵件收件箱', 'expected_load_time': 4.0},
                {'url': '/eqc', 'name': 'EQC模組', 'expected_load_time': 3.0},
                {'url': '/eqc/dashboard', 'name': 'EQC儀表板', 'expected_load_time': 4.0},
                {'url': '/files', 'name': '檔案管理', 'expected_load_time': 3.0},
                {'url': '/files/browser', 'name': '檔案瀏覽器', 'expected_load_time': 4.0},
                {'url': '/monitoring', 'name': '監控模組', 'expected_load_time': 3.0},
                {'url': '/monitoring/health', 'name': '監控健康檢查', 'expected_load_time': 2.0},
                {'url': '/tasks', 'name': '任務模組', 'expected_load_time': 3.0},
                {'url': '/tasks/queue', 'name': '任務佇列', 'expected_load_time': 4.0}
            ],
            'api_endpoints': [
                {'url': '/api/status', 'name': '系統狀態API', 'expected_load_time': 1.0},
                {'url': '/api/analytics/status', 'name': '分析狀態API', 'expected_load_time': 1.0},
                {'url': '/api/email/status', 'name': '郵件狀態API', 'expected_load_time': 1.0},
                {'url': '/api/eqc/status', 'name': 'EQC狀態API', 'expected_load_time': 1.0},
                {'url': '/api/files/status', 'name': '檔案狀態API', 'expected_load_time': 1.0},
                {'url': '/api/monitoring/status', 'name': '監控狀態API', 'expected_load_time': 1.0},
                {'url': '/api/tasks/status', 'name': '任務狀態API', 'expected_load_time': 1.0}
            ]
        }

    def _get_system_info(self) -> Dict[str, Any]:
        """獲取系統信息"""
        try:
            return {
                'cpu_count': psutil.cpu_count(),
                'cpu_count_logical': psutil.cpu_count(logical=True),
                'memory_total_gb': round(psutil.virtual_memory().total / 1024 / 1024 / 1024, 2),
                'platform': sys.platform,
                'python_version': sys.version.split()[0]
            }
        except:
            return {'error': '無法獲取系統信息'}

    def log_performance_result(self, category: str, test_name: str, 
                             metrics: Dict[str, Any], success: bool = True,
                             benchmark_comparison: str = ""):
        """記錄性能測試結果"""
        if category not in self.results:
            self.results[category] = {}
            
        self.results[category][test_name] = {
            'metrics': metrics,
            'success': success,
            'benchmark_comparison': benchmark_comparison,
            'timestamp': datetime.now().isoformat()
        }
        
        status = "✅ PASS" if success else "❌ FAIL"
        print(f"{status} [{category}] {test_name}")
        
        # 打印關鍵指標
        if 'avg_response_time' in metrics:
            print(f"    平均響應時間: {metrics['avg_response_time']*1000:.2f}ms")
        if 'memory_usage_mb' in metrics:
            print(f"    記憶體使用: {metrics['memory_usage_mb']:.2f}MB")
        if 'requests_per_second' in metrics:
            print(f"    每秒請求數: {metrics['requests_per_second']:.2f}")
        if benchmark_comparison:
            print(f"    基準比較: {benchmark_comparison}")

    def check_service_health(self) -> bool:
        """檢查服務健康狀態"""
        try:
            response = requests.get(f"{self.base_url}/health", timeout=5)
            return response.status_code == 200
        except:
            return False

    def measure_page_load_performance(self) -> Dict[str, Any]:
        """測量頁面載入性能"""
        print("\n📄 測量頁面載入性能...")
        
        page_performance = {}
        
        for category, pages in self.test_pages.items():
            print(f"\n測試類別: {category}")
            category_results = {}
            
            for page_config in pages:
                page_url = f"{self.base_url}{page_config['url']}"
                page_name = page_config['name']
                expected_time = page_config['expected_load_time']
                
                print(f"  測試頁面: {page_name}")
                
                # 執行多次測試獲取統計數據
                load_times = []
                content_sizes = []
                status_codes = []
                
                test_rounds = 5
                for round_num in range(test_rounds):
                    try:
                        start_time = time.time()
                        response = requests.get(page_url, timeout=15)
                        load_time = time.time() - start_time
                        
                        load_times.append(load_time)
                        content_sizes.append(len(response.content))
                        status_codes.append(response.status_code)
                        
                        # 清理緩存影響
                        time.sleep(0.5)
                        
                    except Exception as e:
                        print(f"    第{round_num+1}輪測試失敗: {str(e)}")
                        continue
                
                if load_times:
                    # 計算統計數據
                    page_metrics = {
                        'avg_response_time': statistics.mean(load_times),
                        'min_response_time': min(load_times),
                        'max_response_time': max(load_times),
                        'median_response_time': statistics.median(load_times),
                        'std_deviation': statistics.stdev(load_times) if len(load_times) > 1 else 0,
                        'avg_content_size': statistics.mean(content_sizes),
                        'successful_requests': len([sc for sc in status_codes if sc == 200]),
                        'total_requests': len(status_codes),
                        'success_rate': len([sc for sc in status_codes if sc == 200]) / len(status_codes) * 100,
                        'expected_load_time': expected_time,
                        'performance_rating': 'good' if statistics.mean(load_times) <= expected_time else 'needs_improvement'
                    }
                    
                    # 性能評估
                    avg_time = page_metrics['avg_response_time']
                    performance_ok = avg_time <= expected_time
                    
                    benchmark_msg = f"期望: {expected_time}s, 實際: {avg_time:.3f}s"
                    if not performance_ok:
                        benchmark_msg += f" (超出 {((avg_time/expected_time-1)*100):.1f}%)"
                    
                    category_results[page_config['url']] = {
                        'page_name': page_name,
                        'metrics': page_metrics,
                        'performance_ok': performance_ok
                    }
                    
                    self.log_performance_result('page_load_performance', 
                                              f"{category}_{page_config['url'].replace('/', '_')}",
                                              page_metrics, performance_ok, benchmark_msg)
                else:
                    category_results[page_config['url']] = {
                        'page_name': page_name,
                        'error': '所有測試請求失敗',
                        'performance_ok': False
                    }
            
            page_performance[category] = category_results
        
        return page_performance

    def analyze_memory_usage(self, monitoring_duration: int = 180) -> Dict[str, Any]:
        """分析記憶體使用情況"""
        print(f"\n🧠 分析記憶體使用 ({monitoring_duration//60}分鐘)...")
        
        if not self.check_service_health():
            print("⚠️ 服務未運行，無法進行記憶體分析")
            return {'skipped': True, 'reason': 'service_not_running'}
        
        # 初始記憶體基線
        initial_memory = self._get_memory_snapshot()
        print(f"初始記憶體使用: {initial_memory['python_memory_mb']:.2f}MB")
        
        memory_snapshots = []
        load_scenarios = []
        
        start_time = time.time()
        end_time = start_time + monitoring_duration
        
        # 並發執行負載和記憶體監控
        def memory_monitor():
            while time.time() < end_time:
                snapshot = self._get_memory_snapshot()
                snapshot['elapsed_time'] = time.time() - start_time
                memory_snapshots.append(snapshot)
                time.sleep(10)  # 每10秒記錄一次
        
        def load_generator():
            session = requests.Session()
            load_urls = [f"{self.base_url}/", f"{self.base_url}/health"]
            
            round_count = 0
            while time.time() < end_time:
                round_count += 1
                for url in load_urls:
                    try:
                        start_req = time.time()
                        response = session.get(url, timeout=10)
                        req_time = time.time() - start_req
                        
                        load_scenarios.append({
                            'round': round_count,
                            'url': url,
                            'response_time': req_time,
                            'status_code': response.status_code,
                            'elapsed_time': time.time() - start_time
                        })
                    except:
                        pass
                
                time.sleep(5)  # 每5秒執行一輪請求
        
        # 啟動監控線程
        monitor_thread = threading.Thread(target=memory_monitor)
        load_thread = threading.Thread(target=load_generator)
        
        monitor_thread.start()
        load_thread.start()
        
        monitor_thread.join()
        load_thread.join()
        
        # 最終記憶體快照
        final_memory = self._get_memory_snapshot()
        
        # 計算記憶體分析結果
        if memory_snapshots:
            memory_values = [snap['python_memory_mb'] for snap in memory_snapshots]
            system_memory_values = [snap['system_memory_percent'] for snap in memory_snapshots]
            
            memory_analysis = {
                'monitoring_duration_minutes': monitoring_duration / 60,
                'total_snapshots': len(memory_snapshots),
                'total_requests': len(load_scenarios),
                'initial_memory_mb': initial_memory['python_memory_mb'],
                'final_memory_mb': final_memory['python_memory_mb'],
                'memory_growth_mb': final_memory['python_memory_mb'] - initial_memory['python_memory_mb'],
                'memory_stats': {
                    'avg_memory_mb': statistics.mean(memory_values),
                    'min_memory_mb': min(memory_values),
                    'max_memory_mb': max(memory_values),
                    'std_deviation_mb': statistics.stdev(memory_values) if len(memory_values) > 1 else 0
                },
                'system_memory_stats': {
                    'avg_system_usage_percent': statistics.mean(system_memory_values),
                    'max_system_usage_percent': max(system_memory_values)
                },
                'memory_efficiency': {
                    'memory_per_request_kb': (final_memory['python_memory_mb'] - initial_memory['python_memory_mb']) * 1024 / len(load_scenarios) if load_scenarios else 0,
                    'memory_leak_detected': final_memory['python_memory_mb'] > initial_memory['python_memory_mb'] * 1.1  # 超過10%增長視為可能洩漏
                },
                'detailed_snapshots': memory_snapshots,
                'load_history': load_scenarios
            }
            
            # 記憶體效率評估
            memory_growth = memory_analysis['memory_growth_mb']
            memory_efficient = memory_growth < 50  # 50MB增長閾值
            
            efficiency_msg = f"增長: {memory_growth:+.2f}MB"
            if memory_analysis['memory_efficiency']['memory_leak_detected']:
                efficiency_msg += " (可能有記憶體洩漏)"
            
            self.log_performance_result('memory_usage_analysis', 'sustained_load_memory',
                                      memory_analysis, memory_efficient, efficiency_msg)
            
            return memory_analysis
        
        return {'error': '無法收集記憶體數據'}

    def _get_memory_snapshot(self) -> Dict[str, Any]:
        """獲取記憶體快照"""
        try:
            # 系統記憶體
            system_memory = psutil.virtual_memory()
            
            # Python進程記憶體
            python_memory = 0
            python_processes = []
            
            for proc in psutil.process_iter(['pid', 'name', 'memory_info']):
                try:
                    if 'python' in proc.info['name'].lower():
                        memory_mb = proc.info['memory_info'].rss / 1024 / 1024
                        python_memory += memory_mb
                        python_processes.append({
                            'pid': proc.info['pid'],
                            'memory_mb': memory_mb
                        })
                except:
                    continue
            
            return {
                'timestamp': datetime.now().isoformat(),
                'system_memory_total_gb': system_memory.total / 1024 / 1024 / 1024,
                'system_memory_percent': system_memory.percent,
                'system_memory_available_gb': system_memory.available / 1024 / 1024 / 1024,
                'python_memory_mb': python_memory,
                'python_process_count': len(python_processes),
                'python_processes': python_processes
            }
        except Exception as e:
            return {'error': str(e)}

    def test_concurrent_performance(self) -> Dict[str, Any]:
        """測試併發性能"""
        print("\n⚡ 測試併發性能...")
        
        if not self.check_service_health():
            print("⚠️ 服務未運行，無法進行併發測試")
            return {'skipped': True, 'reason': 'service_not_running'}
        
        concurrent_scenarios = [
            {'workers': 5, 'requests_per_worker': 10, 'name': '輕度併發'},
            {'workers': 10, 'requests_per_worker': 15, 'name': '中度併發'},
            {'workers': 20, 'requests_per_worker': 10, 'name': '高度併發'}
        ]
        
        concurrent_results = {}
        
        for scenario in concurrent_scenarios:
            workers = scenario['workers']
            requests_per_worker = scenario['requests_per_worker']
            scenario_name = scenario['name']
            
            print(f"  執行 {scenario_name}: {workers}個工作者，每個{requests_per_worker}個請求")
            
            test_url = f"{self.base_url}/"
            total_requests = workers * requests_per_worker
            
            def make_request():
                start_time = time.time()
                try:
                    response = requests.get(test_url, timeout=15)
                    return {
                        'success': True,
                        'response_time': time.time() - start_time,
                        'status_code': response.status_code,
                        'content_length': len(response.content)
                    }
                except Exception as e:
                    return {
                        'success': False,
                        'response_time': time.time() - start_time,
                        'error': str(e)
                    }
            
            # 記錄併發測試開始時的記憶體
            pre_test_memory = self._get_memory_snapshot()
            
            start_time = time.time()
            
            with concurrent.futures.ThreadPoolExecutor(max_workers=workers) as executor:
                futures = [executor.submit(make_request) for _ in range(total_requests)]
                results = [f.result() for f in concurrent.futures.as_completed(futures)]
            
            total_time = time.time() - start_time
            
            # 記錄併發測試結束時的記憶體
            post_test_memory = self._get_memory_snapshot()
            
            # 分析結果
            successful_requests = [r for r in results if r['success']]
            failed_requests = [r for r in results if not r['success']]
            
            if successful_requests:
                response_times = [r['response_time'] for r in successful_requests]
                
                scenario_metrics = {
                    'scenario_name': scenario_name,
                    'total_requests': total_requests,
                    'successful_requests': len(successful_requests),
                    'failed_requests': len(failed_requests),
                    'success_rate': len(successful_requests) / total_requests * 100,
                    'total_time': total_time,
                    'requests_per_second': total_requests / total_time,
                    'response_time_stats': {
                        'avg': statistics.mean(response_times),
                        'min': min(response_times),
                        'max': max(response_times),
                        'median': statistics.median(response_times),
                        'std_dev': statistics.stdev(response_times) if len(response_times) > 1 else 0
                    },
                    'memory_impact': {
                        'pre_test_memory_mb': pre_test_memory.get('python_memory_mb', 0),
                        'post_test_memory_mb': post_test_memory.get('python_memory_mb', 0),
                        'memory_increase_mb': post_test_memory.get('python_memory_mb', 0) - pre_test_memory.get('python_memory_mb', 0)
                    }
                }
                
                # 性能評估
                success_rate = scenario_metrics['success_rate']
                avg_response = scenario_metrics['response_time_stats']['avg']
                rps = scenario_metrics['requests_per_second']
                
                performance_ok = success_rate >= 95 and avg_response <= 5.0
                
                benchmark_msg = (f"成功率: {success_rate:.1f}%, "
                               f"平均響應: {avg_response*1000:.2f}ms, "
                               f"RPS: {rps:.2f}")
                
                concurrent_results[scenario_name] = scenario_metrics
                
                self.log_performance_result('concurrent_performance', scenario_name.replace(' ', '_'),
                                          scenario_metrics, performance_ok, benchmark_msg)
            else:
                concurrent_results[scenario_name] = {
                    'scenario_name': scenario_name,
                    'error': '所有請求失敗',
                    'total_requests': total_requests
                }
                
                self.log_performance_result('concurrent_performance', scenario_name.replace(' ', '_'),
                                          {'error': '所有請求失敗'}, False, "併發測試完全失敗")
        
        return concurrent_results

    def run_sustained_load_test(self, duration_minutes: int = 5) -> Dict[str, Any]:
        """執行持續負載測試"""
        print(f"\n🔄 執行持續負載測試 ({duration_minutes}分鐘)...")
        
        if not self.check_service_health():
            print("⚠️ 服務未運行，無法進行持續負載測試")
            return {'skipped': True, 'reason': 'service_not_running'}
        
        test_duration = duration_minutes * 60
        start_time = time.time()
        end_time = start_time + test_duration
        
        # 測試配置
        concurrent_workers = 3
        request_interval = 2  # 每2秒一個請求
        
        requests_log = []
        performance_snapshots = []
        
        def sustained_requester():
            session = requests.Session()
            request_count = 0
            
            while time.time() < end_time:
                request_count += 1
                current_time = time.time()
                elapsed_time = current_time - start_time
                
                try:
                    req_start = time.time()
                    response = session.get(f"{self.base_url}/", timeout=10)
                    req_duration = time.time() - req_start
                    
                    requests_log.append({
                        'request_id': request_count,
                        'elapsed_time': elapsed_time,
                        'response_time': req_duration,
                        'status_code': response.status_code,
                        'content_length': len(response.content),
                        'success': response.status_code == 200
                    })
                    
                except Exception as e:
                    requests_log.append({
                        'request_id': request_count,
                        'elapsed_time': elapsed_time,
                        'error': str(e),
                        'success': False
                    })
                
                time.sleep(request_interval)
        
        def performance_monitor():
            while time.time() < end_time:
                memory_snapshot = self._get_memory_snapshot()
                memory_snapshot['elapsed_time'] = time.time() - start_time
                performance_snapshots.append(memory_snapshot)
                time.sleep(30)  # 每30秒記錄一次
        
        # 啟動併發測試
        request_threads = []
        for i in range(concurrent_workers):
            thread = threading.Thread(target=sustained_requester)
            request_threads.append(thread)
            thread.start()
        
        monitor_thread = threading.Thread(target=performance_monitor)
        monitor_thread.start()
        
        # 等待所有線程完成
        for thread in request_threads:
            thread.join()
        monitor_thread.join()
        
        # 分析持續負載結果
        successful_requests = [req for req in requests_log if req['success']]
        failed_requests = [req for req in requests_log if not req['success']]
        
        if successful_requests:
            response_times = [req['response_time'] for req in successful_requests]
            
            # 計算時間段性能趨勢
            time_segments = []
            segment_duration = 60  # 每分鐘一個時間段
            
            for segment_start in range(0, int(test_duration), segment_duration):
                segment_end = segment_start + segment_duration
                segment_requests = [req for req in successful_requests 
                                  if segment_start <= req['elapsed_time'] < segment_end]
                
                if segment_requests:
                    segment_response_times = [req['response_time'] for req in segment_requests]
                    time_segments.append({
                        'segment_minutes': f"{segment_start//60}-{segment_end//60}",
                        'request_count': len(segment_requests),
                        'avg_response_time': statistics.mean(segment_response_times),
                        'min_response_time': min(segment_response_times),
                        'max_response_time': max(segment_response_times)
                    })
            
            sustained_metrics = {
                'test_duration_minutes': duration_minutes,
                'concurrent_workers': concurrent_workers,
                'total_requests': len(requests_log),
                'successful_requests': len(successful_requests),
                'failed_requests': len(failed_requests),
                'success_rate': len(successful_requests) / len(requests_log) * 100,
                'overall_performance': {
                    'avg_response_time': statistics.mean(response_times),
                    'min_response_time': min(response_times),
                    'max_response_time': max(response_times),
                    'std_deviation': statistics.stdev(response_times) if len(response_times) > 1 else 0
                },
                'time_segment_analysis': time_segments,
                'performance_stability': {
                    'response_time_variance': statistics.variance(response_times) if len(response_times) > 1 else 0,
                    'performance_degradation': self._calculate_performance_degradation(time_segments)
                },
                'resource_usage': performance_snapshots,
                'detailed_requests': requests_log
            }
            
            # 性能評估
            avg_response = sustained_metrics['overall_performance']['avg_response_time']
            success_rate = sustained_metrics['success_rate']
            degradation = sustained_metrics['performance_stability']['performance_degradation']
            
            performance_ok = (success_rate >= 95 and 
                            avg_response <= 3.0 and 
                            degradation < 50)  # 性能劣化小於50%
            
            benchmark_msg = (f"持續{duration_minutes}分鐘, "
                           f"成功率: {success_rate:.1f}%, "
                           f"平均響應: {avg_response*1000:.2f}ms, "
                           f"性能劣化: {degradation:.1f}%")
            
            self.log_performance_result('sustained_load_test', 'sustained_performance',
                                      sustained_metrics, performance_ok, benchmark_msg)
            
            return sustained_metrics
        
        return {'error': '持續負載測試中沒有成功的請求'}

    def _calculate_performance_degradation(self, time_segments: List[Dict]) -> float:
        """計算性能劣化百分比"""
        if len(time_segments) < 2:
            return 0.0
        
        first_segment_avg = time_segments[0]['avg_response_time']
        last_segment_avg = time_segments[-1]['avg_response_time']
        
        if first_segment_avg == 0:
            return 0.0
        
        degradation = ((last_segment_avg - first_segment_avg) / first_segment_avg) * 100
        return max(0, degradation)  # 只關心劣化，不考慮改善

    def establish_performance_baselines(self) -> Dict[str, Any]:
        """建立性能基準線"""
        print("\n📊 建立性能基準線...")
        
        if not self.check_service_health():
            print("⚠️ 服務未運行，無法建立性能基準線")
            return {'skipped': True, 'reason': 'service_not_running'}
        
        # 預熱系統
        print("  預熱系統...")
        for _ in range(5):
            try:
                requests.get(f"{self.base_url}/", timeout=10)
                time.sleep(0.5)
            except:
                pass
        
        baseline_tests = {
            'single_request_baseline': self._baseline_single_request(),
            'light_concurrent_baseline': self._baseline_light_concurrent(),
            'memory_baseline': self._baseline_memory_usage(),
            'api_response_baseline': self._baseline_api_responses()
        }
        
        baselines_summary = {
            'establishment_time': datetime.now().isoformat(),
            'baselines': baseline_tests,
            'recommended_thresholds': {
                'max_response_time_ms': 3000,
                'min_success_rate_percent': 95,
                'max_memory_growth_mb': 50,
                'max_concurrent_response_time_ms': 5000
            }
        }
        
        self.log_performance_result('performance_baselines', 'baselines_established',
                                  baselines_summary, True, 
                                  f"建立了{len(baseline_tests)}個基準線")
        
        return baselines_summary

    def _baseline_single_request(self) -> Dict[str, Any]:
        """建立單一請求基準線"""
        response_times = []
        
        for _ in range(10):
            try:
                start_time = time.time()
                response = requests.get(f"{self.base_url}/", timeout=10)
                response_time = time.time() - start_time
                
                if response.status_code == 200:
                    response_times.append(response_time)
            except:
                pass
            time.sleep(0.2)
        
        if response_times:
            return {
                'avg_response_time': statistics.mean(response_times),
                'min_response_time': min(response_times),
                'max_response_time': max(response_times),
                'samples': len(response_times)
            }
        return {'error': '無法建立單一請求基準線'}

    def _baseline_light_concurrent(self) -> Dict[str, Any]:
        """建立輕度併發基準線"""
        def make_request():
            try:
                start_time = time.time()
                response = requests.get(f"{self.base_url}/", timeout=10)
                return {
                    'response_time': time.time() - start_time,
                    'success': response.status_code == 200
                }
            except:
                return {'success': False}
        
        with concurrent.futures.ThreadPoolExecutor(max_workers=5) as executor:
            futures = [executor.submit(make_request) for _ in range(25)]
            results = [f.result() for f in futures]
        
        successful_results = [r for r in results if r['success']]
        
        if successful_results:
            response_times = [r['response_time'] for r in successful_results]
            return {
                'avg_response_time': statistics.mean(response_times),
                'success_rate': len(successful_results) / len(results) * 100,
                'total_requests': len(results)
            }
        return {'error': '無法建立併發基準線'}

    def _baseline_memory_usage(self) -> Dict[str, Any]:
        """建立記憶體使用基準線"""
        memory_snapshot = self._get_memory_snapshot()
        return {
            'baseline_memory_mb': memory_snapshot.get('python_memory_mb', 0),
            'system_memory_percent': memory_snapshot.get('system_memory_percent', 0),
            'process_count': memory_snapshot.get('python_process_count', 0)
        }

    def _baseline_api_responses(self) -> Dict[str, Any]:
        """建立API響應基準線"""
        api_urls = [
            '/health',
            '/api/status'
        ]
        
        api_baselines = {}
        
        for api_url in api_urls:
            response_times = []
            for _ in range(5):
                try:
                    start_time = time.time()
                    response = requests.get(f"{self.base_url}{api_url}", timeout=10)
                    response_time = time.time() - start_time
                    
                    if response.status_code == 200:
                        response_times.append(response_time)
                except:
                    pass
                time.sleep(0.1)
            
            if response_times:
                api_baselines[api_url] = {
                    'avg_response_time': statistics.mean(response_times),
                    'min_response_time': min(response_times)
                }
        
        return api_baselines

    def run_complete_performance_test(self) -> Dict[str, Any]:
        """執行完整的性能測試"""
        print("=" * 80)
        print("🔬 性能指標測試系統")
        print(f"測試目標: {self.base_url}")
        print("=" * 80)
        
        # 檢查服務是否運行
        if not self.check_service_health():
            print("❌ 服務未運行，請先啟動服務")
            return None
        
        print("✅ 服務運行正常，開始性能測試...")
        
        try:
            # 1. 建立性能基準線
            self.results['performance_baselines'] = self.establish_performance_baselines()
            
            # 2. 頁面載入性能測試
            self.results['page_load_performance'] = self.measure_page_load_performance()
            
            # 3. 併發性能測試
            self.results['concurrent_performance'] = self.test_concurrent_performance()
            
            # 4. 記憶體使用分析（較短時間以節省測試時間）
            self.results['memory_usage_analysis'] = self.analyze_memory_usage(monitoring_duration=120)
            
            # 5. 持續負載測試（較短時間）
            self.results['sustained_load_test'] = self.run_sustained_load_test(duration_minutes=3)
            
            # 添加測試完成時間
            self.results['test_metadata']['test_end_time'] = datetime.now().isoformat()
            self.results['test_metadata']['total_duration'] = (
                datetime.fromisoformat(self.results['test_metadata']['test_end_time']) - 
                datetime.fromisoformat(self.results['test_metadata']['test_start_time'])
            ).total_seconds()
            
            return self.results
            
        except Exception as e:
            print(f"❌ 性能測試過程中發生錯誤: {str(e)}")
            return None

    def save_results(self, filename: str = None) -> str:
        """保存測試結果"""
        if filename is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"performance_metrics_report_{timestamp}.json"
        
        filepath = self.project_root / filename
        
        with open(filepath, 'w', encoding='utf-8') as f:
            json.dump(self.results, f, indent=2, ensure_ascii=False)
        
        print(f"\n📄 性能指標報告已保存: {filepath}")
        return str(filepath)

    def print_summary(self):
        """打印測試摘要"""
        print("\n" + "=" * 80)
        print("📋 性能指標測試摘要")
        print("=" * 80)
        
        # 頁面載入性能摘要
        if 'page_load_performance' in self.results:
            total_pages = 0
            good_performance_pages = 0
            
            for category, pages in self.results['page_load_performance'].items():
                for page_url, page_data in pages.items():
                    if 'metrics' in page_data:
                        total_pages += 1
                        if page_data.get('performance_ok', False):
                            good_performance_pages += 1
            
            if total_pages > 0:
                print(f"頁面載入性能: {good_performance_pages}/{total_pages} 達標 "
                      f"({good_performance_pages/total_pages*100:.1f}%)")
        
        # 併發性能摘要
        if 'concurrent_performance' in self.results:
            successful_scenarios = 0
            total_scenarios = 0
            
            for scenario_name, scenario_data in self.results['concurrent_performance'].items():
                if 'success_rate' in scenario_data:
                    total_scenarios += 1
                    if scenario_data['success_rate'] >= 95:
                        successful_scenarios += 1
            
            if total_scenarios > 0:
                print(f"併發性能: {successful_scenarios}/{total_scenarios} 場景達標")
        
        # 記憶體使用摘要
        if 'memory_usage_analysis' in self.results and 'memory_growth_mb' in self.results['memory_usage_analysis']:
            memory_data = self.results['memory_usage_analysis']
            memory_growth = memory_data['memory_growth_mb']
            memory_efficient = memory_growth < 50
            
            print(f"記憶體效率: {memory_growth:+.2f}MB 增長 "
                  f"({'✅ 良好' if memory_efficient else '⚠️ 需關注'})")
        
        # 持續負載摘要
        if 'sustained_load_test' in self.results and 'success_rate' in self.results['sustained_load_test']:
            sustained_data = self.results['sustained_load_test']
            success_rate = sustained_data['success_rate']
            avg_response = sustained_data['overall_performance']['avg_response_time'] * 1000
            
            print(f"持續負載: {success_rate:.1f}% 成功率, {avg_response:.2f}ms 平均響應")
        
        print("=" * 80)


def main():
    """主函數"""
    if len(sys.argv) > 1 and sys.argv[1] == '--help':
        print("""
性能指標測試系統
用法: python performance_metrics_test.py [選項]

選項:
  --help          顯示此幫助信息
  --url URL       指定測試URL (默認: http://localhost:8000)

測試項目:
  1. 頁面載入性能測試
  2. 記憶體使用分析
  3. 併發性能測試
  4. 持續負載測試
  5. 性能基準線建立

範例:
  python performance_metrics_test.py
  python performance_metrics_test.py --url http://localhost:5000
        """)
        return
    
    # 解析命令行參數
    base_url = "http://localhost:8000"
    
    for i, arg in enumerate(sys.argv):
        if arg == '--url' and i + 1 < len(sys.argv):
            base_url = sys.argv[i + 1]
    
    # 創建測試器並運行測試
    tester = PerformanceMetricsTester(base_url)
    results = tester.run_complete_performance_test()
    
    if results:
        # 保存結果
        report_file = tester.save_results()
        
        # 打印摘要
        tester.print_summary()
        
        print(f"\n✅ 性能指標測試完成！詳細報告: {report_file}")
    else:
        print("\n❌ 性能指標測試失敗")
        sys.exit(1)


if __name__ == "__main__":
    main()