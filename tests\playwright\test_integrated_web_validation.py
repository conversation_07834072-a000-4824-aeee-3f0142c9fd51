#!/usr/bin/env python3
"""
完整的整合服務 Playwright 測試套件
測試修復後的整合服務的所有功能模組

作為 test-automator agent 創建
"""

import os
import sys
import pytest
import subprocess
import time
import json
import asyncio
from pathlib import Path
from datetime import datetime
from typing import Dict, List, Any, Optional

# 添加專案根目錄到 Python 路徑
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

# 導入 Playwright
try:
    from playwright.async_api import async_playwright, <PERSON>, <PERSON><PERSON><PERSON>, BrowserContext
    from playwright.sync_api import sync_playwright
except ImportError:
    print("Playwright 未安裝，正在安裝...")
    subprocess.run([sys.executable, "-m", "pip", "install", "playwright"])
    subprocess.run([sys.executable, "-m", "playwright", "install"])
    from playwright.async_api import async_playwright, <PERSON>, <PERSON><PERSON><PERSON>, BrowserContext
    from playwright.sync_api import sync_playwright

class IntegratedServiceProcess:
    """整合服務進程管理器"""
    
    def __init__(self, flask_port: int = 5000, fastapi_port: int = 8010):
        self.flask_port = flask_port
        self.fastapi_port = fastapi_port
        self.process = None
        self.startup_timeout = 60  # 60秒啟動超時
        
    def start(self) -> bool:
        """啟動整合服務"""
        try:
            print(f"正在啟動整合服務 (Flask: {self.flask_port}, FastAPI: {self.fastapi_port})...")
            
            # 設置環境變數
            env = os.environ.copy()
            env['PYTHONIOENCODING'] = 'utf-8'
            env['PYTHONUTF8'] = '1'
            env['FLASK_ENV'] = 'testing'
            
            # 啟動整合服務
            start_script = project_root / "start_integrated_services.py"
            self.process = subprocess.Popen([
                sys.executable, str(start_script),
                "--mode", "integrated",
                "--flask-port", str(self.flask_port),
                "--fastapi-port", str(self.fastapi_port),
                "--no-monitor",  # 測試時不需要監控
                "--check-deployment"  # 執行部署檢查
            ], env=env, stdout=subprocess.PIPE, stderr=subprocess.PIPE, text=True)
            
            # 等待服務啟動
            return self._wait_for_startup()
            
        except Exception as e:
            print(f"啟動整合服務失敗: {e}")
            return False
    
    def _wait_for_startup(self) -> bool:
        """等待服務啟動完成"""
        import requests
        from requests.adapters import HTTPAdapter
        from urllib3.util.retry import Retry
        
        # 配置重試策略
        session = requests.Session()
        retry_strategy = Retry(
            total=3,
            backoff_factor=1,
            status_forcelist=[429, 500, 502, 503, 504],
        )
        adapter = HTTPAdapter(max_retries=retry_strategy)
        session.mount("http://", adapter)
        
        start_time = time.time()
        
        while time.time() - start_time < self.startup_timeout:
            try:
                # 檢查 Flask 健康狀態
                flask_response = session.get(f"http://localhost:{self.flask_port}/health", timeout=5)
                if flask_response.status_code == 200:
                    print(f"Flask 服務已啟動 (端口 {self.flask_port})")
                    
                    # 檢查 FastAPI 健康狀態
                    try:
                        fastapi_response = session.get(f"http://localhost:{self.fastapi_port}/health", timeout=5)
                        if fastapi_response.status_code == 200:
                            print(f"FastAPI 服務已啟動 (端口 {self.fastapi_port})")
                            return True
                    except requests.exceptions.RequestException:
                        # FastAPI 可能還沒啟動，繼續等待
                        pass
                    
                    # 即使 FastAPI 沒啟動，Flask 正常也可以開始測試
                    if time.time() - start_time > 30:  # 30秒後如果Flask正常就開始測試
                        print("Flask 服務正常，開始測試（FastAPI 可能需要更多時間啟動）")
                        return True
                        
            except requests.exceptions.RequestException:
                pass
            
            time.sleep(2)
            
            # 檢查進程是否還在運行
            if self.process and self.process.poll() is not None:
                stdout, stderr = self.process.communicate()
                print(f"進程意外終止，退出碼: {self.process.returncode}")
                print(f"STDOUT: {stdout}")
                print(f"STDERR: {stderr}")
                return False
        
        print(f"服務啟動超時 ({self.startup_timeout}秒)")
        return False
    
    def stop(self):
        """停止整合服務"""
        if self.process:
            try:
                self.process.terminate()
                self.process.wait(timeout=10)
                print("整合服務已停止")
            except subprocess.TimeoutExpired:
                self.process.kill()
                print("強制停止整合服務")
            except Exception as e:
                print(f"停止服務時發生錯誤: {e}")

class WebTestResults:
    """測試結果收集器"""
    
    def __init__(self):
        self.results = {
            "timestamp": datetime.now().isoformat(),
            "total_tests": 0,
            "passed_tests": 0,
            "failed_tests": 0,
            "test_details": [],
            "console_errors": [],
            "screenshots": [],
            "performance_metrics": {}
        }
    
    def add_test_result(self, test_name: str, passed: bool, details: str = "", 
                       screenshot_path: str = None, metrics: Dict = None):
        """添加測試結果"""
        self.results["total_tests"] += 1
        if passed:
            self.results["passed_tests"] += 1
        else:
            self.results["failed_tests"] += 1
        
        test_result = {
            "test_name": test_name,
            "passed": passed,
            "details": details,
            "timestamp": datetime.now().isoformat()
        }
        
        if screenshot_path:
            test_result["screenshot"] = screenshot_path
            self.results["screenshots"].append(screenshot_path)
        
        if metrics:
            test_result["metrics"] = metrics
            self.results["performance_metrics"][test_name] = metrics
        
        self.results["test_details"].append(test_result)
    
    def add_console_error(self, error: Dict):
        """添加控制台錯誤"""
        self.results["console_errors"].append(error)
    
    def get_summary(self) -> str:
        """獲取測試摘要"""
        success_rate = (self.results["passed_tests"] / self.results["total_tests"] * 100) if self.results["total_tests"] > 0 else 0
        
        summary = f"""
=== 整合服務測試結果摘要 ===
總測試數: {self.results["total_tests"]}
通過測試: {self.results["passed_tests"]}
失敗測試: {self.results["failed_tests"]}
成功率: {success_rate:.1f}%
控制台錯誤: {len(self.results["console_errors"])}
截圖數量: {len(self.results["screenshots"])}
測試時間: {self.results["timestamp"]}
"""
        return summary
    
    def save_to_file(self, filepath: str):
        """保存結果到文件"""
        with open(filepath, 'w', encoding='utf-8') as f:
            json.dump(self.results, f, ensure_ascii=False, indent=2)

class IntegratedWebTester:
    """整合 Web 測試器"""
    
    def __init__(self, headless: bool = True):
        self.headless = headless
        self.results = WebTestResults()
        self.browser = None
        self.context = None
        self.page = None
        self.flask_port = 5000
        self.fastapi_port = 8010
        self.base_url = f"http://localhost:{self.flask_port}"
        self.fastapi_url = f"http://localhost:{self.fastapi_port}"
        
        # 創建截圖目錄
        self.screenshot_dir = project_root / "test_screenshots"
        self.screenshot_dir.mkdir(exist_ok=True)
        
        # 測試頁面配置
        self.test_pages = {
            "home": {"url": "/", "title_contains": ["首頁", "Home", "Welcome", "郵件"], "required_elements": []},
            "email": {"url": "/email", "title_contains": ["郵件", "Email"], "required_elements": [".email-list", ".sync-status"]},
            "analytics": {"url": "/analytics", "title_contains": ["分析", "Analytics"], "required_elements": [".chart-container", ".analytics-dashboard"]},
            "files": {"url": "/files", "title_contains": ["檔案", "Files"], "required_elements": [".file-list", ".upload-form"]},
            "eqc": {"url": "/eqc", "title_contains": ["EQC", "品質"], "required_elements": [".eqc-dashboard", ".compliance-check"]},
            "tasks": {"url": "/tasks", "title_contains": ["任務", "Tasks"], "required_elements": [".task-queue", ".task-status"]},
            "monitoring": {"url": "/monitoring", "title_contains": ["監控", "Monitoring"], "required_elements": [".health-status", ".system-metrics"]}
        }
        
        # API 端點配置
        self.api_endpoints = {
            "health": "/health",
            "email_api": "/email/api/status",
            "analytics_api": "/analytics/api/status",
            "files_api": "/files/api/status",
            "eqc_api": "/eqc/api/status",
            "tasks_api": "/tasks/api/status",
            "monitoring_api": "/monitoring/api/status"
        }
    
    async def initialize(self) -> bool:
        """初始化瀏覽器"""
        try:
            playwright = await async_playwright().start()
            self.browser = await playwright.chromium.launch(
                headless=self.headless,
                args=['--no-sandbox', '--disable-dev-shm-usage']
            )
            
            # 創建瀏覽器上下文
            self.context = await self.browser.new_context(
                viewport={'width': 1280, 'height': 720},
                ignore_https_errors=True,
                user_agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36"
            )
            
            # 監聽控制台錯誤
            self.context.on("console", self._handle_console_message)
            self.context.on("pageerror", self._handle_page_error)
            
            # 創建頁面
            self.page = await self.context.new_page()
            
            # 設置請求超時
            self.page.set_default_timeout(30000)  # 30秒
            
            return True
            
        except Exception as e:
            print(f"初始化瀏覽器失敗: {e}")
            return False
    
    def _handle_console_message(self, msg):
        """處理控制台消息"""
        if msg.type in ['error', 'warning']:
            error_info = {
                "type": msg.type,
                "text": msg.text,
                "location": msg.location,
                "timestamp": datetime.now().isoformat()
            }
            self.results.add_console_error(error_info)
            print(f"Console {msg.type}: {msg.text}")
    
    def _handle_page_error(self, error):
        """處理頁面錯誤"""
        error_info = {
            "type": "page_error",
            "text": str(error),
            "timestamp": datetime.now().isoformat()
        }
        self.results.add_console_error(error_info)
        print(f"Page error: {error}")
    
    async def take_screenshot(self, name: str) -> str:
        """截圖"""
        screenshot_path = self.screenshot_dir / f"{name}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.png"
        await self.page.screenshot(path=str(screenshot_path), full_page=True)
        return str(screenshot_path)
    
    async def test_page_load(self, page_name: str, config: Dict) -> bool:
        """測試頁面載入"""
        try:
            url = self.base_url + config["url"]
            print(f"正在測試頁面: {page_name} ({url})")
            
            # 記錄開始時間
            start_time = time.time()
            
            # 導航到頁面
            response = await self.page.goto(url, wait_until="domcontentloaded", timeout=30000)
            
            # 檢查HTTP狀態碼
            if response.status >= 400:
                self.results.add_test_result(
                    f"{page_name}_load", False, 
                    f"HTTP錯誤: {response.status}"
                )
                return False
            
            # 等待頁面載入完成
            await self.page.wait_for_load_state("networkidle", timeout=10000)
            
            load_time = time.time() - start_time
            
            # 檢查頁面標題
            title = await self.page.title()
            title_check = any(keyword in title for keyword in config["title_contains"]) if config["title_contains"] else True
            
            if not title_check:
                print(f"標題檢查失敗: '{title}' 不包含 {config['title_contains']}")
            
            # 檢查必要元素
            elements_check = True
            missing_elements = []
            
            for selector in config.get("required_elements", []):
                try:
                    await self.page.wait_for_selector(selector, timeout=5000)
                except:
                    elements_check = False
                    missing_elements.append(selector)
            
            if missing_elements:
                print(f"缺少元素: {missing_elements}")
            
            # 截圖
            screenshot_path = await self.take_screenshot(f"{page_name}_page")
            
            # 記錄性能指標
            metrics = {
                "load_time": load_time,
                "status_code": response.status,
                "title": title,
                "page_size": len(await self.page.content())
            }
            
            # 判斷測試結果
            passed = response.status < 400 and title_check and elements_check
            details = f"載入時間: {load_time:.2f}s, 狀態碼: {response.status}, 標題: {title}"
            
            if missing_elements:
                details += f", 缺少元素: {missing_elements}"
            
            self.results.add_test_result(
                f"{page_name}_load", passed, details, screenshot_path, metrics
            )
            
            return passed
            
        except Exception as e:
            print(f"測試頁面 {page_name} 時發生錯誤: {e}")
            screenshot_path = await self.take_screenshot(f"{page_name}_error")
            self.results.add_test_result(
                f"{page_name}_load", False, f"異常: {str(e)}", screenshot_path
            )
            return False
    
    async def test_api_endpoint(self, endpoint_name: str, path: str) -> bool:
        """測試 API 端點"""
        try:
            print(f"正在測試 API: {endpoint_name} ({path})")
            
            # 使用 page.request 來測試 API
            start_time = time.time()
            response = await self.page.request.get(self.base_url + path)
            response_time = time.time() - start_time
            
            status_code = response.status
            
            # 嘗試解析 JSON 響應
            try:
                response_data = await response.json()
                response_text = json.dumps(response_data, indent=2)[:200]
            except:
                response_text = (await response.text())[:200]
            
            # 記錄性能指標
            metrics = {
                "response_time": response_time,
                "status_code": status_code,
                "response_size": len(await response.text())
            }
            
            # 判斷測試結果
            passed = 200 <= status_code < 400
            details = f"響應時間: {response_time:.2f}s, 狀態碼: {status_code}, 響應: {response_text}..."
            
            self.results.add_test_result(
                f"{endpoint_name}_api", passed, details, None, metrics
            )
            
            return passed
            
        except Exception as e:
            print(f"測試 API {endpoint_name} 時發生錯誤: {e}")
            self.results.add_test_result(
                f"{endpoint_name}_api", False, f"異常: {str(e)}"
            )
            return False
    
    async def test_javascript_errors(self) -> bool:
        """檢查 JavaScript 錯誤"""
        try:
            # 訪問主頁來觸發 JavaScript
            await self.page.goto(self.base_url, wait_until="networkidle")
            
            # 等待一段時間讓 JavaScript 執行
            await self.page.wait_for_timeout(3000)
            
            # 檢查控制台錯誤
            js_errors = [error for error in self.results.results["console_errors"] 
                        if error["type"] == "error"]
            
            passed = len(js_errors) == 0
            details = f"JavaScript 錯誤數量: {len(js_errors)}"
            
            if js_errors:
                details += f", 錯誤詳情: {[error['text'] for error in js_errors[:3]]}"
            
            self.results.add_test_result("javascript_errors", passed, details)
            
            return passed
            
        except Exception as e:
            print(f"檢查 JavaScript 錯誤時發生異常: {e}")
            self.results.add_test_result("javascript_errors", False, f"異常: {str(e)}")
            return False
    
    async def test_responsive_design(self) -> bool:
        """測試響應式設計"""
        try:
            # 測試不同的視窗大小
            viewports = [
                {"width": 1920, "height": 1080, "name": "desktop"},
                {"width": 768, "height": 1024, "name": "tablet"},
                {"width": 375, "height": 667, "name": "mobile"}
            ]
            
            passed_count = 0
            total_count = len(viewports)
            
            for viewport in viewports:
                try:
                    await self.page.set_viewport_size({"width": viewport["width"], "height": viewport["height"]})
                    await self.page.goto(self.base_url, wait_until="domcontentloaded")
                    await self.page.wait_for_timeout(2000)
                    
                    # 截圖
                    screenshot_path = await self.take_screenshot(f"responsive_{viewport['name']}")
                    
                    # 檢查是否有橫向滾動條（表示響應式問題）
                    scroll_width = await self.page.evaluate("document.documentElement.scrollWidth")
                    viewport_width = viewport["width"]
                    
                    if scroll_width <= viewport_width + 20:  # 允許20px的容錯
                        passed_count += 1
                        print(f"響應式測試通過: {viewport['name']} ({viewport_width}x{viewport['height']})")
                    else:
                        print(f"響應式測試失敗: {viewport['name']}, 內容寬度: {scroll_width}, 視窗寬度: {viewport_width}")
                        
                except Exception as e:
                    print(f"測試 {viewport['name']} 視窗時發生錯誤: {e}")
            
            passed = passed_count == total_count
            details = f"通過視窗: {passed_count}/{total_count}"
            
            self.results.add_test_result("responsive_design", passed, details)
            
            # 恢復默認視窗大小
            await self.page.set_viewport_size({"width": 1280, "height": 720})
            
            return passed
            
        except Exception as e:
            print(f"測試響應式設計時發生異常: {e}")
            self.results.add_test_result("responsive_design", False, f"異常: {str(e)}")
            return False
    
    async def run_all_tests(self) -> Dict[str, Any]:
        """執行所有測試"""
        print("開始執行完整的整合服務測試...")
        
        all_results = []
        
        try:
            # 1. 測試所有頁面載入
            print("\n=== 測試頁面載入 ===")
            for page_name, config in self.test_pages.items():
                result = await self.test_page_load(page_name, config)
                all_results.append(result)
                await asyncio.sleep(1)  # 避免請求過於頻繁
            
            # 2. 測試所有 API 端點
            print("\n=== 測試 API 端點 ===")
            for endpoint_name, path in self.api_endpoints.items():
                result = await self.test_api_endpoint(endpoint_name, path)
                all_results.append(result)
                await asyncio.sleep(0.5)
            
            # 3. 測試 JavaScript 錯誤
            print("\n=== 測試 JavaScript 錯誤 ===")
            js_result = await self.test_javascript_errors()
            all_results.append(js_result)
            
            # 4. 測試響應式設計
            print("\n=== 測試響應式設計 ===")
            responsive_result = await self.test_responsive_design()
            all_results.append(responsive_result)
            
            # 5. 最終狀態截圖
            await self.page.goto(self.base_url)
            final_screenshot = await self.take_screenshot("final_state")
            
            print(f"\n測試完成！結果摘要:")
            print(self.results.get_summary())
            
            return {
                "success": all(all_results),
                "total_tests": len(all_results),
                "passed_tests": sum(all_results),
                "results": self.results.results
            }
            
        except Exception as e:
            print(f"執行測試時發生異常: {e}")
            return {
                "success": False,
                "error": str(e),
                "results": self.results.results
            }
    
    async def cleanup(self):
        """清理資源"""
        if self.page:
            await self.page.close()
        if self.context:
            await self.context.close()
        if self.browser:
            await self.browser.close()

async def run_integrated_web_tests(headless: bool = True) -> Dict[str, Any]:
    """執行完整的整合 Web 測試"""
    
    # 啟動整合服務
    service = IntegratedServiceProcess()
    
    if not service.start():
        return {
            "success": False,
            "error": "無法啟動整合服務"
        }
    
    try:
        # 等待服務完全啟動
        await asyncio.sleep(10)
        
        # 初始化測試器
        tester = IntegratedWebTester(headless=headless)
        
        if not await tester.initialize():
            return {
                "success": False,
                "error": "無法初始化瀏覽器"
            }
        
        try:
            # 執行所有測試
            results = await tester.run_all_tests()
            
            # 保存結果
            results_file = project_root / "integrated_web_test_results.json"
            tester.results.save_to_file(str(results_file))
            
            print(f"\n測試結果已保存到: {results_file}")
            print(f"截圖已保存到: {tester.screenshot_dir}")
            
            return results
            
        finally:
            await tester.cleanup()
            
    finally:
        # 停止整合服務
        service.stop()

def main():
    """主函數"""
    import argparse
    
    parser = argparse.ArgumentParser(description='整合服務 Playwright 測試')
    parser.add_argument('--headless', action='store_true', default=True,
                       help='以無頭模式運行瀏覽器')
    parser.add_argument('--show-browser', action='store_true',
                       help='顯示瀏覽器窗口（非無頭模式）')
    
    args = parser.parse_args()
    
    headless = args.headless and not args.show_browser
    
    # 運行測試
    results = asyncio.run(run_integrated_web_tests(headless=headless))
    
    if results["success"]:
        print(f"\n🎉 測試成功！通過 {results['passed_tests']}/{results['total_tests']} 個測試")
        sys.exit(0)
    else:
        print(f"\n❌ 測試失敗！通過 {results.get('passed_tests', 0)}/{results.get('total_tests', 0)} 個測試")
        if "error" in results:
            print(f"錯誤: {results['error']}")
        sys.exit(1)

if __name__ == "__main__":
    main()