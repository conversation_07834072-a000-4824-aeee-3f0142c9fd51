#!/usr/bin/env python3
"""
架構遷移回歸測試
確保從 email_inbox_app.py 遷移到模組化 frontend/ 架構後沒有功能被破壞
"""

import os
import sys
import time
import json
import subprocess
import requests
from pathlib import Path
from typing import Dict, List, Any, Optional
from datetime import datetime

# 添加專案根目錄到 Python 路徑
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))


class MigrationRegressionTester:
    """架構遷移回歸測試器"""
    
    def __init__(self):
        self.project_root = project_root
        self.test_results = {}
        
    def test_legacy_file_removal(self) -> Dict[str, Any]:
        """測試舊文件是否已正確移除"""
        print("🔍 檢查舊架構文件是否已移除...")
        
        legacy_files_to_check = [
            'email_inbox_app.py',
            'email_inbox_app.py.backup',
            'email_inbox_app.py.old',
            'app.py',  # 如果存在於根目錄
        ]
        
        results = {}
        properly_removed = []
        still_exists = []
        
        for filename in legacy_files_to_check:
            file_path = self.project_root / filename
            exists = file_path.exists()
            
            results[filename] = {
                'path': str(file_path),
                'exists': exists,
                'size': file_path.stat().st_size if exists else 0
            }
            
            if exists:
                still_exists.append(filename)
                print(f"❌ 舊文件仍存在: {filename}")
            else:
                properly_removed.append(filename)
                print(f"✅ 舊文件已移除: {filename}")
        
        # 檢查是否有任何對舊文件的引用
        reference_check = self._check_legacy_references()
        
        return {
            'success': len(still_exists) == 0 and reference_check['success'],
            'properly_removed': properly_removed,
            'still_exists': still_exists,
            'reference_check': reference_check,
            'details': results
        }
    
    def _check_legacy_references(self) -> Dict[str, Any]:
        """檢查是否還有對舊文件的引用"""
        print("🔍 檢查舊架構引用...")
        
        # 搜索可能包含舊引用的文件
        search_patterns = [
            'email_inbox_app',
            'from email_inbox_app',
            'import email_inbox_app'
        ]
        
        # 要搜索的文件類型和目錄
        search_locations = [
            'start_integrated_services.py',
            'start_integrated_services_backup.py',
            '*.py',
            'frontend/**/*.py',
            'src/**/*.py'
        ]
        
        found_references = []
        
        try:
            for pattern in search_patterns:
                # 使用grep搜索引用
                result = subprocess.run([
                    'grep', '-r', '--include=*.py', pattern, str(self.project_root)
                ], capture_output=True, text=True, cwd=self.project_root)
                
                if result.returncode == 0 and result.stdout.strip():
                    lines = result.stdout.strip().split('\n')
                    for line in lines:
                        if line and not line.startswith('Binary file'):
                            found_references.append({
                                'pattern': pattern,
                                'line': line.strip()
                            })
                            
        except FileNotFoundError:
            # grep not available, do manual check
            print("⚠️ grep 不可用，跳過引用檢查")
            return {'success': True, 'method': 'skipped', 'found_references': []}
        
        success = len(found_references) == 0
        
        if found_references:
            print("❌ 發現舊架構引用:")
            for ref in found_references:
                print(f"   - {ref['line']}")
        else:
            print("✅ 未發現舊架構引用")
        
        return {
            'success': success,
            'method': 'grep_search',
            'found_references': found_references,
            'search_patterns': search_patterns
        }
    
    def test_new_architecture_functionality(self) -> Dict[str, Any]:
        """測試新架構的核心功能"""
        print("🔍 測試新架構核心功能...")
        
        tests = {
            'app_factory': self._test_app_factory(),
            'blueprint_loading': self._test_blueprint_loading(), 
            'module_structure': self._test_module_structure(),
            'config_loading': self._test_config_loading(),
            'error_handling': self._test_error_handling()
        }
        
        successful_tests = sum(1 for test in tests.values() if test['success'])
        total_tests = len(tests)
        
        return {
            'success': successful_tests >= total_tests * 0.8,  # 80%成功率
            'successful_tests': successful_tests,
            'total_tests': total_tests,
            'success_rate': (successful_tests / total_tests * 100) if total_tests > 0 else 0,
            'details': tests
        }
    
    def _test_app_factory(self) -> Dict[str, Any]:
        """測試應用工廠模式"""
        try:
            from frontend.app import create_app
            
            # 測試不同環境的應用創建
            environments = ['development', 'testing', 'production']
            results = {}
            
            for env in environments:
                try:
                    app = create_app(env)
                    results[env] = {
                        'success': True,
                        'blueprints_count': len(app.blueprints),
                        'has_config': hasattr(app, 'config')
                    }
                except Exception as e:
                    results[env] = {
                        'success': False,
                        'error': str(e)
                    }
            
            success = all(r['success'] for r in results.values())
            
            return {
                'success': success,
                'environments_tested': len(environments),
                'results': results
            }
            
        except Exception as e:
            return {
                'success': False,
                'error': str(e)
            }
    
    def _test_blueprint_loading(self) -> Dict[str, Any]:
        """測試藍圖載入"""
        try:
            from frontend.app import create_app
            app = create_app('testing')
            
            expected_blueprints = {
                'shared', 'email', 'analytics', 'eqc', 
                'task', 'monitoring', 'file'
            }
            
            registered_blueprints = set(app.blueprints.keys())
            
            return {
                'success': expected_blueprints.issubset(registered_blueprints),
                'expected_blueprints': list(expected_blueprints),
                'registered_blueprints': list(registered_blueprints),
                'missing_blueprints': list(expected_blueprints - registered_blueprints),
                'extra_blueprints': list(registered_blueprints - expected_blueprints)
            }
            
        except Exception as e:
            return {
                'success': False,
                'error': str(e)
            }
    
    def _test_module_structure(self) -> Dict[str, Any]:
        """測試模組結構"""
        modules_to_check = [
            ('frontend.email.routes.email_routes', 'email_bp'),
            ('frontend.analytics.routes.analytics_routes', 'analytics_bp'),
            ('frontend.eqc.routes.eqc_routes', 'eqc_bp'),
            ('frontend.tasks.routes.task_routes', 'task_bp'),
            ('frontend.monitoring.routes.monitoring_routes', 'monitoring_bp'),
            ('frontend.file_management.routes.file_routes', 'file_bp')
        ]
        
        results = {}
        successful_imports = 0
        
        for module_name, blueprint_name in modules_to_check:
            try:
                module = __import__(module_name, fromlist=[blueprint_name])
                blueprint = getattr(module, blueprint_name)
                
                results[module_name] = {
                    'success': True,
                    'has_blueprint': hasattr(module, blueprint_name),
                    'blueprint_name': blueprint.name if hasattr(blueprint, 'name') else None
                }
                successful_imports += 1
                
            except Exception as e:
                results[module_name] = {
                    'success': False,
                    'error': str(e)
                }
        
        return {
            'success': successful_imports >= len(modules_to_check) * 0.8,
            'successful_imports': successful_imports,
            'total_modules': len(modules_to_check),
            'details': results
        }
    
    def _test_config_loading(self) -> Dict[str, Any]:
        """測試配置載入"""
        try:
            from frontend.config import config, Config
            
            # 檢查配置類是否存在
            config_types = ['development', 'testing', 'production', 'default']
            available_configs = []
            
            for config_type in config_types:
                if config_type in config:
                    available_configs.append(config_type)
            
            # 測試配置創建
            test_config = config.get('testing')
            if test_config:
                config_instance = test_config()
                has_init_app = hasattr(config_instance, 'init_app')
            else:
                has_init_app = False
            
            return {
                'success': len(available_configs) >= 3 and has_init_app,
                'available_configs': available_configs,
                'expected_configs': config_types,
                'has_init_app_method': has_init_app
            }
            
        except Exception as e:
            return {
                'success': False,
                'error': str(e)
            }
    
    def _test_error_handling(self) -> Dict[str, Any]:
        """測試錯誤處理"""
        try:
            from frontend.shared.utils.error_handler import ErrorHandler
            
            # 檢查錯誤處理器是否有必要的方法
            has_register_method = hasattr(ErrorHandler, 'register_error_handlers')
            
            if has_register_method:
                # 測試錯誤處理器註冊
                from frontend.app import create_app
                app = create_app('testing')
                
                # 測試404錯誤處理
                with app.test_client() as client:
                    response = client.get('/nonexistent-path')
                    handles_404 = response.status_code in [404, 302]  # 404或重定向都可接受
                
                return {
                    'success': True,
                    'has_register_method': has_register_method,
                    'handles_404': handles_404
                }
            else:
                return {
                    'success': False,
                    'error': 'ErrorHandler missing register_error_handlers method'
                }
                
        except Exception as e:
            return {
                'success': False,
                'error': str(e)
            }
    
    def test_functional_equivalence(self) -> Dict[str, Any]:
        """測試功能等效性 - 確保遷移後功能完整"""
        print("🔍 測試功能等效性...")
        
        # 啟動測試服務器進行功能測試
        test_server = TestServer()
        
        if not test_server.start():
            return {
                'success': False,
                'error': '無法啟動測試服務器'
            }
        
        try:
            tests = {
                'main_routes': self._test_main_routes(test_server),
                'module_routes': self._test_module_routes(test_server),
                'health_check': self._test_health_check(test_server),
                'static_files': self._test_static_files(test_server)
            }
            
            successful_tests = sum(1 for test in tests.values() if test['success'])
            total_tests = len(tests)
            
            return {
                'success': successful_tests >= total_tests * 0.7,  # 70%成功率
                'successful_tests': successful_tests,
                'total_tests': total_tests,
                'details': tests
            }
            
        finally:
            test_server.stop()
    
    def _test_main_routes(self, test_server) -> Dict[str, Any]:
        """測試主要路由"""
        main_routes = [
            ('/', [302, 308], 'main_redirect'),
            ('/health', [200], 'health_check'),
            ('/favicon.ico', [200, 404], 'favicon')
        ]
        
        results = {}
        success_count = 0
        
        for path, expected_codes, name in main_routes:
            try:
                response = requests.get(f"{test_server.base_url}{path}", timeout=10, allow_redirects=False)
                success = response.status_code in expected_codes
                
                results[name] = {
                    'path': path,
                    'expected_codes': expected_codes,
                    'actual_code': response.status_code,
                    'success': success
                }
                
                if success:
                    success_count += 1
                    
            except Exception as e:
                results[name] = {
                    'path': path,
                    'success': False,
                    'error': str(e)
                }
        
        return {
            'success': success_count >= len(main_routes) * 0.8,
            'success_count': success_count,
            'total_routes': len(main_routes),
            'details': results
        }
    
    def _test_module_routes(self, test_server) -> Dict[str, Any]:
        """測試模組路由"""
        module_routes = [
            ('/email/', 'email_module'),
            ('/analytics/', 'analytics_module'),
            ('/eqc/', 'eqc_module'),
            ('/tasks/', 'tasks_module'),
            ('/monitoring/', 'monitoring_module'),
            ('/files/', 'files_module')
        ]
        
        results = {}
        accessible_count = 0
        
        for path, name in module_routes:
            try:
                response = requests.get(f"{test_server.base_url}{path}", timeout=10, allow_redirects=False)
                # 模組路由可能返回200, 302, 404等，只要不是5xx錯誤即可
                accessible = response.status_code < 500
                
                results[name] = {
                    'path': path,
                    'status_code': response.status_code,
                    'accessible': accessible
                }
                
                if accessible:
                    accessible_count += 1
                    
            except Exception as e:
                results[name] = {
                    'path': path,
                    'accessible': False,
                    'error': str(e)
                }
        
        return {
            'success': accessible_count >= len(module_routes) * 0.7,
            'accessible_count': accessible_count,
            'total_modules': len(module_routes),
            'details': results
        }
    
    def _test_health_check(self, test_server) -> Dict[str, Any]:
        """測試健康檢查端點"""
        try:
            response = requests.get(f"{test_server.base_url}/health", timeout=10)
            
            if response.status_code == 200:
                data = response.json()
                
                required_fields = ['status', 'modules']
                has_required_fields = all(field in data for field in required_fields)
                
                modules = data.get('modules', {})
                has_modules = len(modules) > 0
                
                return {
                    'success': has_required_fields and has_modules,
                    'status_code': response.status_code,
                    'has_required_fields': has_required_fields,
                    'modules_count': len(modules),
                    'response_data': data
                }
            else:
                return {
                    'success': False,
                    'status_code': response.status_code,
                    'error': f'健康檢查返回非200狀態: {response.status_code}'
                }
                
        except Exception as e:
            return {
                'success': False,
                'error': str(e)
            }
    
    def _test_static_files(self, test_server) -> Dict[str, Any]:
        """測試靜態文件服務"""
        static_paths = [
            '/static/shared/css/base.css',
            '/static/email/css/inbox.css',
            '/static/shared/js/main.js'
        ]
        
        results = {}
        working_count = 0
        
        for path in static_paths:
            try:
                response = requests.get(f"{test_server.base_url}{path}", timeout=5)
                # 靜態文件200表示存在，404表示路由正確但文件不存在
                working = response.status_code in [200, 404]
                
                results[path] = {
                    'status_code': response.status_code,
                    'working': working
                }
                
                if working:
                    working_count += 1
                    
            except Exception as e:
                results[path] = {
                    'working': False,
                    'error': str(e)
                }
        
        return {
            'success': working_count >= len(static_paths) * 0.5,
            'working_count': working_count,
            'total_paths': len(static_paths),
            'details': results
        }
    
    def run_comprehensive_regression_test(self) -> Dict[str, Any]:
        """運行綜合回歸測試"""
        start_time = time.time()
        
        print("🧪 開始架構遷移回歸測試")
        print("="*60)
        
        results = {}
        
        # 1. 舊文件清理檢查
        print("\n🗑️ 第一階段: 舊文件清理檢查")
        print("-"*40)
        results['legacy_cleanup'] = self.test_legacy_file_removal()
        
        # 2. 新架構功能測試
        print("\n🏗️ 第二階段: 新架構功能測試")
        print("-"*40)
        results['architecture_functionality'] = self.test_new_architecture_functionality()
        
        # 3. 功能等效性測試
        print("\n⚡ 第三階段: 功能等效性測試")
        print("-"*40)
        results['functional_equivalence'] = self.test_functional_equivalence()
        
        # 計算總體結果
        total_time = time.time() - start_time
        results['summary'] = self._calculate_regression_summary(results, total_time)
        
        self.test_results = results
        return results
    
    def _calculate_regression_summary(self, results: Dict[str, Any], total_time: float) -> Dict[str, Any]:
        """計算回歸測試摘要"""
        test_categories = ['legacy_cleanup', 'architecture_functionality', 'functional_equivalence']
        
        successful_categories = 0
        critical_failures = []
        
        for category in test_categories:
            if results.get(category, {}).get('success', False):
                successful_categories += 1
            else:
                if category in ['legacy_cleanup', 'architecture_functionality']:
                    critical_failures.append(category)
        
        success_rate = (successful_categories / len(test_categories) * 100) if test_categories else 0
        
        # 如果有關鍵失敗，整體判定為失敗
        overall_success = len(critical_failures) == 0 and success_rate >= 70
        
        return {
            'overall_success': overall_success,
            'success_rate': success_rate,
            'successful_categories': successful_categories,
            'total_categories': len(test_categories),
            'critical_failures': critical_failures,
            'execution_time': total_time,
            'timestamp': datetime.now().isoformat()
        }
    
    def generate_regression_report(self) -> str:
        """生成回歸測試報告"""
        if not self.test_results:
            return "尚未執行回歸測試"
        
        summary = self.test_results.get('summary', {})
        
        report = f"""
架構遷移回歸測試報告
{'='*60}

測試概要:
    整體結果: {'✅ 遷移成功' if summary.get('overall_success') else '❌ 遷移有問題'}
    成功率: {summary.get('success_rate', 0):.1f}%
    成功類別: {summary.get('successful_categories', 0)}/{summary.get('total_categories', 0)}
    關鍵失敗: {len(summary.get('critical_failures', []))}
    執行時間: {summary.get('execution_time', 0):.2f}秒

測試結果:
{'='*60}

1. 舊文件清理: {'✅ 完成' if self.test_results.get('legacy_cleanup', {}).get('success') else '❌ 未完成'}
2. 新架構功能: {'✅ 正常' if self.test_results.get('architecture_functionality', {}).get('success') else '❌ 異常'}
3. 功能等效性: {'✅ 等效' if self.test_results.get('functional_equivalence', {}).get('success') else '❌ 有差異'}
"""

        # 添加詳細信息
        if 'legacy_cleanup' in self.test_results:
            cleanup = self.test_results['legacy_cleanup']
            if cleanup.get('still_exists'):
                report += f"\n⚠️ 仍存在的舊文件: {', '.join(cleanup['still_exists'])}"
            
            ref_check = cleanup.get('reference_check', {})
            if ref_check.get('found_references'):
                report += f"\n⚠️ 發現 {len(ref_check['found_references'])} 個舊架構引用"

        if 'architecture_functionality' in self.test_results:
            arch = self.test_results['architecture_functionality']
            report += f"\n新架構功能測試: {arch.get('successful_tests', 0)}/{arch.get('total_tests', 0)} 通過"

        if 'functional_equivalence' in self.test_results:
            func = self.test_results['functional_equivalence']
            report += f"\n功能等效性測試: {func.get('successful_tests', 0)}/{func.get('total_tests', 0)} 通過"

        # 添加關鍵失敗詳情
        critical_failures = summary.get('critical_failures', [])
        if critical_failures:
            report += f"\n\n關鍵失敗詳情:\n{'-'*20}"
            for failure in critical_failures:
                failure_details = self.test_results.get(failure, {})
                if 'error' in failure_details:
                    report += f"\n{failure}: {failure_details['error']}"

        report += "\n" + "="*60
        return report
    
    def save_regression_report(self, filename: str = None):
        """保存回歸測試報告"""
        if filename is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"migration_regression_test_{timestamp}"
        
        # 保存JSON報告
        json_file = self.project_root / f"{filename}.json"
        with open(json_file, 'w', encoding='utf-8') as f:
            json.dump(self.test_results, f, ensure_ascii=False, indent=2)
        
        # 保存文本報告
        txt_file = self.project_root / f"{filename}.txt"
        with open(txt_file, 'w', encoding='utf-8') as f:
            f.write(self.generate_regression_report())
        
        print(f"📄 JSON報告已保存: {json_file}")
        print(f"📄 文本報告已保存: {txt_file}")


class TestServer:
    """測試服務器輔助類"""
    
    def __init__(self, port: int = 5003):
        self.port = port
        self.base_url = f"http://localhost:{port}"
        self.process = None
        
    def start(self, timeout: int = 30) -> bool:
        """啟動測試服務器"""
        try:
            env = os.environ.copy()
            env['FLASK_ENV'] = 'testing'
            env['PYTHONIOENCODING'] = 'utf-8'
            env['PYTHONUTF8'] = '1'
            
            start_script = f'''
import sys
from pathlib import Path
sys.path.insert(0, str(Path.cwd()))

try:
    from frontend.app import create_app
    app = create_app('testing')
    app.run(host='0.0.0.0', port={self.port}, debug=False, use_reloader=False)
except Exception as e:
    print(f"測試服務器啟動失敗: {{e}}")
    sys.exit(1)
'''
            
            self.process = subprocess.Popen(
                [sys.executable, '-c', start_script],
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True,
                env=env
            )
            
            # 等待服務器啟動
            for i in range(timeout):
                try:
                    response = requests.get(f"{self.base_url}/health", timeout=1)
                    if response.status_code == 200:
                        return True
                except requests.exceptions.RequestException:
                    pass
                time.sleep(1)
            
            return False
            
        except Exception as e:
            return False
    
    def stop(self):
        """停止測試服務器"""
        if self.process:
            self.process.terminate()
            self.process.wait()


def main():
    """主函數"""
    print("🧪 架構遷移回歸測試")
    print("確保從 email_inbox_app.py 遷移到模組化架構後功能完整")
    print("="*60)
    
    tester = MigrationRegressionTester()
    
    try:
        # 執行回歸測試
        results = tester.run_comprehensive_regression_test()
        
        # 顯示結果
        print("\n" + "="*60)
        print("回歸測試完成!")
        print("="*60)
        print(tester.generate_regression_report())
        
        # 保存報告
        tester.save_regression_report()
        
        return 0 if results['summary']['overall_success'] else 1
        
    except KeyboardInterrupt:
        print("\n❌ 回歸測試被用戶中斷")
        return 1
    except Exception as e:
        print(f"\n❌ 回歸測試執行失敗: {e}")
        import traceback
        traceback.print_exc()
        return 1


if __name__ == '__main__':
    exit_code = main()
    sys.exit(exit_code)