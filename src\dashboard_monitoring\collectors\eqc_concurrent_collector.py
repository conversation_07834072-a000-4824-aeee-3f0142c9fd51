#!/usr/bin/env python3
"""
EQC 並發監控收集器
整合到統一監控儀表板系統中，專門監控 EQC 多人並發處理狀態
"""

import time
import psutil
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
from dataclasses import dataclass, field

# 移除不存在的 dashboard_monitor 導入
# from src.dashboard_monitoring.utils.dashboard_helpers import dashboard_monitor
# BaseMetrics 不存在，我們直接使用 dataclass
from loguru import logger

@dataclass
class EQCConcurrentMetrics:
    """EQC 並發監控指標"""
    
    # 會話統計
    total_sessions: int = 0
    active_sessions: int = 0
    processing_sessions: int = 0
    completed_sessions: int = 0
    failed_sessions: int = 0
    
    # 用戶統計
    unique_users: int = 0
    concurrent_users: int = 0
    
    # 檔案鎖定統計
    total_file_locks: int = 0
    active_file_locks: int = 0
    
    # 系統負載指標
    cpu_usage_percent: float = 0.0
    memory_usage_percent: float = 0.0
    disk_io_percent: float = 0.0
    
    # 並發性能指標
    avg_response_time_ms: float = 0.0
    max_concurrent_reached: int = 0
    queue_depth: int = 0
    
    # 錯誤統計
    path_conflicts: int = 0
    timeout_sessions: int = 0
    resource_warnings: int = 0
    
    # 預測指標
    predicted_load_next_hour: float = 0.0
    system_capacity_utilization: float = 0.0
    
    timestamp: datetime = field(default_factory=datetime.now)
    
    def is_overloaded(self) -> bool:
        """檢查系統是否過載"""
        return (
            self.concurrent_users > 10 or  # 超過10個並發用戶
            self.cpu_usage_percent > 85 or  # CPU使用率超過85%
            self.memory_usage_percent > 90 or  # 記憶體使用率超過90%
            self.queue_depth > 20  # 隊列深度超過20
        )
    
    def get_load_level(self) -> str:
        """獲取負載等級"""
        if self.is_overloaded():
            return "high"
        elif (self.concurrent_users > 5 or 
              self.cpu_usage_percent > 70 or 
              self.memory_usage_percent > 75):
            return "medium"
        else:
            return "low"
    
    def get_health_warnings(self) -> List[str]:
        """獲取健康警告"""
        warnings = []
        
        if self.path_conflicts > 0:
            warnings.append(f"檢測到 {self.path_conflicts} 個路徑衝突")
        
        if self.timeout_sessions > 0:
            warnings.append(f"{self.timeout_sessions} 個會話超時")
        
        if self.cpu_usage_percent > 85:
            warnings.append(f"CPU 使用率過高: {self.cpu_usage_percent:.1f}%")
        
        if self.memory_usage_percent > 90:
            warnings.append(f"記憶體使用率過高: {self.memory_usage_percent:.1f}%")
        
        if self.queue_depth > 15:
            warnings.append(f"任務隊列過長: {self.queue_depth}")
        
        return warnings


class EQCConcurrentCollector:
    """EQC 並發監控收集器"""
    
    def __init__(self):
        self.logger = logger.bind(component="EQCConcurrentCollector")
        self._last_metrics: Optional[EQCConcurrentMetrics] = None
        self._performance_history: List[Dict] = []
        
    def collect_metrics(self) -> EQCConcurrentMetrics:
        """收集 EQC 並發監控指標"""
        try:
            # 收集會話統計
            session_stats = self._collect_session_stats()
            
            # 收集檔案鎖定統計
            file_lock_stats = self._collect_file_lock_stats()
            
            # 收集系統資源統計
            system_stats = self._collect_system_stats()
            
            # 收集性能統計
            performance_stats = self._collect_performance_stats()
            
            # 構建指標
            metrics = EQCConcurrentMetrics(
                # 會話統計
                **session_stats,
                
                # 檔案鎖定統計
                **file_lock_stats,
                
                # 系統統計
                **system_stats,
                
                # 性能統計
                **performance_stats,
                
                timestamp=datetime.now()
            )
            
            # 更新歷史記錄
            self._update_performance_history(metrics)
            self._last_metrics = metrics
            
            return metrics
            
        except Exception as e:
            self.logger.error(f"收集 EQC 並發指標失敗: {e}")
            return EQCConcurrentMetrics()
    
    def _collect_session_stats(self) -> Dict[str, int]:
        """收集會話統計"""
        try:
            from src.services.eqc_session_manager import get_eqc_session_manager
            session_manager = get_eqc_session_manager()
            
            stats = session_manager.get_session_stats()
            
            # 計算並發用戶數
            sessions = session_manager.get_all_sessions()
            unique_users = len(set(session.user_id for session in sessions if session.user_id))
            concurrent_users = len([s for s in sessions if s.status.value in ['active', 'processing']])
            
            return {
                'total_sessions': stats.get('total_sessions', 0),
                'active_sessions': stats.get('active_sessions', 0),
                'processing_sessions': stats.get('processing_sessions', 0),
                'completed_sessions': stats.get('completed_sessions', 0),
                'failed_sessions': stats.get('failed_sessions', 0),
                'unique_users': unique_users,
                'concurrent_users': concurrent_users
            }
            
        except Exception as e:
            self.logger.warning(f"收集會話統計失敗: {e}")
            return {}
    
    def _collect_file_lock_stats(self) -> Dict[str, int]:
        """收集檔案鎖定統計"""
        try:
            from src.services.file_lock_manager import get_file_lock_manager
            file_lock_manager = get_file_lock_manager()
            
            active_locks = file_lock_manager.get_active_locks()
            
            # 統計路徑衝突
            path_conflicts = 0  # 可以從日誌或錯誤計數器獲取
            
            return {
                'total_file_locks': len(active_locks),
                'active_file_locks': len(active_locks),
                'path_conflicts': path_conflicts
            }
            
        except Exception as e:
            self.logger.warning(f"收集檔案鎖定統計失敗: {e}")
            return {}
    
    def _collect_system_stats(self) -> Dict[str, float]:
        """收集系統資源統計"""
        try:
            # CPU 使用率
            cpu_percent = psutil.cpu_percent(interval=1)
            
            # 記憶體使用率
            memory = psutil.virtual_memory()
            memory_percent = memory.percent
            
            # 磁碟 I/O
            disk_io = psutil.disk_io_counters()
            disk_io_percent = 0.0  # 可以計算 I/O 百分比
            
            return {
                'cpu_usage_percent': cpu_percent,
                'memory_usage_percent': memory_percent,
                'disk_io_percent': disk_io_percent
            }
            
        except Exception as e:
            self.logger.warning(f"收集系統統計失敗: {e}")
            return {}
    
    def _collect_performance_stats(self) -> Dict[str, Any]:
        """收集性能統計"""
        try:
            # 從 Dramatiq 任務管理器獲取隊列深度
            queue_depth = self._get_dramatiq_queue_depth()
            
            # 計算平均響應時間
            avg_response_time = self._calculate_avg_response_time()
            
            # 獲取最大並發數
            max_concurrent = self._get_max_concurrent_reached()
            
            # 預測負載
            predicted_load = self._predict_next_hour_load()
            
            # 計算容量利用率
            capacity_utilization = self._calculate_capacity_utilization()
            
            return {
                'queue_depth': queue_depth,
                'avg_response_time_ms': avg_response_time,
                'max_concurrent_reached': max_concurrent,
                'predicted_load_next_hour': predicted_load,
                'system_capacity_utilization': capacity_utilization
            }
            
        except Exception as e:
            self.logger.warning(f"收集性能統計失敗: {e}")
            return {}
    
    def _get_dramatiq_queue_depth(self) -> int:
        """獲取 Dramatiq 隊列深度"""
        try:
            import redis
            r = redis.Redis(host='localhost', port=6379, db=0)

            # 檢查 Dramatiq 隊列
            total_tasks = 0
            queues = ['eqc_queue', 'search_queue', 'processing_queue', 'health_queue']

            for queue_name in queues:
                queue_key = f"dramatiq:{queue_name}"
                queue_length = r.llen(queue_key)
                total_tasks += queue_length

            return total_tasks

        except Exception as e:
            self.logger.debug(f"獲取 Dramatiq 隊列深度失敗: {e}")
            return 0
    
    def _calculate_avg_response_time(self) -> float:
        """計算平均響應時間"""
        if len(self._performance_history) < 2:
            return 0.0
        
        recent_times = [h.get('response_time', 0) for h in self._performance_history[-10:]]
        return sum(recent_times) / len(recent_times) if recent_times else 0.0
    
    def _get_max_concurrent_reached(self) -> int:
        """獲取達到的最大並發數"""
        if not self._performance_history:
            return 0
        
        return max(h.get('concurrent_users', 0) for h in self._performance_history[-100:])
    
    def _predict_next_hour_load(self) -> float:
        """預測下一小時負載"""
        # 簡單的線性預測，可以使用更複雜的算法
        if len(self._performance_history) < 5:
            return 0.0
        
        recent_loads = [h.get('concurrent_users', 0) for h in self._performance_history[-12:]]  # 最近1小時
        return sum(recent_loads) / len(recent_loads) if recent_loads else 0.0
    
    def _calculate_capacity_utilization(self) -> float:
        """計算系統容量利用率"""
        if not self._last_metrics:
            return 0.0
        
        # 假設系統最大容量為20個並發用戶
        max_capacity = 20
        current_load = self._last_metrics.concurrent_users
        
        return min(100.0, (current_load / max_capacity) * 100)
    
    def _update_performance_history(self, metrics: EQCConcurrentMetrics):
        """更新性能歷史記錄"""
        history_entry = {
            'timestamp': metrics.timestamp,
            'concurrent_users': metrics.concurrent_users,
            'cpu_percent': metrics.cpu_usage_percent,
            'memory_percent': metrics.memory_usage_percent,
            'response_time': metrics.avg_response_time_ms
        }
        
        self._performance_history.append(history_entry)
        
        # 保留最近2小時的數據
        cutoff_time = datetime.now() - timedelta(hours=2)
        self._performance_history = [
            h for h in self._performance_history 
            if h['timestamp'] > cutoff_time
        ]


# 全局實例
_eqc_concurrent_collector = None

def get_eqc_concurrent_collector() -> EQCConcurrentCollector:
    """獲取 EQC 並發監控收集器實例"""
    global _eqc_concurrent_collector
    if _eqc_concurrent_collector is None:
        _eqc_concurrent_collector = EQCConcurrentCollector()
    return _eqc_concurrent_collector
