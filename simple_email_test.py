"""
Simple Email Functionality Test
Test the actual email functionality that was failing with the mo column error
"""

import sys
from pathlib import Path

# Add src directory to Python path
project_root = Path(__file__).parent
src_path = project_root / "src"
sys.path.insert(0, str(src_path))

def test_direct_database_import():
    """Test direct database operations"""
    print("Testing Direct Database Operations:")
    print("-" * 50)
    
    try:
        print("1. Testing database model imports...")
        from src.infrastructure.adapters.database.models import (
            DatabaseEngine, EmailDB, Base
        )
        print("   OK - Database models imported successfully")
        
        print("2. Initializing database engine...")
        db_engine = DatabaseEngine("sqlite:///email_inbox.db")
        db_engine.initialize()
        print("   OK - Database engine initialized")
        
        print("3. Creating database session...")
        session = db_engine.get_session()
        print("   OK - Database session created")
        
        print("4. Testing the original problematic query...")
        try:
            # This is the query that was failing before repair
            emails = session.query(EmailDB).limit(3).all()
            print(f"   OK - Successfully queried emails (found {len(emails)} emails)")
            
            # Test accessing the 'mo' field specifically
            for i, email in enumerate(emails):
                mo_value = email.mo if hasattr(email, 'mo') else 'NOT_FOUND'
                pd_value = email.pd if hasattr(email, 'pd') else 'NOT_FOUND'
                lot_value = email.lot if hasattr(email, 'lot') else 'NOT_FOUND'
                print(f"   Email {i+1}: mo='{mo_value}', pd='{pd_value}', lot='{lot_value}'")
                
        except Exception as e:
            print(f"   ERROR - Email query failed: {e}")
            return False
        
        print("5. Testing email record creation with mo field...")
        try:
            from datetime import datetime
            
            new_email = EmailDB(
                message_id="repair-verification-001",
                sender="<EMAIL>",
                sender_display_name="Repair Verification",
                subject="Schema Repair Test",
                body="Testing mo column functionality",
                received_time=datetime.now(),
                created_at=datetime.now(),
                mo="TEST-MO-12345",  # This was the problematic field
                pd="TEST-PD-12345",
                lot="TEST-LOT-12345"
            )
            
            session.add(new_email)
            session.commit()
            
            test_id = new_email.id
            print(f"   OK - Successfully created email with mo field (ID: {test_id})")
            
            # Clean up
            session.query(EmailDB).filter_by(id=test_id).delete()
            session.commit()
            print("   OK - Test data cleaned up")
            
        except Exception as e:
            print(f"   ERROR - Email creation failed: {e}")
            return False
        
        session.close()
        print("6. Database session closed successfully")
        
        return True
        
    except ImportError as e:
        print(f"   ERROR - Import failed: {e}")
        return False
    except Exception as e:
        print(f"   ERROR - Unexpected error: {e}")
        return False

def test_original_error_scenario():
    """Test the exact scenario that was causing the original error"""
    print("\nTesting Original Error Scenario:")
    print("-" * 50)
    
    try:
        import sqlite3
        
        print("1. Direct SQL test of the problematic query...")
        conn = sqlite3.connect("email_inbox.db")
        cursor = conn.cursor()
        
        # This is the exact query that was failing
        problematic_query = """
        SELECT emails.id AS emails_id, emails.message_id AS emails_message_id, 
               emails.sender AS emails_sender, emails.sender_display_name AS emails_sender_display_name, 
               emails.subject AS emails_subject, emails.body AS emails_body, 
               emails.received_time AS emails_received_time, emails.created_at AS emails_created_at, 
               emails.is_read AS emails_is_read, emails.is_processed AS emails_is_processed, 
               emails.has_attachments AS emails_has_attachments, emails.attachment_count AS emails_attachment_count, 
               emails.pd AS emails_pd, emails.lot AS emails_lot, emails.mo AS emails_mo, 
               emails.yield_value AS emails_yield_value, emails.vendor_code AS emails_vendor_code, 
               emails.parsed_at AS emails_parsed_at, emails.parse_status AS emails_parse_status, 
               emails.parse_error AS emails_parse_error, emails.extraction_method AS emails_extraction_method, 
               emails.llm_analysis_result AS emails_llm_analysis_result, 
               emails.llm_analysis_timestamp AS emails_llm_analysis_timestamp, 
               emails.llm_service_used AS emails_llm_service_used
        FROM emails 
        LIMIT 1
        """
        
        cursor.execute(problematic_query)
        result = cursor.fetchone()
        
        print("   OK - Original problematic query now executes successfully!")
        if result:
            print("   OK - Query returned data")
        else:
            print("   OK - Query executed (no data, but no error)")
        
        conn.close()
        return True
        
    except sqlite3.OperationalError as e:
        if "no such column: emails.mo" in str(e):
            print("   ERROR - The mo column issue still exists!")
            print(f"   Error: {e}")
            return False
        else:
            print(f"   ERROR - Different database error: {e}")
            return False
    except Exception as e:
        print(f"   ERROR - Unexpected error: {e}")
        return False

def main():
    print("=" * 70)
    print("EMAIL FUNCTIONALITY REPAIR VERIFICATION")
    print("=" * 70)
    print("Verifying that the database schema repair fixed the email issues")
    print()
    
    # Run tests
    database_test = test_direct_database_import()
    original_error_test = test_original_error_scenario()
    
    print("\n" + "=" * 70)
    print("VERIFICATION RESULTS")
    print("=" * 70)
    
    if database_test and original_error_test:
        print("SUCCESS - Email functionality repair verification passed!")
        print()
        print("Results:")
        print("- Database schema repair was successful")
        print("- Email queries now work correctly") 
        print("- The 'mo' column error has been resolved")
        print("- Original problematic query now executes without errors")
        print()
        print("The error 'no such column: emails.mo' has been FIXED!")
        
        return True
    else:
        print("FAILURE - Verification failed")
        print()
        print("Issues found:")
        if not database_test:
            print("- Database operations still failing")
        if not original_error_test:
            print("- Original error scenario still failing")
        
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)