# 中文編碼問題修正指南

## 🎯 問題描述
在Windows環境下運行Flask應用程式時出現中文編碼警告，雖不影響功能但會產生警告訊息。

## 🔧 解決方案

### 1. 環境變數設定
在啟動應用程式前設定以下環境變數：

```powershell
# PowerShell
$env:PYTHONIOENCODING = "utf-8"
$env:PYTHONUTF8 = "1"
$env:LANG = "zh_TW.UTF-8"
```

```cmd
# CMD
set PYTHONIOENCODING=utf-8
set PYTHONUTF8=1
set LANG=zh_TW.UTF-8
```

### 2. 更新啟動腳本
修改 `dev_env.ps1` 腳本：

```powershell
# 在腳本開頭添加
$env:PYTHONIOENCODING = "utf-8"
$env:PYTHONUTF8 = "1"
$env:LANG = "zh_TW.UTF-8"

# 然後啟動應用程式
python frontend/app.py
```

### 3. Flask應用程式配置
在 `frontend/app.py` 中添加編碼設定：

```python
import os
import sys

# 設定編碼
if sys.platform.startswith('win'):
    os.environ['PYTHONIOENCODING'] = 'utf-8'
    os.environ['PYTHONUTF8'] = '1'
```

### 4. 系統級設定（可選）
在Windows系統環境變數中永久設定：
1. 開啟「系統內容」→「進階」→「環境變數」
2. 新增系統變數：
   - `PYTHONIOENCODING` = `utf-8`
   - `PYTHONUTF8` = `1`
   - `LANG` = `zh_TW.UTF-8`

## ✅ 驗證修正
修正後應該不再出現中文編碼相關的警告訊息。

## 📝 注意事項
- 這些設定不會影響應用程式功能
- 主要是改善開發體驗，消除警告訊息
- 建議在開發環境腳本中統一設定