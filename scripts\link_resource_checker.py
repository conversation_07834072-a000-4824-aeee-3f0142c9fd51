#!/usr/bin/env python3
"""
連結和資源檢查器
檢查所有內部連結和靜態資源是否正常運作
"""

import os
import sys
import re
import time
import json
from pathlib import Path
from urllib.parse import urljoin, urlparse
from typing import Dict, List, Set, Tuple, Optional
from dataclasses import dataclass, asdict
from collections import defaultdict

import requests
from bs4 import BeautifulSoup
from flask import Flask

# 添加專案根目錄到 Python 路徑
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from frontend.app import create_app


@dataclass
class CheckResult:
    """檢查結果數據類"""
    url: str
    status_code: int
    response_time: float
    error: Optional[str] = None
    content_type: Optional[str] = None


@dataclass
class ResourceCheck:
    """資源檢查結果"""
    resource_type: str  # 'link', 'css', 'js', 'image', 'favicon'
    url: str
    source_page: str
    status_code: int
    error: Optional[str] = None


class LinkResourceChecker:
    """連結和資源檢查器"""
    
    def __init__(self, base_url: str = "http://localhost:5000"):
        self.base_url = base_url.rstrip('/')
        self.session = requests.Session()
        self.session.timeout = 10
        self.app = None
        self.client = None
        
        # 檢查結果
        self.route_results: List[CheckResult] = []
        self.resource_results: List[ResourceCheck] = []
        self.discovered_links: Set[str] = set()
        self.checked_resources: Set[str] = set()
        
        # 統計
        self.stats = {
            'total_routes': 0,
            'successful_routes': 0,
            'failed_routes': 0,
            'total_resources': 0,
            'successful_resources': 0,
            'failed_resources': 0,
            'total_links': 0,
            'successful_links': 0,
            'failed_links': 0
        }
    
    def start_flask_app(self) -> Flask:
        """啟動 Flask 測試應用程式"""
        print("🚀 啟動 Flask 測試應用程式...")
        
        # 設定測試環境
        os.environ['FLASK_ENV'] = 'testing'
        
        app = create_app('testing')
        app.config['TESTING'] = True
        app.config['WTF_CSRF_ENABLED'] = False
        
        return app
    
    def get_all_routes(self, app: Flask) -> List[str]:
        """獲取所有路由"""
        routes = []
        
        with app.app_context():
            for rule in app.url_map.iter_rules():
                if rule.endpoint != 'static':  # 跳過靜態資源路由
                    # 只處理 GET 方法的路由
                    if 'GET' in rule.methods:
                        # 處理帶參數的路由
                        if '<' in rule.rule:
                            # 為帶參數的路由提供測試值
                            test_url = self._get_test_url_for_rule(rule.rule)
                            if test_url:
                                routes.append(test_url)
                        else:
                            routes.append(rule.rule)
        
        return sorted(set(routes))
    
    def _get_test_url_for_rule(self, rule: str) -> Optional[str]:
        """為帶參數的路由生成測試 URL"""
        # 常見的測試參數值
        test_params = {
            'id': '1',
            'email_id': '1',
            'task_id': '1',
            'file_id': '1',
            'report_id': '1',
            'filename': 'test.txt',
            'path': 'test'
        }
        
        # 替換路由參數
        test_url = rule
        for param, value in test_params.items():
            test_url = re.sub(f'<[^>]*{param}[^>]*>', value, test_url)
        
        # 如果還有未替換的參數，跳過這個路由
        if '<' in test_url:
            return None
            
        return test_url
    
    def check_route(self, route: str) -> CheckResult:
        """檢查單個路由"""
        start_time = time.time()
        
        try:
            # 使用 Flask 測試客戶端而不是 HTTP 請求
            response = self.client.get(route, follow_redirects=True)
            response_time = time.time() - start_time
            
            result = CheckResult(
                url=route,
                status_code=response.status_code,
                response_time=response_time,
                content_type=response.headers.get('content-type', '')
            )
            
            if response.status_code != 200:
                result.error = f"HTTP {response.status_code}"
            
            return result
            
        except Exception as e:
            response_time = time.time() - start_time
            return CheckResult(
                url=route,
                status_code=0,
                response_time=response_time,
                error=str(e)
            )
    
    def extract_resources_from_html(self, url: str, html_content: str) -> List[Tuple[str, str]]:
        """從 HTML 中提取資源連結"""
        resources = []
        
        try:
            soup = BeautifulSoup(html_content, 'html.parser')
            
            # CSS 檔案
            for link in soup.find_all('link', rel='stylesheet'):
                href = link.get('href')
                if href:
                    resources.append(('css', href))
            
            # JavaScript 檔案
            for script in soup.find_all('script', src=True):
                src = script.get('src')
                if src:
                    resources.append(('js', src))
            
            # 圖片
            for img in soup.find_all('img', src=True):
                src = img.get('src')
                if src:
                    resources.append(('image', src))
            
            # 內部連結
            for a in soup.find_all('a', href=True):
                href = a.get('href')
                if href and not href.startswith(('http://', 'https://', 'mailto:', 'tel:', '#')):
                    resources.append(('link', href))
                    self.discovered_links.add(href)
            
            # Favicon
            for link in soup.find_all('link', rel=['icon', 'shortcut icon']):
                href = link.get('href')
                if href:
                    resources.append(('favicon', href))
                    
        except Exception as e:
            print(f"⚠️  解析 HTML 失敗 {url}: {e}")
        
        return resources
    
    def check_resource(self, resource_type: str, resource_url: str, source_page: str) -> ResourceCheck:
        """檢查單個資源"""
        # 處理相對 URL
        if resource_url.startswith('/'):
            check_url = resource_url
        elif resource_url.startswith('./'):
            # 處理相對路徑
            check_url = resource_url[2:]  # 移除 './'
        elif not resource_url.startswith(('http://', 'https://')):
            check_url = '/' + resource_url.lstrip('/')
        else:
            # 外部 URL，跳過檢查
            return ResourceCheck(
                resource_type=resource_type,
                url=resource_url,
                source_page=source_page,
                status_code=200,  # 假設外部資源正常
                error=None
            )
        
        try:
            # 使用 Flask 測試客戶端檢查資源
            response = self.client.get(check_url)
            
            return ResourceCheck(
                resource_type=resource_type,
                url=check_url,
                source_page=source_page,
                status_code=response.status_code,
                error=None if response.status_code == 200 else f"HTTP {response.status_code}"
            )
            
        except Exception as e:
            return ResourceCheck(
                resource_type=resource_type,
                url=check_url,
                source_page=source_page,
                status_code=0,
                error=str(e)
            )
    
    def run_comprehensive_check(self) -> Dict:
        """執行全面檢查"""
        print("🔍 開始全面連結和資源檢查...")
        
        # 啟動 Flask 應用程式
        self.app = self.start_flask_app()
        self.client = self.app.test_client()
        
        # 獲取所有路由
        routes = self.get_all_routes(self.app)
        print(f"📋 發現 {len(routes)} 個路由")
        
        # 檢查每個路由
        print("\n🔗 檢查路由...")
        for i, route in enumerate(routes, 1):
            print(f"  [{i}/{len(routes)}] 檢查路由: {route}")
            result = self.check_route(route)
            self.route_results.append(result)
            
            # 更新統計
            self.stats['total_routes'] += 1
            if result.status_code == 200:
                self.stats['successful_routes'] += 1
            else:
                self.stats['failed_routes'] += 1
                print(f"    ❌ 失敗: {result.error or f'HTTP {result.status_code}'}")
            
            # 如果是 HTML 頁面，提取資源
            if (result.status_code == 200 and 
                result.content_type and 
                'text/html' in result.content_type):
                
                try:
                    # 重新獲取頁面內容以提取資源
                    response = self.client.get(route)
                    html_content = response.get_data(as_text=True)
                    resources = self.extract_resources_from_html(result.url, html_content)
                    
                    # 檢查每個資源
                    for resource_type, resource_url in resources:
                        resource_key = f"{resource_type}:{resource_url}"
                        if resource_key not in self.checked_resources:
                            self.checked_resources.add(resource_key)
                            
                            resource_result = self.check_resource(resource_type, resource_url, result.url)
                            self.resource_results.append(resource_result)
                            
                            # 更新統計
                            if resource_type == 'link':
                                self.stats['total_links'] += 1
                                if resource_result.status_code == 200:
                                    self.stats['successful_links'] += 1
                                else:
                                    self.stats['failed_links'] += 1
                            else:
                                self.stats['total_resources'] += 1
                                if resource_result.status_code == 200:
                                    self.stats['successful_resources'] += 1
                                else:
                                    self.stats['failed_resources'] += 1
                
                except Exception as e:
                    print(f"    ⚠️  提取資源失敗: {e}")
        
        # 檢查發現的內部連結
        print(f"\n🔗 檢查發現的內部連結 ({len(self.discovered_links)} 個)...")
        for link in self.discovered_links:
            if not any(r.url.endswith(link) for r in self.route_results):
                result = self.check_route(link)
                self.route_results.append(result)
                
                self.stats['total_links'] += 1
                if result.status_code == 200:
                    self.stats['successful_links'] += 1
                else:
                    self.stats['failed_links'] += 1
        
        return self.generate_report()
    
    def generate_report(self) -> Dict:
        """生成檢查報告"""
        # 分類失敗的項目
        failed_routes = [r for r in self.route_results if r.status_code != 200]
        failed_resources = [r for r in self.resource_results if r.status_code != 200]
        
        # 按類型分組失敗的資源
        failed_by_type = defaultdict(list)
        for resource in failed_resources:
            failed_by_type[resource.resource_type].append(resource)
        
        report = {
            'timestamp': time.strftime('%Y-%m-%d %H:%M:%S'),
            'base_url': self.base_url,
            'statistics': self.stats,
            'failed_routes': [asdict(r) for r in failed_routes],
            'failed_resources_by_type': {
                resource_type: [asdict(r) for r in resources]
                for resource_type, resources in failed_by_type.items()
            },
            'summary': {
                'total_checks': (self.stats['total_routes'] + 
                               self.stats['total_resources'] + 
                               self.stats['total_links']),
                'total_failures': (self.stats['failed_routes'] + 
                                 self.stats['failed_resources'] + 
                                 self.stats['failed_links']),
                'success_rate': 0
            }
        }
        
        # 計算成功率
        total_checks = report['summary']['total_checks']
        if total_checks > 0:
            success_rate = ((total_checks - report['summary']['total_failures']) / total_checks) * 100
            report['summary']['success_rate'] = round(success_rate, 2)
        
        return report
    
    def print_report(self, report: Dict):
        """打印檢查報告"""
        print("\n" + "="*80)
        print("📊 連結和資源檢查報告")
        print("="*80)
        
        # 統計摘要
        stats = report['statistics']
        print(f"\n📈 統計摘要:")
        print(f"  路由檢查: {stats['successful_routes']}/{stats['total_routes']} 成功")
        print(f"  資源檢查: {stats['successful_resources']}/{stats['total_resources']} 成功")
        print(f"  連結檢查: {stats['successful_links']}/{stats['total_links']} 成功")
        print(f"  總成功率: {report['summary']['success_rate']}%")
        
        # 失敗的路由
        if report['failed_routes']:
            print(f"\n❌ 失敗的路由 ({len(report['failed_routes'])} 個):")
            for route in report['failed_routes']:
                error_msg = route['error'] or f"HTTP {route['status_code']}"
                print(f"  • {route['url']} - {error_msg}")
        
        # 失敗的資源
        for resource_type, resources in report['failed_resources_by_type'].items():
            if resources:
                print(f"\n❌ 失敗的{resource_type}資源 ({len(resources)} 個):")
                for resource in resources:
                    print(f"  • {resource['url']}")
                    print(f"    來源: {resource['source_page']}")
                    error_msg = resource['error'] or f"HTTP {resource['status_code']}"
                    print(f"    錯誤: {error_msg}")
        
        # 總結
        if report['summary']['total_failures'] == 0:
            print(f"\n✅ 所有檢查都通過！沒有發現 404 錯誤或遺失的資源。")
        else:
            print(f"\n⚠️  發現 {report['summary']['total_failures']} 個問題需要修復。")


def main():
    """主函數"""
    print("🔍 Flask 前端連結和資源檢查器")
    print("="*50)
    
    # 檢查 Flask 應用程式是否可以啟動
    try:
        checker = LinkResourceChecker()
        report = checker.run_comprehensive_check()
        
        # 打印報告
        checker.print_report(report)
        
        # 保存報告到檔案
        report_file = 'link_resource_check_report.json'
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(report, f, ensure_ascii=False, indent=2)
        
        print(f"\n📄 詳細報告已保存到: {report_file}")
        
        # 返回適當的退出碼
        if report['summary']['total_failures'] > 0:
            sys.exit(1)
        else:
            sys.exit(0)
            
    except Exception as e:
        print(f"❌ 檢查過程中發生錯誤: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)


if __name__ == '__main__':
    main()