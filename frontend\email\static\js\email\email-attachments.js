/**
 * 郵件附件相關功能
 * 提供附件下載、進度顯示等功能
 */

/**
 * 附件管理類
 */
class EmailAttachments {
    constructor() {
        this.addProgressStyles();
    }
    
    /**
     * 下載附件
     */
    async downloadAttachment(attachmentId, filename) {
        try {
            // 顯示下載進度
            this.showDownloadProgress(filename);
            
            // 發送下載請求
            // TODO: 需要實現附件下載 API 路由
            console.log('附件下載功能暫時停用 - 需要實現相關 API 路由');
            throw new Error('附件下載功能暫時停用');
            /*
            const response = await fetch(`/email/api/attachments/${attachmentId}/download`);
            */
            
            if (!response.ok) {
                const errorData = await response.json();
                throw new Error(errorData.error || '下載失敗');
            }
            
            // 檢查是否為 JSON 錯誤回應
            const contentType = response.headers.get('content-type');
            if (contentType && contentType.includes('application/json')) {
                const errorData = await response.json();
                throw new Error(errorData.error || '下載失敗');
            }
            
            // 獲取檔案資料
            const blob = await response.blob();
            
            // 建立下載連結
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = filename;
            
            // 觸發下載
            document.body.appendChild(a);
            a.click();
            
            // 清理
            document.body.removeChild(a);
            window.URL.revokeObjectURL(url);
            
            // 隱藏下載進度
            this.hideDownloadProgress();
            
            // 顯示成功訊息
            EmailUIUtils.showSuccessMessage(`附件 ${filename} 下載成功`);
            
        } catch (error) {
            console.error('下載附件失敗:', error);
            this.hideDownloadProgress();
            EmailUIUtils.showErrorMessage(`下載失敗: ${error.message}`);
        }
    }
    
    /**
     * 顯示下載進度
     */
    showDownloadProgress(filename) {
        // 移除現有的進度提示
        const existingProgress = document.querySelector('.download-progress');
        if (existingProgress) {
            existingProgress.remove();
        }
        
        // 建立進度提示
        const progressDiv = document.createElement('div');
        progressDiv.className = 'download-progress';
        progressDiv.innerHTML = `
            <div class="progress-content">
                <div class="progress-icon">⬇️</div>
                <div class="progress-text">正在下載 ${EmailUIUtils.escapeHtml(filename)}...</div>
                <div class="progress-bar">
                    <div class="progress-fill"></div>
                </div>
            </div>
        `;
        
        // 添加樣式
        progressDiv.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: #2196F3;
            color: white;
            padding: 15px 20px;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.3);
            z-index: 1000;
            min-width: 300px;
            animation: slideIn 0.3s ease-out;
        `;
        
        document.body.appendChild(progressDiv);
    }
    
    /**
     * 隱藏下載進度
     */
    hideDownloadProgress() {
        const progressDiv = document.querySelector('.download-progress');
        if (progressDiv) {
            progressDiv.style.animation = 'slideOut 0.3s ease-in';
            setTimeout(() => {
                progressDiv.remove();
            }, 300);
        }
    }
    
    /**
     * 渲染附件列表
     */
    renderAttachmentsList(attachments, containerId) {
        const container = document.getElementById(containerId);
        if (!container || !attachments || attachments.length === 0) {
            if (container) {
                container.innerHTML = '<p>無附件</p>';
            }
            return;
        }
        
        const attachmentsList = attachments.map(attachment => 
            this.createAttachmentItem(attachment)
        ).join('');
        
        container.innerHTML = `
            <div class="attachments-header">
                <h4>📎 附件 (${attachments.length})</h4>
            </div>
            <div class="attachments-list">
                ${attachmentsList}
            </div>
        `;
        
        // 綁定下載事件
        this.bindAttachmentEvents(container);
    }
    
    /**
     * 創建附件項目 HTML
     */
    createAttachmentItem(attachment) {
        const fileSize = EmailUIUtils.formatFileSize(attachment.size_bytes || 0);
        const iconClass = this.getFileIcon(attachment.filename);
        
        return `
            <div class="attachment-item" data-attachment-id="${attachment.id}">
                <div class="attachment-icon">
                    <span class="${iconClass}">${this.getFileEmoji(attachment.filename)}</span>
                </div>
                <div class="attachment-info">
                    <div class="attachment-name" title="${EmailUIUtils.escapeHtml(attachment.filename)}">
                        ${EmailUIUtils.escapeHtml(EmailUIUtils.truncateText(attachment.filename, 30))}
                    </div>
                    <div class="attachment-meta">
                        <span class="attachment-size">${fileSize}</span>
                        <span class="attachment-type">${EmailUIUtils.escapeHtml(attachment.content_type || '')}</span>
                    </div>
                </div>
                <div class="attachment-actions">
                    <button class="btn-download-attachment" 
                            data-id="${attachment.id}" 
                            data-filename="${EmailUIUtils.escapeHtml(attachment.filename)}"
                            title="下載附件">
                        <span class="btn-icon">📥</span>
                        <span class="btn-text">下載</span>
                    </button>
                </div>
            </div>
        `;
    }
    
    /**
     * 綁定附件事件
     */
    bindAttachmentEvents(container) {
        const downloadButtons = container.querySelectorAll('.btn-download-attachment');
        downloadButtons.forEach(button => {
            button.addEventListener('click', (e) => {
                e.preventDefault();
                const attachmentId = parseInt(button.dataset.id);
                const filename = button.dataset.filename;
                this.downloadAttachment(attachmentId, filename);
            });
        });
    }
    
    /**
     * 獲取檔案圖示類別
     */
    getFileIcon(filename) {
        const ext = this.getFileExtension(filename).toLowerCase();
        const iconMap = {
            'pdf': 'file-pdf',
            'doc': 'file-word', 'docx': 'file-word',
            'xls': 'file-excel', 'xlsx': 'file-excel',
            'ppt': 'file-powerpoint', 'pptx': 'file-powerpoint',
            'zip': 'file-archive', 'rar': 'file-archive', '7z': 'file-archive',
            'jpg': 'file-image', 'jpeg': 'file-image', 'png': 'file-image', 'gif': 'file-image',
            'txt': 'file-text', 'csv': 'file-text',
            'mp4': 'file-video', 'avi': 'file-video', 'mov': 'file-video',
            'mp3': 'file-audio', 'wav': 'file-audio',
        };
        return iconMap[ext] || 'file-generic';
    }
    
    /**
     * 獲取檔案 Emoji
     */
    getFileEmoji(filename) {
        const ext = this.getFileExtension(filename).toLowerCase();
        const emojiMap = {
            'pdf': '📄',
            'doc': '📝', 'docx': '📝',
            'xls': '📊', 'xlsx': '📊',
            'ppt': '📈', 'pptx': '📈',
            'zip': '🗜️', 'rar': '🗜️', '7z': '🗜️',
            'jpg': '🖼️', 'jpeg': '🖼️', 'png': '🖼️', 'gif': '🖼️',
            'txt': '📄', 'csv': '📄',
            'mp4': '🎬', 'avi': '🎬', 'mov': '🎬',
            'mp3': '🎵', 'wav': '🎵',
        };
        return emojiMap[ext] || '📁';
    }
    
    /**
     * 獲取檔案副檔名
     */
    getFileExtension(filename) {
        if (!filename || typeof filename !== 'string') return '';
        const lastDot = filename.lastIndexOf('.');
        return lastDot !== -1 ? filename.slice(lastDot + 1) : '';
    }
    
    /**
     * 添加進度條樣式
     */
    addProgressStyles() {
        if (document.querySelector('#download-progress-styles')) return;
        
        const style = document.createElement('style');
        style.id = 'download-progress-styles';
        style.textContent = `
            @keyframes slideIn {
                from { transform: translateX(100%); opacity: 0; }
                to { transform: translateX(0); opacity: 1; }
            }
            @keyframes slideOut {
                from { transform: translateX(0); opacity: 1; }
                to { transform: translateX(100%); opacity: 0; }
            }
            .progress-content {
                display: flex;
                align-items: center;
                gap: 10px;
            }
            .progress-bar {
                flex: 1;
                height: 4px;
                background: rgba(255,255,255,0.3);
                border-radius: 2px;
                overflow: hidden;
            }
            .progress-fill {
                height: 100%;
                background: white;
                border-radius: 2px;
                animation: progressPulse 1.5s infinite;
            }
            @keyframes progressPulse {
                0% { transform: translateX(-100%); }
                100% { transform: translateX(100%); }
            }
            .attachment-item {
                display: flex;
                align-items: center;
                gap: 12px;
                padding: 12px;
                border: 1px solid #e1e5e9;
                border-radius: 6px;
                margin-bottom: 8px;
                background: #f8f9fa;
                transition: background-color 0.2s;
            }
            .attachment-item:hover {
                background: #e9ecef;
            }
            .attachment-icon {
                font-size: 24px;
                display: flex;
                align-items: center;
                justify-content: center;
                width: 40px;
                height: 40px;
                background: white;
                border-radius: 4px;
                border: 1px solid #dee2e6;
            }
            .attachment-info {
                flex: 1;
                min-width: 0;
            }
            .attachment-name {
                font-weight: 500;
                color: #495057;
                margin-bottom: 4px;
                word-break: break-all;
            }
            .attachment-meta {
                display: flex;
                gap: 8px;
                font-size: 12px;
                color: #6c757d;
            }
            .attachment-actions {
                display: flex;
                gap: 8px;
            }
            .btn-download-attachment {
                display: flex;
                align-items: center;
                gap: 4px;
                padding: 6px 12px;
                background: #007bff;
                color: white;
                border: none;
                border-radius: 4px;
                cursor: pointer;
                font-size: 14px;
                transition: background-color 0.2s;
            }
            .btn-download-attachment:hover {
                background: #0056b3;
            }
            .btn-download-attachment .btn-icon {
                font-size: 14px;
            }
            .attachments-header h4 {
                margin: 0 0 12px 0;
                color: #495057;
                font-size: 16px;
            }
        `;
        document.head.appendChild(style);
    }
    
    /**
     * 批量下載附件
     */
    async batchDownloadAttachments(attachments) {
        if (!attachments || attachments.length === 0) {
            EmailUIUtils.showErrorMessage('沒有可下載的附件');
            return;
        }
        
        EmailUIUtils.showSuccessMessage(`開始下載 ${attachments.length} 個附件...`);
        
        for (let i = 0; i < attachments.length; i++) {
            const attachment = attachments[i];
            try {
                await this.downloadAttachment(attachment.id, attachment.filename);
                // 添加延遲避免瀏覽器阻止多重下載
                if (i < attachments.length - 1) {
                    await new Promise(resolve => setTimeout(resolve, 1000));
                }
            } catch (error) {
                console.error(`下載附件 ${attachment.filename} 失敗:`, error);
            }
        }
    }
}

// 全域可用
window.EmailAttachments = EmailAttachments;

// 創建全域實例
window.emailAttachments = new EmailAttachments();