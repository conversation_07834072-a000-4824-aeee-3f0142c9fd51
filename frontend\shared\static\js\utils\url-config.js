/**
 * URL 配置模組 - 自動檢測作業系統並配置適當的 URL
 * 支援 Windows 和 Linux 環境的自動切換
 */

class UrlConfig {
    static config = null;
    
    /**
     * 檢測作業系統類型
     * @returns {string} 'windows' | 'linux' | 'unknown'
     */
    static detectOS() {
        const userAgent = navigator.userAgent || navigator.platform || '';
        
        if (userAgent.indexOf('Win') !== -1) {
            return 'windows';
        } else if (userAgent.indexOf('Linux') !== -1 || userAgent.indexOf('X11') !== -1) {
            return 'linux';
        } else if (userAgent.indexOf('Mac') !== -1) {
            return 'macos';  // 支援 macOS
        }
        
        return 'unknown';
    }
    
    /**
     * 檢測當前是否為本地開發環境
     * @returns {boolean}
     */
    static isLocalDevelopment() {
        const hostname = window.location.hostname;
        return hostname === 'localhost' || 
               hostname === '127.0.0.1' || 
               hostname === '0.0.0.0' ||
               hostname.startsWith('192.168.') ||
               hostname.startsWith('10.') ||
               hostname.startsWith('172.');
    }
    
    /**
     * 獲取適當的主機名
     * @returns {string}
     */
    static getHostname() {
        const os = this.detectOS();
        const currentHost = window.location.hostname;
        
        // 如果已經在正確的主機上，直接使用當前主機
        if (currentHost && currentHost !== 'about:blank') {
            return currentHost;
        }
        
        // 根據作業系統選擇預設主機
        switch (os) {
            case 'windows':
                return '127.0.0.1';  // Windows 偏好 127.0.0.1
            case 'linux':
            case 'macos':
                return 'localhost';   // Linux/macOS 偏好 localhost
            default:
                return 'localhost';   // 預設使用 localhost
        }
    }
    
    /**
     * 獲取 API 服務的基礎 URL（整合服務）
     * @returns {string}
     */
    static getApiBaseUrl() {
        const hostname = this.getHostname();
        const port = 8010;  // FastAPI 服務端口
        const protocol = window.location.protocol === 'https:' ? 'https' : 'http';
        
        return `${protocol}://${hostname}:${port}/api`;
    }
    
    /**
     * 獲取 FT QC Summary UI 的 URL
     * @returns {string}
     */
    static getFtSummaryUrl() {
        const hostname = this.getHostname();
        const port = 5000;  // Flask 前端服務端口
        const protocol = window.location.protocol === 'https:' ? 'https' : 'http';

        return `${protocol}://${hostname}:${port}/analytics/dashboard`;
    }
    
    /**
     * 獲取 Flask 服務的基礎 URL (郵件收件匣)
     * @returns {string}
     */
    static getFlaskBaseUrl() {
        const hostname = this.getHostname();
        const port = 5000;  // Flask 實際端口
        const protocol = window.location.protocol === 'https:' ? 'https' : 'http';
        
        return `${protocol}://${hostname}:${port}`;
    }
    
    /**
     * 獲取完整的 URL 配置
     * @returns {Object}
     */
    static getConfig() {
        if (!this.config) {
            const os = this.detectOS();
            const hostname = this.getHostname();
            const isLocal = this.isLocalDevelopment();
            
            this.config = {
                os: os,
                hostname: hostname,
                isLocalDevelopment: isLocal,
                api: {
                    base: this.getApiBaseUrl(),
                    fastapi: this.getApiBaseUrl(),
                    flask: this.getFlaskBaseUrl()
                },
                ui: {
                    ftSummary: this.getFtSummaryUrl(),
                    emailInbox: this.getFlaskBaseUrl(),
                    databaseManager: `${this.getFlaskBaseUrl()}/database-manager`
                },
                ports: {
                    integrated: 8010,  // 實際 FastAPI 服務端口
                    legacy_fastapi: 8010,
                    legacy_flask: 5000
                }
            };
            
            console.log('🔧 URL 配置初始化:', this.config);
        }
        
        return this.config;
    }
    
    /**
     * 創建下載 URL
     * @param {string} filePath - 檔案路徑
     * @returns {string}
     */
    static createDownloadUrl(filePath) {
        const apiBase = this.getApiBaseUrl();
        return `${apiBase}/download_file?file_path=${encodeURIComponent(filePath)}`;
    }
    
    /**
     * 測試服務連接性
     * @returns {Promise<Object>}
     */
    static async testConnectivity() {
        const config = this.getConfig();
        const results = {};
        
        // 測試 FastAPI 服務
        try {
            const response = await fetch(`${config.api.fastapi}/health`);
            results.fastapi = {
                available: response.ok,
                status: response.status,
                url: config.api.fastapi
            };
        } catch (error) {
            results.fastapi = {
                available: false,
                error: error.message,
                url: config.api.fastapi
            };
        }
        
        // 測試 Flask 服務
        try {
            const response = await fetch(`${config.api.flask}/`);
            results.flask = {
                available: response.ok,
                status: response.status,
                url: config.api.flask
            };
        } catch (error) {
            results.flask = {
                available: false,
                error: error.message,
                url: config.api.flask
            };
        }
        
        return results;
    }
    
    /**
     * 重置配置（強制重新檢測）
     */
    static resetConfig() {
        this.config = null;
    }
    
    /**
     * 獲取調試信息
     * @returns {Object}
     */
    static getDebugInfo() {
        return {
            userAgent: navigator.userAgent,
            platform: navigator.platform,
            detectedOS: this.detectOS(),
            currentLocation: {
                hostname: window.location.hostname,
                port: window.location.port,
                protocol: window.location.protocol,
                href: window.location.href
            },
            config: this.getConfig()
        };
    }
}

// 導出配置類
if (typeof module !== 'undefined' && module.exports) {
    module.exports = UrlConfig;
} else if (typeof window !== 'undefined') {
    window.UrlConfig = UrlConfig;
}

// 自動初始化（僅在瀏覽器環境中）
if (typeof window !== 'undefined') {
    // 頁面載入完成後自動檢測
    document.addEventListener('DOMContentLoaded', () => {
        const config = UrlConfig.getConfig();
        console.log(`🖥️ 檢測到作業系統: ${config.os}`);
        console.log(`🌐 使用主機: ${config.hostname}`);
        console.log(`📡 API 基礎 URL: ${config.api.base}`);
    });
}