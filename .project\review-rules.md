# Code Review Rules

## Custom Review Criteria

<!-- Add your project-specific review rules here -->

### Architecture Rules
<!-- e.g., "All API calls must go through the service layer" -->

### Code Style Rules
<!-- e.g., "Use descriptive variable names, avoid abbreviations" -->

### Security Rules
<!-- e.g., "Never log sensitive data" -->

### Performance Rules
<!-- e.g., "Database queries must be optimized for our expected load" -->

### Business Logic Rules
<!-- e.g., "All financial calculations must use decimal types" -->

### Testing Rules
<!-- e.g., "Critical paths must have integration tests" -->

---
*This file is used by handoff-ai for context-aware code review*