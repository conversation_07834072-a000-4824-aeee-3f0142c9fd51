<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>FT-EQC 分組配對分析系統</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    
    <!-- 載入模組化 CSS -->
    <link rel="stylesheet" href="/static/css/variables.css">
    <link rel="stylesheet" href="/static/css/base.css">
    <link rel="stylesheet" href="/static/css/components.css">
    <link rel="stylesheet" href="/static/css/layout.css">
    <link rel="stylesheet" href="/static/css/special-components.css">
    <link rel="stylesheet" href="/static/css/responsive.css">
</head>
<body>
    <div class="container">
        <div class="header">
            <h1><i class="fas fa-project-diagram" aria-hidden="true"></i> Online EQC 分析程式</h1>
        </div>

        <div class="main-content">
            <!-- 橫向布局控制區域 -->
            <div class="controls-container">
                <!-- 主要資料夾輸入區域 (左側大區域) -->
                <div class="folder-input-card">
                    <div class="folder-icon">
                        <i class="fas fa-folder-open" aria-hidden="true"></i>
                    </div>
                    <h3 style="margin: 0 0 15px 0; color: var(--secondary-color);">📁 資料夾輸入</h3>
                    
                    <input type="text" class="folder-input" id="folderPath" 
                           placeholder="D:\project\python\outlook_summary\doc\20250523" 
                           value="D:\project\python\outlook_summary\doc\20250523">
                    
                    <button class="primary-btn" aria-label="執行一鍵完成程式碼對比處理">
                        <i class="fas fa-magic" aria-hidden="true"></i> 一鍵完成程式碼對比
                    </button>
                    
                    <div style="margin-top: 12px; font-size: 12px; color: #666; line-height: 1.4;">
                        自動生成 EQCTOTALDATA → 程式碼區間檢測 → 雙重搜尋 → 完整報告
                    </div>
                </div>

                <!-- 檔案上傳 (右側第1個) -->
                <div class="side-card">
                    <h4><i class="fas fa-upload" aria-hidden="true"></i> 檔案上傳</h4>
                    <div class="upload-zone" id="uploadZone" style="min-height: 80px; display: flex; flex-direction: column; justify-content: center;">
                        <i class="fas fa-cloud-upload-alt" aria-hidden="true" style="font-size: 1.5em; color: var(--primary-color); margin-bottom: 8px;"></i>
                        <div style="font-size: 12px; text-align: center;">
                            拖放檔案或點擊上傳<br>
                            <small style="color: #666;">支援 ZIP, 7Z, RAR<br>(最大 <span id="maxSizeDisplay">1000</span>MB)</small>
                        </div>
                        <input type="file" id="fileInput" accept=".zip,.7z,.rar,.tar,.gz" style="display: none;">
                    </div>
                    <div id="uploadStatus" style="margin-top: 8px; font-size: 11px; display: none;"></div>
                </div>

                <!-- CODE區間設定 (右側第2個) -->
                <div class="side-card">
                    <h4><i class="fas fa-code" aria-hidden="true"></i> CODE 區間設定</h4>
                    <div style="margin-bottom: 10px; padding: 8px; background: #e7f3ff; border: 1px solid #b3d9ff; border-radius: 4px; font-size: 10px; color: #0066cc;">
                        <i class="fas fa-info-circle" aria-hidden="true"></i> 智能設定: 自動檢測最佳區間
                    </div>
                    <div class="region-group" style="margin-bottom: 10px;">
                        <label style="font-size: 11px; margin-bottom: 4px; display: block;">主要 CODE 區間:</label>
                        <div class="input-row" style="display: flex; align-items: center; gap: 4px;">
                            <input type="number" id="mainStart" placeholder="起始" style="width: 50px; padding: 4px; border: 1px solid #ddd; border-radius: 3px; font-size: 11px;" aria-label="主要CODE區間起始欄位">
                            <span style="font-size: 11px;">-</span>
                            <input type="number" id="mainEnd" placeholder="結束" style="width: 50px; padding: 4px; border: 1px solid #ddd; border-radius: 3px; font-size: 11px;" aria-label="主要CODE區間結束欄位">
                        </div>
                        <span class="field-count" id="mainCount" style="font-size: 10px; color: #666;">(未設定)</span>
                    </div>
                    <div class="region-group">
                        <label style="font-size: 11px; margin-bottom: 4px; display: block;">備用 CODE 區間:</label>
                        <div class="input-row" style="display: flex; align-items: center; gap: 4px;">
                            <input type="number" id="backupStart" placeholder="起始" style="width: 50px; padding: 4px; border: 1px solid #ddd; border-radius: 3px; font-size: 11px;" aria-label="備用CODE區間起始欄位">
                            <span style="font-size: 11px;">-</span>
                            <input type="number" id="backupEnd" placeholder="結束" style="width: 50px; padding: 4px; border: 1px solid #ddd; border-radius: 3px; font-size: 11px;" aria-label="備用CODE區間結束欄位">
                        </div>
                        <span class="field-count" id="backupCount" style="font-size: 10px; color: #666;">(未設定)</span>
                    </div>
                </div>

                <!-- 處理進度 (右側第3個) -->
                <div class="side-card">
                    <h4><i class="fas fa-tasks" aria-hidden="true"></i> 處理進度</h4>
                    <div id="progressDisplay" style="padding: 10px; background: #f8f9ff; border-radius: 4px; border: 1px solid #e9ecef;">
                        <div class="progress-step" id="progressStep" style="font-size: 11px; font-weight: 600; color: var(--secondary-color); margin-bottom: 4px; display: flex; align-items: center; gap: 4px;">
                            <i class="fas fa-clock" aria-hidden="true"></i> 等待開始...
                        </div>
                        <div class="progress-detail" id="progressDetail" style="font-size: 10px; color: var(--text-muted); line-height: 1.3;">
                            點擊「一鍵完成」開始處理
                        </div>
                    </div>
                    <div class="progress-bar-container" id="progressBarContainer" style="display: none; margin-top: 8px;">
                        <div style="height: 6px; background: var(--border-color); border-radius: 3px; overflow: hidden; margin-bottom: 4px;">
                            <div id="progressFill" style="height: 100%; background: linear-gradient(90deg, var(--primary-color) 0%, #764ba2 100%); width: 0%; transition: var(--transition);"></div>
                        </div>
                        <div class="progress-text" id="progressText" style="text-align: center; font-size: 10px; color: var(--text-muted); font-weight: 500;">0%</div>
                    </div>
                </div>
            </div>

            <!-- 今日處理記錄 (獨立行) -->
            <div class="side-card" style="margin-bottom: var(--spacing-lg);">
                <h4><i class="fas fa-history" aria-hidden="true"></i> 今日處理記錄
                    <div style="margin-left: auto; display: flex; gap: 4px;">
                        <button style="background: #f8f9fa; border: 1px solid #dee2e6; color: #6c757d; font-size: 10px; padding: 2px 6px; border-radius: 3px; cursor: pointer;" title="重新整理列表" aria-label="重新整理列表">
                            <i class="fas fa-sync-alt" aria-hidden="true"></i> 重新整理
                        </button>
                        <button style="background: #dc3545; border: 1px solid #dc3545; color: white; font-size: 10px; padding: 2px 6px; border-radius: 3px; cursor: pointer;" title="手動清理24小時前的檔案" aria-label="手動清理24小時前的檔案">
                            <i class="fas fa-trash-alt" aria-hidden="true"></i> 清理
                        </button>
                    </div>
                </h4>
                <div id="todayRecords" style="max-height: 120px; overflow-y: auto;">
                    <div id="todayRecordsEmpty" style="text-align: center; color: #6c757d; padding: 15px;">
                        <i class="fas fa-clock" aria-hidden="true"></i>
                        <div style="margin-top: 6px; font-size: 12px;">尚無今日處理記錄</div>
                        <div style="font-size: 10px; margin-top: 3px; color: #999;">處理完成後將顯示下載連結</div>
                        <div style="font-size: 10px; margin-top: 6px; color: #007bff;">
                            <i class="fas fa-info-circle" aria-hidden="true"></i> 如果處理完成但未顯示，請點擊「重新整理」按鈕
                        </div>
                    </div>
                    <div id="todayRecordsList" style="display: none;"></div>
                </div>
            </div>

            <!-- 處理指示器 -->
            <div class="processing-indicator" id="processingIndicator" style="display: none;">
                <div class="spinner"></div>
                <div>正在處理...</div>
            </div>

            <!-- 下半部：詳細資料區域 -->
            <div class="details-container">
                <div class="timeline-header">
                    <h3><i class="fas fa-chart-line" aria-hidden="true"></i> 詳細資料</h3>
                </div>

                <div class="detail-container" id="detailContainer">
                    <div class="detail-item" id="eqcDetailItem">
                        <div class="detail-header" role="button" tabindex="0" aria-label="展開或收合EQC處理結果詳細資料" aria-expanded="false">
                            <i class="fas fa-chevron-right" id="detailChevron" aria-hidden="true"></i>
                            <span style="font-weight: bold;">EQC 處理結果</span>
                            <span class="status-badge" id="statusBadge">點擊查看詳情</span>
                        </div>
                        
                        <!-- 收合狀態的Summary摘要 -->
                        <div class="summary-preview" id="summaryPreview">
                            <div class="summary-metrics">
                                <div class="metric-item">
                                    <div class="metric-value" id="summaryFailCount">0</div>
                                    <div class="metric-label">Online EQC FAIL</div>
                                </div>
                                <div class="metric-item">
                                    <div class="metric-value" id="summaryPassCount">0</div>
                                    <div class="metric-label">EQC RT PASS</div>
                                </div>
                                <div class="metric-item">
                                    <div class="metric-value" id="summaryMatchRate">0</div>
                                    <div class="metric-label">匹配數量</div>
                                </div>
                                <div class="metric-item">
                                    <div class="metric-value" id="summarySearchMethod">-</div>
                                    <div class="metric-label">搜尋方法</div>
                                </div>
                                <div class="metric-item">
                                    <div class="metric-value" id="summarySearchStatus">-</div>
                                    <div class="metric-label">搜尋狀態</div>
                                </div>
                            </div>
                            <div class="summary-highlight">
                                <i class="fas fa-info-circle" aria-hidden="true"></i>
                                <span id="summaryHighlight">點擊展開查看詳細資料和前10項FAIL/PASS摘要</span>
                            </div>
                        </div>
                        
                        <div class="detail-content" id="detailContent" style="display: none;">
                            <!-- 動態內容區域 -->
                        </div>
                    </div>
                    
                    <div class="empty-state" id="emptyState">
                        <i class="fas fa-chart-line" aria-hidden="true" style="font-size: 4em; color: #ccc; margin-bottom: 20px;"></i>
                        <h4>尚未開始處理</h4>
                        <p>請選擇資料夾或上傳檔案，然後點擊「一鍵完成到程式碼對比」按鈕</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 報告預覽模態框 -->
    <div id="reportPreviewModal" style="display: none; position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0,0,0,0.8); z-index: 1000; overflow: auto;">
        <div style="position: relative; background: white; margin: 2% auto; width: 90%; max-width: 1000px; border-radius: 15px; box-shadow: 0 20px 60px rgba(0,0,0,0.3);">
            <!-- 模態框標題列 -->
            <div style="background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%); color: white; padding: 20px; border-radius: 15px 15px 0 0; display: flex; justify-content: space-between; align-items: center;">
                <h3 style="margin: 0; display: flex; align-items: center; gap: 10px;">
                    <i class="fas fa-file-alt" aria-hidden="true"></i> EQC 處理報告預覽
                </h3>
                <button style="background: none; border: none; color: white; font-size: 24px; cursor: pointer; padding: 5px;" aria-label="關閉預覽視窗">
                    <i class="fas fa-times" aria-hidden="true"></i>
                </button>
            </div>
            
            <!-- 報告內容區域 -->
            <div style="padding: 30px; max-height: 70vh; overflow-y: auto;">
                <div id="reportContent" style="font-family: 'Courier New', monospace; background: #f8f9fa; padding: 20px; border-radius: 8px; white-space: pre-wrap; line-height: 1.6; font-size: 14px;">
                    載入中...
                </div>
            </div>
            
            <!-- 操作按鈕區域 -->
            <div style="padding: 20px; border-top: 1px solid #e9ecef; display: flex; gap: 10px; justify-content: flex-end;">
                <button class="btn btn-primary" aria-label="下載EQC處理報告">
                    <i class="fas fa-download" aria-hidden="true"></i> 下載報告
                </button>
                <button class="btn btn-secondary" aria-label="複製報告內容到剪貼板">
                    <i class="fas fa-copy" aria-hidden="true"></i> 複製內容
                </button>
                <button class="btn btn-outline-secondary" aria-label="關閉報告預覽視窗">
                    <i class="fas fa-times" aria-hidden="true"></i> 關閉
                </button>
            </div>
        </div>
    </div>

    <!-- 載入模組化的 JavaScript 核心模組 -->
    <script src="../static/js/core/utils.js"></script>
    <script src="../static/js/core/dom-manager.js"></script>
    <script src="../static/js/core/status-manager.js"></script>
    <script src="../static/js/core/api-client.js"></script>

    <!-- 載入 UI 組件模組 -->
    <script src="../static/js/components/countdown-modal.js"></script>
    <script src="../static/js/components/file-upload.js"></script>
    <script src="../static/js/components/progress-display.js"></script>
    <script src="../static/js/components/detail-panel.js"></script>
    <script src="../static/js/components/modal.js"></script>
    <script src="../static/js/components/download-modal.js"></script>

    <!-- 載入業務邏輯模組 -->
    <script src="../static/js/business/eqc-processor.js"></script>

    <!-- 載入主控制器 (必須最後載入) -->
    <script src="../static/js/main.js"></script>

    <!-- 主控制器會自動處理所有初始化和事件綁定 -->
</body>
</html>
