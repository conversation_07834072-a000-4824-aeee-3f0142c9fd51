<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>FT-EQC 分組配對分析系統</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    
    <!-- 載入模組化 CSS -->
    <link rel="stylesheet" href="{{ url_for('shared.static', filename='css/variables.css') }}">
    <link rel="stylesheet" href="{{ url_for('shared.static', filename='css/base.css') }}">
    <link rel="stylesheet" href="{{ url_for('shared.static', filename='css/global.css') }}">
    <link rel="stylesheet" href="{{ url_for('eqc.static', filename='css/eqc.css') }}">
</head>
<body>
    <div class="container">
        <div class="header">
            <h1><i class="fas fa-project-diagram" aria-hidden="true"></i> Online EQC 分析程式</h1>

            <!-- 導航按鈕區域 - 與主應用程式風格一致 -->
            <div style="margin-top: 15px; display: flex; gap: 10px; justify-content: center; align-items: center; flex-wrap: wrap;">
                <a href="/eqc/ui" class="btn btn-primary" target="_blank">
                    <span class="btn-icon">►</span>
                    <span class="btn-text">FT-EQC 處理</span>
                </a>
                <a href="javascript:void(0)" id="ft-summary-link" class="btn btn-primary" target="_blank">
                    <span class="btn-icon">📈</span>
                    <span class="btn-text">FT-Summary UI</span>
                </a>
                <a href="/eqc/history" class="btn btn-info" target="_blank">
                    <span class="btn-icon">📊</span>
                    <span class="btn-text">處理歷史</span>
                </a>
                <a href="/email/inbox" class="btn btn-secondary">
                    <span class="btn-icon">📧</span>
                    <span class="btn-text">返回收件匣</span>
                </a>
            </div>
        </div>

        <div class="main-content">
            <!-- 橫向布局控制區域 -->
            <div class="controls-container">
                <!-- 主要資料夾輸入區域 (左側大區域) -->
                <div class="folder-input-card">
                    <div class="folder-icon">
                        <i class="fas fa-folder-open" aria-hidden="true"></i>
                    </div>
                    <h3 style="margin: 0 0 15px 0; color: var(--secondary-color);">📁 資料夾輸入</h3>
                    
                    <input type="text" class="folder-input" id="folderPath" 
                           placeholder="D:\project\python\outlook_summary\doc\20250523" 
                           value="D:\project\python\outlook_summary\doc\20250523">
                    
                    <!-- 處理模式切換 -->
                    <div class="mode-selector" style="margin-bottom: 15px;">
                        <label style="display: flex; align-items: center; gap: 10px; font-size: 14px; color: var(--text-muted);">
                            <input type="checkbox" id="asyncModeToggle" checked disabled>
                            <span>🚀 啟用異步模式 (多人並發支援)</span>
                            <span id="processing-mode-indicator" class="mode-sync">同步模式</span>
                        </label>
                        <div style="font-size: 12px; color: var(--text-muted); margin-top: 5px;">
                            異步模式：支援多人同時使用，任務在背景執行，可查看即時進度
                        </div>
                    </div>

                    <button class="primary-btn" onclick="processCompleteEQCWorkflow()" aria-label="執行一鍵完成程式碼對比處理">
                        <i class="fas fa-magic" aria-hidden="true"></i> 一鍵完成程式碼對比
                    </button>
                    
                    <div style="margin-top: 12px; font-size: 12px; color: #666; line-height: 1.4;">
                        自動生成 EQCTOTALDATA → 程式碼區間檢測 → 雙重搜尋 → 完整報告
                    </div>
                </div>

                <!-- 檔案上傳 (右側第1個) -->
                <div class="side-card">
                    <h4><i class="fas fa-upload" aria-hidden="true"></i> 檔案上傳</h4>
                    <div class="upload-zone" id="uploadZone" style="min-height: 80px; display: flex; flex-direction: column; justify-content: center;">
                        <i class="fas fa-cloud-upload-alt" aria-hidden="true" style="font-size: 1.5em; color: var(--primary-color); margin-bottom: 8px;"></i>
                        <div style="font-size: 12px; text-align: center;">
                            拖放檔案或點擊上傳<br>
                            <small style="color: #666;">支援 ZIP, 7Z, RAR<br>(最大 <span id="maxSizeDisplay">1000</span>MB)</small>
                        </div>
                        <input type="file" id="fileInput" accept=".zip,.7z,.rar,.tar,.gz" style="display: none;">
                    </div>
                    <div id="uploadStatus" style="margin-top: 8px; font-size: 11px; display: none;"></div>
                </div>

                <!-- CODE區間設定 (右側第2個) -->
                <div class="side-card">
                    <h4><i class="fas fa-code" aria-hidden="true"></i> CODE 區間設定</h4>
                    <div style="margin-bottom: 10px; padding: 8px; background: #e7f3ff; border: 1px solid #b3d9ff; border-radius: 4px; font-size: 10px; color: #0066cc;">
                        <i class="fas fa-info-circle" aria-hidden="true"></i> 智能設定: 自動檢測最佳區間
                    </div>
                    <div class="region-group" style="margin-bottom: 10px;">
                        <label style="font-size: 11px; margin-bottom: 4px; display: block;">主要 CODE 區間:</label>
                        <div class="input-row" style="display: flex; align-items: center; gap: 4px;">
                            <input type="number" id="mainStart" placeholder="起始" style="width: 50px; padding: 4px; border: 1px solid #ddd; border-radius: 3px; font-size: 11px;" aria-label="主要CODE區間起始欄位">
                            <span style="font-size: 11px;">-</span>
                            <input type="number" id="mainEnd" placeholder="結束" style="width: 50px; padding: 4px; border: 1px solid #ddd; border-radius: 3px; font-size: 11px;" aria-label="主要CODE區間結束欄位">
                        </div>
                        <span class="field-count" id="mainCount" style="font-size: 10px; color: #666;">(未設定)</span>
                    </div>
                    <div class="region-group">
                        <label style="font-size: 11px; margin-bottom: 4px; display: block;">備用 CODE 區間:</label>
                        <div class="input-row" style="display: flex; align-items: center; gap: 4px;">
                            <input type="number" id="backupStart" placeholder="起始" style="width: 50px; padding: 4px; border: 1px solid #ddd; border-radius: 3px; font-size: 11px;" aria-label="備用CODE區間起始欄位">
                            <span style="font-size: 11px;">-</span>
                            <input type="number" id="backupEnd" placeholder="結束" style="width: 50px; padding: 4px; border: 1px solid #ddd; border-radius: 3px; font-size: 11px;" aria-label="備用CODE區間結束欄位">
                        </div>
                        <span class="field-count" id="backupCount" style="font-size: 10px; color: #666;">(未設定)</span>
                    </div>
                </div>

                <!-- 處理進度 (右側第3個) -->
                <div class="side-card">
                    <h4><i class="fas fa-tasks" aria-hidden="true"></i> 處理進度</h4>
                    <div id="progressDisplay" style="padding: 10px; background: #f8f9ff; border-radius: 4px; border: 1px solid #e9ecef;">
                        <div class="progress-step" id="progressStep" style="font-size: 11px; font-weight: 600; color: var(--secondary-color); margin-bottom: 4px; display: flex; align-items: center; gap: 4px;">
                            <i class="fas fa-clock" aria-hidden="true"></i> 等待開始...
                        </div>
                        <div class="progress-detail" id="progressDetail" style="font-size: 10px; color: var(--text-muted); line-height: 1.3;">
                            點擊「一鍵完成」開始處理
                        </div>
                    </div>
                    <div class="progress-bar-container" id="progressBarContainer" style="display: none; margin-top: 8px;">
                        <div style="height: 6px; background: var(--border-color); border-radius: 3px; overflow: hidden; margin-bottom: 4px;">
                            <div id="progressFill" style="height: 100%; background: linear-gradient(90deg, var(--primary-color) 0%, #764ba2 100%); width: 0%; transition: var(--transition);"></div>
                        </div>
                        <div class="progress-text" id="progressText" style="text-align: center; font-size: 10px; color: var(--text-muted); font-weight: 500;">0%</div>
                    </div>
                </div>
            </div>

            <!-- 今日處理記錄 -->
            <div class="side-card" style="margin-bottom: var(--spacing-lg);">
                <h4><i class="fas fa-history" aria-hidden="true"></i> 今日處理記錄
                    <div style="margin-left: auto; display: flex; gap: 4px;">
                        <button id="refreshTodayRecords" style="background: #f8f9fa; border: 1px solid #dee2e6; color: #6c757d; font-size: 10px; padding: 2px 6px; border-radius: 3px; cursor: pointer;" title="重新整理列表" aria-label="重新整理列表">
                            <i class="fas fa-sync-alt" aria-hidden="true"></i> 重新整理
                        </button>
                        <button id="clearOldRecords" style="background: #dc3545; border: 1px solid #dc3545; color: white; font-size: 10px; padding: 2px 6px; border-radius: 3px; cursor: pointer;" title="清理舊記錄" aria-label="清理舊記錄">
                            <i class="fas fa-trash-alt" aria-hidden="true"></i> 清理
                        </button>
                    </div>
                </h4>

                <!-- 統計資訊 -->
                <div id="todayStats" style="display: none; margin-bottom: 10px; padding: 8px; background: #f8f9fa; border-radius: 6px; font-size: 11px;">
                    <div style="display: flex; justify-content: space-between; align-items: center;">
                        <span><i class="fas fa-tasks" style="color: #007bff;"></i> 總任務: <strong id="totalTasks">0</strong></span>
                        <span><i class="fas fa-check-circle" style="color: #28a745;"></i> 完成: <strong id="completedTasks">0</strong></span>
                        <span><i class="fas fa-clock" style="color: #ffc107;"></i> 平均: <strong id="avgTime">0s</strong></span>
                    </div>
                </div>

                <div id="todayRecords" style="max-height: 200px; overflow-y: auto;">
                    <div id="todayRecordsEmpty" style="text-align: center; color: #6c757d; padding: 15px;">
                        <i class="fas fa-clock" aria-hidden="true"></i>
                        <div style="margin-top: 6px; font-size: 12px;">尚無今日處理記錄</div>
                        <div style="font-size: 10px; margin-top: 3px; color: #999;">處理完成後將顯示下載連結</div>
                        <div style="font-size: 10px; margin-top: 6px; color: #007bff;">
                            <i class="fas fa-info-circle" aria-hidden="true"></i> 如果處理完成但未顯示，請點擊「重新整理」按鈕
                        </div>
                    </div>
                    <div id="todayRecordsList" style="display: none;"></div>
                </div>
            </div>

            <!-- 處理指示器 -->
            <div class="processing-indicator" id="processingIndicator" style="display: none;">
                <div class="spinner"></div>
                <div>正在處理...</div>
            </div>
        </div>
    </div>

    <!-- 載入核心依賴 -->
    <script src="{{ url_for('shared.static', filename='js/core/utils.js') }}"></script>
    <script src="{{ url_for('shared.static', filename='js/core/api-client.js') }}"></script>

    <!-- 載入 URL 配置 -->
    <script src="{{ url_for('shared.static', filename='js/utils/url-config.js') }}"></script>

    <!-- 載入 EQC 專用腳本 -->
    <script src="{{ url_for('eqc.static', filename='js/eqc-processor.js') }}"></script>
    <script src="{{ url_for('eqc.static', filename='js/eqc-dashboard.js') }}"></script>

    <script>
        // 設置動態 URL 連結
        document.addEventListener('DOMContentLoaded', function() {
            setupDynamicUrls();
        });

        /**
         * 設置動態 URL 連結
         */
        function setupDynamicUrls() {
            try {
                // 獲取 URL 配置
                const config = UrlConfig.getConfig();

                // 設置 FT-Summary UI 連結
                const ftSummaryLink = document.getElementById('ft-summary-link');
                if (ftSummaryLink) {
                    ftSummaryLink.href = config.ui.ftSummary;
                    console.log('🔗 FT-Summary UI URL 設置為:', config.ui.ftSummary);
                }

                console.log('✅ EQC 儀表板 URL 配置完成');

            } catch (error) {
                console.error('❌ 設置動態 URL 時發生錯誤:', error);

                // 回退到預設 URL
                const ftSummaryLink = document.getElementById('ft-summary-link');
                if (ftSummaryLink) {
                    ftSummaryLink.href = 'http://127.0.0.1:8010/ft-summary-ui';
                    console.log('⚠️ 使用回退 URL:', ftSummaryLink.href);
                }
            }
        }
    </script>
</body>
</html>