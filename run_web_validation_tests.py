#!/usr/bin/env python3
"""
快速執行整合服務 Web 驗證測試腳本
作為 test-automator agent 創建的便捷執行工具
"""

import os
import sys
import subprocess
import time
from pathlib import Path

# 添加專案根目錄到 Python 路徑
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def install_playwright_if_needed():
    """安裝 Playwright 如果需要的話"""
    try:
        import playwright
        print("✓ Playwright 已安裝")
        return True
    except ImportError:
        print("正在安裝 Playwright...")
        try:
            subprocess.run([sys.executable, "-m", "pip", "install", "playwright"], check=True)
            subprocess.run([sys.executable, "-m", "playwright", "install"], check=True)
            print("✓ Playwright 安裝完成")
            return True
        except subprocess.CalledProcessError as e:
            print(f"✗ Playwright 安裝失敗: {e}")
            return False

def check_environment():
    """檢查環境設定"""
    print("正在檢查環境設定...")
    
    # 檢查必要文件
    required_files = [
        "start_integrated_services.py",
        "tests/playwright/test_integrated_web_validation.py",
        "frontend/app.py"
    ]
    
    missing_files = []
    for file_path in required_files:
        if not (project_root / file_path).exists():
            missing_files.append(file_path)
    
    if missing_files:
        print(f"✗ 缺少必要文件: {missing_files}")
        return False
    
    # 檢查環境變數文件
    env_file = project_root / ".env"
    if not env_file.exists():
        print("⚠ 未找到 .env 文件，將使用默認設定")
        # 創建基本的 .env 文件用於測試
        create_test_env_file()
    else:
        print("✓ 找到 .env 文件")
    
    print("✓ 環境檢查完成")
    return True

def create_test_env_file():
    """創建測試用的 .env 文件"""
    test_env_content = """# 測試環境配置
EMAIL_ADDRESS=<EMAIL>
EMAIL_PASSWORD=test_password
POP3_SERVER=pop.example.com
POP3_PORT=995

# LINE 通知 (可選)
LINE_CHANNEL_ACCESS_TOKEN=
LINE_USER_ID=

# 測試環境設定
FLASK_ENV=testing
PYTHONIOENCODING=utf-8
PYTHONUTF8=1
"""
    
    env_file = project_root / ".env"
    with open(env_file, 'w', encoding='utf-8') as f:
        f.write(test_env_content)
    
    print("✓ 已創建測試用 .env 文件")

def run_tests(show_browser=False, verbose=False):
    """執行測試"""
    print("正在執行整合服務 Web 驗證測試...")
    print("=" * 60)
    
    # 準備測試命令
    test_script = project_root / "tests" / "playwright" / "test_integrated_web_validation.py"
    
    cmd = [sys.executable, str(test_script)]
    
    if show_browser:
        cmd.append("--show-browser")
    
    # 設置環境變數
    env = os.environ.copy()
    env['PYTHONIOENCODING'] = 'utf-8'
    env['PYTHONUTF8'] = '1'
    env['PYTHONPATH'] = str(project_root)
    
    try:
        # 執行測試
        if verbose:
            # 顯示詳細輸出
            result = subprocess.run(cmd, env=env, cwd=str(project_root))
            return result.returncode == 0
        else:
            # 捕獲輸出
            result = subprocess.run(cmd, env=env, cwd=str(project_root), 
                                  capture_output=True, text=True)
            
            # 顯示結果
            if result.stdout:
                print(result.stdout)
            if result.stderr and result.returncode != 0:
                print("錯誤輸出:")
                print(result.stderr)
            
            return result.returncode == 0
            
    except Exception as e:
        print(f"執行測試時發生錯誤: {e}")
        return False

def main():
    """主函數"""
    import argparse
    
    parser = argparse.ArgumentParser(description='執行整合服務 Web 驗證測試')
    parser.add_argument('--show-browser', action='store_true',
                       help='顯示瀏覽器窗口進行測試')
    parser.add_argument('--verbose', '-v', action='store_true',
                       help='顯示詳細輸出')
    parser.add_argument('--install-only', action='store_true',
                       help='僅安裝依賴，不執行測試')
    
    args = parser.parse_args()
    
    print("整合服務 Web 驗證測試執行器")
    print("=" * 60)
    
    # 1. 安裝 Playwright
    if not install_playwright_if_needed():
        print("無法安裝 Playwright，測試終止")
        sys.exit(1)
    
    if args.install_only:
        print("依賴安裝完成")
        sys.exit(0)
    
    # 2. 檢查環境
    if not check_environment():
        print("環境檢查失敗，測試終止")
        sys.exit(1)
    
    # 3. 執行測試
    print("\n開始執行測試...")
    success = run_tests(show_browser=args.show_browser, verbose=args.verbose)
    
    # 4. 顯示結果
    print("\n" + "=" * 60)
    if success:
        print("測試執行成功！")
        print("\n測試結果文件:")
        results_file = project_root / "integrated_web_test_results.json"
        if results_file.exists():
            print(f"   - 詳細結果: {results_file}")
        
        screenshots_dir = project_root / "test_screenshots"
        if screenshots_dir.exists():
            screenshot_files = list(screenshots_dir.glob("*.png"))
            print(f"   - 截圖數量: {len(screenshot_files)}")
            print(f"   - 截圖目錄: {screenshots_dir}")
        
        print("\n建議後續步驟:")
        print("   1. 檢查測試結果文件了解詳細情況")
        print("   2. 查看截圖確認頁面顯示正常")
        print("   3. 如有失敗項目，檢查相關模組實現")
        
    else:
        print("測試執行失敗")
        print("\n故障排除建議:")
        print("   1. 檢查 .env 文件配置是否正確")
        print("   2. 確認所有前端模組文件存在")
        print("   3. 檢查端口 5000 和 8010 是否被佔用")
        print("   4. 使用 --verbose 參數查看詳細錯誤信息")
        print("   5. 使用 --show-browser 參數查看瀏覽器實際情況")
        
        sys.exit(1)

if __name__ == "__main__":
    main()