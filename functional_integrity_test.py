#!/usr/bin/env python3
"""
功能完整性專用測試
驗證所有URL和API端點的功能一致性
創建日期: 2025-08-13
作者: backend-architect agent
"""

import os
import sys
import json
import time
import requests
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Any, Tuple
import concurrent.futures
from urllib.parse import urljoin


class FunctionalIntegrityTester:
    """功能完整性測試器"""
    
    def __init__(self, base_url: str = "http://localhost:8000"):
        self.base_url = base_url
        self.project_root = Path(__file__).parent
        self.session = requests.Session()
        self.session.timeout = 15
        
        self.results = {
            'test_metadata': {
                'test_start_time': datetime.now().isoformat(),
                'base_url': base_url,
                'tester': 'functional-integrity-tester'
            },
            'frontend_modules': {},
            'api_endpoints': {},
            'critical_paths': {},
            'edge_cases': {},
            'cross_module_integration': {}
        }
        
        # 定義前端模組及其端點
        self.frontend_modules = {
            'analytics': {
                'name': '分析模組',
                'base_path': '/analytics',
                'endpoints': [
                    {'path': '/', 'name': '主頁', 'critical': True},
                    {'path': '/dashboard', 'name': '儀表板', 'critical': True},
                    {'path': '/reports', 'name': '報告頁面', 'critical': True},
                    {'path': '/vendor_analysis', 'name': '廠商分析', 'critical': False},
                    {'path': '/csv_processor', 'name': 'CSV處理器', 'critical': False}
                ]
            },
            'email': {
                'name': '郵件模組',
                'base_path': '/email',
                'endpoints': [
                    {'path': '/', 'name': '郵件主頁', 'critical': True},
                    {'path': '/inbox', 'name': '收件箱', 'critical': True},
                    {'path': '/settings', 'name': '郵件設定', 'critical': True},
                    {'path': '/compose', 'name': '撰寫郵件', 'critical': False}
                ]
            },
            'eqc': {
                'name': 'EQC模組',
                'base_path': '/eqc',
                'endpoints': [
                    {'path': '/', 'name': 'EQC主頁', 'critical': True},
                    {'path': '/dashboard', 'name': 'EQC儀表板', 'critical': True},
                    {'path': '/quality_check', 'name': '質量檢查', 'critical': True},
                    {'path': '/compliance', 'name': '合規檢查', 'critical': True}
                ]
            },
            'file_management': {
                'name': '檔案管理模組',
                'base_path': '/files',
                'endpoints': [
                    {'path': '/', 'name': '檔案主頁', 'critical': True},
                    {'path': '/upload', 'name': '檔案上傳', 'critical': True},
                    {'path': '/browser', 'name': '檔案瀏覽器', 'critical': True},
                    {'path': '/manager', 'name': '檔案管理器', 'critical': False}
                ]
            },
            'monitoring': {
                'name': '監控模組',
                'base_path': '/monitoring',
                'endpoints': [
                    {'path': '/', 'name': '監控主頁', 'critical': True},
                    {'path': '/health', 'name': '健康檢查', 'critical': True},
                    {'path': '/system', 'name': '系統監控', 'critical': True},
                    {'path': '/realtime', 'name': '即時監控', 'critical': False}
                ]
            },
            'tasks': {
                'name': '任務模組',
                'base_path': '/tasks',
                'endpoints': [
                    {'path': '/', 'name': '任務主頁', 'critical': True},
                    {'path': '/queue', 'name': '任務佇列', 'critical': True},
                    {'path': '/scheduler', 'name': '任務排程器', 'critical': True},
                    {'path': '/manager', 'name': '任務管理器', 'critical': False}
                ]
            }
        }
        
        # 定義API端點
        self.api_endpoints = {
            'system': {
                'name': '系統API',
                'endpoints': [
                    {'path': '/health', 'method': 'GET', 'name': '健康檢查', 'critical': True},
                    {'path': '/api/status', 'method': 'GET', 'name': '系統狀態', 'critical': True}
                ]
            },
            'analytics': {
                'name': '分析API',
                'endpoints': [
                    {'path': '/api/analytics/status', 'method': 'GET', 'name': '分析狀態', 'critical': True},
                    {'path': '/api/analytics/reports', 'method': 'GET', 'name': '報告列表', 'critical': False}
                ]
            },
            'email': {
                'name': '郵件API',
                'endpoints': [
                    {'path': '/api/email/status', 'method': 'GET', 'name': '郵件狀態', 'critical': True},
                    {'path': '/api/email/list', 'method': 'GET', 'name': '郵件列表', 'critical': False}
                ]
            },
            'eqc': {
                'name': 'EQC API',
                'endpoints': [
                    {'path': '/api/eqc/status', 'method': 'GET', 'name': 'EQC狀態', 'critical': True},
                    {'path': '/api/eqc/jobs', 'method': 'GET', 'name': 'EQC任務', 'critical': False}
                ]
            },
            'files': {
                'name': '檔案API',
                'endpoints': [
                    {'path': '/api/files/status', 'method': 'GET', 'name': '檔案狀態', 'critical': True},
                    {'path': '/api/files/list', 'method': 'GET', 'name': '檔案列表', 'critical': False}
                ]
            },
            'monitoring': {
                'name': '監控API',
                'endpoints': [
                    {'path': '/api/monitoring/status', 'method': 'GET', 'name': '監控狀態', 'critical': True},
                    {'path': '/api/monitoring/metrics', 'method': 'GET', 'name': '監控指標', 'critical': False}
                ]
            },
            'tasks': {
                'name': '任務API',
                'endpoints': [
                    {'path': '/api/tasks/status', 'method': 'GET', 'name': '任務狀態', 'critical': True},
                    {'path': '/api/tasks/queue', 'method': 'GET', 'name': '任務佇列狀態', 'critical': False}
                ]
            }
        }

    def log_test_result(self, category: str, test_name: str, success: bool, 
                       message: str = "", data: Any = None, duration: float = 0,
                       critical: bool = False):
        """記錄測試結果"""
        if category not in self.results:
            self.results[category] = {}
            
        self.results[category][test_name] = {
            'success': success,
            'message': message,
            'data': data,
            'duration_ms': round(duration * 1000, 2),
            'critical': critical,
            'timestamp': datetime.now().isoformat()
        }
        
        criticality = "🔴" if critical else "🟡"
        status = "✅ PASS" if success else "❌ FAIL"
        print(f"{status} {criticality} [{category}] {test_name}")
        if message:
            print(f"    {message}")
        if duration > 0:
            print(f"    執行時間: {round(duration * 1000, 2)}ms")

    def check_service_health(self) -> bool:
        """檢查服務健康狀態"""
        try:
            response = self.session.get(f"{self.base_url}/health", timeout=5)
            return response.status_code == 200
        except:
            return False

    def test_single_endpoint(self, url: str, method: str = 'GET', 
                           timeout: int = 15) -> Dict[str, Any]:
        """測試單一端點"""
        start_time = time.time()
        
        try:
            if method.upper() == 'GET':
                response = self.session.get(url, timeout=timeout)
            elif method.upper() == 'POST':
                response = self.session.post(url, timeout=timeout)
            else:
                response = self.session.request(method, url, timeout=timeout)
            
            duration = time.time() - start_time
            
            # 分析響應
            result = {
                'success': True,
                'status_code': response.status_code,
                'response_time': duration,
                'content_length': len(response.content),
                'content_type': response.headers.get('content-type', ''),
                'has_content': len(response.content) > 0,
                'is_html': 'text/html' in response.headers.get('content-type', ''),
                'is_json': 'application/json' in response.headers.get('content-type', '')
            }
            
            # 檢查響應是否成功
            if response.status_code not in [200, 201, 202]:
                result['success'] = False
                result['error'] = f"HTTP {response.status_code}"
            
            # 對於HTML頁面，檢查是否包含錯誤訊息
            if result['is_html'] and response.status_code == 200:
                content_lower = response.text.lower()
                error_indicators = ['error', '錯誤', '404', '500', 'not found', 'internal server error']
                if any(indicator in content_lower for indicator in error_indicators):
                    result['has_error_content'] = True
                    result['warning'] = "頁面包含錯誤內容"
                else:
                    result['has_error_content'] = False
            
            # 對於JSON響應，嘗試解析
            if result['is_json']:
                try:
                    json_data = response.json()
                    result['json_valid'] = True
                    result['json_data'] = json_data
                    
                    # 檢查JSON中的錯誤標誌
                    if isinstance(json_data, dict):
                        if json_data.get('error') or json_data.get('status') == 'error':
                            result['has_error_content'] = True
                except:
                    result['json_valid'] = False
                    result['warning'] = "JSON響應解析失敗"
            
            return result
            
        except requests.exceptions.Timeout:
            return {
                'success': False,
                'error': 'Timeout',
                'response_time': time.time() - start_time
            }
        except requests.exceptions.ConnectionError:
            return {
                'success': False,
                'error': 'Connection Error',
                'response_time': time.time() - start_time
            }
        except Exception as e:
            return {
                'success': False,
                'error': str(e),
                'response_time': time.time() - start_time
            }

    def test_frontend_modules(self) -> Dict[str, Any]:
        """測試前端模組"""
        print("\n🎨 測試前端模組...")
        
        frontend_results = {}
        
        for module_id, module_config in self.frontend_modules.items():
            print(f"\n測試模組: {module_config['name']}")
            module_results = {
                'module_name': module_config['name'],
                'base_path': module_config['base_path'],
                'endpoints': {},
                'summary': {
                    'total_endpoints': len(module_config['endpoints']),
                    'successful_endpoints': 0,
                    'failed_endpoints': 0,
                    'critical_failures': 0
                }
            }
            
            for endpoint_config in module_config['endpoints']:
                endpoint_path = endpoint_config['path']
                endpoint_name = endpoint_config['name']
                is_critical = endpoint_config['critical']
                
                # 構建完整URL
                if endpoint_path == '/':
                    full_url = f"{self.base_url}{module_config['base_path']}"
                else:
                    full_url = f"{self.base_url}{module_config['base_path']}{endpoint_path}"
                
                # 測試端點
                result = self.test_single_endpoint(full_url)
                
                module_results['endpoints'][endpoint_path] = {
                    'name': endpoint_name,
                    'full_url': full_url,
                    'critical': is_critical,
                    'result': result
                }
                
                # 更新統計
                if result['success'] and not result.get('has_error_content', False):
                    module_results['summary']['successful_endpoints'] += 1
                    success = True
                else:
                    module_results['summary']['failed_endpoints'] += 1
                    success = False
                    if is_critical:
                        module_results['summary']['critical_failures'] += 1
                
                # 記錄結果
                status_info = f"狀態碼: {result.get('status_code', 'N/A')}"
                if 'response_time' in result:
                    status_info += f", 響應時間: {result['response_time']*1000:.2f}ms"
                if result.get('warning'):
                    status_info += f", 警告: {result['warning']}"
                
                self.log_test_result('frontend_modules', 
                                   f"{module_id}_{endpoint_path.replace('/', '_')}",
                                   success, status_info, result,
                                   result.get('response_time', 0), is_critical)
            
            frontend_results[module_id] = module_results
        
        return frontend_results

    def test_api_endpoints(self) -> Dict[str, Any]:
        """測試API端點"""
        print("\n🌐 測試API端點...")
        
        api_results = {}
        
        for api_group, group_config in self.api_endpoints.items():
            print(f"\n測試API組: {group_config['name']}")
            group_results = {
                'group_name': group_config['name'],
                'endpoints': {},
                'summary': {
                    'total_endpoints': len(group_config['endpoints']),
                    'successful_endpoints': 0,
                    'failed_endpoints': 0,
                    'critical_failures': 0
                }
            }
            
            for endpoint_config in group_config['endpoints']:
                endpoint_path = endpoint_config['path']
                endpoint_method = endpoint_config.get('method', 'GET')
                endpoint_name = endpoint_config['name']
                is_critical = endpoint_config['critical']
                
                # 構建完整URL
                full_url = f"{self.base_url}{endpoint_path}"
                
                # 測試端點
                result = self.test_single_endpoint(full_url, endpoint_method)
                
                group_results['endpoints'][endpoint_path] = {
                    'name': endpoint_name,
                    'method': endpoint_method,
                    'full_url': full_url,
                    'critical': is_critical,
                    'result': result
                }
                
                # 更新統計
                if result['success'] and not result.get('has_error_content', False):
                    group_results['summary']['successful_endpoints'] += 1
                    success = True
                else:
                    group_results['summary']['failed_endpoints'] += 1
                    success = False
                    if is_critical:
                        group_results['summary']['critical_failures'] += 1
                
                # 記錄結果
                status_info = f"{endpoint_method} 狀態碼: {result.get('status_code', 'N/A')}"
                if 'response_time' in result:
                    status_info += f", 響應時間: {result['response_time']*1000:.2f}ms"
                if result.get('json_valid') is not None:
                    status_info += f", JSON有效: {result['json_valid']}"
                if result.get('warning'):
                    status_info += f", 警告: {result['warning']}"
                
                self.log_test_result('api_endpoints', 
                                   f"{api_group}_{endpoint_path.replace('/', '_').replace('api_', '')}",
                                   success, status_info, result,
                                   result.get('response_time', 0), is_critical)
            
            api_results[api_group] = group_results
        
        return api_results

    def test_critical_user_paths(self) -> Dict[str, Any]:
        """測試關鍵用戶路徑"""
        print("\n🛤️ 測試關鍵用戶路徑...")
        
        # 定義關鍵用戶路徑
        critical_paths = {
            'email_workflow': {
                'name': '郵件處理工作流程',
                'steps': [
                    {'url': '/email', 'name': '進入郵件模組'},
                    {'url': '/email/inbox', 'name': '查看收件箱'},
                    {'url': '/email/settings', 'name': '檢查設定'}
                ]
            },
            'eqc_workflow': {
                'name': 'EQC處理工作流程',
                'steps': [
                    {'url': '/eqc', 'name': '進入EQC模組'},
                    {'url': '/eqc/dashboard', 'name': '查看儀表板'},
                    {'url': '/eqc/quality_check', 'name': '執行質量檢查'}
                ]
            },
            'file_management_workflow': {
                'name': '檔案管理工作流程',
                'steps': [
                    {'url': '/files', 'name': '進入檔案模組'},
                    {'url': '/files/browser', 'name': '瀏覽檔案'},
                    {'url': '/files/upload', 'name': '上傳檔案頁面'}
                ]
            },
            'monitoring_workflow': {
                'name': '系統監控工作流程',
                'steps': [
                    {'url': '/monitoring', 'name': '進入監控模組'},
                    {'url': '/monitoring/health', 'name': '健康檢查'},
                    {'url': '/monitoring/system', 'name': '系統狀態'}
                ]
            }
        }
        
        path_results = {}
        
        for path_id, path_config in critical_paths.items():
            print(f"\n測試路徑: {path_config['name']}")
            
            path_result = {
                'path_name': path_config['name'],
                'steps': [],
                'overall_success': True,
                'total_time': 0,
                'failed_steps': []
            }
            
            # 使用新的session來模擬用戶會話
            path_session = requests.Session()
            path_session.timeout = 15
            
            for i, step in enumerate(path_config['steps']):
                step_url = f"{self.base_url}{step['url']}"
                step_name = step['name']
                
                print(f"  步驟 {i+1}: {step_name}")
                
                start_time = time.time()
                try:
                    response = path_session.get(step_url)
                    duration = time.time() - start_time
                    path_result['total_time'] += duration
                    
                    step_success = response.status_code == 200
                    if not step_success:
                        path_result['overall_success'] = False
                        path_result['failed_steps'].append(step_name)
                    
                    step_result = {
                        'step_number': i + 1,
                        'step_name': step_name,
                        'url': step_url,
                        'success': step_success,
                        'status_code': response.status_code,
                        'response_time': duration,
                        'content_length': len(response.content)
                    }
                    
                    path_result['steps'].append(step_result)
                    
                    # 記錄每個步驟
                    self.log_test_result('critical_paths', 
                                       f"{path_id}_step_{i+1}",
                                       step_success, 
                                       f"步驟: {step_name}, 狀態: {response.status_code}",
                                       step_result, duration, True)
                    
                except Exception as e:
                    duration = time.time() - start_time
                    path_result['total_time'] += duration
                    path_result['overall_success'] = False
                    path_result['failed_steps'].append(step_name)
                    
                    step_result = {
                        'step_number': i + 1,
                        'step_name': step_name,
                        'url': step_url,
                        'success': False,
                        'error': str(e),
                        'response_time': duration
                    }
                    
                    path_result['steps'].append(step_result)
                    
                    self.log_test_result('critical_paths', 
                                       f"{path_id}_step_{i+1}",
                                       False, f"步驟失敗: {step_name}, 錯誤: {str(e)}",
                                       step_result, duration, True)
                
                # 步驟間短暫等待
                time.sleep(0.5)
            
            # 記錄整個路徑的結果
            self.log_test_result('critical_paths', f"{path_id}_overall",
                               path_result['overall_success'],
                               f"路徑完成: {len(path_result['steps'])}步驟, "
                               f"總時間: {path_result['total_time']:.2f}秒, "
                               f"失敗步驟: {len(path_result['failed_steps'])}",
                               path_result, path_result['total_time'], True)
            
            path_results[path_id] = path_result
            path_session.close()
        
        return path_results

    def test_edge_cases(self) -> Dict[str, Any]:
        """測試邊界情況"""
        print("\n🔍 測試邊界情況...")
        
        edge_cases = {
            'invalid_routes': [
                '/nonexistent',
                '/email/invalid',
                '/api/invalid',
                '/files/nonexistent',
                '/admin/secret'
            ],
            'malformed_requests': [
                '/api/files?invalid=param&bad=data',
                '/email/inbox?page=-1',
                '/eqc/dashboard?limit=99999'
            ],
            'special_characters': [
                '/files/中文路徑',
                '/email/test%20space',
                '/api/test?param=特殊字符'
            ]
        }
        
        edge_results = {}
        
        for category, test_urls in edge_cases.items():
            print(f"\n測試類別: {category}")
            category_results = []
            
            for test_url in test_urls:
                full_url = f"{self.base_url}{test_url}"
                result = self.test_single_endpoint(full_url)
                
                # 對於邊界情況，我們期望得到適當的錯誤處理
                expected_status_codes = [400, 404, 405, 422, 500]
                appropriate_handling = result.get('status_code') in expected_status_codes
                
                category_results.append({
                    'test_url': test_url,
                    'full_url': full_url,
                    'result': result,
                    'appropriate_handling': appropriate_handling
                })
                
                self.log_test_result('edge_cases', 
                                   f"{category}_{test_url.replace('/', '_').replace('?', '_')}",
                                   appropriate_handling,
                                   f"URL: {test_url}, 狀態: {result.get('status_code', 'N/A')}, "
                                   f"適當處理: {appropriate_handling}",
                                   result, result.get('response_time', 0))
            
            edge_results[category] = category_results
        
        return edge_results

    def test_cross_module_integration(self) -> Dict[str, Any]:
        """測試跨模組整合"""
        print("\n🔗 測試跨模組整合...")
        
        # 定義跨模組整合測試場景
        integration_scenarios = {
            'email_to_files': {
                'name': '郵件附件到檔案管理',
                'sequence': [
                    '/email/inbox',
                    '/files/browser'
                ]
            },
            'eqc_to_analytics': {
                'name': 'EQC結果到分析報告',
                'sequence': [
                    '/eqc/quality_check',
                    '/analytics/reports'
                ]
            },
            'monitoring_to_tasks': {
                'name': '監控警報到任務管理',
                'sequence': [
                    '/monitoring/system',
                    '/tasks/queue'
                ]
            }
        }
        
        integration_results = {}
        
        for scenario_id, scenario_config in integration_scenarios.items():
            print(f"\n測試整合場景: {scenario_config['name']}")
            
            scenario_session = requests.Session()
            scenario_session.timeout = 15
            
            scenario_result = {
                'scenario_name': scenario_config['name'],
                'sequence_results': [],
                'overall_success': True,
                'session_consistency': True
            }
            
            for i, url_path in enumerate(scenario_config['sequence']):
                full_url = f"{self.base_url}{url_path}"
                
                start_time = time.time()
                try:
                    response = scenario_session.get(full_url)
                    duration = time.time() - start_time
                    
                    step_success = response.status_code == 200
                    if not step_success:
                        scenario_result['overall_success'] = False
                    
                    step_result = {
                        'step': i + 1,
                        'url_path': url_path,
                        'success': step_success,
                        'status_code': response.status_code,
                        'response_time': duration,
                        'has_session_data': 'session' in response.headers.get('set-cookie', '').lower()
                    }
                    
                    scenario_result['sequence_results'].append(step_result)
                    
                except Exception as e:
                    scenario_result['overall_success'] = False
                    step_result = {
                        'step': i + 1,
                        'url_path': url_path,
                        'success': False,
                        'error': str(e)
                    }
                    scenario_result['sequence_results'].append(step_result)
            
            self.log_test_result('cross_module_integration', scenario_id,
                               scenario_result['overall_success'],
                               f"整合場景: {scenario_config['name']}, "
                               f"步驟: {len(scenario_config['sequence'])}, "
                               f"會話一致性: {scenario_result['session_consistency']}",
                               scenario_result)
            
            integration_results[scenario_id] = scenario_result
            scenario_session.close()
        
        return integration_results

    def run_complete_functional_test(self) -> Dict[str, Any]:
        """執行完整的功能完整性測試"""
        print("=" * 80)
        print("🔬 功能完整性測試系統")
        print(f"測試目標: {self.base_url}")
        print("=" * 80)
        
        # 檢查服務是否運行
        if not self.check_service_health():
            print("❌ 服務未運行，請先啟動服務")
            return None
        
        print("✅ 服務運行正常，開始功能測試...")
        
        try:
            # 1. 測試前端模組
            self.results['frontend_modules'] = self.test_frontend_modules()
            
            # 2. 測試API端點
            self.results['api_endpoints'] = self.test_api_endpoints()
            
            # 3. 測試關鍵用戶路徑
            self.results['critical_paths'] = self.test_critical_user_paths()
            
            # 4. 測試邊界情況
            self.results['edge_cases'] = self.test_edge_cases()
            
            # 5. 測試跨模組整合
            self.results['cross_module_integration'] = self.test_cross_module_integration()
            
            # 添加測試完成時間
            self.results['test_metadata']['test_end_time'] = datetime.now().isoformat()
            self.results['test_metadata']['total_duration'] = (
                datetime.fromisoformat(self.results['test_metadata']['test_end_time']) - 
                datetime.fromisoformat(self.results['test_metadata']['test_start_time'])
            ).total_seconds()
            
            return self.results
            
        except Exception as e:
            print(f"❌ 功能測試過程中發生錯誤: {str(e)}")
            return None
        finally:
            self.session.close()

    def save_results(self, filename: str = None) -> str:
        """保存測試結果"""
        if filename is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"functional_integrity_report_{timestamp}.json"
        
        filepath = self.project_root / filename
        
        with open(filepath, 'w', encoding='utf-8') as f:
            json.dump(self.results, f, indent=2, ensure_ascii=False)
        
        print(f"\n📄 功能完整性報告已保存: {filepath}")
        return str(filepath)

    def print_summary(self):
        """打印測試摘要"""
        print("\n" + "=" * 80)
        print("📋 功能完整性測試摘要")
        print("=" * 80)
        
        # 前端模組摘要
        if 'frontend_modules' in self.results:
            total_frontend = sum(data['summary']['total_endpoints'] for data in self.results['frontend_modules'].values())
            successful_frontend = sum(data['summary']['successful_endpoints'] for data in self.results['frontend_modules'].values())
            critical_failures_frontend = sum(data['summary']['critical_failures'] for data in self.results['frontend_modules'].values())
            
            print(f"前端模組: {successful_frontend}/{total_frontend} 通過 "
                  f"({successful_frontend/total_frontend*100:.1f}%)")
            if critical_failures_frontend > 0:
                print(f"  🔴 關鍵失敗: {critical_failures_frontend}")
        
        # API端點摘要
        if 'api_endpoints' in self.results:
            total_api = sum(data['summary']['total_endpoints'] for data in self.results['api_endpoints'].values())
            successful_api = sum(data['summary']['successful_endpoints'] for data in self.results['api_endpoints'].values())
            critical_failures_api = sum(data['summary']['critical_failures'] for data in self.results['api_endpoints'].values())
            
            print(f"API端點: {successful_api}/{total_api} 通過 "
                  f"({successful_api/total_api*100:.1f}%)")
            if critical_failures_api > 0:
                print(f"  🔴 關鍵失敗: {critical_failures_api}")
        
        # 關鍵路徑摘要
        if 'critical_paths' in self.results:
            path_results = [path for path in self.results['critical_paths'].values() 
                          if isinstance(path, dict) and 'overall_success' in path]
            successful_paths = sum(1 for path in path_results if path['overall_success'])
            
            print(f"關鍵路徑: {successful_paths}/{len(path_results)} 通過 "
                  f"({successful_paths/len(path_results)*100:.1f}%)")
        
        # 整合測試摘要
        if 'cross_module_integration' in self.results:
            integration_results = [scenario for scenario in self.results['cross_module_integration'].values() 
                                 if isinstance(scenario, dict) and 'overall_success' in scenario]
            successful_integration = sum(1 for scenario in integration_results if scenario['overall_success'])
            
            print(f"跨模組整合: {successful_integration}/{len(integration_results)} 通過 "
                  f"({successful_integration/len(integration_results)*100:.1f}%)")
        
        print("=" * 80)


def main():
    """主函數"""
    if len(sys.argv) > 1 and sys.argv[1] == '--help':
        print("""
功能完整性測試系統
用法: python functional_integrity_test.py [選項]

選項:
  --help          顯示此幫助信息
  --url URL       指定測試URL (默認: http://localhost:8000)

測試項目:
  1. 前端模組功能測試 (6個模組)
  2. API端點功能測試
  3. 關鍵用戶路徑測試
  4. 邊界情況測試
  5. 跨模組整合測試

範例:
  python functional_integrity_test.py
  python functional_integrity_test.py --url http://localhost:5000
        """)
        return
    
    # 解析命令行參數
    base_url = "http://localhost:8000"
    
    for i, arg in enumerate(sys.argv):
        if arg == '--url' and i + 1 < len(sys.argv):
            base_url = sys.argv[i + 1]
    
    # 創建測試器並運行測試
    tester = FunctionalIntegrityTester(base_url)
    results = tester.run_complete_functional_test()
    
    if results:
        # 保存結果
        report_file = tester.save_results()
        
        # 打印摘要
        tester.print_summary()
        
        print(f"\n✅ 功能完整性測試完成！詳細報告: {report_file}")
    else:
        print("\n❌ 功能完整性測試失敗")
        sys.exit(1)


if __name__ == "__main__":
    main()