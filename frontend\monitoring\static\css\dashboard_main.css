/* 統一監控儀表板 - 主要樣式 */

/* 全域重置和基礎樣式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

/* ===== 主題系統 ===== */
/* 淺色主題 (預設) */
:root {
    /* 主題基礎顏色 */
    --theme-bg-primary: #f8f9fa;
    --theme-bg-secondary: #ffffff;
    --theme-bg-tertiary: #e9ecef;
    --theme-bg-quaternary: #f1f3f4;
    --theme-text-primary: #2c3e50;
    --theme-text-secondary: #6c757d;
    --theme-text-tertiary: #95a5a6;
    --theme-text-light: #ffffff;
    --theme-border-color: #dee2e6;
    --theme-border-light: #f8f9fa;
    --theme-shadow-light: 0 2px 4px rgba(0,0,0,0.1);
    --theme-shadow-medium: 0 4px 8px rgba(0,0,0,0.15);
    --theme-shadow-heavy: 0 8px 16px rgba(0,0,0,0.2);
    --theme-shadow-focus: 0 0 0 3px rgba(52, 152, 219, 0.25);
    
    /* 語義化顏色 */
    --primary-color: #2c3e50;
    --secondary-color: #3498db;
    --success-color: #27ae60;
    --warning-color: #f39c12;
    --error-color: #e74c3c;
    --critical-color: #c0392b;
    --info-color: #17a2b8;
    --muted-color: #6c757d;
    
    /* 狀態顏色變體 */
    --success-light: #d4edda;
    --warning-light: #fff3cd;
    --error-light: #f8d7da;
    --info-light: #d1ecf1;
    
    /* 背景顏色 - 使用主題變數 */
    --bg-primary: var(--theme-bg-primary);
    --bg-secondary: var(--theme-bg-secondary);
    --bg-tertiary: var(--theme-bg-tertiary);
    --bg-quaternary: var(--theme-bg-quaternary);
    --bg-dark: #343a40;
    
    /* 文字顏色 - 使用主題變數 */
    --text-primary: var(--theme-text-primary);
    --text-secondary: var(--theme-text-secondary);
    --text-tertiary: var(--theme-text-tertiary);
    --text-light: var(--theme-text-light);
    
    /* 邊框和陰影 - 使用主題變數 */
    --border-color: var(--theme-border-color);
    --border-light: var(--theme-border-light);
    --shadow-light: var(--theme-shadow-light);
    --shadow-medium: var(--theme-shadow-medium);
    --shadow-heavy: var(--theme-shadow-heavy);
    --shadow-focus: var(--theme-shadow-focus);
    
    /* 間距系統 */
    --spacing-xs: 0.25rem;    /* 4px */
    --spacing-sm: 0.5rem;     /* 8px */
    --spacing-md: 1rem;       /* 16px */
    --spacing-lg: 1.5rem;     /* 24px */
    --spacing-xl: 2rem;       /* 32px */
    --spacing-xxl: 3rem;      /* 48px */
    --spacing-xxxl: 4rem;     /* 64px */
    
    /* 字體系統 */
    --font-xs: 0.75rem;       /* 12px */
    --font-sm: 0.875rem;      /* 14px */
    --font-md: 1rem;          /* 16px */
    --font-lg: 1.125rem;      /* 18px */
    --font-xl: 1.25rem;       /* 20px */
    --font-xxl: 1.5rem;       /* 24px */
    --font-xxxl: 2rem;        /* 32px */
    
    /* 字體權重 */
    --font-weight-light: 300;
    --font-weight-normal: 400;
    --font-weight-medium: 500;
    --font-weight-semibold: 600;
    --font-weight-bold: 700;
    
    /* 行高 */
    --line-height-tight: 1.25;
    --line-height-normal: 1.5;
    --line-height-relaxed: 1.75;
    
    /* 邊框圓角 */
    --border-radius-sm: 4px;
    --border-radius-md: 8px;
    --border-radius-lg: 12px;
    --border-radius-xl: 16px;
    --border-radius-full: 9999px;
    
    /* 動畫時間 */
    --transition-fast: 0.15s;
    --transition-normal: 0.3s;
    --transition-slow: 0.5s;
    
    /* Z-index 層級 */
    --z-dropdown: 1000;
    --z-sticky: 1020;
    --z-fixed: 1030;
    --z-modal-backdrop: 1040;
    --z-modal: 1050;
    --z-popover: 1060;
    --z-tooltip: 1070;
    
    /* 斷點 */
    --breakpoint-sm: 576px;
    --breakpoint-md: 768px;
    --breakpoint-lg: 992px;
    --breakpoint-xl: 1200px;
    --breakpoint-xxl: 1400px;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
    background-color: var(--bg-primary);
    color: var(--text-primary);
    line-height: 1.6;
    font-size: var(--font-md);
}

/* 頂部導航欄 */
.dashboard-header {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    color: var(--text-light);
    padding: var(--spacing-md) 0;
    box-shadow: var(--shadow-medium);
    position: sticky;
    top: 0;
    z-index: 1000;
}

.header-container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 var(--spacing-lg);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.header-left {
    display: flex;
    align-items: center;
    gap: var(--spacing-lg);
}

.dashboard-title {
    font-size: var(--font-xxl);
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.title-icon {
    font-size: 1.8rem;
}

.system-status {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    background: rgba(255, 255, 255, 0.1);
    padding: var(--spacing-sm) var(--spacing-md);
    border-radius: var(--border-radius-md);
    font-size: var(--font-sm);
}

.status-indicator {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: var(--success-color);
    animation: pulse 2s infinite;
}

.status-indicator.warning {
    background: var(--warning-color);
}

.status-indicator.error {
    background: var(--error-color);
}

.header-right {
    display: flex;
    align-items: center;
    gap: var(--spacing-lg);
    font-size: var(--font-sm);
}

.header-controls {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
}

.last-update, .connection-status {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.connection-indicator {
    width: 6px;
    height: 6px;
    border-radius: 50%;
    background: var(--success-color);
}

.connection-indicator.disconnected {
    background: var(--error-color);
}

/* 告警橫幅 */
.alert-banner {
    background: var(--error-color);
    color: var(--text-light);
    padding: var(--spacing-sm) 0;
    animation: slideDown var(--transition-normal) ease-out;
}

.alert-container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 var(--spacing-lg);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.alert-close {
    background: none;
    border: none;
    color: var(--text-light);
    font-size: var(--font-xl);
    cursor: pointer;
    padding: var(--spacing-xs);
    border-radius: var(--border-radius-sm);
    transition: background-color var(--transition-fast);
}

.alert-close:hover {
    background: rgba(255, 255, 255, 0.2);
}

/* 主要內容區域 */
.dashboard-main {
    padding: var(--spacing-xl) 0;
}

.dashboard-container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 var(--spacing-lg);
}

/* 概覽統計區域 */
.overview-section {
    margin-bottom: var(--spacing-xxl);
}

.overview-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: var(--spacing-lg);
}

.overview-card {
    background: var(--bg-secondary);
    border-radius: var(--border-radius-lg);
    padding: var(--spacing-lg);
    box-shadow: var(--shadow-light);
    transition: transform var(--transition-fast), box-shadow var(--transition-fast);
    border-left: 4px solid var(--secondary-color);
}

.overview-card:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-medium);
}

.overview-card.email-overview {
    border-left-color: var(--info-color);
}

.overview-card.dramatiq-overview {
    border-left-color: var(--secondary-color);
}

.overview-card.system-overview {
    border-left-color: var(--success-color);
}

.overview-card.business-overview {
    border-left-color: var(--warning-color);
}

.card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-md);
}

.card-header h3 {
    font-size: var(--font-lg);
    font-weight: 600;
    color: var(--text-primary);
}

.card-status {
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--border-radius-sm);
    font-size: var(--font-xs);
    font-weight: 500;
    background: var(--success-color);
    color: var(--text-light);
}

.card-status.warning {
    background: var(--warning-color);
}

.card-status.error {
    background: var(--error-color);
}

.metric-row {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: var(--spacing-md);
}

.metric-item {
    text-align: center;
}

.metric-label {
    display: block;
    font-size: var(--font-xs);
    color: var(--text-secondary);
    margin-bottom: var(--spacing-xs);
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.metric-value {
    display: block;
    font-size: var(--font-xl);
    font-weight: 700;
    color: var(--text-primary);
}

.metric-value.error {
    color: var(--error-color);
}

.metric-value.warning {
    color: var(--warning-color);
}

.metric-value.success {
    color: var(--success-color);
}

/* 監控區域 */
.monitoring-section {
    margin-bottom: var(--spacing-xxl);
}

.monitoring-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: var(--spacing-lg);
}

.monitoring-panel {
    background: var(--bg-secondary);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-light);
    overflow: hidden;
    transition: transform var(--transition-fast), box-shadow var(--transition-fast);
}

.monitoring-panel:hover {
    transform: translateY(-1px);
    box-shadow: var(--shadow-medium);
}

.panel-header {
    background: linear-gradient(135deg, #f8f9fa, #e9ecef);
    padding: var(--spacing-md) var(--spacing-lg);
    border-bottom: 1px solid var(--border-color);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.panel-header h3 {
    font-size: var(--font-lg);
    font-weight: 600;
    color: var(--text-primary);
}

.panel-controls {
    display: flex;
    gap: var(--spacing-sm);
}

.refresh-btn, .expand-btn, .clear-all-btn {
    background: none;
    border: 1px solid var(--border-color);
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--border-radius-sm);
    cursor: pointer;
    font-size: var(--font-sm);
    transition: all var(--transition-fast);
}

.refresh-btn:hover, .expand-btn:hover, .clear-all-btn:hover {
    background: var(--secondary-color);
    color: var(--text-light);
    border-color: var(--secondary-color);
}

.panel-content {
    padding: var(--spacing-lg);
}

/* 載入中遮罩 */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.7);
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    z-index: 9999;
    opacity: 0;
    visibility: hidden;
    transition: all var(--transition-normal);
}

.loading-overlay.show {
    opacity: 1;
    visibility: visible;
}

.loading-spinner {
    width: 40px;
    height: 40px;
    border: 4px solid rgba(255, 255, 255, 0.3);
    border-top: 4px solid var(--text-light);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: var(--spacing-md);
}

.loading-text {
    color: var(--text-light);
    font-size: var(--font-lg);
}

/* 動畫 */
@keyframes pulse {
    0%, 100% {
        opacity: 1;
    }
    50% {
        opacity: 0.5;
    }
}

@keyframes spin {
    0% {
        transform: rotate(0deg);
    }
    100% {
        transform: rotate(360deg);
    }
}

@keyframes slideDown {
    from {
        transform: translateY(-100%);
    }
    to {
        transform: translateY(0);
    }
}

/* 響應式設計 */
@media (max-width: 1200px) {
    .dashboard-container {
        padding: 0 var(--spacing-md);
    }
    
    .overview-cards {
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    }
    
    .monitoring-grid {
        grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    }
}

@media (max-width: 768px) {
    .header-container {
        flex-direction: column;
        gap: var(--spacing-md);
        text-align: center;
    }
    
    .header-left, .header-right {
        flex-direction: column;
        gap: var(--spacing-sm);
    }
    
    .dashboard-title {
        font-size: var(--font-xl);
    }
    
    .overview-cards {
        grid-template-columns: 1fr;
    }
    
    .monitoring-grid {
        grid-template-columns: 1fr;
    }
    
    .metric-row {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .panel-header {
        flex-direction: column;
        gap: var(--spacing-sm);
        text-align: center;
    }
}

@media (max-width: 480px) {
    .dashboard-container {
        padding: 0 var(--spacing-sm);
    }
    
    .overview-card, .monitoring-panel {
        margin: 0 -var(--spacing-xs);
    }
    
    .metric-row {
        grid-template-columns: 1fr;
        gap: var(--spacing-sm);
    }
    
    .metric-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: var(--spacing-sm);
        background: var(--bg-primary);
        border-radius: var(--border-radius-sm);
    }
    
    .metric-label {
        margin-bottom: 0;
    }
}

/* ===== 深色主題定義 ===== */
[data-theme="dark"] {
    /* 深色主題基礎顏色 */
    --theme-bg-primary: #1a1a1a;
    --theme-bg-secondary: #2d2d2d;
    --theme-bg-tertiary: #3a3a3a;
    --theme-bg-quaternary: #404040;
    --theme-text-primary: #ffffff;
    --theme-text-secondary: #b0b0b0;
    --theme-text-tertiary: #8a8a8a;
    --theme-text-light: #ffffff;
    --theme-border-color: #404040;
    --theme-border-light: #4a4a4a;
    --theme-shadow-light: 0 2px 4px rgba(0,0,0,0.3);
    --theme-shadow-medium: 0 4px 8px rgba(0,0,0,0.4);
    --theme-shadow-heavy: 0 8px 16px rgba(0,0,0,0.5);
    --theme-shadow-focus: 0 0 0 3px rgba(52, 152, 219, 0.4);
    
    /* 深色主題狀態顏色變體 */
    --success-light: rgba(39, 174, 96, 0.2);
    --warning-light: rgba(243, 156, 18, 0.2);
    --error-light: rgba(231, 76, 60, 0.2);
    --info-light: rgba(23, 162, 184, 0.2);
}

/* ===== 主題切換按鈕 ===== */
.theme-toggle {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    color: var(--text-light);
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--border-radius-md);
    cursor: pointer;
    font-size: var(--font-sm);
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    transition: all var(--transition-fast);
    min-width: 80px;
    justify-content: center;
}

.theme-toggle:hover {
    background: rgba(255, 255, 255, 0.2);
    border-color: rgba(255, 255, 255, 0.3);
    transform: translateY(-1px);
}

.theme-toggle:focus {
    outline: none;
    box-shadow: var(--shadow-focus);
}

.theme-toggle-icon {
    font-size: var(--font-md);
    transition: transform var(--transition-normal);
}

.theme-toggle:hover .theme-toggle-icon {
    transform: rotate(180deg);
}

/* 深色主題特殊樣式調整 */
[data-theme="dark"] .overview-card,
[data-theme="dark"] .monitoring-panel {
    background: var(--bg-secondary);
    border: 1px solid var(--border-color);
}

[data-theme="dark"] .panel-header {
    background: linear-gradient(135deg, var(--bg-tertiary), var(--bg-secondary));
}

[data-theme="dark"] .dashboard-header {
    background: linear-gradient(135deg, #1e3a8a, #1e40af);
}

[data-theme="dark"] .progress-text {
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.8);
}

[data-theme="dark"] .theme-toggle {
    background: rgba(255, 255, 255, 0.05);
    border-color: rgba(255, 255, 255, 0.1);
}

[data-theme="dark"] .theme-toggle:hover {
    background: rgba(255, 255, 255, 0.1);
    border-color: rgba(255, 255, 255, 0.2);
}

/* 系統偏好設定自動切換 */
@media (prefers-color-scheme: dark) {
    :root:not([data-theme]) {
        --theme-bg-primary: #1a1a1a;
        --theme-bg-secondary: #2d2d2d;
        --theme-bg-tertiary: #3a3a3a;
        --theme-text-primary: #ffffff;
        --theme-text-secondary: #b0b0b0;
        --theme-text-light: #ffffff;
        --theme-border-color: #404040;
        --theme-shadow-light: 0 2px 4px rgba(0,0,0,0.3);
        --theme-shadow-medium: 0 4px 8px rgba(0,0,0,0.4);
        --theme-shadow-heavy: 0 8px 16px rgba(0,0,0,0.5);
    }
    
    :root:not([data-theme]) .overview-card,
    :root:not([data-theme]) .monitoring-panel {
        background: var(--bg-secondary);
        border: 1px solid var(--border-color);
    }
    
    :root:not([data-theme]) .panel-header {
        background: linear-gradient(135deg, var(--bg-tertiary), var(--bg-secondary));
    }
    
    :root:not([data-theme]) .dashboard-header {
        background: linear-gradient(135deg, #1e3a8a, #1e40af);
    }
    
    :root:not([data-theme]) .progress-text {
        text-shadow: 0 1px 2px rgba(0, 0, 0, 0.8);
    }
}

/* 列印樣式 */
@media print {
    .dashboard-header, .panel-controls, .loading-overlay {
        display: none !important;
    }
    
    .overview-cards, .monitoring-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: var(--spacing-md);
    }
    
    .overview-card, .monitoring-panel {
        break-inside: avoid;
        box-shadow: none;
        border: 1px solid var(--border-color);
    }
}