#!/usr/bin/env python3
"""
直接測試 Flask 應用的功能
避免Unicode編碼問題，專注於核心功能驗證
"""

import os
import sys
import time
import traceback
from pathlib import Path

# 設定編碼環境
os.environ['PYTHONIOENCODING'] = 'utf-8'
os.environ['PYTHONUTF8'] = '1'

# 添加專案根目錄到 Python 路徑
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_flask_app_creation():
    """測試 Flask 應用創建"""
    print("Testing Flask app creation...")
    
    try:
        from frontend.app import create_app
        
        print("  Creating Flask app with 'testing' config...")
        app = create_app('testing')
        
        if app:
            print("  PASS: Flask app created successfully")
            return app, True
        else:
            print("  FAIL: Flask app creation returned None")
            return None, False
            
    except Exception as e:
        print(f"  FAIL: Flask app creation failed - {e}")
        print(f"  Traceback: {traceback.format_exc()}")
        return None, False

def test_routes_with_test_client(app):
    """使用測試客戶端測試路由"""
    print("Testing routes with test client...")
    
    if not app:
        print("  SKIP: No app available")
        return []
    
    # 測試路由
    test_routes = [
        ('/', 'Home page'),
        ('/health', 'Health check'),
        ('/email', 'Email module'),
        ('/analytics', 'Analytics module'),
        ('/files', 'File management'),
        ('/eqc', 'EQC module'),
        ('/tasks', 'Task management'),
        ('/monitoring', 'Monitoring module')
    ]
    
    results = []
    
    with app.test_client() as client:
        for route, description in test_routes:
            try:
                print(f"  Testing {description}: {route}")
                response = client.get(route)
                
                if response.status_code < 400:
                    print(f"    PASS: Status {response.status_code}")
                    results.append((route, True, response.status_code, ''))
                else:
                    print(f"    FAIL: Status {response.status_code}")
                    results.append((route, False, response.status_code, 'HTTP error'))
                    
            except Exception as e:
                print(f"    FAIL: Exception - {e}")
                results.append((route, False, 0, str(e)))
    
    return results

def test_module_imports():
    """測試模組導入"""
    print("Testing module imports...")
    
    modules_to_test = [
        ('frontend.config', 'Frontend config'),
        ('frontend.app', 'Frontend app'),
        ('frontend.email.routes.email_routes', 'Email routes'),
        ('frontend.analytics.routes.analytics_routes', 'Analytics routes'),
        ('frontend.file_management.routes.file_routes', 'File routes'),
        ('frontend.eqc.routes.eqc_routes', 'EQC routes'),
        ('frontend.tasks.routes.task_routes', 'Task routes'),
        ('frontend.monitoring.routes.monitoring_routes', 'Monitoring routes'),
    ]
    
    results = []
    
    for module_name, description in modules_to_test:
        try:
            print(f"  Testing {description}: {module_name}")
            __import__(module_name)
            print(f"    PASS: Module imported successfully")
            results.append((module_name, True, description))
        except Exception as e:
            print(f"    FAIL: Import failed - {e}")
            results.append((module_name, False, f"{description} - {e}"))
    
    return results

def test_directory_structure():
    """測試目錄結構"""
    print("Testing directory structure...")
    
    required_paths = [
        ('frontend/app.py', 'Main Flask app'),
        ('frontend/config.py', 'Flask config'),
        ('frontend/shared/templates', 'Templates directory'),
        ('frontend/shared/static', 'Static files directory'),
        ('frontend/email/routes', 'Email routes directory'),
        ('frontend/analytics/routes', 'Analytics routes directory'),
        ('frontend/file_management/routes', 'File management routes directory'),
        ('frontend/eqc/routes', 'EQC routes directory'),
        ('frontend/tasks/routes', 'Tasks routes directory'),
        ('frontend/monitoring/routes', 'Monitoring routes directory'),
    ]
    
    results = []
    
    for path_str, description in required_paths:
        path = project_root / path_str
        if path.exists():
            print(f"  PASS: {description} - {path}")
            results.append((path_str, True, description))
        else:
            print(f"  FAIL: {description} - Missing: {path}")
            results.append((path_str, False, f"{description} - Missing"))
    
    return results

def generate_report(structure_results, import_results, app_test_result, route_results):
    """生成測試報告"""
    print("\n" + "=" * 60)
    print("TEST RESULTS SUMMARY")
    print("=" * 60)
    
    # 統計結果
    total_tests = 0
    passed_tests = 0
    
    # 目錄結構測試
    print(f"\n1. Directory Structure Tests:")
    for path, success, desc in structure_results:
        status = "PASS" if success else "FAIL"
        print(f"   {status}: {desc}")
        total_tests += 1
        if success:
            passed_tests += 1
    
    # 模組導入測試
    print(f"\n2. Module Import Tests:")
    for module, success, desc in import_results:
        status = "PASS" if success else "FAIL"
        print(f"   {status}: {desc}")
        total_tests += 1
        if success:
            passed_tests += 1
    
    # Flask 應用測試
    print(f"\n3. Flask App Creation:")
    app, success = app_test_result
    status = "PASS" if success else "FAIL"
    print(f"   {status}: Flask app creation")
    total_tests += 1
    if success:
        passed_tests += 1
    
    # 路由測試
    print(f"\n4. Route Tests:")
    for route, success, status_code, error in route_results:
        status_str = "PASS" if success else "FAIL"
        print(f"   {status_str}: {route} (Status: {status_code})")
        total_tests += 1
        if success:
            passed_tests += 1
    
    # 總結
    success_rate = (passed_tests / total_tests * 100) if total_tests > 0 else 0
    print(f"\nOVERALL SUMMARY:")
    print(f"Total Tests: {total_tests}")
    print(f"Passed: {passed_tests}")
    print(f"Failed: {total_tests - passed_tests}")
    print(f"Success Rate: {success_rate:.1f}%")
    
    # 保存結果到JSON
    import json
    
    report_data = {
        "timestamp": time.strftime("%Y-%m-%d %H:%M:%S"),
        "summary": {
            "total_tests": total_tests,
            "passed_tests": passed_tests,
            "failed_tests": total_tests - passed_tests,
            "success_rate": success_rate
        },
        "details": {
            "directory_structure": [
                {"path": path, "success": success, "description": desc}
                for path, success, desc in structure_results
            ],
            "module_imports": [
                {"module": module, "success": success, "description": desc}
                for module, success, desc in import_results
            ],
            "flask_app": {
                "success": app_test_result[1],
                "description": "Flask app creation"
            },
            "routes": [
                {"route": route, "success": success, "status_code": status_code, "error": error}
                for route, success, status_code, error in route_results
            ]
        }
    }
    
    report_file = project_root / "direct_flask_test_results.json"
    with open(report_file, 'w', encoding='utf-8') as f:
        json.dump(report_data, f, ensure_ascii=False, indent=2)
    
    print(f"\nDetailed results saved to: {report_file}")
    
    return success_rate >= 80  # 80% 通過率視為成功

def main():
    """主函數"""
    print("Direct Flask Application Test")
    print("=" * 60)
    
    # 1. 測試目錄結構
    structure_results = test_directory_structure()
    
    # 2. 測試模組導入
    import_results = test_module_imports()
    
    # 3. 測試 Flask 應用創建
    app_test_result = test_flask_app_creation()
    
    # 4. 測試路由（如果應用創建成功）
    if app_test_result[1]:  # 如果應用創建成功
        route_results = test_routes_with_test_client(app_test_result[0])
    else:
        route_results = []
        print("SKIP: Route testing (Flask app creation failed)")
    
    # 5. 生成報告
    overall_success = generate_report(structure_results, import_results, app_test_result, route_results)
    
    if overall_success:
        print("\nCONCLUSION: Tests mostly PASSED. The integrated service is ready for deployment.")
        return 0
    else:
        print("\nCONCLUSION: Tests FAILED. Please fix the issues before deployment.")
        return 1

if __name__ == "__main__":
    sys.exit(main())