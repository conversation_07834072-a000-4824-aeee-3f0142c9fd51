/**
 * EQC 並發監控前端組件
 * 整合到統一監控儀表板，提供實時並發狀態監控
 */

class EQCConcurrentMonitor {
    constructor() {
        this.updateInterval = 5000; // 5秒更新間隔
        this.isMonitoring = false;
        this.intervalId = null;
        this.lastUpdate = null;
        
        // 監控數據
        this.currentMetrics = null;
        this.alertThresholds = {
            concurrent_users_warning: 8,
            concurrent_users_critical: 12,
            cpu_warning: 75,
            cpu_critical: 85,
            memory_warning: 80,
            memory_critical: 90,
            queue_warning: 10,
            queue_critical: 20
        };
        
        this.init();
    }
    
    init() {
        console.log('🔧 初始化 EQC 並發監控器');
        this.createMonitoringUI();
        this.bindEvents();
    }
    
    createMonitoringUI() {
        // 檢查是否已存在監控面板
        if (document.getElementById('eqc-concurrent-monitor')) {
            return;
        }
        
        const monitorHTML = `
            <div id="eqc-concurrent-monitor" class="monitoring-panel">
                <div class="panel-header">
                    <h3>🚀 EQC 並發監控</h3>
                    <div class="panel-controls">
                        <button id="toggle-monitoring" class="btn btn-primary">開始監控</button>
                        <button id="refresh-data" class="btn btn-secondary">刷新</button>
                        <span id="last-update" class="last-update">未更新</span>
                    </div>
                </div>
                
                <div class="monitoring-content">
                    <!-- 系統狀態概覽 -->
                    <div class="status-overview">
                        <div class="status-card" id="system-status">
                            <div class="status-icon">⚡</div>
                            <div class="status-info">
                                <div class="status-title">系統狀態</div>
                                <div class="status-value" id="system-load-level">正常</div>
                            </div>
                        </div>
                        
                        <div class="status-card" id="concurrent-users">
                            <div class="status-icon">👥</div>
                            <div class="status-info">
                                <div class="status-title">並發用戶</div>
                                <div class="status-value" id="concurrent-users-count">0</div>
                            </div>
                        </div>
                        
                        <div class="status-card" id="active-sessions">
                            <div class="status-icon">📋</div>
                            <div class="status-info">
                                <div class="status-title">活躍會話</div>
                                <div class="status-value" id="active-sessions-count">0</div>
                            </div>
                        </div>
                        
                        <div class="status-card" id="queue-depth">
                            <div class="status-icon">📊</div>
                            <div class="status-info">
                                <div class="status-title">隊列深度</div>
                                <div class="status-value" id="queue-depth-count">0</div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 系統資源監控 -->
                    <div class="resource-monitoring">
                        <h4>系統資源</h4>
                        <div class="resource-bars">
                            <div class="resource-item">
                                <label>CPU 使用率</label>
                                <div class="progress-bar">
                                    <div class="progress-fill" id="cpu-progress"></div>
                                    <span class="progress-text" id="cpu-text">0%</span>
                                </div>
                            </div>
                            
                            <div class="resource-item">
                                <label>記憶體使用率</label>
                                <div class="progress-bar">
                                    <div class="progress-fill" id="memory-progress"></div>
                                    <span class="progress-text" id="memory-text">0%</span>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 告警和建議 -->
                    <div class="alerts-section">
                        <h4>系統告警</h4>
                        <div id="alerts-container" class="alerts-container">
                            <div class="no-alerts">暫無告警</div>
                        </div>
                    </div>
                    
                    <!-- 過載預防建議 -->
                    <div class="recommendations-section">
                        <h4>過載預防建議</h4>
                        <div id="recommendations-container" class="recommendations-container">
                            <div class="no-recommendations">系統運行正常</div>
                        </div>
                    </div>
                </div>
            </div>
        `;
        
        // 添加到頁面
        const container = document.querySelector('.main-content') || document.body;
        container.insertAdjacentHTML('beforeend', monitorHTML);
        
        // 添加樣式
        this.addMonitoringStyles();
    }
    
    addMonitoringStyles() {
        const style = document.createElement('style');
        style.textContent = `
            .monitoring-panel {
                background: white;
                border-radius: 8px;
                box-shadow: 0 2px 10px rgba(0,0,0,0.1);
                margin: 20px 0;
                padding: 20px;
            }
            
            .panel-header {
                display: flex;
                justify-content: space-between;
                align-items: center;
                margin-bottom: 20px;
                border-bottom: 1px solid #eee;
                padding-bottom: 15px;
            }
            
            .panel-controls {
                display: flex;
                gap: 10px;
                align-items: center;
            }
            
            .last-update {
                font-size: 12px;
                color: #666;
                margin-left: 10px;
            }
            
            .status-overview {
                display: grid;
                grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
                gap: 15px;
                margin-bottom: 25px;
            }
            
            .status-card {
                display: flex;
                align-items: center;
                padding: 15px;
                border-radius: 6px;
                border: 1px solid #e0e0e0;
                transition: all 0.3s ease;
            }
            
            .status-card.normal { border-left: 4px solid #4CAF50; }
            .status-card.warning { border-left: 4px solid #FF9800; }
            .status-card.critical { border-left: 4px solid #F44336; }
            
            .status-icon {
                font-size: 24px;
                margin-right: 15px;
            }
            
            .status-title {
                font-size: 12px;
                color: #666;
                margin-bottom: 5px;
            }
            
            .status-value {
                font-size: 18px;
                font-weight: bold;
                color: #333;
            }
            
            .resource-monitoring {
                margin-bottom: 25px;
            }
            
            .resource-item {
                margin-bottom: 15px;
            }
            
            .resource-item label {
                display: block;
                margin-bottom: 5px;
                font-size: 14px;
                color: #555;
            }
            
            .progress-bar {
                position: relative;
                height: 20px;
                background: #f0f0f0;
                border-radius: 10px;
                overflow: hidden;
            }
            
            .progress-fill {
                height: 100%;
                background: linear-gradient(90deg, #4CAF50, #8BC34A);
                border-radius: 10px;
                transition: width 0.3s ease;
                width: 0%;
            }
            
            .progress-fill.warning {
                background: linear-gradient(90deg, #FF9800, #FFC107);
            }
            
            .progress-fill.critical {
                background: linear-gradient(90deg, #F44336, #FF5722);
            }
            
            .progress-text {
                position: absolute;
                top: 50%;
                left: 50%;
                transform: translate(-50%, -50%);
                font-size: 12px;
                font-weight: bold;
                color: #333;
            }
            
            .alerts-container, .recommendations-container {
                min-height: 60px;
                padding: 10px;
                border: 1px solid #e0e0e0;
                border-radius: 4px;
                background: #fafafa;
            }
            
            .alert-item {
                padding: 8px 12px;
                margin-bottom: 8px;
                border-radius: 4px;
                font-size: 14px;
            }
            
            .alert-warning {
                background: #fff3cd;
                border: 1px solid #ffeaa7;
                color: #856404;
            }
            
            .alert-critical {
                background: #f8d7da;
                border: 1px solid #f5c6cb;
                color: #721c24;
            }
            
            .recommendation-item {
                padding: 8px 12px;
                margin-bottom: 8px;
                background: #d1ecf1;
                border: 1px solid #bee5eb;
                border-radius: 4px;
                color: #0c5460;
                font-size: 14px;
            }
            
            .no-alerts, .no-recommendations {
                text-align: center;
                color: #666;
                font-style: italic;
                padding: 20px;
            }
        `;
        document.head.appendChild(style);
    }
    
    bindEvents() {
        // 開始/停止監控按鈕
        document.getElementById('toggle-monitoring')?.addEventListener('click', () => {
            this.toggleMonitoring();
        });
        
        // 刷新數據按鈕
        document.getElementById('refresh-data')?.addEventListener('click', () => {
            this.refreshData();
        });
    }
    
    toggleMonitoring() {
        if (this.isMonitoring) {
            this.stopMonitoring();
        } else {
            this.startMonitoring();
        }
    }
    
    startMonitoring() {
        console.log('🚀 開始 EQC 並發監控');
        this.isMonitoring = true;
        
        // 更新按鈕狀態
        const toggleBtn = document.getElementById('toggle-monitoring');
        if (toggleBtn) {
            toggleBtn.textContent = '停止監控';
            toggleBtn.className = 'btn btn-danger';
        }
        
        // 立即更新一次
        this.refreshData();
        
        // 設置定時更新
        this.intervalId = setInterval(() => {
            this.refreshData();
        }, this.updateInterval);
    }
    
    stopMonitoring() {
        console.log('🛑 停止 EQC 並發監控');
        this.isMonitoring = false;
        
        // 清除定時器
        if (this.intervalId) {
            clearInterval(this.intervalId);
            this.intervalId = null;
        }
        
        // 更新按鈕狀態
        const toggleBtn = document.getElementById('toggle-monitoring');
        if (toggleBtn) {
            toggleBtn.textContent = '開始監控';
            toggleBtn.className = 'btn btn-primary';
        }
    }
    
    async refreshData() {
        try {
            console.log('📊 刷新 EQC 並發監控數據');
            
            // 獲取並發狀態
            const concurrentResponse = await fetch('/api/monitoring/eqc/concurrent-status');
            const concurrentData = await concurrentResponse.json();
            
            // 獲取過載預防建議
            const preventionResponse = await fetch('/api/monitoring/eqc/overload-prevention');
            const preventionData = await preventionResponse.json();
            
            if (concurrentData.status === 'success') {
                this.currentMetrics = concurrentData.data;
                this.updateUI(concurrentData.data, preventionData.data);
                this.lastUpdate = new Date();
                
                // 更新最後更新時間
                const lastUpdateEl = document.getElementById('last-update');
                if (lastUpdateEl) {
                    lastUpdateEl.textContent = `最後更新: ${this.lastUpdate.toLocaleTimeString()}`;
                }
            }
            
        } catch (error) {
            console.error('❌ 刷新監控數據失敗:', error);
            this.showError('無法獲取監控數據');
        }
    }
    
    updateUI(concurrentData, preventionData) {
        // 更新系統狀態
        this.updateSystemStatus(concurrentData);
        
        // 更新資源監控
        this.updateResourceMonitoring(concurrentData);
        
        // 更新告警
        this.updateAlerts(concurrentData);
        
        // 更新建議
        this.updateRecommendations(preventionData);
    }
    
    updateSystemStatus(data) {
        const { concurrent_status, performance_metrics } = data;
        
        // 系統負載等級
        const loadLevelEl = document.getElementById('system-load-level');
        const systemStatusCard = document.getElementById('system-status');
        if (loadLevelEl && systemStatusCard) {
            loadLevelEl.textContent = this.getLoadLevelText(concurrent_status.load_level);
            systemStatusCard.className = `status-card ${this.getStatusClass(concurrent_status.load_level)}`;
        }
        
        // 並發用戶數
        const concurrentUsersEl = document.getElementById('concurrent-users-count');
        const concurrentUsersCard = document.getElementById('concurrent-users');
        if (concurrentUsersEl && concurrentUsersCard) {
            concurrentUsersEl.textContent = concurrent_status.concurrent_users;
            concurrentUsersCard.className = `status-card ${this.getUsersStatusClass(concurrent_status.concurrent_users)}`;
        }
        
        // 活躍會話數
        const activeSessionsEl = document.getElementById('active-sessions-count');
        if (activeSessionsEl) {
            activeSessionsEl.textContent = concurrent_status.active_sessions;
        }
        
        // 隊列深度
        const queueDepthEl = document.getElementById('queue-depth-count');
        const queueDepthCard = document.getElementById('queue-depth');
        if (queueDepthEl && queueDepthCard) {
            queueDepthEl.textContent = performance_metrics.queue_depth;
            queueDepthCard.className = `status-card ${this.getQueueStatusClass(performance_metrics.queue_depth)}`;
        }
    }
    
    updateResourceMonitoring(data) {
        const { system_resources } = data;
        
        // CPU 使用率
        this.updateProgressBar('cpu', system_resources.cpu_usage_percent, this.alertThresholds.cpu_warning, this.alertThresholds.cpu_critical);
        
        // 記憶體使用率
        this.updateProgressBar('memory', system_resources.memory_usage_percent, this.alertThresholds.memory_warning, this.alertThresholds.memory_critical);
    }
    
    updateProgressBar(type, value, warningThreshold, criticalThreshold) {
        const progressEl = document.getElementById(`${type}-progress`);
        const textEl = document.getElementById(`${type}-text`);
        
        if (progressEl && textEl) {
            progressEl.style.width = `${value}%`;
            textEl.textContent = `${value.toFixed(1)}%`;
            
            // 設置顏色
            progressEl.className = 'progress-fill';
            if (value >= criticalThreshold) {
                progressEl.classList.add('critical');
            } else if (value >= warningThreshold) {
                progressEl.classList.add('warning');
            }
        }
    }
    
    updateAlerts(data) {
        const alertsContainer = document.getElementById('alerts-container');
        if (!alertsContainer) return;
        
        const warnings = data.health_status.warnings || [];
        
        if (warnings.length === 0) {
            alertsContainer.innerHTML = '<div class="no-alerts">暫無告警</div>';
        } else {
            const alertsHTML = warnings.map(warning => 
                `<div class="alert-item alert-warning">⚠️ ${warning}</div>`
            ).join('');
            alertsContainer.innerHTML = alertsHTML;
        }
    }
    
    updateRecommendations(preventionData) {
        const recommendationsContainer = document.getElementById('recommendations-container');
        if (!recommendationsContainer || !preventionData) return;
        
        const actions = preventionData.prevention_actions || [];
        
        if (actions.length === 0) {
            recommendationsContainer.innerHTML = '<div class="no-recommendations">系統運行正常</div>';
        } else {
            const recommendationsHTML = actions.map(action => 
                `<div class="recommendation-item">💡 ${action}</div>`
            ).join('');
            recommendationsContainer.innerHTML = recommendationsHTML;
        }
    }
    
    getLoadLevelText(level) {
        const levels = {
            'low': '正常',
            'medium': '中等負載',
            'high': '高負載'
        };
        return levels[level] || '未知';
    }
    
    getStatusClass(level) {
        const classes = {
            'low': 'normal',
            'medium': 'warning',
            'high': 'critical'
        };
        return classes[level] || 'normal';
    }
    
    getUsersStatusClass(count) {
        if (count >= this.alertThresholds.concurrent_users_critical) return 'critical';
        if (count >= this.alertThresholds.concurrent_users_warning) return 'warning';
        return 'normal';
    }
    
    getQueueStatusClass(depth) {
        if (depth >= this.alertThresholds.queue_critical) return 'critical';
        if (depth >= this.alertThresholds.queue_warning) return 'warning';
        return 'normal';
    }
    
    showError(message) {
        console.error('❌ EQC 監控錯誤:', message);
        // 可以添加錯誤提示 UI
    }
    
    destroy() {
        this.stopMonitoring();
        const monitorPanel = document.getElementById('eqc-concurrent-monitor');
        if (monitorPanel) {
            monitorPanel.remove();
        }
    }
}

// 全局實例
window.eqcConcurrentMonitor = null;

// 初始化函數
function initEQCConcurrentMonitor() {
    if (!window.eqcConcurrentMonitor) {
        window.eqcConcurrentMonitor = new EQCConcurrentMonitor();
        console.log('✅ EQC 並發監控器已初始化');
    }
    return window.eqcConcurrentMonitor;
}

// 自動初始化（如果頁面已加載）
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', initEQCConcurrentMonitor);
} else {
    initEQCConcurrentMonitor();
}
