/**
 * Reports Module
 * 報告模組 JavaScript
 */

class ReportsManager {
    constructor() {
        this.currentReport = null;
        this.reportData = {};
        this.charts = {};
        this.filters = {
            dateFrom: '',
            dateTo: '',
            reportType: 'summary',
            category: 'all'
        };
        
        this.init();
    }
    
    init() {
        console.log('Reports Manager: Initializing...');
        this.bindEvents();
        this.loadReportsList();
        this.initializeDatePickers();
    }
    
    bindEvents() {
        // 報告類型選擇
        document.addEventListener('change', (e) => {
            if (e.target.matches('.report-type-select')) {
                this.handleReportTypeChange(e);
            }
        });
        
        // 生成報告按鈕
        document.addEventListener('click', (e) => {
            if (e.target.matches('.generate-report-btn')) {
                this.handleGenerateReport(e);
            }
        });
        
        // 匯出按鈕
        document.addEventListener('click', (e) => {
            if (e.target.matches('.export-report-btn')) {
                this.handleExportReport(e);
            }
        });
        
        // 預覽按鈕
        document.addEventListener('click', (e) => {
            if (e.target.matches('.preview-report-btn')) {
                this.handlePreviewReport(e);
            }
        });
        
        // 篩選器變更
        document.addEventListener('change', (e) => {
            if (e.target.matches('.report-filter')) {
                this.handleFilterChange(e);
            }
        });
    }
    
    initializeDatePickers() {
        // 設定預設日期範圍（最近30天）
        const today = new Date();
        const thirtyDaysAgo = new Date(today.getTime() - (30 * 24 * 60 * 60 * 1000));
        
        const dateFromInput = document.querySelector('#dateFrom');
        const dateToInput = document.querySelector('#dateTo');
        
        if (dateFromInput) {
            dateFromInput.value = this.formatDate(thirtyDaysAgo);
            this.filters.dateFrom = dateFromInput.value;
        }
        
        if (dateToInput) {
            dateToInput.value = this.formatDate(today);
            this.filters.dateTo = dateToInput.value;
        }
    }
    
    async loadReportsList() {
        try {
            const response = await fetch('/analytics/api/reports/list');
            
            if (response.ok) {
                const reports = await response.json();
                this.updateReportsList(reports);
            }
        } catch (error) {
            console.error('Failed to load reports list:', error);
        }
    }
    
    updateReportsList(reports) {
        const container = document.querySelector('.reports-list');
        if (!container) return;
        
        container.innerHTML = '';
        
        reports.forEach(report => {
            const reportItem = this.createReportItem(report);
            container.appendChild(reportItem);
        });
    }
    
    createReportItem(report) {
        const item = document.createElement('div');
        item.className = 'report-item';
        item.dataset.reportId = report.id;
        
        item.innerHTML = `
            <div class="report-header">
                <h4 class="report-title">${report.title}</h4>
                <span class="report-date">${this.formatDateTime(report.created_at)}</span>
            </div>
            <div class="report-info">
                <span class="report-type">${report.type}</span>
                <span class="report-size">${report.size || 'N/A'}</span>
            </div>
            <div class="report-actions">
                <button class="btn btn-sm btn-primary preview-report-btn" data-report-id="${report.id}">預覽</button>
                <button class="btn btn-sm btn-secondary export-report-btn" data-report-id="${report.id}">下載</button>
            </div>
        `;
        
        return item;
    }
    
    handleReportTypeChange(e) {
        const reportType = e.target.value;
        this.filters.reportType = reportType;
        
        // 更新表單欄位根據報告類型
        this.updateFormFields(reportType);
    }
    
    updateFormFields(reportType) {
        const fieldsContainer = document.querySelector('.report-fields');
        if (!fieldsContainer) return;
        
        // 根據報告類型顯示不同的欄位
        const typeConfigs = {
            'summary': {
                fields: ['dateRange', 'category'],
                title: '摘要報告'
            },
            'detailed': {
                fields: ['dateRange', 'category', 'includeCharts', 'includeData'],
                title: '詳細報告'
            },
            'financial': {
                fields: ['dateRange', 'currency', 'includeBreakdown'],
                title: '財務報告'
            },
            'performance': {
                fields: ['dateRange', 'metrics', 'benchmark'],
                title: '效能報告'
            }
        };
        
        const config = typeConfigs[reportType] || typeConfigs.summary;
        this.renderFormFields(config);
    }
    
    renderFormFields(config) {
        const fieldsContainer = document.querySelector('.report-fields');
        if (!fieldsContainer) return;
        
        fieldsContainer.innerHTML = `
            <h5>${config.title} 設定</h5>
            ${config.fields.map(field => this.renderField(field)).join('')}
        `;
    }
    
    renderField(fieldName) {
        const fieldTemplates = {
            'dateRange': `
                <div class="form-group">
                    <label>日期範圍</label>
                    <div class="date-range">
                        <input type="date" id="dateFrom" class="form-control report-filter" data-filter="dateFrom">
                        <span>至</span>
                        <input type="date" id="dateTo" class="form-control report-filter" data-filter="dateTo">
                    </div>
                </div>
            `,
            'category': `
                <div class="form-group">
                    <label>分類</label>
                    <select class="form-control report-filter" data-filter="category">
                        <option value="all">全部</option>
                        <option value="email">電子郵件</option>
                        <option value="analytics">分析</option>
                        <option value="tasks">任務</option>
                        <option value="monitoring">監控</option>
                    </select>
                </div>
            `,
            'includeCharts': `
                <div class="form-group">
                    <label class="checkbox-label">
                        <input type="checkbox" class="report-filter" data-filter="includeCharts" checked>
                        包含圖表
                    </label>
                </div>
            `,
            'includeData': `
                <div class="form-group">
                    <label class="checkbox-label">
                        <input type="checkbox" class="report-filter" data-filter="includeData" checked>
                        包含原始資料
                    </label>
                </div>
            `
        };
        
        return fieldTemplates[fieldName] || '';
    }
    
    handleFilterChange(e) {
        const filterName = e.target.dataset.filter;
        const value = e.target.type === 'checkbox' ? e.target.checked : e.target.value;
        
        this.filters[filterName] = value;
    }
    
    async handleGenerateReport(e) {
        e.preventDefault();
        
        const generateBtn = e.target;
        const originalText = generateBtn.textContent;
        
        // 顯示載入狀態
        generateBtn.textContent = '生成中...';
        generateBtn.disabled = true;
        
        try {
            const response = await fetch('/analytics/api/reports/generate', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(this.filters)
            });
            
            if (response.ok) {
                const result = await response.json();
                this.showSuccess('報告生成成功！');
                this.loadReportsList(); // 重新載入報告列表
                
                if (result.preview_url) {
                    this.showPreview(result.preview_url);
                }
            } else {
                throw new Error('報告生成失敗');
            }
            
        } catch (error) {
            console.error('Generate report error:', error);
            this.showError('報告生成失敗：' + error.message);
        } finally {
            generateBtn.textContent = originalText;
            generateBtn.disabled = false;
        }
    }
    
    async handlePreviewReport(e) {
        const reportId = e.target.dataset.reportId;
        
        try {
            const response = await fetch(`/analytics/api/reports/${reportId}/preview`);
            
            if (response.ok) {
                const previewData = await response.json();
                this.showPreview(previewData);
            } else {
                throw new Error('無法載入報告預覽');
            }
            
        } catch (error) {
            console.error('Preview report error:', error);
            this.showError('預覽載入失敗：' + error.message);
        }
    }
    
    handleExportReport(e) {
        const reportId = e.target.dataset.reportId;
        const format = e.target.dataset.format || 'pdf';
        
        const exportUrl = `/analytics/api/reports/${reportId}/export?format=${format}`;
        window.open(exportUrl, '_blank');
    }
    
    showPreview(previewData) {
        const modal = document.querySelector('#reportPreviewModal');
        if (!modal) {
            this.createPreviewModal();
        }
        
        const content = document.querySelector('.report-preview-content');
        if (content) {
            if (typeof previewData === 'string') {
                content.innerHTML = previewData;
            } else {
                content.innerHTML = this.renderPreviewContent(previewData);
            }
        }
        
        // 顯示模態框
        const modalInstance = new bootstrap.Modal(modal);
        modalInstance.show();
    }
    
    createPreviewModal() {
        const modal = document.createElement('div');
        modal.id = 'reportPreviewModal';
        modal.className = 'modal fade';
        modal.innerHTML = `
            <div class="modal-dialog modal-xl">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">報告預覽</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <div class="report-preview-content"></div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">關閉</button>
                        <button type="button" class="btn btn-primary">匯出</button>
                    </div>
                </div>
            </div>
        `;
        
        document.body.appendChild(modal);
    }
    
    renderPreviewContent(data) {
        return `
            <div class="report-preview">
                <h3>${data.title}</h3>
                <div class="report-meta">
                    <span>生成時間：${this.formatDateTime(data.created_at)}</span>
                    <span>報告類型：${data.type}</span>
                </div>
                <div class="report-content">
                    ${data.content || '報告內容載入中...'}
                </div>
            </div>
        `;
    }
    
    formatDate(date) {
        return date.toISOString().split('T')[0];
    }
    
    formatDateTime(dateString) {
        const date = new Date(dateString);
        return date.toLocaleDateString('zh-TW') + ' ' + date.toLocaleTimeString('zh-TW');
    }
    
    showSuccess(message) {
        const alert = document.createElement('div');
        alert.className = 'alert alert-success alert-dismissible';
        alert.innerHTML = `
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;
        
        const container = document.querySelector('.reports-container');
        if (container) {
            container.insertBefore(alert, container.firstChild);
        }
    }
    
    showError(message) {
        const alert = document.createElement('div');
        alert.className = 'alert alert-danger alert-dismissible';
        alert.innerHTML = `
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;
        
        const container = document.querySelector('.reports-container');
        if (container) {
            container.insertBefore(alert, container.firstChild);
        }
    }
}

// 頁面載入完成後初始化
document.addEventListener('DOMContentLoaded', function() {
    if (document.querySelector('.reports-container')) {
        new ReportsManager();
    }
});