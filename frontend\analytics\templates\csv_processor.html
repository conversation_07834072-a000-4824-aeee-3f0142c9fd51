<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CSV處理頁面 - Outlook Summary</title>
    <link rel="stylesheet" href="{{ url_for('analytics.static', filename='css/analytics.css') }}?v={{ range(1000, 9999) | random }}">
    <link rel="stylesheet" href="{{ url_for('shared.static', filename='css/global.css') }}">
</head>
<body>
    <div class="csv-processor-container">
        <header class="processor-header">
            <h1>CSV數據處理中心</h1>
            <div class="header-actions">
                <button id="clear-all-btn" class="btn btn-danger">
                    <span class="btn-icon">🗑️</span>
                    <span class="btn-text">清除全部</span>
                </button>
                <a href="{{ url_for('analytics.dashboard') }}" class="btn btn-outline">
                    <span class="btn-icon">🏠</span>
                    <span class="btn-text">回到儀表板</span>
                </a>
            </div>
        </header>

        <div class="processor-content">
            <!-- 檔案上傳區 -->
            <div class="upload-section">
                <div class="upload-card">
                    <h3>📁 上傳CSV檔案</h3>
                    <div class="upload-area" id="csv-upload-area">
                        <div class="upload-icon">📄</div>
                        <div class="upload-text">
                            <p>拖拽CSV檔案到此處，或點擊選擇檔案</p>
                            <small>支援格式：.csv, .xlsx, .xls (最大 50MB)</small>
                        </div>
                        <input type="file" id="csv-file-input" accept=".csv,.xlsx,.xls" multiple hidden>
                        <button class="btn btn-primary" onclick="document.getElementById('csv-file-input').click()">選擇檔案</button>
                    </div>
                </div>
            </div>

            <!-- 處理選項 -->
            <div class="processing-options">
                <div class="options-card">
                    <h3>⚙️ 處理設定</h3>
                    <div class="options-grid">
                        <div class="option-group">
                            <label for="delimiter">分隔符:</label>
                            <select id="delimiter">
                                <option value="," selected>逗號 (,)</option>
                                <option value=";">分號 (;)</option>
                                <option value="\t">Tab鍵</option>
                                <option value="|">管道符 (|)</option>
                            </select>
                        </div>
                        <div class="option-group">
                            <label for="encoding">編碼:</label>
                            <select id="encoding">
                                <option value="utf-8" selected>UTF-8</option>
                                <option value="big5">Big5</option>
                                <option value="gb2312">GB2312</option>
                                <option value="shift_jis">Shift_JIS</option>
                            </select>
                        </div>
                        <div class="option-group">
                            <label for="header-row">標題行:</label>
                            <select id="header-row">
                                <option value="0" selected>第1行</option>
                                <option value="1">第2行</option>
                                <option value="none">無標題行</option>
                            </select>
                        </div>
                        <div class="option-group">
                            <label>
                                <input type="checkbox" id="skip-empty-rows" checked>
                                跳過空行
                            </label>
                        </div>
                        <div class="option-group">
                            <label>
                                <input type="checkbox" id="trim-whitespace" checked>
                                去除空格
                            </label>
                        </div>
                        <div class="option-group">
                            <label>
                                <input type="checkbox" id="auto-detect" checked>
                                自動檢測格式
                            </label>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 檔案列表 -->
            <div class="file-list-section">
                <div class="file-list-card">
                    <h3>📋 處理清單</h3>
                    <div class="file-list" id="csv-file-list">
                        <!-- 檔案項目會由JavaScript動態添加 -->
                        <div class="empty-state">
                            <div class="empty-icon">📭</div>
                            <p>尚未選擇任何檔案</p>
                            <small>請上傳CSV檔案開始處理</small>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 資料預覽 -->
            <div class="preview-section" id="preview-section" style="display: none;">
                <div class="preview-card">
                    <div class="preview-header">
                        <h3>👁️ 資料預覽</h3>
                        <div class="preview-actions">
                            <button id="refresh-preview-btn" class="btn btn-sm btn-secondary">重新整理</button>
                            <button id="process-data-btn" class="btn btn-sm btn-primary">開始處理</button>
                        </div>
                    </div>
                    <div class="preview-stats">
                        <span class="stat">總行數: <strong id="total-rows">0</strong></span>
                        <span class="stat">總列數: <strong id="total-columns">0</strong></span>
                        <span class="stat">檔案大小: <strong id="file-size">0 KB</strong></span>
                    </div>
                    <div class="preview-table-container">
                        <table id="preview-table" class="preview-table">
                            <!-- 預覽資料會由JavaScript動態生成 -->
                        </table>
                    </div>
                </div>
            </div>

            <!-- 處理結果 -->
            <div class="results-section" id="results-section" style="display: none;">
                <div class="results-card">
                    <div class="results-header">
                        <h3>📊 處理結果</h3>
                        <div class="results-actions">
                            <button id="export-results-btn" class="btn btn-secondary">
                                <span class="btn-icon">💾</span>
                                <span class="btn-text">匯出結果</span>
                            </button>
                            <button id="save-to-database-btn" class="btn btn-primary">
                                <span class="btn-icon">🗄️</span>
                                <span class="btn-text">儲存至資料庫</span>
                            </button>
                        </div>
                    </div>
                    
                    <div class="results-summary">
                        <div class="summary-item">
                            <span class="label">處理成功:</span>
                            <span class="value success" id="success-count">0</span>
                        </div>
                        <div class="summary-item">
                            <span class="label">處理失敗:</span>
                            <span class="value error" id="error-count">0</span>
                        </div>
                        <div class="summary-item">
                            <span class="label">警告:</span>
                            <span class="value warning" id="warning-count">0</span>
                        </div>
                        <div class="summary-item">
                            <span class="label">處理時間:</span>
                            <span class="value" id="processing-time">0秒</span>
                        </div>
                    </div>

                    <div class="results-details">
                        <div class="tabs">
                            <button class="tab-btn active" data-tab="processed">已處理</button>
                            <button class="tab-btn" data-tab="errors">錯誤</button>
                            <button class="tab-btn" data-tab="warnings">警告</button>
                        </div>
                        
                        <div class="tab-content active" id="processed-tab">
                            <div class="results-table-container">
                                <table id="processed-table" class="results-table">
                                    <!-- 處理結果會由JavaScript動態生成 -->
                                </table>
                            </div>
                        </div>
                        
                        <div class="tab-content" id="errors-tab">
                            <div class="error-list" id="error-list">
                                <!-- 錯誤列表會由JavaScript動態生成 -->
                            </div>
                        </div>
                        
                        <div class="tab-content" id="warnings-tab">
                            <div class="warning-list" id="warning-list">
                                <!-- 警告列表會由JavaScript動態生成 -->
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 處理進度 -->
            <div class="progress-section" id="progress-section" style="display: none;">
                <div class="progress-card">
                    <h3>⏳ 處理進度</h3>
                    <div class="progress-bar">
                        <div class="progress-fill" id="progress-fill" style="width: 0%;"></div>
                    </div>
                    <div class="progress-text">
                        <span id="progress-percentage">0%</span>
                        <span id="progress-status">準備中...</span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="{{ url_for('shared.static', filename='js/main.js') }}"></script>
    <script src="{{ url_for('analytics.static', filename='js/csv-processor.js') }}"></script>
</body>
</html>