/**
 * 主控制器模組
 * 整合所有模組，提供統一的應用程序入口和控制邏輯
 */

class MainController {
    constructor() {
        this.isInitialized = false;
        this.modules = {
            fileUpload: null,
            progressDisplay: null,
            detailPanel: null,
            modal: null,
            eqcProcessor: null,
            countdownModal: null
        };
    }
    
    /**
     * 初始化應用程序
     */
    async init() {
        if (this.isInitialized) {
            console.log('📋 應用程序已初始化，跳過重複初始化');
            return;
        }
        
        console.log('🚀 初始化主控制器...');
        
        try {
            // 初始化所有模組
            await this.initializeModules();
            
            // 設置全局事件監聽器
            this.setupGlobalEventListeners();
            
            // 設置全局函數
            this.setupGlobalFunctions();
            
            // 初始化 UI 狀態
            this.initializeUIState();
            
            this.isInitialized = true;
            console.log('✅ 主控制器初始化完成');
            
            // 顯示歡迎訊息
            StatusManager.showToast('EQC 分析系統已就緒', 'success');
            
        } catch (error) {
            console.error('❌ 主控制器初始化失敗:', error);
            StatusManager.showToast('系統初始化失敗', 'error');
        }
    }
    
    /**
     * 初始化所有模組
     */
    async initializeModules() {
        console.log('📦 初始化所有模組...');
        
        // 初始化核心模組 (已在 HTML 中載入)
        
        // 初始化 UI 組件模組
        if (typeof fileUpload !== 'undefined') {
            await fileUpload.init();
            this.modules.fileUpload = fileUpload;
        }
        
        if (typeof progressDisplay !== 'undefined') {
            progressDisplay.init();
            this.modules.progressDisplay = progressDisplay;
        }
        
        if (typeof detailPanel !== 'undefined') {
            detailPanel.init();
            this.modules.detailPanel = detailPanel;
        }
        
        if (typeof modal !== 'undefined') {
            modal.init();
            this.modules.modal = modal;
        }

        // 初始化倒數計時組件
        if (typeof countdownModal !== 'undefined' && countdownModal) {
            this.modules.countdownModal = countdownModal;
        } else if (typeof CountdownModal !== 'undefined') {
            window.countdownModal = new CountdownModal();
            this.modules.countdownModal = countdownModal;
        }

        // 初始化業務邏輯模組
        if (typeof eqcProcessor !== 'undefined') {
            this.modules.eqcProcessor = eqcProcessor;
        }
        
        console.log('✅ 所有模組初始化完成');
    }
    
    /**
     * 設置全局事件監聽器
     */
    setupGlobalEventListeners() {
        // 框架級事件監聽器
        window.addEventListener('beforeunload', () => this.cleanup());
        window.addEventListener('error', (event) => {
            console.error('全局錯誤:', event.error);
            StatusManager.showToast('發生未預期的錯誤', 'error');
        });
        window.addEventListener('unhandledrejection', (event) => {
            console.error('未處理的 Promise 拒絕:', event.reason);
            StatusManager.showToast('發生異步錯誤', 'error');
        });
        
        // 🔧 無侵入式 JavaScript：設置所有按鈕和控件事件監聽器
        this.setupUIEventListeners();
    }
    
    /**
     * 設置 UI 事件監聽器 (無侵入式 JavaScript)
     */
    setupUIEventListeners() {
        console.log('📋 設置無侵入式事件監聽器...');
        
        // 主要處理按鈕
        const processButton = document.querySelector('.primary-btn');
        if (processButton) {
            processButton.addEventListener('click', () => this.processCompleteEQCWorkflow());
            console.log('✅ 主要處理按鈕事件已綁定');
        }
        
        // 今日記錄控制按鈕
        this.setupTodayRecordsButtons();
        
        // 詳細面板切換
        this.setupDetailPanelEvents();
        
        // CODE 區間輸入欄位
        this.setupCodeRegionInputs();
        
        // 報告預覽模態框按鈕
        this.setupReportModalButtons();
        
        console.log('✅ 所有 UI 事件監聽器設置完成');
    }
    
    /**
     * 設置今日記錄相關按鈕事件
     */
    setupTodayRecordsButtons() {
        
        // 重新整理按鈕
        const refreshButton = document.querySelector('button[title="重新整理列表"]');
        if (refreshButton) {
            refreshButton.addEventListener('click', () => this.refreshTodayRecords());
        }
        
        // 清理按鈕
        const cleanupButton = document.querySelector('button[title="手動清理24小時前的檔案"]');
        if (cleanupButton) {
            cleanupButton.addEventListener('click', () => this.manualCleanupFiles());
        }
    }
    
    /**
     * 設置詳細面板事件
     */
    setupDetailPanelEvents() {
        const detailHeader = document.querySelector('.detail-header');
        if (detailHeader) {
            // 移除現有的 onclick 屬性（如果存在）
            detailHeader.removeAttribute('onclick');
            
            // 點擊事件
            detailHeader.addEventListener('click', () => this.toggleDetail());
            
            // 鍵盤導航支援
            detailHeader.addEventListener('keydown', (event) => {
                if (event.key === 'Enter' || event.key === ' ') {
                    event.preventDefault();
                    this.toggleDetail();
                }
            });
        }
    }
    
    /**
     * 設置 CODE 區間輸入欄位事件
     */
    setupCodeRegionInputs() {
        const inputIds = ['mainStart', 'mainEnd', 'backupStart', 'backupEnd'];
        inputIds.forEach(id => {
            const input = document.getElementById(id);
            if (input) {
                // 移除現有的 oninput 屬性（如果存在）
                input.removeAttribute('oninput');
                input.addEventListener('input', () => this.updateFieldCount());
            }
        });
    }
    
    /**
     * 設置報告預覽模態框按鈕事件
     */
    setupReportModalButtons() {
        // 關閉按鈕（標題列）
        const closeButton = document.querySelector('#reportPreviewModal button[aria-label="關閉預覽視窗"]');
        if (closeButton) {
            closeButton.removeAttribute('onclick');
            closeButton.addEventListener('click', () => this.modules.modal?.closeReportPreview());
        }
        
        // 下載報告按鈕
        const downloadButton = document.querySelector('#reportPreviewModal button[aria-label="下載EQC處理報告"]');
        if (downloadButton) {
            downloadButton.removeAttribute('onclick');
            downloadButton.addEventListener('click', () => this.modules.modal?.downloadReport());
        }
        
        // 複製內容按鈕
        const copyButton = document.querySelector('#reportPreviewModal button[aria-label="複製報告內容到剪貼板"]');
        if (copyButton) {
            copyButton.removeAttribute('onclick');
            copyButton.addEventListener('click', () => this.modules.modal?.copyReportContent());
        }
        
        // 關閉按鈕（底部）
        const closeButtonBottom = document.querySelector('#reportPreviewModal button[aria-label="關閉報告預覽視窗"]');
        if (closeButtonBottom) {
            closeButtonBottom.removeAttribute('onclick');
            closeButtonBottom.addEventListener('click', () => this.modules.modal?.closeReportPreview());
        }
    }
    
    /**
     * 設置全局函數
     */
    setupGlobalFunctions() {
        window.processCompleteEQCWorkflow = () => this.processCompleteEQCWorkflow();
        window.updateFieldCount = () => this.updateFieldCount();
        window.refreshTodayRecords = () => this.refreshTodayRecords();
        window.downloadHistoryFileByDataAttr = (buttonId) => this.modules.eqcProcessor?.downloadFileByDataAttr(buttonId);
        window.toggleDetail = () => this.toggleDetail();
        window.setFolderPath = (path) => this.setFolderPath(path);
        window.closeReportPreview = () => this.modules.modal?.closeReportPreview();
        window.downloadReport = () => this.modules.modal?.downloadReport();
        window.copyReportContent = () => this.modules.modal?.copyReportContent();
        window.manualCleanupFiles = () => this.manualCleanupFiles();
    }
    
    /**
     * 初始化 UI 狀態
     */
    initializeUIState() {
        this.updateFieldCount();
        this.modules.progressDisplay?.reset();
        this.modules.detailPanel?.reset();
        this.refreshTodayRecords();
        this.setupFolderPathChangeListener();
    }

    /**
     * 設置資料夾路徑變更監聽器
     */
    setupFolderPathChangeListener() {
        const folderPathInput = DOMManager.get('folderPath');
        if (folderPathInput) {
            // 使用防抖動避免過於頻繁的清理
            let timeoutId = null;
            
            const handleFolderChange = () => {
                clearTimeout(timeoutId);
                timeoutId = setTimeout(() => {
                    console.log('📁 資料夾路徑已變更，清空 CODE 區間設定...');
                    this.clearCodeRegionSettings();
                    this.modules.progressDisplay?.reset();
                    this.modules.detailPanel?.reset();
                    StatusManager.showToast('已清空 CODE 區間設定，請重新執行處理', 'info');
                }, 500); // 500ms 防抖動
            };
            
            folderPathInput.addEventListener('input', handleFolderChange);
            folderPathInput.addEventListener('change', handleFolderChange);
            console.log('✅ 資料夾路徑變更監聽器已設置');
        }
    }

    /**
     * 重置處理結果
     */
    resetProcessingResults() {
        this.modules.eqcProcessor?.reset();
        this.modules.progressDisplay?.reset();
        this.modules.detailPanel?.reset();
        this.clearCodeRegionSettings();
        StatusManager.showToast('已清空處理結果和 CODE 區間設定，請重新執行處理', 'info');
    }
    
    /**
     * 執行完整 EQC 工作流程
     */
    async processCompleteEQCWorkflow() {
        const folderPath = DOMManager.getValue('folderPath');
        
        if (!folderPath || folderPath.trim() === '') {
            StatusManager.showToast('請輸入資料夾路徑', 'warning');
            DOMManager.focus('folderPath');
            return;
        }
        
        if (!folderPath.includes(':\\') && !folderPath.startsWith('/')) {
            StatusManager.showToast('請輸入有效的資料夾路徑', 'warning');
            DOMManager.focus('folderPath');
            return;
        }
        
        try {
            // 檢查模組版本
            if (this.modules.eqcProcessor?.version) {
                console.log('📋 EQCProcessor 版本:', this.modules.eqcProcessor.version);
            }
            if (window.detailPanel?.version) {
                console.log('📋 DetailPanel 版本:', window.detailPanel.version);
            }
            
            await this.modules.eqcProcessor?.processCompleteWorkflow(folderPath);
        } catch (error) {
            console.error('EQC 工作流程執行失敗:', error);
            StatusManager.showToast(`執行失敗: ${error.message}`, 'error');
        }
    }
    
    /**
     * 更新欄位計算
     */
    updateFieldCount() {
        const mainStart = DOMManager.getValue('mainStart');
        const mainEnd = DOMManager.getValue('mainEnd');
        const backupStart = DOMManager.getValue('backupStart');
        const backupEnd = DOMManager.getValue('backupEnd');

        // 主要區間計算
        let mainText = '';
        if (mainStart && mainEnd) {
            const start = parseInt(mainStart);
            const end = parseInt(mainEnd);
            if (start > 0 && end > 0 && end >= start) {
                mainText = `(${end - start + 1} 個欄位)`;
            } else {
                mainText = `(輸入無效)`;
            }
        } else if (mainStart || mainEnd) {
            mainText = `(請填入完整區間)`;
        } else {
            mainText = `(未設定)`;
        }
        DOMManager.setText('mainCount', mainText);

        // 備用區間計算
        let backupText = '';
        if (backupStart && backupEnd) {
            const start = parseInt(backupStart);
            const end = parseInt(backupEnd);
            if (start > 0 && end > 0 && end >= start) {
                backupText = `(${end - start + 1} 個欄位)`;
            } else {
                backupText = `(輸入無效)`;
            }
        } else if (backupStart || backupEnd) {
            backupText = `(請填入完整區間)`;
        } else {
            backupText = `(未設定)`;
        }
        DOMManager.setText('backupCount', backupText);
    }
    
    /**
     * 清空 CODE 區間設定
     */
    clearCodeRegionSettings() {
        const elements = ['mainStart', 'mainEnd', 'backupStart', 'backupEnd'];
        elements.forEach(id => {
            const element = DOMManager.get(id);
            if (element) element.value = '';
        });
        this.updateFieldCount();
    }
    
    /**
     * 刷新今日記錄
     */
    async refreshTodayRecords() {
        try {
            StatusManager.showToast('正在刷新今日記錄...', 'info');
            await this.modules.eqcProcessor?.updateTodayRecords();
            StatusManager.showToast('今日記錄已更新', 'success');
        } catch (error) {
            console.warn('刷新今日記錄失敗:', error);
            StatusManager.showToast('刷新今日記錄失敗', 'error');
        }
    }

    
    /**
     * 設定資料夾路徑
     * @param {string} path - 資料夾路徑
     */
    setFolderPath(path) {
        DOMManager.setValue('folderPath', path);
        this.resetProcessingResults();
    }

    /**
     * 切換詳細面板
     */
    toggleDetail() {
        this.modules.detailPanel?.toggle();
        
        // 🔧 更新 aria-expanded 屬性 (無障礙性支援)
        const detailHeader = document.querySelector('.detail-header');
        if (detailHeader) {
            const isExpanded = this.modules.detailPanel?.getCurrentStatus()?.isExpanded;
            detailHeader.setAttribute('aria-expanded', isExpanded ? 'true' : 'false');
        }
    }
    
    /**
     * 清理資源
     */
    cleanup() {
        Object.values(this.modules).forEach(module => {
            if (module && typeof module.cleanup === 'function') {
                module.cleanup();
            }
        });
    }
    
    /**
     * 獲取應用程序狀態
     * @returns {Object} 應用程序狀態
     */
    getApplicationStatus() {
        return {
            isInitialized: this.isInitialized,
            modules: Object.keys(this.modules).reduce((status, key) => {
                const module = this.modules[key];
                status[key] = {
                    loaded: !!module,
                    status: module && typeof module.getCurrentStatus === 'function' 
                        ? module.getCurrentStatus() 
                        : 'unknown'
                };
                return status;
            }, {})
        };
    }
    
    /**
     * 手動清理檔案
     */
    async manualCleanupFiles() {
        try {
            // 顯示確認對話框
            const confirmed = confirm(
                '確定要執行手動清理嗎？\n\n' +
                '這將刪除24小時前的處理檔案，包括：\n' +
                '• EQC處理結果檔案\n' +
                '• 臨時檔案\n' +
                '• 上傳檔案\n\n' +
                '此操作無法復原！'
            );
            
            if (!confirmed) {
                return;
            }
            
            // 顯示處理中提示
            StatusManager.showToast('正在執行檔案清理...', 'info');
            
            // 調用清理API
            const response = await fetch('/api/cleanup_files_manual', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                }
            });
            
            const result = await response.json();
            
            if (result.status === 'success') {
                const cleanedCount = result.data?.total_cleaned || 0;
                
                // 顯示清理結果
                if (cleanedCount > 0) {
                    StatusManager.showToast(
                        `✅ 清理完成！共清理 ${cleanedCount} 個檔案`, 
                        'success'
                    );
                    
                    // 詳細資訊顯示在console
                    console.log('🗑️ 檔案清理詳細結果:', result.data);
                    
                    // 刷新今日記錄（因為可能有檔案被清理）
                    this.refreshTodayRecords();
                } else {
                    StatusManager.showToast('✅ 清理完成！沒有找到需要清理的檔案', 'info');
                }
                
            } else {
                throw new Error(result.message || '清理失敗');
            }
            
        } catch (error) {
            console.error('❌ 手動清理失敗:', error);
            StatusManager.showToast(`❌ 清理失敗: ${error.message}`, 'error');
        }
    }
    
    /**
     * 重新初始化應用程序
     */
    async reinitialize() {
        this.isInitialized = false;
        this.cleanup();
        await Utils.sleep(100);
        await this.init();
    }
}

// 創建全局主控制器實例
const mainController = new MainController();

// 頁面載入完成後自動初始化
document.addEventListener('DOMContentLoaded', async () => {
    await mainController.init();
});

// 導出模組
if (typeof module !== 'undefined' && module.exports) {
    module.exports = MainController;
} else if (typeof window !== 'undefined') {
    window.MainController = MainController;
    window.mainController = mainController;
}
