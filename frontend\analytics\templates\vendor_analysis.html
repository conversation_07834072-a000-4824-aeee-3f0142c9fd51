<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>廠商分析 - Outlook Summary</title>
    <link rel="stylesheet" href="{{ url_for('analytics.static', filename='css/analytics.css') }}?v={{ range(1000, 9999) | random }}">
    <link rel="stylesheet" href="{{ url_for('shared.static', filename='css/global.css') }}">
</head>
<body>
    <div class="vendor-analysis-container">
        <header class="analysis-header">
            <h1>廠商分析</h1>
            <div class="header-actions">
                <button id="refresh-analysis-btn" class="btn btn-primary">
                    <span class="btn-icon">🔄</span>
                    <span class="btn-text">重新分析</span>
                </button>
                <button id="export-analysis-btn" class="btn btn-secondary">
                    <span class="btn-icon">📊</span>
                    <span class="btn-text">匯出分析</span>
                </button>
                <a href="{{ url_for('analytics.dashboard') }}" class="btn btn-outline">
                    <span class="btn-icon">🏠</span>
                    <span class="btn-text">回到儀表板</span>
                </a>
            </div>
        </header>

        <div class="analysis-content">
            <!-- 分析篩選器 -->
            <div class="analysis-filters">
                <div class="filter-row">
                    <div class="filter-group">
                        <label for="time-period">分析期間:</label>
                        <select id="time-period">
                            <option value="7">近7天</option>
                            <option value="30" selected>近30天</option>
                            <option value="90">近3個月</option>
                            <option value="365">近1年</option>
                        </select>
                    </div>
                    <div class="filter-group">
                        <label for="vendor-type">廠商類型:</label>
                        <select id="vendor-type">
                            <option value="all" selected>全部</option>
                            <option value="supplier">供應商</option>
                            <option value="contractor">承包商</option>
                            <option value="consultant">顧問</option>
                            <option value="service">服務商</option>
                        </select>
                    </div>
                    <div class="filter-group">
                        <label for="region">地區:</label>
                        <select id="region">
                            <option value="all" selected>全部地區</option>
                            <option value="tw">台灣</option>
                            <option value="cn">中國大陸</option>
                            <option value="sea">東南亞</option>
                            <option value="other">其他</option>
                        </select>
                    </div>
                    <button id="apply-filters-btn" class="btn btn-primary">套用篩選</button>
                </div>
            </div>

            <!-- 概覽統計 -->
            <div class="overview-stats">
                <div class="stat-card">
                    <div class="stat-icon">🏢</div>
                    <div class="stat-content">
                        <h3>廠商總數</h3>
                        <div class="stat-value" id="total-vendors">{{ stats.total_vendors or 0 }}</div>
                        <div class="stat-change positive">+{{ stats.new_vendors or 0 }} 本月新增</div>
                    </div>
                </div>
                <div class="stat-card">
                    <div class="stat-icon">✅</div>
                    <div class="stat-content">
                        <h3>活躍廠商</h3>
                        <div class="stat-value" id="active-vendors">{{ stats.active_vendors or 0 }}</div>
                        <div class="stat-change">{{ '%.1f'|format(stats.active_rate or 0) }}% 活躍率</div>
                    </div>
                </div>
                <div class="stat-card">
                    <div class="stat-icon">📊</div>
                    <div class="stat-content">
                        <h3>合規廠商</h3>
                        <div class="stat-value" id="compliant-vendors">{{ stats.compliant_vendors or 0 }}</div>
                        <div class="stat-change {{ 'positive' if stats.compliance_rate > 80 else 'negative' }}">{{ '%.1f'|format(stats.compliance_rate or 0) }}% 合規率</div>
                    </div>
                </div>
                <div class="stat-card">
                    <div class="stat-icon">⚠️</div>
                    <div class="stat-content">
                        <h3>風險廠商</h3>
                        <div class="stat-value" id="risk-vendors">{{ stats.risk_vendors or 0 }}</div>
                        <div class="stat-change negative">需要關注</div>
                    </div>
                </div>
            </div>

            <!-- 分析圖表 -->
            <div class="analysis-charts">
                <div class="chart-section">
                    <div class="chart-card">
                        <div class="chart-header">
                            <h3>廠商類型分佈</h3>
                            <div class="chart-actions">
                                <button class="btn btn-sm btn-secondary" onclick="refreshChart('vendor-type-chart')">重新整理</button>
                            </div>
                        </div>
                        <div class="chart-container">
                            <canvas id="vendor-type-chart"></canvas>
                        </div>
                    </div>

                    <div class="chart-card">
                        <div class="chart-header">
                            <h3>地區分佈</h3>
                            <div class="chart-actions">
                                <button class="btn btn-sm btn-secondary" onclick="refreshChart('region-chart')">重新整理</button>
                            </div>
                        </div>
                        <div class="chart-container">
                            <canvas id="region-chart"></canvas>
                        </div>
                    </div>
                </div>

                <div class="chart-section">
                    <div class="chart-card full-width">
                        <div class="chart-header">
                            <h3>廠商活躍度趨勢</h3>
                            <div class="chart-actions">
                                <select id="trend-period">
                                    <option value="daily">每日</option>
                                    <option value="weekly" selected>每週</option>
                                    <option value="monthly">每月</option>
                                </select>
                                <button class="btn btn-sm btn-secondary" onclick="refreshChart('activity-trend-chart')">重新整理</button>
                            </div>
                        </div>
                        <div class="chart-container">
                            <canvas id="activity-trend-chart"></canvas>
                        </div>
                    </div>
                </div>

                <div class="chart-section">
                    <div class="chart-card full-width">
                        <div class="chart-header">
                            <h3>合規性評分</h3>
                            <div class="chart-actions">
                                <button class="btn btn-sm btn-secondary" onclick="refreshChart('compliance-chart')">重新整理</button>
                            </div>
                        </div>
                        <div class="chart-container">
                            <canvas id="compliance-chart"></canvas>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 廠商詳細列表 -->
            <div class="vendor-details">
                <div class="details-header">
                    <h3>廠商詳細資訊</h3>
                    <div class="search-box">
                        <input type="text" id="vendor-search" placeholder="搜尋廠商名稱或代碼...">
                        <button id="search-btn" class="btn btn-sm btn-primary">搜尋</button>
                    </div>
                </div>

                <div class="vendor-table">
                    <table>
                        <thead>
                            <tr>
                                <th>廠商名稱</th>
                                <th>類型</th>
                                <th>地區</th>
                                <th>郵件數量</th>
                                <th>最後活躍</th>
                                <th>合規評分</th>
                                <th>風險等級</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody id="vendor-table-body">
                            {% for vendor in vendors %}
                            <tr>
                                <td>
                                    <div class="vendor-info">
                                        <strong>{{ vendor.name }}</strong>
                                        <small>{{ vendor.code }}</small>
                                    </div>
                                </td>
                                <td>
                                    <span class="vendor-type">{{ vendor.type }}</span>
                                </td>
                                <td>{{ vendor.region }}</td>
                                <td>{{ vendor.email_count }}</td>
                                <td>{{ vendor.last_activity.strftime('%Y-%m-%d') if vendor.last_activity else 'N/A' }}</td>
                                <td>
                                    <div class="compliance-score">
                                        <span class="score {{ 'high' if vendor.compliance_score > 80 else 'medium' if vendor.compliance_score > 60 else 'low' }}">
                                            {{ vendor.compliance_score }}分
                                        </span>
                                    </div>
                                </td>
                                <td>
                                    <span class="risk-level {{ vendor.risk_level.lower() }}">
                                        {{ vendor.risk_level }}
                                    </span>
                                </td>
                                <td>
                                    <div class="action-buttons">
                                        <button class="btn btn-sm btn-secondary" onclick="viewVendorDetails('{{ vendor.id }}')">詳情</button>
                                        <button class="btn btn-sm btn-outline" onclick="exportVendorReport('{{ vendor.id }}')">匯出</button>
                                    </div>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>

                <div class="pagination" id="vendor-pagination">
                    <!-- 分頁控制會由JavaScript動態生成 -->
                </div>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="{{ url_for('shared.static', filename='js/main.js') }}"></script>
    <script src="{{ url_for('shared.static', filename='lib/chart.min.js') }}"></script>
    <script src="{{ url_for('analytics.static', filename='js/vendor-analysis.js') }}"></script>
</body>
</html>