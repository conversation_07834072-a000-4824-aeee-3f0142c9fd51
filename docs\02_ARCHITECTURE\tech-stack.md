# 技術堆疊文檔 (Technology Stack Documentation)

**專案名稱**: Outlook Summary System - 半導體郵件處理系統  
**版本**: 5.1 (Vue 3 前端遷移準備版本)  
**最後更新**: 2025-08-11

## 📋 目錄

- [技術架構概覽](#技術架構概覽)
- [前端技術堆疊](#前端技術堆疊)
- [Vue 3 遷移技術堆疊](#vue-3-遷移技術堆疊)
- [核心依賴項](#核心依賴項)
- [數據庫設計](#數據庫設計)
- [開發工具與環境](#開發工具與環境)
- [最佳實踐建議](#最佳實踐建議)

---

## 🏗️ 技術架構概覽

### 系統架構圖

```
┌─────────────────────────────────────────────────────────────┐
│                    前端用戶界面層                              │
├─────────────────────────────────────────────────────────────┤
│  🔄 遷移中: Flask Templates → Vue 3 SPA                   │
│  ✅ 當前: Flask Templates + Jinja2 (模組化藍圖架構)       │
│  🎯 目標: Vue 3 + Composition API + TypeScript            │
│  📦 狀態管理: Pinia Store + Composables                    │
│  🎨 UI框架: Element Plus + TailwindCSS                   │
└─────────────────────────────────────────────────────────────┘
                              │
                              ▼
┌─────────────────────────────────────────────────────────────┐
│                    應用服務層                                │
├─────────────────────────────────────────────────────────────┤
│  🌐 統一入口: 整合服務 (Port 5555) ✅                      │
│  ├── 📧 Flask 前端服務 (6個模組藍圖)                        │
│  ├── ⚙️  FastAPI EQC 服務 (Port 8010)                      │
│  ├── 📊 任務隊列: Dramatiq + Redis                          │
│  └── 🔄 WebSocket 實時通訊                                  │
└─────────────────────────────────────────────────────────────┘
                              │
                              ▼
┌─────────────────────────────────────────────────────────────┐
│                    數據持久層                                │
├─────────────────────────────────────────────────────────────┤
│  🗄️  SQLite (主數據庫)                                      │
│  📊 SQLAlchemy 2.0.23 (ORM)                               │
│  🔴 Redis (任務隊列 & 緩存)                                 │
└─────────────────────────────────────────────────────────────┘
```

### 核心設計原則

1. **模組化架構**: ✅ 已完成 Flask 藍圖系統，6個功能模組
2. **關注點分離**: 前端、後端、共享資源清晰分離  
3. **異步處理**: 支援長時間運行的 EQC 分析任務
4. **統一入口**: 單一端口整合所有服務
5. **容錯設計**: 優雅處理服務失效情況
6. **🆕 漸進式遷移**: Flask → Vue 3 平滑過渡，零停機時間
7. **🆕 現代化前端**: Vue 3 + Composition API + TypeScript
8. **🆕 響應式設計**: 支援桌面和行動裝置的統一體驗

---

## 🎨 前端技術堆疊

### 當前技術組合 (基於 requirements.txt)

| 技術組件 | 版本 | 用途 | 狀態 |
|---------|------|------|------|
| **Flask** | 2.3.3 | Web 框架 & 藍圖架構 | ✅ 模組化重構完成 |
| **Jinja2** | 3.1.2 | 服務端模板引擎 | ✅ 藍圖整合完成 |
| **Werkzeug** | 2.3.7 | WSGI 工具包 | ✅ 運行中 |
| **MarkupSafe** | 2.1.3 | HTML 安全處理 | ✅ 運行中 |
| **FastAPI** | 0.104.1 | 異步 API 框架 | ✅ EQC 服務運作中 |
| **SQLAlchemy** | 2.0.23 | ORM 數據庫抽象層 | ✅ 現代化 ORM |
| **JavaScript** | ES6+ | 前端邏輯 | ✅ 現代化完成 |
| **Dramatiq** | 1.15.0 | 異步任務佇列 | ✅ Redis 整合完成 |
| **Playwright** | 1.52.0 | E2E 測試框架 | ✅ 測試基礎設施 |
| **Redis** | 5.0.1 | 任務佇列 & 快取 | ✅ 運作中 |

### 模組化前端架構 ✅ 已完成

```
frontend/ (100% 實施完成)
├── app.py                         # Flask 主應用 (工廠模式) ✅
├── config.py                      # 統一配置管理 ✅
├── shared/                        # 共享前端資源 ✅
│   ├── static/css/               # 全局樣式系統
│   ├── static/js/core/           # 核心 JavaScript 模組
│   ├── static/js/components/     # UI 組件庫
│   └── utils/                    # Python 工具模組
├── email/                         # 📧 郵件模組 ✅
├── analytics/                     # 📊 分析模組 ✅  
├── file_management/               # 📁 檔案管理模組 ✅
├── eqc/                          # ⚙️ EQC 模組 ✅
├── tasks/                        # 📋 任務模組 ✅
├── monitoring/                   # 📈 監控模組 ✅
└── logs/                         # 📜 日誌系統 ✅
```

**每個功能模組包含完整結構:**
- `templates/` - HTML 模板檔案 ✅
- `static/css/` - 模組專用樣式 ✅ 
- `static/js/` - 模組專用 JavaScript ✅
- `routes/` - 路由處理檔案 ✅
- `README.md` - 模組說明文件 ✅

---

## 🎯 Vue 3 遷移技術堆疊

### 遷移策略概覽

本專案採用**漸進式遷移**策略，確保業務連續性並最小化風險：

```
階段 1: 準備階段 (當前) ✅
├── Flask 藍圖架構模組化重構完成
├── 前端資源按功能模組組織
├── API 介面標準化
└── 測試基礎設施建立

階段 2: Vue 3 基礎設施 🔄
├── Vue 3 + Vite 專案初始化
├── TypeScript 配置與類型定義
├── Pinia 狀態管理設置
└── UI 框架整合 (Element Plus + TailwindCSS)

階段 3: 模組遷移 📋
├── 核心模組 (email, analytics)
├── 輔助模組 (file-management, eqc)
├── 管理模組 (tasks, monitoring)
└── 測試與驗證

階段 4: 部署與優化 🚀
├── 生產環境部署
├── 效能優化與監控
├── 用戶培訓與文檔
└── 舊系統退役
```

### Vue 3 核心技術選型

| 技術組件 | 版本 | 用途 | 遷移階段 |
|---------|------|------|---------|
| **Vue** | 3.4+ | 核心框架 | 階段 2 |
| **TypeScript** | 5.0+ | 類型安全 | 階段 2 |
| **Vite** | 5.0+ | 建置工具 | 階段 2 |
| **Vue Router** | 4.0+ | 路由管理 | 階段 2 |
| **Pinia** | 2.1+ | 狀態管理 | 階段 2 |
| **Element Plus** | 2.4+ | UI 組件庫 | 階段 2 |
| **TailwindCSS** | 3.4+ | CSS 框架 | 階段 2 |
| **Axios** | 1.6+ | HTTP 客戶端 | 階段 2 |
| **Vue I18n** | 9.8+ | 國際化 | 階段 3 |
| **VueUse** | 10.7+ | Composition 工具 | 階段 3 |

### 開發工具鏈

```json
{
  "開發環境": {
    "Node.js": ">=18.17.0",
    "npm": ">=9.0.0",
    "Vue DevTools": "6.5+",
    "Volar (VS Code)": "最新版"
  },
  "建置工具": {
    "Vite": "5.0+",
    "Rollup": "內建於 Vite",
    "PostCSS": "8.4+",
    "Autoprefixer": "10.4+"
  },
  "代碼品質": {
    "ESLint": "8.57+",
    "Prettier": "3.1+",
    "TypeScript": "5.0+",
    "Husky": "8.0+"
  },
  "測試框架": {
    "Vitest": "1.1+",
    "Vue Test Utils": "2.4+",
    "Happy DOM": "12.10+",
    "Playwright": "1.52+" 
  }
}
```

### Vue 3 專案結構

```
vue-frontend/ (新建目錄)
├── public/                        # 靜態資源
│   ├── favicon.ico
│   └── index.html
├── src/
│   ├── main.ts                    # 應用程式入口
│   ├── App.vue                    # 根組件
│   ├── router/                    # 路由配置
│   │   └── index.ts
│   ├── stores/                    # Pinia 狀態管理
│   │   ├── index.ts
│   │   ├── email.ts              # 郵件狀態
│   │   ├── analytics.ts          # 分析狀態
│   │   ├── fileManagement.ts     # 檔案管理狀態
│   │   ├── eqc.ts                # EQC 狀態
│   │   ├── tasks.ts              # 任務狀態
│   │   └── monitoring.ts         # 監控狀態
│   ├── views/                     # 頁面組件
│   │   ├── email/
│   │   │   ├── EmailInboxView.vue
│   │   │   └── EmailDetailView.vue
│   │   ├── analytics/
│   │   │   └── AnalyticsDashboardView.vue
│   │   └── ... (其他模組)
│   ├── components/                # 可重用組件
│   │   ├── common/               # 通用組件
│   │   │   ├── AppHeader.vue
│   │   │   ├── AppSidebar.vue
│   │   │   └── AppFooter.vue
│   │   ├── email/                # 郵件相關組件
│   │   │   ├── EmailList.vue
│   │   │   ├── EmailItem.vue
│   │   │   └── EmailViewer.vue
│   │   └── ... (其他模組組件)
│   ├── composables/               # 組合式函數
│   │   ├── useEmailOperations.ts
│   │   ├── useNotification.ts
│   │   ├── useAsync.ts
│   │   └── useWebSocket.ts
│   ├── api/                       # API 接口
│   │   ├── client.ts             # 基礎 HTTP 客戶端
│   │   ├── email.ts              # 郵件 API
│   │   ├── analytics.ts          # 分析 API
│   │   └── ... (其他模組 API)
│   ├── types/                     # TypeScript 類型定義
│   │   ├── email.ts
│   │   ├── analytics.ts
│   │   ├── api.ts
│   │   └── common.ts
│   ├── utils/                     # 工具函數
│   │   ├── date.ts
│   │   ├── validation.ts
│   │   └── format.ts
│   ├── assets/                    # 靜態資產
│   │   ├── styles/
│   │   │   ├── main.css
│   │   │   ├── variables.css
│   │   │   └── components.css
│   │   └── images/
│   └── locales/                   # 國際化資源
│       ├── zh-TW.json
│       └── en.json
├── tests/                         # 測試文件
│   ├── components/
│   ├── composables/
│   ├── stores/
│   └── e2e/
├── docs/                          # Vue 專案文檔
├── package.json
├── tsconfig.json
├── vite.config.ts
├── tailwind.config.js
└── .eslintrc.js
```

### API 整合策略

Vue 3 前端將透過標準化的 REST API 與現有 Flask/FastAPI 後端整合：

```typescript
// api/client.ts - 統一 HTTP 客戶端
import axios from 'axios'
import type { ApiResponse } from '@/types/api'

const client = axios.create({
  baseURL: '/api',
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
    'X-Requested-With': 'XMLHttpRequest'
  }
})

// 請求攔截器
client.interceptors.request.use(config => {
  // 添加認證 token
  const token = localStorage.getItem('auth_token')
  if (token) {
    config.headers.Authorization = `Bearer ${token}`
  }
  return config
})

// 響應攔截器
client.interceptors.response.use(
  response => response.data,
  error => {
    // 統一錯誤處理
    const message = error.response?.data?.message || '請求失敗'
    return Promise.reject(new Error(message))
  }
)

export { client }
```

### WebSocket 整合

```typescript
// composables/useWebSocket.ts
import { ref, onUnmounted } from 'vue'

export function useWebSocket(url: string) {
  const socket = ref<WebSocket | null>(null)
  const isConnected = ref(false)
  const data = ref<any>(null)
  const error = ref<string | null>(null)

  function connect() {
    try {
      socket.value = new WebSocket(url)
      
      socket.value.onopen = () => {
        isConnected.value = true
        error.value = null
      }
      
      socket.value.onmessage = (event) => {
        data.value = JSON.parse(event.data)
      }
      
      socket.value.onclose = () => {
        isConnected.value = false
      }
      
      socket.value.onerror = (err) => {
        error.value = 'WebSocket 連接失敗'
        console.error('WebSocket error:', err)
      }
    } catch (err) {
      error.value = 'WebSocket 初始化失敗'
    }
  }

  function disconnect() {
    socket.value?.close()
    socket.value = null
    isConnected.value = false
  }

  onUnmounted(() => {
    disconnect()
  })

  return {
    isConnected: readonly(isConnected),
    data: readonly(data),
    error: readonly(error),
    connect,
    disconnect
  }
}
```

---

## 📦 核心依賴項

### Web 框架層 (與 requirements.txt 一致)

```txt
# Flask 前端服務核心
Flask==2.3.3
SQLAlchemy==2.0.23
Jinja2==3.1.2
Werkzeug==2.3.7
MarkupSafe==2.1.3
itsdangerous==2.1.2
click==8.1.7
blinker==1.7.0

# FastAPI EQC 服務核心  
fastapi==0.104.1
uvicorn==0.24.0
pydantic==2.5.0
starlette==0.27.0
loguru==0.7.2
```

### 數據處理層 (與 requirements.txt 一致)

```txt
# 數據分析
pandas==2.1.3
numpy==1.24.3
openpyxl==3.1.2
xlsxwriter==3.1.9
PyYAML==6.0.1
structlog==23.2.0
cryptography==41.0.7

# 任務佇列
dramatiq[redis]==1.15.0
dramatiq-dashboard==0.4.0
redis==5.0.1
```

### 測試框架 (與 requirements.txt 一致)

```txt
# E2E 測試
playwright==1.52.0
pytest-playwright==0.4.4

# 其他工具
python-dotenv==1.0.0
```

---

## 🗄️ 數據庫設計

### 技術選型

| 組件 | 技術 | 版本 | 用途 | 狀態 |
|------|------|------|------|------|
| **主數據庫** | SQLite | 3.x | 結構化數據存儲 | ✅ 運行中 |
| **ORM** | SQLAlchemy | 2.0.23 | 數據庫抽象層 | ✅ 現代化 |
| **緩存** | Redis | 5.0.1 | 任務隊列 & 會話存儲 | ✅ 運行中 |

### 核心數據模型

```python
# 郵件系統模型
class EmailDB(Base):
    __tablename__ = 'emails'
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    message_id = Column(String(255), unique=True, nullable=False, index=True)
    sender = Column(String(255), nullable=False, index=True)
    subject = Column(Text, nullable=False)
    body = Column(Text, nullable=True)
    received_time = Column(DateTime, nullable=False, index=True)
    
    # 業務邏輯欄位
    pd = Column(String(100), nullable=True, index=True)
    lot = Column(String(100), nullable=True, index=True)
    mo = Column(String(100), nullable=True, index=True)
    yield_value = Column(String(50), nullable=True)
    vendor_code = Column(String(50), nullable=True, index=True)
    
    # LLM 分析結果
    llm_analysis_result = Column(Text, nullable=True)
    llm_analysis_timestamp = Column(DateTime, nullable=True)
    llm_service_used = Column(String(50), nullable=True)
```

---

## 🛠️ 開發工具與環境

### Python 環境需求

```yaml
Python版本要求: ">=3.9"
建議版本: "3.11.x"

虛擬環境管理:
  工具: venv (內建) 或 uv
  路徑: ./venv_win_3_11_12/
  
包管理:
  依賴文件: requirements.txt
  鎖定版本: 是
  開發依賴: 整合管理
```

### Git 工作流程

```bash
# 分支策略
main                    # 主分支，穩定版本
├── development         # 開發分支
├── feature/*          # 功能分支
├── task/*            # 任務分支 (當前: task/3-migrate-files)
└── hotfix/*          # 熱修復分支

# 提交規範
feat: 新功能
fix: 修復 bug
docs: 文檔更新
style: 格式調整
refactor: 重構代碼
test: 測試相關
chore: 構建過程或輔助工具變動
```

---

## 🎯 最佳實踐建議

### 代碼品質

#### 1. Python 代碼規範

```python
# 使用型別提示和現代化語法
from typing import List, Optional, Dict, Any
from dataclasses import dataclass

@dataclass
class EmailRequest:
    sender: str
    subject: str
    body: Optional[str] = None
    
def process_email(email_data: Dict[str, Any]) -> Optional[EmailModel]:
    """處理郵件數據
    
    Args:
        email_data: 郵件原始數據
        
    Returns:
        處理後的郵件模型，失敗返回 None
    """
    pass
```

#### 2. Flask 藍圖最佳實踐

```python
# frontend/email/routes/email_routes.py
from flask import Blueprint, render_template, request, jsonify
from frontend.shared.utils.error_handler import handle_api_error

email_bp = Blueprint(
    'email', 
    __name__, 
    template_folder='../templates',
    static_folder='../static',
    static_url_path='/static/email'
)

@email_bp.route('/')
def inbox():
    """郵件收件匣頁面"""
    try:
        return render_template('email/inbox.html')
    except Exception as e:
        return handle_api_error(e)
```

### 測試策略

#### 單元測試

```python
import pytest
from unittest.mock import Mock, patch

class TestEmailService:
    """郵件服務測試類別"""
    
    @pytest.fixture
    def email_service(self):
        """測試用的郵件服務實例"""
        return EmailService()
    
    def test_create_email_success(self, email_service):
        """測試創建郵件成功"""
        # Arrange
        email_data = {
            'sender': '<EMAIL>',
            'subject': 'Test Subject',
            'body': 'Test Body'
        }
        
        # Act
        result = email_service.create_email(email_data)
        
        # Assert
        assert result is not None
        assert result.subject == email_data['subject']
```

---

## 📊 性能指標與監控

### 關鍵性能指標 (KPI)

| 指標類型 | 目標值 | 監控方法 | 當前狀態 |
|---------|--------|----------|----------|
| **響應時間** | < 200ms (API) | APM 工具監控 | ✅ 達成 |
| **處理能力** | > 1000 郵件/小時 | 業務監控 | ✅ 達成 |
| **系統可用性** | > 99.5% | 健康檢查 | ✅ 達成 |
| **錯誤率** | < 1% | 錯誤日誌監控 | ✅ 達成 |
| **模組完整性** | 100% | 藍圖驗證 | ✅ 達成 |

---

## 📝 變更記錄

| 版本 | 日期 | 變更內容 | 狀態 |
|------|------|----------|------|
| 5.1 | 2025-08-11 | **新增 Vue 3 遷移技術堆疊規劃** | 🎯 當前版本 |
| 5.0 | 2025-08-11 | **開始 Vue 3 + Composition API 前端遷移準備** | 📋 進行中 |
| 3.1 | 2025-08-11 | 更新技術堆疊文檔以反映當前專案狀態 | ✅ 完成 |
| 3.0 | 2025-01-10 | Flask 藍圖架構驗證完成 - 100% 功能驗證通過 | ✅ 完成 |
| 2.1 | 2025-08-10 | 完成前端模組化重構 | ✅ 完成 |
| 2.0 | 2025-08-09 | 整合 FastAPI 服務 | ✅ 完成 |

---

**文檔維護**: 本文檔與 requirements.txt、project_info.json 和實際專案結構保持完全一致，確保技術決策的可追溯性和團隊知識的傳承。

**特注**: task/3-migrate-files 分支的 Flask 藍圖架構重構已 100% 完成並通過驗證。