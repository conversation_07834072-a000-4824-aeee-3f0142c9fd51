#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sqlite3
import os
from pathlib import Path

def check_db_file(db_path):
    print(f"\n=== {db_path} ===")
    
    if not db_path.exists():
        print("File not found")
        return
    
    file_size = db_path.stat().st_size
    print(f"Size: {file_size} bytes ({file_size / 1024:.2f} KB)")
    
    if file_size == 0:
        print("Empty file")
        return
    
    try:
        conn = sqlite3.connect(str(db_path))
        cursor = conn.cursor()
        
        # 檢查表格
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
        tables = cursor.fetchall()
        print(f"Tables: {len(tables)}")
        
        total_records = 0
        for table in tables:
            table_name = table[0]
            cursor.execute(f"SELECT COUNT(*) FROM {table_name};")
            count = cursor.fetchone()[0]
            total_records += count
            if count > 0:
                print(f"  - {table_name}: {count} records")
        
        print(f"Total records: {total_records}")
        
        # 如果有郵件表格，顯示幾筆資料
        try:
            cursor.execute("SELECT COUNT(*) FROM emails;")
            email_count = cursor.fetchone()[0]
            if email_count > 0:
                cursor.execute("SELECT subject FROM emails LIMIT 3;")
                subjects = cursor.fetchall()
                print("Sample emails:")
                for subject in subjects:
                    print(f"  - {subject[0][:50]}...")
        except:
            pass
        
        conn.close()
        
    except Exception as e:
        print(f"Error: {e}")

def main():
    db_files = [
        "data/email_inbox_backup_20250813_213508.db",
        "email_inbox.db", 
        "frontend/email_inbox.db",
        "outlook.db",
        "data/eqc_task_status.db",
        "data/email_inbox.db"
    ]
    
    project_root = Path(__file__).parent
    
    for db_file in db_files:
        db_path = project_root / db_file
        check_db_file(db_path)

if __name__ == "__main__":
    main()