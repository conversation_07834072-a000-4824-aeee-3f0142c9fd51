{"test_start_time": "2025-08-13T21:26:16.878865", "base_url": "http://localhost:8000", "tests": [{"name": "Health endpoint", "success": true, "message": "Status: healthy", "status_code": 200, "response_time_ms": 2055.03, "timestamp": "2025-08-13T21:26:18.933899"}, {"name": "Module check: email", "success": true, "message": "Module status OK", "status_code": null, "response_time_ms": 0, "timestamp": "2025-08-13T21:26:18.933899"}, {"name": "Module check: analytics", "success": true, "message": "Module status OK", "status_code": null, "response_time_ms": 0, "timestamp": "2025-08-13T21:26:18.933899"}, {"name": "Module check: file_management", "success": true, "message": "Module status OK", "status_code": null, "response_time_ms": 0, "timestamp": "2025-08-13T21:26:18.933899"}, {"name": "Module check: eqc", "success": true, "message": "Module status OK", "status_code": null, "response_time_ms": 0, "timestamp": "2025-08-13T21:26:18.933899"}, {"name": "Module check: tasks", "success": true, "message": "Module status OK", "status_code": null, "response_time_ms": 0, "timestamp": "2025-08-13T21:26:18.933899"}, {"name": "Module check: monitoring", "success": true, "message": "Module status OK", "status_code": null, "response_time_ms": 0, "timestamp": "2025-08-13T21:26:18.933899"}, {"name": "Home redirect", "success": true, "message": "URL: /", "status_code": 302, "response_time_ms": 2055.74, "timestamp": "2025-08-13T21:26:20.989638"}, {"name": "Health check", "success": true, "message": "URL: /health", "status_code": 200, "response_time_ms": 2038.42, "timestamp": "2025-08-13T21:26:23.028055"}, {"name": "Email inbox page", "success": true, "message": "Page load successful", "status_code": 200, "response_time_ms": 2094.49, "timestamp": "2025-08-13T21:26:25.122545"}, {"name": "Email inbox page - Content check", "success": true, "message": "No obvious errors in content", "status_code": null, "response_time_ms": 0, "timestamp": "2025-08-13T21:26:25.122545"}, {"name": "Monitoring dashboard", "success": true, "message": "Page load successful", "status_code": 200, "response_time_ms": 2073.64, "timestamp": "2025-08-13T21:26:27.196183"}, {"name": "Monitoring dashboard - Content check", "success": true, "message": "No obvious errors in content", "status_code": null, "response_time_ms": 0, "timestamp": "2025-08-13T21:26:27.196183"}, {"name": "Database management page", "success": true, "message": "Page load successful", "status_code": 200, "response_time_ms": 2067.89, "timestamp": "2025-08-13T21:26:29.264071"}, {"name": "Database management page - Content check", "success": true, "message": "No obvious errors in content", "status_code": null, "response_time_ms": 0, "timestamp": "2025-08-13T21:26:29.264071"}, {"name": "FastAPI docs endpoint", "success": true, "message": "FastAPI service accessible", "status_code": 200, "response_time_ms": 2084.85, "timestamp": "2025-08-13T21:26:31.348922"}], "summary": {"total_tests": 16, "passed_tests": 16, "failed_tests": 0, "success_rate": 100.0, "overall_success": true, "test_end_time": "2025-08-13T21:26:31.349921"}}