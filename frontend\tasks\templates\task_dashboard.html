<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>任務儀表板 - Outlook Summary</title>
    <link rel="stylesheet" href="{{ url_for('tasks.static', filename='css/tasks.css') }}?v={{ range(1000, 9999) | random }}">
    <link rel="stylesheet" href="{{ url_for('shared.static', filename='css/global.css') }}">
</head>
<body>
    <div class="task-dashboard-container">
        <header class="dashboard-header">
            <h1>任務管理儀表板</h1>
            <div class="header-stats">
                <div class="stat-item">
                    <span class="stat-label">運行中任務:</span>
                    <span class="stat-value running">{{ stats.running_tasks or 0 }}</span>
                </div>
                <div class="stat-item">
                    <span class="stat-label">等待中任務:</span>
                    <span class="stat-value pending">{{ stats.pending_tasks or 0 }}</span>
                </div>
                <div class="stat-item">
                    <span class="stat-label">今日完成:</span>
                    <span class="stat-value completed">{{ stats.completed_today or 0 }}</span>
                </div>
                <div class="stat-item">
                    <span class="stat-label">失敗任務:</span>
                    <span class="stat-value failed">{{ stats.failed_tasks or 0 }}</span>
                </div>
            </div>
            <div class="header-actions">
                <button id="create-task-btn" class="btn btn-primary">
                    <span class="btn-icon">➕</span>
                    <span class="btn-text">新增任務</span>
                </button>
                <button id="refresh-dashboard-btn" class="btn btn-secondary">
                    <span class="btn-icon">🔄</span>
                    <span class="btn-text">重新整理</span>
                </button>
                <a href="{{ url_for('tasks.queue') }}" class="btn btn-outline">
                    <span class="btn-icon">📋</span>
                    <span class="btn-text">任務隊列</span>
                </a>
            </div>
        </header>

        <div class="dashboard-content">
            <!-- 系統狀態區 -->
            <div class="system-status-section">
                <div class="status-grid">
                    <div class="status-card">
                        <div class="status-header">
                            <h3>🖥️ 系統狀態</h3>
                            <div class="status-indicator {{ 'healthy' if system_status.is_healthy else 'warning' }}">
                                {{ '正常' if system_status.is_healthy else '異常' }}
                            </div>
                        </div>
                        <div class="status-metrics">
                            <div class="metric">
                                <span class="metric-label">CPU使用率:</span>
                                <div class="metric-bar">
                                    <div class="bar-fill" style="width: {{ system_status.cpu_usage }}%"></div>
                                </div>
                                <span class="metric-value">{{ system_status.cpu_usage }}%</span>
                            </div>
                            <div class="metric">
                                <span class="metric-label">記憶體使用:</span>
                                <div class="metric-bar">
                                    <div class="bar-fill" style="width: {{ system_status.memory_usage }}%"></div>
                                </div>
                                <span class="metric-value">{{ system_status.memory_usage }}%</span>
                            </div>
                            <div class="metric">
                                <span class="metric-label">磁碟使用:</span>
                                <div class="metric-bar">
                                    <div class="bar-fill" style="width: {{ system_status.disk_usage }}%"></div>
                                </div>
                                <span class="metric-value">{{ system_status.disk_usage }}%</span>
                            </div>
                        </div>
                    </div>

                    <div class="status-card">
                        <div class="status-header">
                            <h3>⚙️ 工作執行器狀態</h3>
                            <div class="worker-count">{{ system_status.active_workers }}/{{ system_status.total_workers }} 活躍</div>
                        </div>
                        <div class="worker-list">
                            {% for worker in workers %}
                            <div class="worker-item {{ worker.status }}">
                                <div class="worker-info">
                                    <span class="worker-name">{{ worker.name }}</span>
                                    <span class="worker-status {{ worker.status }}">{{ worker.status_display }}</span>
                                </div>
                                <div class="worker-stats">
                                    <small>處理中: {{ worker.current_tasks }}, 已完成: {{ worker.completed_tasks }}</small>
                                </div>
                            </div>
                            {% endfor %}
                        </div>
                    </div>

                    <div class="status-card">
                        <div class="status-header">
                            <h3>📊 效能指標</h3>
                            <div class="performance-score {{ performance.score_class }}">{{ performance.score }}/100</div>
                        </div>
                        <div class="performance-metrics">
                            <div class="perf-metric">
                                <span class="perf-label">平均執行時間:</span>
                                <span class="perf-value">{{ '%.2f'|format(performance.avg_execution_time) }}s</span>
                            </div>
                            <div class="perf-metric">
                                <span class="perf-label">成功率:</span>
                                <span class="perf-value">{{ '%.1f'|format(performance.success_rate) }}%</span>
                            </div>
                            <div class="perf-metric">
                                <span class="perf-label">吞吐量:</span>
                                <span class="perf-value">{{ performance.throughput }}/min</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 任務概覽圖表 -->
            <div class="charts-section">
                <div class="chart-row">
                    <div class="chart-card">
                        <div class="chart-header">
                            <h3>任務執行趨勢</h3>
                            <div class="chart-controls">
                                <select id="trend-timeframe">
                                    <option value="24h" selected>過去24小時</option>
                                    <option value="7d">過去7天</option>
                                    <option value="30d">過去30天</option>
                                </select>
                                <button class="btn btn-sm btn-secondary" onclick="refreshChart('task-trend-chart')">重新整理</button>
                            </div>
                        </div>
                        <div class="chart-container">
                            <canvas id="task-trend-chart"></canvas>
                        </div>
                    </div>

                    <div class="chart-card">
                        <div class="chart-header">
                            <h3>任務類型分佈</h3>
                        </div>
                        <div class="chart-container">
                            <canvas id="task-type-chart"></canvas>
                        </div>
                    </div>
                </div>

                <div class="chart-row">
                    <div class="chart-card full-width">
                        <div class="chart-header">
                            <h3>執行時間分析</h3>
                            <div class="chart-controls">
                                <select id="execution-timeframe">
                                    <option value="today" selected>今天</option>
                                    <option value="week">本週</option>
                                    <option value="month">本月</option>
                                </select>
                            </div>
                        </div>
                        <div class="chart-container">
                            <canvas id="execution-time-chart"></canvas>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 活躍任務監控 -->
            <div class="active-tasks-section">
                <div class="active-tasks-card">
                    <div class="card-header">
                        <h3>🔄 活躍任務監控</h3>
                        <div class="task-filters">
                            <select id="task-type-filter">
                                <option value="all" selected>全部類型</option>
                                <option value="email_sync">郵件同步</option>
                                <option value="file_process">檔案處理</option>
                                <option value="data_analysis">數據分析</option>
                                <option value="backup">備份</option>
                                <option value="cleanup">清理</option>
                            </select>
                            <select id="task-status-filter">
                                <option value="all" selected>全部狀態</option>
                                <option value="running">運行中</option>
                                <option value="pending">等待中</option>
                                <option value="paused">已暫停</option>
                            </select>
                            <button id="apply-task-filters-btn" class="btn btn-sm btn-primary">套用篩選</button>
                        </div>
                    </div>

                    <div class="task-list" id="active-tasks-list">
                        {% for task in active_tasks %}
                        <div class="task-item {{ task.status }}" data-task-id="{{ task.id }}">
                            <div class="task-info">
                                <div class="task-header">
                                    <h4 class="task-name">{{ task.name }}</h4>
                                    <div class="task-meta">
                                        <span class="task-type">{{ task.type_display }}</span>
                                        <span class="task-priority {{ task.priority.lower() }}">{{ task.priority }}</span>
                                        <span class="task-status {{ task.status }}">{{ task.status_display }}</span>
                                    </div>
                                </div>
                                <div class="task-details">
                                    <p class="task-description">{{ task.description }}</p>
                                    <div class="task-progress">
                                        <div class="progress-bar">
                                            <div class="progress-fill" style="width: {{ task.progress_percentage }}%"></div>
                                        </div>
                                        <span class="progress-text">{{ task.progress_percentage }}%</span>
                                    </div>
                                    <div class="task-timing">
                                        <span class="start-time">開始: {{ task.started_at.strftime('%Y-%m-%d %H:%M') if task.started_at else 'N/A' }}</span>
                                        {% if task.estimated_completion %}
                                        <span class="estimated-completion">預計完成: {{ task.estimated_completion.strftime('%H:%M') }}</span>
                                        {% endif %}
                                        <span class="elapsed-time">已執行: {{ task.elapsed_time }}</span>
                                    </div>
                                </div>
                            </div>
                            <div class="task-actions">
                                {% if task.status == 'running' %}
                                <button class="btn btn-sm btn-warning" onclick="pauseTask('{{ task.id }}')">暫停</button>
                                <button class="btn btn-sm btn-danger" onclick="stopTask('{{ task.id }}')">停止</button>
                                {% elif task.status == 'paused' %}
                                <button class="btn btn-sm btn-primary" onclick="resumeTask('{{ task.id }}')">繼續</button>
                                <button class="btn btn-sm btn-danger" onclick="cancelTask('{{ task.id }}')">取消</button>
                                {% elif task.status == 'pending' %}
                                <button class="btn btn-sm btn-primary" onclick="startTask('{{ task.id }}')">開始</button>
                                <button class="btn btn-sm btn-secondary" onclick="editTask('{{ task.id }}')">編輯</button>
                                <button class="btn btn-sm btn-danger" onclick="deleteTask('{{ task.id }}')">刪除</button>
                                {% endif %}
                                <button class="btn btn-sm btn-outline" onclick="viewTaskLogs('{{ task.id }}')">查看日誌</button>
                                <div class="dropdown">
                                    <button class="btn btn-sm btn-outline dropdown-toggle">更多</button>
                                    <div class="dropdown-menu">
                                        <a href="#" onclick="duplicateTask('{{ task.id }}')">複製任務</a>
                                        <a href="#" onclick="setPriority('{{ task.id }}')">設定優先級</a>
                                        <a href="#" onclick="scheduleTask('{{ task.id }}')">排程執行</a>
                                        <div class="dropdown-divider"></div>
                                        <a href="#" onclick="exportTaskReport('{{ task.id }}')">匯出報告</a>
                                    </div>
                                </div>
                            </div>
                        </div>
                        {% endfor %}
                    </div>

                    <div class="pagination" id="active-tasks-pagination">
                        <!-- 分頁控制會由JavaScript動態生成 -->
                    </div>
                </div>
            </div>

            <!-- 最近完成的任務 -->
            <div class="recent-tasks-section">
                <div class="recent-tasks-card">
                    <div class="card-header">
                        <h3>✅ 最近完成的任務</h3>
                        <div class="card-actions">
                            <button id="view-all-completed-btn" class="btn btn-sm btn-outline">查看全部</button>
                            <button id="clear-completed-btn" class="btn btn-sm btn-danger">清除記錄</button>
                        </div>
                    </div>

                    <div class="recent-task-list">
                        {% for task in recent_completed_tasks %}
                        <div class="recent-task-item {{ task.result }}">
                            <div class="task-result-icon">
                                {% if task.result == 'success' %}✅
                                {% elif task.result == 'failed' %}❌
                                {% elif task.result == 'cancelled' %}⛔
                                {% endif %}
                            </div>
                            <div class="task-info">
                                <h4 class="task-name">{{ task.name }}</h4>
                                <div class="task-meta">
                                    <span class="task-type">{{ task.type_display }}</span>
                                    <span class="completion-time">完成於: {{ task.completed_at.strftime('%Y-%m-%d %H:%M') if task.completed_at else 'N/A' }}</span>
                                    <span class="execution-duration">耗時: {{ task.execution_duration }}</span>
                                </div>
                                {% if task.result_message %}
                                <p class="task-result-message">{{ task.result_message }}</p>
                                {% endif %}
                            </div>
                            <div class="task-actions">
                                <button class="btn btn-sm btn-outline" onclick="viewTaskDetails('{{ task.id }}')">詳情</button>
                                {% if task.result == 'failed' %}
                                <button class="btn btn-sm btn-primary" onclick="retryTask('{{ task.id }}')">重試</button>
                                {% endif %}
                                <button class="btn btn-sm btn-secondary" onclick="duplicateTask('{{ task.id }}')">複製</button>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 任務建立模態框 -->
    <div class="modal" id="create-task-modal">
        <div class="modal-content large">
            <div class="modal-header">
                <h3>新增任務</h3>
                <button class="modal-close">&times;</button>
            </div>
            <div class="modal-body">
                <form id="create-task-form">
                    <div class="form-row">
                        <div class="form-group">
                            <label for="task-name">任務名稱:</label>
                            <input type="text" id="task-name" required>
                        </div>
                        <div class="form-group">
                            <label for="task-type">任務類型:</label>
                            <select id="task-type" required>
                                <option value="">請選擇...</option>
                                <option value="email_sync">郵件同步</option>
                                <option value="file_process">檔案處理</option>
                                <option value="data_analysis">數據分析</option>
                                <option value="backup">備份</option>
                                <option value="cleanup">清理</option>
                                <option value="custom">自定義</option>
                            </select>
                        </div>
                    </div>
                    
                    <div class="form-row">
                        <div class="form-group">
                            <label for="task-priority">優先級:</label>
                            <select id="task-priority">
                                <option value="LOW">低</option>
                                <option value="MEDIUM" selected>中等</option>
                                <option value="HIGH">高</option>
                                <option value="CRITICAL">緊急</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="task-schedule-type">執行方式:</label>
                            <select id="task-schedule-type">
                                <option value="immediate" selected>立即執行</option>
                                <option value="scheduled">定時執行</option>
                                <option value="recurring">週期執行</option>
                            </select>
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="task-description">任務描述:</label>
                        <textarea id="task-description" rows="3"></textarea>
                    </div>

                    <div class="form-group">
                        <label for="task-parameters">任務參數 (JSON格式):</label>
                        <textarea id="task-parameters" rows="5" placeholder='{"key": "value"}'></textarea>
                    </div>

                    <div class="schedule-settings" id="schedule-settings" style="display: none;">
                        <h4>排程設定</h4>
                        <div class="form-row">
                            <div class="form-group">
                                <label for="schedule-datetime">執行時間:</label>
                                <input type="datetime-local" id="schedule-datetime">
                            </div>
                            <div class="form-group">
                                <label for="schedule-timezone">時區:</label>
                                <select id="schedule-timezone">
                                    <option value="Asia/Taipei" selected>Asia/Taipei</option>
                                    <option value="UTC">UTC</option>
                                </select>
                            </div>
                        </div>
                    </div>

                    <div class="recurring-settings" id="recurring-settings" style="display: none;">
                        <h4>週期設定</h4>
                        <div class="form-row">
                            <div class="form-group">
                                <label for="recurring-pattern">週期模式:</label>
                                <select id="recurring-pattern">
                                    <option value="daily">每日</option>
                                    <option value="weekly">每週</option>
                                    <option value="monthly">每月</option>
                                    <option value="custom">自定義</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label for="recurring-interval">間隔:</label>
                                <input type="number" id="recurring-interval" value="1" min="1">
                            </div>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button id="save-task-btn" class="btn btn-primary">建立任務</button>
                <button class="btn btn-secondary" onclick="closeModal('create-task-modal')">取消</button>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="{{ url_for('shared.static', filename='js/main.js') }}"></script>
    <script src="{{ url_for('shared.static', filename='lib/chart.min.js') }}"></script>
    <script src="{{ url_for('tasks.static', filename='js/task-dashboard.js') }}"></script>
</body>
</html>