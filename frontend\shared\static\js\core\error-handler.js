/**
 * 錯誤處理器
 * 提供統一的錯誤處理和報告功能
 */

class ErrorHandler {
    constructor() {
        this.errorQueue = [];
        this.maxErrors = 50;
        this.isInitialized = false;
        
        this.init();
    }
    
    init() {
        if (this.isInitialized) return;
        
        this.setupGlobalErrorHandlers();
        this.setupUnhandledRejectionHandler();
        this.setupConsoleErrorCapture();
        
        this.isInitialized = true;
        console.log('✅ 錯誤處理器已初始化');
    }
    
    setupGlobalErrorHandlers() {
        // 捕獲 JavaScript 錯誤
        window.addEventListener('error', (event) => {
            this.handleError({
                type: 'javascript',
                message: event.message,
                filename: event.filename,
                lineno: event.lineno,
                colno: event.colno,
                error: event.error,
                stack: event.error?.stack,
                timestamp: new Date().toISOString()
            });
        });
        
        // 捕獲資源載入錯誤
        window.addEventListener('error', (event) => {
            if (event.target !== window) {
                this.handleError({
                    type: 'resource',
                    message: `資源載入失敗: ${event.target.src || event.target.href}`,
                    element: event.target.tagName,
                    source: event.target.src || event.target.href,
                    timestamp: new Date().toISOString()
                });
            }
        }, true);
    }
    
    setupUnhandledRejectionHandler() {
        // 捕獲未處理的 Promise 拒絕
        window.addEventListener('unhandledrejection', (event) => {
            this.handleError({
                type: 'promise',
                message: event.reason?.message || '未處理的 Promise 拒絕',
                reason: event.reason,
                stack: event.reason?.stack,
                timestamp: new Date().toISOString()
            });
        });
    }
    
    setupConsoleErrorCapture() {
        // 捕獲 console.error 調用
        const originalConsoleError = console.error;
        console.error = (...args) => {
            this.handleError({
                type: 'console',
                message: args.join(' '),
                args: args,
                timestamp: new Date().toISOString()
            });
            
            // 調用原始的 console.error
            originalConsoleError.apply(console, args);
        };
    }
    
    handleError(errorInfo) {
        // 添加到錯誤隊列
        this.addToQueue(errorInfo);
        
        // 根據錯誤類型決定處理方式
        switch (errorInfo.type) {
            case 'javascript':
                this.handleJavaScriptError(errorInfo);
                break;
            case 'resource':
                this.handleResourceError(errorInfo);
                break;
            case 'promise':
                this.handlePromiseError(errorInfo);
                break;
            case 'api':
                this.handleApiError(errorInfo);
                break;
            case 'network':
                this.handleNetworkError(errorInfo);
                break;
            default:
                this.handleGenericError(errorInfo);
        }
        
        // 發送錯誤報告（如果配置了）
        this.reportError(errorInfo);
    }
    
    addToQueue(errorInfo) {
        this.errorQueue.push(errorInfo);
        
        // 限制隊列大小
        if (this.errorQueue.length > this.maxErrors) {
            this.errorQueue.shift();
        }
    }
    
    handleJavaScriptError(errorInfo) {
        console.error('🚨 JavaScript 錯誤:', errorInfo);
        
        // 顯示用戶友好的錯誤訊息
        if (window.notificationComponent) {
            window.notificationComponent.error(
                '頁面發生錯誤，請重新整理頁面或聯繫技術支援',
                'JavaScript 錯誤'
            );
        }
        
        // 嚴重錯誤可能需要重新載入頁面
        if (this.isCriticalError(errorInfo)) {
            this.handleCriticalError(errorInfo);
        }
    }
    
    handleResourceError(errorInfo) {
        console.warn('📦 資源載入錯誤:', errorInfo);
        
        // 嘗試重新載入資源
        if (this.shouldRetryResource(errorInfo)) {
            this.retryResourceLoad(errorInfo);
        } else {
            // 顯示資源載入失敗訊息
            if (window.notificationComponent) {
                window.notificationComponent.warning(
                    '部分資源載入失敗，頁面功能可能受影響',
                    '資源載入錯誤'
                );
            }
        }
    }
    
    handlePromiseError(errorInfo) {
        console.error('🔄 Promise 錯誤:', errorInfo);
        
        // 檢查是否為 API 相關錯誤
        if (this.isApiError(errorInfo)) {
            this.handleApiError({
                ...errorInfo,
                type: 'api'
            });
        } else {
            // 顯示一般 Promise 錯誤
            if (window.notificationComponent) {
                window.notificationComponent.error(
                    '操作執行失敗，請稍後再試',
                    '執行錯誤'
                );
            }
        }
    }
    
    handleApiError(errorInfo) {
        console.error('🌐 API 錯誤:', errorInfo);
        
        const { status, message } = errorInfo;
        
        if (status === 401) {
            // 身份驗證錯誤
            this.handleAuthError(errorInfo);
        } else if (status === 403) {
            // 權限錯誤
            this.handlePermissionError(errorInfo);
        } else if (status === 404) {
            // 資源不存在
            this.handleNotFoundError(errorInfo);
        } else if (status >= 500) {
            // 伺服器錯誤
            this.handleServerError(errorInfo);
        } else {
            // 其他 API 錯誤
            if (window.notificationComponent) {
                window.notificationComponent.error(
                    message || 'API 請求失敗，請稍後再試',
                    'API 錯誤'
                );
            }
        }
    }
    
    handleNetworkError(errorInfo) {
        console.error('🌐 網路錯誤:', errorInfo);
        
        if (window.notificationComponent) {
            window.notificationComponent.error(
                '網路連接失敗，請檢查網路連接後重試',
                '網路錯誤'
            );
        }
        
        // 可以添加重試機制
        this.scheduleRetry(errorInfo);
    }
    
    handleGenericError(errorInfo) {
        console.error('❌ 一般錯誤:', errorInfo);
        
        if (window.notificationComponent) {
            window.notificationComponent.error(
                errorInfo.message || '發生未知錯誤',
                '系統錯誤'
            );
        }
    }
    
    handleAuthError(errorInfo) {
        if (window.notificationComponent) {
            window.notificationComponent.error(
                '登入已過期，請重新登入',
                '身份驗證錯誤'
            );
        }
        
        // 可以重定向到登入頁面
        setTimeout(() => {
            window.location.href = '/login';
        }, 3000);
    }
    
    handlePermissionError(errorInfo) {
        if (window.notificationComponent) {
            window.notificationComponent.error(
                '您沒有執行此操作的權限',
                '權限錯誤'
            );
        }
    }
    
    handleNotFoundError(errorInfo) {
        if (window.notificationComponent) {
            window.notificationComponent.warning(
                '請求的資源不存在',
                '資源不存在'
            );
        }
    }
    
    handleServerError(errorInfo) {
        if (window.notificationComponent) {
            window.notificationComponent.error(
                '伺服器發生錯誤，請稍後再試',
                '伺服器錯誤'
            );
        }
        
        // 可以添加自動重試機制
        this.scheduleRetry(errorInfo);
    }
    
    handleCriticalError(errorInfo) {
        console.error('💥 嚴重錯誤:', errorInfo);
        
        if (window.modalManager) {
            window.modalManager.error(
                '系統發生嚴重錯誤，建議重新載入頁面',
                () => {
                    window.location.reload();
                },
                {
                    title: '嚴重錯誤',
                    confirmText: '重新載入',
                    showCancel: false
                }
            );
        } else {
            // 回退方案
            if (confirm('系統發生嚴重錯誤，是否重新載入頁面？')) {
                window.location.reload();
            }
        }
    }
    
    isCriticalError(errorInfo) {
        // 判斷是否為嚴重錯誤
        const criticalPatterns = [
            /Cannot read property/,
            /Cannot read properties/,
            /is not defined/,
            /is not a function/,
            /Maximum call stack/,
            /Out of memory/
        ];
        
        return criticalPatterns.some(pattern => 
            pattern.test(errorInfo.message)
        );
    }
    
    shouldRetryResource(errorInfo) {
        // 判斷是否應該重試資源載入
        const retryableResources = ['.js', '.css', '.json'];
        const source = errorInfo.source || '';
        
        return retryableResources.some(ext => source.includes(ext));
    }
    
    retryResourceLoad(errorInfo) {
        // 重試資源載入
        const source = errorInfo.source;
        if (!source) return;
        
        setTimeout(() => {
            if (source.endsWith('.js')) {
                const script = document.createElement('script');
                script.src = source + '?retry=' + Date.now();
                document.head.appendChild(script);
            } else if (source.endsWith('.css')) {
                const link = document.createElement('link');
                link.rel = 'stylesheet';
                link.href = source + '?retry=' + Date.now();
                document.head.appendChild(link);
            }
        }, 1000);
    }
    
    isApiError(errorInfo) {
        // 判斷是否為 API 相關錯誤
        const message = errorInfo.message || '';
        const apiPatterns = [
            /fetch/i,
            /XMLHttpRequest/i,
            /HTTP/i,
            /API/i,
            /ajax/i
        ];
        
        return apiPatterns.some(pattern => pattern.test(message));
    }
    
    scheduleRetry(errorInfo) {
        // 安排重試
        if (errorInfo.retryCount >= 3) {
            console.warn('⚠️ 重試次數已達上限:', errorInfo);
            return;
        }
        
        const retryDelay = Math.pow(2, errorInfo.retryCount || 0) * 1000; // 指數退避
        
        setTimeout(() => {
            console.log('🔄 重試操作:', errorInfo);
            // 這裡可以添加具體的重試邏輯
        }, retryDelay);
    }
    
    reportError(errorInfo) {
        // 發送錯誤報告到伺服器（如果配置了）
        if (!this.shouldReportError(errorInfo)) {
            return;
        }
        
        try {
            fetch('/api/error-report', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    ...errorInfo,
                    userAgent: navigator.userAgent,
                    url: window.location.href,
                    timestamp: new Date().toISOString()
                })
            }).catch(err => {
                console.warn('錯誤報告發送失敗:', err);
            });
        } catch (err) {
            console.warn('錯誤報告發送失敗:', err);
        }
    }
    
    shouldReportError(errorInfo) {
        // 判斷是否應該報告錯誤
        const reportableTypes = ['javascript', 'promise', 'api'];
        return reportableTypes.includes(errorInfo.type);
    }
    
    // 手動錯誤報告方法
    reportManualError(error, context = {}) {
        this.handleError({
            type: 'manual',
            message: error.message || error,
            stack: error.stack,
            context: context,
            timestamp: new Date().toISOString()
        });
    }
    
    // 獲取錯誤統計
    getErrorStats() {
        const stats = {
            total: this.errorQueue.length,
            byType: {},
            recent: this.errorQueue.slice(-10)
        };
        
        this.errorQueue.forEach(error => {
            stats.byType[error.type] = (stats.byType[error.type] || 0) + 1;
        });
        
        return stats;
    }
    
    // 清除錯誤隊列
    clearErrors() {
        this.errorQueue = [];
        console.log('🧹 錯誤隊列已清除');
    }
    
    // 靜態方法，用於全域初始化
    static init() {
        if (!window.errorHandler) {
            window.errorHandler = new ErrorHandler();
        }
        return window.errorHandler;
    }
}

// 全域實例
if (typeof window !== 'undefined') {
    window.ErrorHandler = ErrorHandler;
    
    // 自動初始化
    document.addEventListener('DOMContentLoaded', function() {
        ErrorHandler.init();
    });
}