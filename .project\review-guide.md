# Code Review Guide

## Overview
This guide defines how AI-assisted code review should be conducted using the comprehensive project documentation generated by handoff-ai.

## Prerequisites
Before conducting code review, ensure all foundational documentation is generated and up-to-date:

### Required Documentation
- [ ] **Design Principles** - Core architectural and design decisions
- [ ] **Constraints** - Technical and business limitations
- [ ] **Golden Paths** - Preferred patterns and approaches
- [ ] **BDD Features** - Behavioral specifications
- [ ] **Architecture** - System design and component relationships
- [ ] **Review Rules** - Custom project-specific criteria
- [ ] **Assumptions** - Project context and decisions

## Review Process

### 1. Context Analysis
- Review the change against documented design principles
- Check compliance with defined constraints
- Verify alignment with golden path patterns
- Assess impact on documented architecture

### 2. Behavioral Validation
- Ensure changes match BDD feature specifications
- Verify expected behavior is maintained or properly updated
- Check for edge cases covered in documentation

### 3. Custom Criteria Check
- Apply project-specific review rules
- Validate against team conventions
- Check domain-specific requirements

### 4. Documentation Impact
- Identify if changes require documentation updates
- Flag potential impacts on existing assumptions
- Suggest documentation improvements

## Review Categories

### Architecture Compliance
- Does this change fit within the documented system design?
- Are component boundaries respected?
- Is the data flow consistent with documented patterns?

### Pattern Adherence
- Does this follow documented golden paths?
- Are established conventions maintained?
- Is error handling consistent with project standards?

### Constraint Validation
- Are technical constraints respected?
- Do changes stay within documented limitations?
- Are performance requirements maintained?

### Feature Alignment
- Does implementation match BDD specifications?
- Are acceptance criteria satisfied?
- Is behavior consistent with documented features?

---
*This guide enables context-aware code review using handoff-ai generated documentation*