"""
Test Database Functionality
Verify that the database repair fixed the issues and email functionality works
"""

import sqlite3
import sys
import os
from pathlib import Path
from datetime import datetime

# Add src directory to Python path
project_root = Path(__file__).parent
src_path = project_root / "src"
sys.path.insert(0, str(src_path))

def test_basic_queries(cursor):
    """Test basic database queries"""
    print("Testing basic database operations:")
    print("-" * 40)
    
    tests = []
    
    # Test 1: Basic SELECT with all columns
    try:
        cursor.execute("SELECT COUNT(*) FROM emails")
        count = cursor.fetchone()[0]
        tests.append(("Basic COUNT query", True, f"Found {count} emails"))
    except Exception as e:
        tests.append(("Basic COUNT query", False, str(e)))
    
    # Test 2: SELECT with problematic 'mo' column
    try:
        cursor.execute("SELECT id, mo, pd, lot FROM emails LIMIT 1")
        result = cursor.fetchone()
        tests.append(("Query with 'mo' column", True, "Query executed successfully"))
    except Exception as e:
        tests.append(("Query with 'mo' column", False, str(e)))
    
    # Test 3: Full ORM-style query (the one that was failing)
    try:
        cursor.execute("""
            SELECT emails.id AS emails_id, emails.message_id AS emails_message_id, 
                   emails.sender AS emails_sender, emails.mo AS emails_mo, 
                   emails.pd AS emails_pd, emails.lot AS emails_lot
            FROM emails LIMIT 1
        """)
        result = cursor.fetchone()
        tests.append(("ORM-style query", True, "Complex query executed successfully"))
    except Exception as e:
        tests.append(("ORM-style query", False, str(e)))
    
    # Test 4: All LLM-related columns
    try:
        cursor.execute("""
            SELECT extraction_method, llm_analysis_result, 
                   llm_analysis_timestamp, llm_service_used 
            FROM emails LIMIT 1
        """)
        result = cursor.fetchone()
        tests.append(("LLM columns query", True, "LLM columns accessible"))
    except Exception as e:
        tests.append(("LLM columns query", False, str(e)))
    
    # Display results
    for test_name, success, message in tests:
        status = "OK" if success else "ERROR"
        print(f"  [{status}] {test_name}: {message}")
    
    return all(test[1] for test in tests)

def test_insert_and_update(cursor):
    """Test insert and update operations"""
    print("\nTesting INSERT and UPDATE operations:")
    print("-" * 40)
    
    try:
        # Test INSERT with new columns
        test_email_data = {
            'message_id': 'test-message-001',
            'sender': '<EMAIL>',
            'sender_display_name': 'Test Sender',
            'subject': 'Test Email for Schema Validation',
            'body': 'This is a test email to validate the database schema.',
            'received_time': datetime.now().isoformat(),
            'created_at': datetime.now().isoformat(),
            'is_read': False,
            'is_processed': False,
            'has_attachments': False,
            'attachment_count': 0,
            'pd': 'TEST-PD-001',
            'lot': 'TEST-LOT-001',
            'mo': 'TEST-MO-001',  # This was the missing column
            'yield_value': '95.5',
            'vendor_code': 'TEST-VENDOR',
            'parse_status': 'pending',
            'extraction_method': 'test',
            'llm_service_used': 'test-llm'
        }
        
        insert_sql = """
            INSERT INTO emails (
                message_id, sender, sender_display_name, subject, body, 
                received_time, created_at, is_read, is_processed, 
                has_attachments, attachment_count, pd, lot, mo, 
                yield_value, vendor_code, parse_status, extraction_method, 
                llm_service_used
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        """
        
        cursor.execute(insert_sql, (
            test_email_data['message_id'], test_email_data['sender'],
            test_email_data['sender_display_name'], test_email_data['subject'],
            test_email_data['body'], test_email_data['received_time'],
            test_email_data['created_at'], test_email_data['is_read'],
            test_email_data['is_processed'], test_email_data['has_attachments'],
            test_email_data['attachment_count'], test_email_data['pd'],
            test_email_data['lot'], test_email_data['mo'],
            test_email_data['yield_value'], test_email_data['vendor_code'],
            test_email_data['parse_status'], test_email_data['extraction_method'],
            test_email_data['llm_service_used']
        ))
        
        test_id = cursor.lastrowid
        print(f"  [OK] INSERT: Test email inserted with ID {test_id}")
        
        # Test UPDATE with new columns
        cursor.execute("""
            UPDATE emails 
            SET mo = ?, extraction_method = ?, llm_analysis_result = ?
            WHERE id = ?
        """, ('UPDATED-MO-001', 'updated-method', '{"test": "result"}', test_id))
        
        print(f"  [OK] UPDATE: Test email updated successfully")
        
        # Test SELECT to verify
        cursor.execute("""
            SELECT mo, extraction_method, llm_analysis_result 
            FROM emails WHERE id = ?
        """, (test_id,))
        
        result = cursor.fetchone()
        if result:
            mo, method, llm_result = result
            print(f"  [OK] VERIFY: mo='{mo}', method='{method}', llm_result='{llm_result}'")
        
        # Clean up test data
        cursor.execute("DELETE FROM emails WHERE id = ?", (test_id,))
        print(f"  [OK] CLEANUP: Test email deleted")
        
        return True
        
    except Exception as e:
        print(f"  [ERROR] Database operations failed: {e}")
        return False

def main():
    print("=" * 70)
    print("DATABASE FUNCTIONALITY TEST")
    print("=" * 70)
    
    db_path = project_root / "data" / "email_inbox.db"
    
    try:
        conn = sqlite3.connect(str(db_path))
        cursor = conn.cursor()
        
        # Run all tests
        basic_test = test_basic_queries(cursor)
        insert_test = test_insert_and_update(cursor)
        
        # Commit any test changes
        conn.commit()
        
        print("\n" + "=" * 70)
        print("TEST SUMMARY")
        print("=" * 70)
        
        all_tests_passed = basic_test and insert_test
        
        if all_tests_passed:
            print("SUCCESS - All database functionality tests passed!")
            print("The database schema repair was successful.")
            print("Email functionality should now work correctly.")
        else:
            print("FAILURE - Some tests failed.")
            print("Additional investigation may be needed.")
        
        # Show current database status
        cursor.execute("SELECT COUNT(*) FROM emails")
        email_count = cursor.fetchone()[0]
        
        cursor.execute("PRAGMA table_info(emails)")
        columns = cursor.fetchall()
        
        print(f"\nDatabase Status:")
        print(f"  Total emails: {email_count}")
        print(f"  Total columns in emails table: {len(columns)}")
        print(f"  Database file size: {os.path.getsize(db_path)} bytes")
        
        return all_tests_passed
        
    except Exception as e:
        print(f"ERROR: {e}")
        return False
        
    finally:
        if 'conn' in locals():
            conn.close()

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)