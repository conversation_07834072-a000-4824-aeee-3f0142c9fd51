/**
 * CSV Processor Module
 * CSV 處理模組 JavaScript
 */

class CSVProcessor {
    constructor() {
        this.uploadedFiles = [];
        this.processedData = null;
        this.currentStep = 'upload';
        this.charts = {};
        
        this.init();
    }
    
    init() {
        console.log('CSV Processor: Initializing...');
        this.bindEvents();
        this.setupUploadArea();
    }
    
    bindEvents() {
        // 檔案上傳
        document.addEventListener('change', (e) => {
            if (e.target.matches('#csvFileInput')) {
                this.handleFileSelect(e);
            }
        });
        
        // 拖放上傳
        document.addEventListener('dragover', (e) => {
            if (e.target.closest('.csv-upload-area')) {
                e.preventDefault();
                e.target.closest('.csv-upload-area').classList.add('dragover');
            }
        });
        
        document.addEventListener('dragleave', (e) => {
            if (e.target.closest('.csv-upload-area')) {
                e.target.closest('.csv-upload-area').classList.remove('dragover');
            }
        });
        
        document.addEventListener('drop', (e) => {
            if (e.target.closest('.csv-upload-area')) {
                e.preventDefault();
                e.target.closest('.csv-upload-area').classList.remove('dragover');
                this.handleFileDrop(e);
            }
        });
        
        // 處理按鈕
        document.addEventListener('click', (e) => {
            if (e.target.matches('.process-csv-btn')) {
                this.handleProcessCSV(e);
            }
        });
        
        // 下載按鈕
        document.addEventListener('click', (e) => {
            if (e.target.matches('.download-result-btn')) {
                this.handleDownloadResult(e);
            }
        });
        
        // 預覽按鈕
        document.addEventListener('click', (e) => {
            if (e.target.matches('.preview-csv-btn')) {
                this.handlePreviewCSV(e);
            }
        });
        
        // 清除按鈕
        document.addEventListener('click', (e) => {
            if (e.target.matches('.clear-csv-btn')) {
                this.handleClearCSV(e);
            }
        });
    }
    
    setupUploadArea() {
        const uploadArea = document.querySelector('.csv-upload-area');
        if (uploadArea) {
            uploadArea.addEventListener('click', () => {
                document.getElementById('csvFileInput').click();
            });
        }
    }
    
    handleFileSelect(e) {
        const files = Array.from(e.target.files);
        this.processFiles(files);
    }
    
    handleFileDrop(e) {
        const files = Array.from(e.dataTransfer.files);
        this.processFiles(files);
    }
    
    processFiles(files) {
        // 過濾只保留 CSV 檔案
        const csvFiles = files.filter(file => 
            file.type === 'text/csv' || 
            file.name.toLowerCase().endsWith('.csv')
        );
        
        if (csvFiles.length === 0) {
            this.showError('請選擇有效的 CSV 檔案');
            return;
        }
        
        this.uploadedFiles = csvFiles;
        this.displayUploadedFiles();
        this.showProcessButton();
    }
    
    displayUploadedFiles() {
        const container = document.querySelector('.uploaded-files');
        if (!container) return;
        
        container.innerHTML = '';
        
        this.uploadedFiles.forEach((file, index) => {
            const fileItem = this.createFileItem(file, index);
            container.appendChild(fileItem);
        });
        
        // 顯示上傳區域
        container.style.display = 'block';
    }
    
    createFileItem(file, index) {
        const item = document.createElement('div');
        item.className = 'uploaded-file-item';
        item.dataset.fileIndex = index;
        
        item.innerHTML = `
            <div class="file-info">
                <i class="fas fa-file-csv file-icon"></i>
                <div class="file-details">
                    <span class="file-name">${file.name}</span>
                    <span class="file-size">${this.formatFileSize(file.size)}</span>
                </div>
            </div>
            <div class="file-actions">
                <button class="btn btn-sm btn-primary preview-csv-btn" data-file-index="${index}">預覽</button>
                <button class="btn btn-sm btn-danger remove-file-btn" data-file-index="${index}">移除</button>
            </div>
        `;
        
        // 綁定移除按鈕事件
        item.querySelector('.remove-file-btn').addEventListener('click', () => {
            this.removeFile(index);
        });
        
        return item;
    }
    
    removeFile(index) {
        this.uploadedFiles.splice(index, 1);
        this.displayUploadedFiles();
        
        if (this.uploadedFiles.length === 0) {
            this.hideProcessButton();
        }
    }
    
    showProcessButton() {
        const processBtn = document.querySelector('.process-csv-btn');
        if (processBtn) {
            processBtn.style.display = 'inline-block';
        }
    }
    
    hideProcessButton() {
        const processBtn = document.querySelector('.process-csv-btn');
        if (processBtn) {
            processBtn.style.display = 'none';
        }
    }
    
    async handleProcessCSV(e) {
        if (this.uploadedFiles.length === 0) {
            this.showError('請先選擇要處理的 CSV 檔案');
            return;
        }
        
        const processBtn = e.target;
        const originalText = processBtn.textContent;
        
        // 顯示處理狀態
        processBtn.textContent = '處理中...';
        processBtn.disabled = true;
        
        try {
            // 準備檔案資料
            const formData = new FormData();
            this.uploadedFiles.forEach((file, index) => {
                formData.append(`csv_file_${index}`, file);
            });
            
            // 加入處理選項
            const options = this.getProcessingOptions();
            Object.keys(options).forEach(key => {
                formData.append(key, options[key]);
            });
            
            const response = await fetch('/analytics/api/csv/process', {
                method: 'POST',
                body: formData
            });
            
            if (response.ok) {
                this.processedData = await response.json();
                this.displayProcessingResults();
                this.showSuccess('CSV 檔案處理完成！');
            } else {
                const error = await response.json();
                throw new Error(error.message || 'CSV 處理失敗');
            }
            
        } catch (error) {
            console.error('CSV processing error:', error);
            this.showError('處理失敗：' + error.message);
        } finally {
            processBtn.textContent = originalText;
            processBtn.disabled = false;
        }
    }
    
    getProcessingOptions() {
        // 從表單收集處理選項
        return {
            'remove_duplicates': document.querySelector('#removeDuplicates')?.checked || false,
            'fill_missing_values': document.querySelector('#fillMissing')?.checked || false,
            'normalize_data': document.querySelector('#normalizeData')?.checked || false,
            'generate_summary': document.querySelector('#generateSummary')?.checked || true,
            'create_charts': document.querySelector('#createCharts')?.checked || true
        };
    }
    
    displayProcessingResults() {
        if (!this.processedData) return;
        
        // 顯示結果區域
        const resultsContainer = document.querySelector('.processing-results');
        if (resultsContainer) {
            resultsContainer.style.display = 'block';
        }
        
        // 更新統計資訊
        this.displayStatistics();
        
        // 顯示資料預覽
        this.displayDataPreview();
        
        // 生成圖表
        this.generateCharts();
        
        // 顯示下載按鈕
        this.showDownloadButton();
    }
    
    displayStatistics() {
        const stats = this.processedData.statistics;
        if (!stats) return;
        
        // 更新統計數字
        this.updateElement('.total-rows', stats.total_rows);
        this.updateElement('.total-columns', stats.total_columns);
        this.updateElement('.processed-rows', stats.processed_rows);
        this.updateElement('.removed-duplicates', stats.removed_duplicates);
    }
    
    displayDataPreview() {
        const preview = this.processedData.preview;
        if (!preview || !preview.data) return;
        
        const table = document.querySelector('.data-preview-table');
        if (!table) return;
        
        // 清空現有內容
        table.innerHTML = '';
        
        // 創建表頭
        if (preview.columns && preview.columns.length > 0) {
            const thead = document.createElement('thead');
            const headerRow = document.createElement('tr');
            
            preview.columns.forEach(column => {
                const th = document.createElement('th');
                th.textContent = column;
                headerRow.appendChild(th);
            });
            
            thead.appendChild(headerRow);
            table.appendChild(thead);
        }
        
        // 創建表格內容
        const tbody = document.createElement('tbody');
        preview.data.forEach(row => {
            const tr = document.createElement('tr');
            
            Object.values(row).forEach(cellValue => {
                const td = document.createElement('td');
                td.textContent = cellValue || '';
                tr.appendChild(td);
            });
            
            tbody.appendChild(tr);
        });
        
        table.appendChild(tbody);
    }
    
    generateCharts() {
        const chartData = this.processedData.charts;
        if (!chartData) return;
        
        // 生成各種圖表
        if (chartData.distribution) {
            this.createDistributionChart(chartData.distribution);
        }
        
        if (chartData.trends) {
            this.createTrendChart(chartData.trends);
        }
        
        if (chartData.correlation) {
            this.createCorrelationChart(chartData.correlation);
        }
    }
    
    createDistributionChart(data) {
        const ctx = document.getElementById('distributionChart');
        if (!ctx) return;
        
        this.charts.distribution = new Chart(ctx, {
            type: 'bar',
            data: {
                labels: data.labels,
                datasets: [{
                    label: '分佈',
                    data: data.values,
                    backgroundColor: 'rgba(0, 123, 255, 0.8)',
                    borderColor: '#007bff',
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        beginAtZero: true
                    }
                }
            }
        });
    }
    
    createTrendChart(data) {
        const ctx = document.getElementById('trendChart');
        if (!ctx) return;
        
        this.charts.trend = new Chart(ctx, {
            type: 'line',
            data: {
                labels: data.labels,
                datasets: data.datasets.map((dataset, index) => ({
                    label: dataset.label,
                    data: dataset.data,
                    borderColor: this.getChartColor(index),
                    backgroundColor: this.getChartColor(index, 0.1),
                    tension: 0.4
                }))
            },
            options: {
                responsive: true,
                maintainAspectRatio: false
            }
        });
    }
    
    handlePreviewCSV(e) {
        const fileIndex = parseInt(e.target.dataset.fileIndex);
        const file = this.uploadedFiles[fileIndex];
        
        if (!file) return;
        
        const reader = new FileReader();
        reader.onload = (event) => {
            const csvData = event.target.result;
            this.showCSVPreview(file.name, csvData);
        };
        
        // 只讀取前1000行用於預覽
        const chunk = file.slice(0, 100000); // 約100KB
        reader.readAsText(chunk);
    }
    
    showCSVPreview(fileName, csvData) {
        // 解析 CSV 資料
        const lines = csvData.split('\n').slice(0, 10); // 只顯示前10行
        const rows = lines.map(line => line.split(','));
        
        // 創建預覽模態框
        const modal = this.createPreviewModal(fileName, rows);
        document.body.appendChild(modal);
        
        const modalInstance = new bootstrap.Modal(modal);
        modalInstance.show();
        
        // 模態框關閉後移除
        modal.addEventListener('hidden.bs.modal', () => {
            modal.remove();
        });
    }
    
    createPreviewModal(fileName, rows) {
        const modal = document.createElement('div');
        modal.className = 'modal fade';
        modal.innerHTML = `
            <div class="modal-dialog modal-lg">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">CSV 預覽 - ${fileName}</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <div class="csv-preview-table-container">
                            <table class="table table-striped">
                                ${this.generateTableHTML(rows)}
                            </table>
                        </div>
                        <p class="text-muted">僅顯示前10行資料預覽</p>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">關閉</button>
                    </div>
                </div>
            </div>
        `;
        
        return modal;
    }
    
    generateTableHTML(rows) {
        if (rows.length === 0) return '';
        
        let html = '<thead><tr>';
        rows[0].forEach(header => {
            html += `<th>${header}</th>`;
        });
        html += '</tr></thead><tbody>';
        
        rows.slice(1).forEach(row => {
            html += '<tr>';
            row.forEach(cell => {
                html += `<td>${cell}</td>`;
            });
            html += '</tr>';
        });
        
        html += '</tbody>';
        return html;
    }
    
    showDownloadButton() {
        const downloadBtn = document.querySelector('.download-result-btn');
        if (downloadBtn) {
            downloadBtn.style.display = 'inline-block';
        }
    }
    
    handleDownloadResult(e) {
        if (!this.processedData || !this.processedData.download_url) {
            this.showError('沒有可下載的處理結果');
            return;
        }
        
        const format = e.target.dataset.format || 'csv';
        const downloadUrl = `${this.processedData.download_url}?format=${format}`;
        
        window.open(downloadUrl, '_blank');
    }
    
    handleClearCSV(e) {
        this.uploadedFiles = [];
        this.processedData = null;
        
        // 清空上傳區域
        const uploadedFilesContainer = document.querySelector('.uploaded-files');
        if (uploadedFilesContainer) {
            uploadedFilesContainer.innerHTML = '';
            uploadedFilesContainer.style.display = 'none';
        }
        
        // 隱藏結果區域
        const resultsContainer = document.querySelector('.processing-results');
        if (resultsContainer) {
            resultsContainer.style.display = 'none';
        }
        
        // 重置檔案輸入
        const fileInput = document.getElementById('csvFileInput');
        if (fileInput) {
            fileInput.value = '';
        }
        
        this.hideProcessButton();
    }
    
    getChartColor(index, alpha = 1) {
        const colors = [
            '#007bff', '#28a745', '#ffc107', '#dc3545', 
            '#17a2b8', '#6c757d', '#fd7e14', '#6f42c1'
        ];
        
        const color = colors[index % colors.length];
        
        if (alpha < 1) {
            // 轉換為 rgba
            const hex = color.replace('#', '');
            const r = parseInt(hex.substring(0, 2), 16);
            const g = parseInt(hex.substring(2, 4), 16);
            const b = parseInt(hex.substring(4, 6), 16);
            return `rgba(${r}, ${g}, ${b}, ${alpha})`;
        }
        
        return color;
    }
    
    formatFileSize(bytes) {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }
    
    updateElement(selector, value) {
        const element = document.querySelector(selector);
        if (element) {
            element.textContent = value;
        }
    }
    
    showSuccess(message) {
        const alert = document.createElement('div');
        alert.className = 'alert alert-success alert-dismissible';
        alert.innerHTML = `
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;
        
        const container = document.querySelector('.csv-processor-container');
        if (container) {
            container.insertBefore(alert, container.firstChild);
        }
    }
    
    showError(message) {
        const alert = document.createElement('div');
        alert.className = 'alert alert-danger alert-dismissible';
        alert.innerHTML = `
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;
        
        const container = document.querySelector('.csv-processor-container');
        if (container) {
            container.insertBefore(alert, container.firstChild);
        }
    }
}

// 頁面載入完成後初始化
document.addEventListener('DOMContentLoaded', function() {
    if (document.querySelector('.csv-processor-container')) {
        new CSVProcessor();
    }
});