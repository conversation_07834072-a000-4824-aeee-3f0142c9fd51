<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>EQC 處理歷史記錄</title>
    <link rel="stylesheet" href="{{ url_for('shared.static', filename='css/global.css') }}">
    <link rel="stylesheet" href="{{ url_for('eqc.static', filename='css/eqc.css') }}">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft JhengHei', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
        }

        .stats-bar {
            display: flex;
            justify-content: space-around;
            background: #f8f9fa;
            padding: 20px;
            border-bottom: 1px solid #e9ecef;
        }

        .stat-item {
            text-align: center;
        }

        .stat-value {
            font-size: 2em;
            font-weight: bold;
            color: #667eea;
        }

        .stat-label {
            color: #6c757d;
            margin-top: 5px;
        }

        .history-list {
            padding: 30px;
        }

        .history-item {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 15px;
            border-left: 4px solid #667eea;
            transition: transform 0.2s ease;
        }

        .history-item:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }

        .history-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
        }

        .history-title {
            font-size: 1.2em;
            font-weight: 600;
            color: #2c3e50;
        }

        .history-status {
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 0.8em;
            font-weight: 500;
            text-transform: uppercase;
        }

        .status-completed {
            background: #e8f5e8;
            color: #388e3c;
        }

        .status-failed {
            background: #ffebee;
            color: #d32f2f;
        }

        .status-processing {
            background: #e3f2fd;
            color: #1976d2;
        }

        .history-details {
            color: #6c757d;
            font-size: 0.9em;
            line-height: 1.5;
        }

        .btn {
            padding: 8px 16px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 0.9em;
            text-decoration: none;
            display: inline-block;
            margin-right: 10px;
            transition: background-color 0.3s ease;
        }

        .btn-primary {
            background: #667eea;
            color: white;
        }

        .btn-primary:hover {
            background: #5a6fd8;
        }

        .btn-secondary {
            background: #6c757d;
            color: white;
        }

        .btn-secondary:hover {
            background: #5a6268;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>📊 EQC 處理歷史記錄</h1>
            <p>查看所有 EQC 處理任務的執行記錄和結果</p>
        </div>

        <div class="stats-bar">
            <div class="stat-item">
                <div class="stat-value" id="totalTasks">0</div>
                <div class="stat-label">總任務數</div>
            </div>
            <div class="stat-item">
                <div class="stat-value" id="completedTasks">0</div>
                <div class="stat-label">已完成</div>
            </div>
            <div class="stat-item">
                <div class="stat-value" id="failedTasks">0</div>
                <div class="stat-label">失敗</div>
            </div>
            <div class="stat-item">
                <div class="stat-value" id="processingTasks">0</div>
                <div class="stat-label">處理中</div>
            </div>
        </div>

        <div class="history-list" id="historyList">
            <!-- 歷史記錄項目將動態載入 -->
            <div class="history-item">
                <div class="history-header">
                    <div class="history-title">範例 EQC 處理任務</div>
                    <div class="history-status status-completed">已完成</div>
                </div>
                <div class="history-details">
                    <p><strong>處理時間：</strong>2025-01-08 10:30:15</p>
                    <p><strong>資料夾：</strong>D:\project\python\outlook_summary\doc\20250523</p>
                    <p><strong>處理時長：</strong>45.2 秒</p>
                    <p><strong>結果：</strong>成功生成 EQCTOTALDATA.xlsx</p>
                </div>
                <div style="margin-top: 15px;">
                    <a href="#" class="btn btn-primary">下載結果</a>
                    <a href="#" class="btn btn-secondary">查看詳情</a>
                </div>
            </div>
        </div>
    </div>

    <!-- 載入核心依賴 -->
    <script src="{{ url_for('shared.static', filename='js/core/utils.js') }}"></script>
    <script src="{{ url_for('shared.static', filename='js/core/api-client.js') }}"></script>
    <script src="{{ url_for('eqc.static', filename='js/eqc-history.js') }}"></script>

    <script>
        // 初始化歷史記錄頁面
        document.addEventListener('DOMContentLoaded', function() {
            loadHistoryData();
        });

        function loadHistoryData() {
            // 載入歷史記錄數據
            console.log('載入 EQC 處理歷史記錄');
        }
    </script>
</body>
</html>