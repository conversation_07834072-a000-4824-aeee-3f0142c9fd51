/**
 * 統一監控儀表板 - 圖表和視覺化處理
 * 負責處理資料視覺化和圖表渲染
 */

class DashboardCharts {
    constructor() {
        this.charts = new Map();
        this.chartData = new Map();
        this.animationFrameId = null;
        
        this.init();
    }
    
    init() {
        // 初始化所有圖表元件
        this.initProgressBars();
        this.setupAnimations();
    }
    
    initProgressBars() {
        // 初始化進度條
        const progressBars = [
            { id: 'cpuProgress', textId: 'cpuText', type: 'cpu' },
            { id: 'memoryProgress', textId: 'memoryText', type: 'memory' },
            { id: 'diskProgress', textId: 'diskText', type: 'disk' }
        ];
        
        progressBars.forEach(bar => {
            this.createProgressBar(bar.id, bar.textId, bar.type);
        });
    }
    
    createProgressBar(progressId, textId, type) {
        const progressElement = document.getElementById(progressId);
        const textElement = document.getElementById(textId);
        
        if (progressElement && textElement) {
            // 初始化進度條資料
            this.chartData.set(type, {
                element: progressElement,
                textElement: textElement,
                currentValue: 0,
                targetValue: 0,
                history: []
            });
        }
    }
    
    updateProgressBar(type, value, animated = true) {
        const data = this.chartData.get(type);
        if (!data) return;
        
        // 限制值在 0-100 之間
        value = Math.max(0, Math.min(100, value));
        
        // 更新目標值
        data.targetValue = value;
        
        // 添加到歷史記錄
        data.history.push({
            value: value,
            timestamp: Date.now()
        });
        
        // 保持最近 50 個資料點
        if (data.history.length > 50) {
            data.history.shift();
        }
        
        if (animated) {
            this.animateProgressBar(type);
        } else {
            this.setProgressBarValue(type, value);
        }
    }
    
    animateProgressBar(type) {
        const data = this.chartData.get(type);
        if (!data) return;
        
        const animate = () => {
            const diff = data.targetValue - data.currentValue;
            const step = diff * 0.1; // 動畫速度
            
            if (Math.abs(diff) > 0.1) {
                data.currentValue += step;
                this.setProgressBarValue(type, data.currentValue);
                requestAnimationFrame(animate);
            } else {
                data.currentValue = data.targetValue;
                this.setProgressBarValue(type, data.currentValue);
            }
        };
        
        animate();
    }
    
    setProgressBarValue(type, value) {
        const data = this.chartData.get(type);
        if (!data) return;
        
        // 更新進度條寬度
        data.element.style.width = `${value}%`;
        
        // 更新文字
        data.textElement.textContent = `${Math.round(value)}%`;
        
        // 根據值設置顏色類別
        data.element.className = 'progress-fill';
        if (value >= 95) {
            data.element.classList.add('error');
        } else if (value >= 80) {
            data.element.classList.add('warning');
        }
    }
    
    updateVendorStats(vendorData) {
        const vendorGrid = document.getElementById('vendorGrid');
        if (!vendorGrid || !vendorData) return;
        
        // 清空現有內容
        vendorGrid.innerHTML = '';
        
        // 支援的廠商列表
        const supportedVendors = ['GTK', 'JCET', 'ETD', 'LINGSEN', 'XAHT'];
        
        supportedVendors.forEach(vendor => {
            const count = vendorData.vendor_queue_counts?.[vendor] || 0;
            const successRate = vendorData.vendor_success_rates?.[vendor] || 0;
            
            const vendorItem = document.createElement('div');
            vendorItem.className = 'vendor-item fade-in';
            vendorItem.innerHTML = `
                <span class="vendor-name">${vendor}</span>
                <span class="vendor-count">${count}</span>
                <span class="vendor-success-rate">${(successRate * 100).toFixed(1)}%</span>
            `;
            
            vendorGrid.appendChild(vendorItem);
        });
    }
    
    updateTaskTypeStats(dramatiqData) {
        const taskTypeGrid = document.getElementById('taskTypeGrid');
        if (!taskTypeGrid || !dramatiqData) return;
        
        // 清空現有內容
        taskTypeGrid.innerHTML = '';
        
        // 完整的 8 種任務類型配置
        const taskTypes = [
            { key: 'code_comparison', name: 'Code Comparison', class: 'code-comparison' },
            { key: 'csv_to_summary', name: 'CSV to Summary', class: 'csv-to-summary' },
            { key: 'compression', name: 'Compression', class: 'compression' },
            { key: 'decompression', name: 'Decompression', class: 'decompression' },
            { key: 'email_processing', name: 'Email Processing', class: 'email-processing' },
            { key: 'data_analysis', name: 'Data Analysis', class: 'data-analysis' },
            { key: 'file_processing', name: 'File Processing', class: 'file-processing' },
            { key: 'batch_processing', name: 'Batch Processing', class: 'batch-processing' }
        ];
        
        taskTypes.forEach(taskType => {
            const taskData = dramatiqData.task_type_counts?.[taskType.key] || {
                active: 0, pending: 0, completed: 0, failed: 0
            };
            
            const total = taskData.active + taskData.pending + taskData.completed + taskData.failed;
            const successRate = total > 0 ? ((taskData.completed / total) * 100).toFixed(1) : '0.0';
            
            const taskItem = document.createElement('div');
            taskItem.className = `task-type-item ${taskType.class} slide-up`;
            taskItem.style.cursor = 'pointer';
            
            // 添加點擊事件以顯示詳細資訊
            taskItem.addEventListener('click', () => {
                this.showTaskTypeDetails(taskType.key, taskData);
            });
            
            taskItem.innerHTML = `
                <div class="task-type-header">
                    <span class="task-type-name">${taskType.name}</span>
                    <span class="task-type-status ${this.getTaskTypeStatusClass(taskData)}">${this.getTaskTypeStatusText(taskData)}</span>
                </div>
                <div class="task-type-metrics">
                    <div class="task-metric">
                        <span class="task-metric-value">${taskData.active}</span>
                        <span class="task-metric-label">活躍</span>
                    </div>
                    <div class="task-metric">
                        <span class="task-metric-value">${taskData.pending}</span>
                        <span class="task-metric-label">待處理</span>
                    </div>
                    <div class="task-metric">
                        <span class="task-metric-value">${taskData.completed}</span>
                        <span class="task-metric-label">已完成</span>
                    </div>
                    <div class="task-metric">
                        <span class="task-metric-value">${successRate}%</span>
                        <span class="task-metric-label">成功率</span>
                    </div>
                </div>
                <div class="task-type-actions">
                    <button class="task-action-btn" onclick="event.stopPropagation(); showTaskFilter('${taskType.key}')">篩選</button>
                    <button class="task-action-btn" onclick="event.stopPropagation(); showTaskHistory('${taskType.key}')">歷史</button>
                </div>
            `;
            
            taskTypeGrid.appendChild(taskItem);
        });
    }
    
    getTaskTypeStatusClass(taskData) {
        const total = taskData.active + taskData.pending + taskData.completed + taskData.failed;
        if (total === 0) return 'idle';
        
        const failureRate = taskData.failed / total;
        const pendingRate = taskData.pending / total;
        
        if (failureRate > 0.25) return 'error';
        if (failureRate > 0.1 || pendingRate > 0.8) return 'warning';
        if (taskData.active > 0) return 'running';
        return 'idle';
    }
    
    getTaskTypeStatusText(taskData) {
        const total = taskData.active + taskData.pending + taskData.completed + taskData.failed;
        if (total === 0) return '空閒';
        
        const failureRate = taskData.failed / total;
        const pendingRate = taskData.pending / total;
        
        if (failureRate > 0.25) return '異常';
        if (failureRate > 0.1 || pendingRate > 0.8) return '警告';
        if (taskData.active > 0) return '運行中';
        return '正常';
    }
    
    showTaskTypeDetails(taskType, taskData) {
        // 觸發任務類型詳情顯示事件
        window.dispatchEvent(new CustomEvent('showTaskTypeDetails', {
            detail: { taskType, taskData }
        }));
    }
    
    updateWorkerStats(dramatiqData) {
        const workerGrid = document.getElementById('workerGrid');
        if (!workerGrid || !dramatiqData) return;
        
        // 清空現有內容
        workerGrid.innerHTML = '';
        
        const workerStatus = dramatiqData.worker_status || {};
        const workerLoad = dramatiqData.worker_load || {};
        
        Object.keys(workerStatus).forEach(workerName => {
            const status = workerStatus[workerName];
            const load = workerLoad[workerName] || 0;
            
            const workerItem = document.createElement('div');
            workerItem.className = 'worker-item fade-in';
            workerItem.innerHTML = `
                <div class="worker-info">
                    <span class="worker-status-indicator ${status}"></span>
                    <span class="worker-name">${workerName}</span>
                </div>
                <div class="worker-load">負載: ${load}</div>
            `;
            
            workerGrid.appendChild(workerItem);
        });
        
        // 如果沒有工作者，顯示提示
        if (Object.keys(workerStatus).length === 0) {
            workerGrid.innerHTML = '<div class="no-data">暫無工作者資料</div>';
        }
    }
    
    updateServiceHealth(systemData) {
        const serviceGrid = document.getElementById('serviceGrid');
        if (!serviceGrid || !systemData) return;
        
        // 清空現有內容
        serviceGrid.innerHTML = '';
        
        const serviceHealth = systemData.service_health || {};
        
        // 預設服務列表
        const defaultServices = {
            'email_service': '郵件服務',
            'dramatiq_service': 'Dramatiq服務',
            'database': '資料庫',
            'scheduler': '排程器',
            'file_system': '檔案系統'
        };
        
        Object.keys(defaultServices).forEach(serviceKey => {
            const serviceName = defaultServices[serviceKey];
            const status = serviceHealth[serviceKey] || 'unknown';
            
            const serviceItem = document.createElement('div');
            serviceItem.className = 'service-item fade-in';
            serviceItem.innerHTML = `
                <span class="service-name">${serviceName}</span>
                <span class="service-status ${status}">${this.getStatusText(status)}</span>
            `;
            
            serviceGrid.appendChild(serviceItem);
        });
    }
    
    updateFileStats(fileData) {
        // 更新檔案類型統計
        const fileTypeGrid = document.getElementById('fileTypeGrid');
        if (fileTypeGrid && fileData.file_type_counts) {
            fileTypeGrid.innerHTML = '';
            
            Object.entries(fileData.file_type_counts).forEach(([fileType, count]) => {
                const fileItem = document.createElement('div');
                fileItem.className = 'file-type-item fade-in';
                fileItem.innerHTML = `
                    <span class="file-type-name">${fileType.toUpperCase()}</span>
                    <span class="file-type-count">${count}</span>
                `;
                fileTypeGrid.appendChild(fileItem);
            });
        }
        
        // 更新儲存空間統計
        const storageGrid = document.getElementById('storageGrid');
        if (storageGrid && fileData) {
            storageGrid.innerHTML = '';
            
            const storageItems = [
                { name: '暫存資料夾', size: fileData.temp_folder_size_mb || 0 },
                { name: '上傳資料夾', size: fileData.upload_folder_size_mb || 0 },
                { name: '處理完成', size: fileData.processed_folder_size_mb || 0 }
            ];
            
            storageItems.forEach(item => {
                const storageItem = document.createElement('div');
                storageItem.className = 'storage-item fade-in';
                storageItem.innerHTML = `
                    <span class="storage-name">${item.name}</span>
                    <span class="storage-size">${this.formatFileSize(item.size)}</span>
                `;
                storageGrid.appendChild(storageItem);
            });
        }
    }
    
    updateAlertStats(alerts) {
        // 統計不同級別的告警數量
        const alertCounts = {
            critical: 0,
            error: 0,
            warning: 0,
            info: 0
        };
        
        if (Array.isArray(alerts)) {
            alerts.forEach(alert => {
                if (alertCounts.hasOwnProperty(alert.level)) {
                    alertCounts[alert.level]++;
                }
            });
        }
        
        // 更新告警計數顯示
        Object.keys(alertCounts).forEach(level => {
            const countElement = document.getElementById(`${level}AlertCount`);
            if (countElement) {
                countElement.textContent = alertCounts[level];
            }
        });
        
        // 更新告警列表
        this.updateAlertList(alerts);
    }
    
    updateAlertList(alerts) {
        const alertList = document.getElementById('alertList');
        if (!alertList) return;
        
        // 清空現有內容
        alertList.innerHTML = '';
        
        if (!Array.isArray(alerts) || alerts.length === 0) {
            alertList.innerHTML = '<div class="no-alerts">目前沒有活躍告警</div>';
            return;
        }
        
        // 按時間排序（最新的在前）
        const sortedAlerts = alerts.sort((a, b) => 
            new Date(b.triggered_at) - new Date(a.triggered_at)
        );
        
        sortedAlerts.forEach(alert => {
            const alertItem = document.createElement('div');
            alertItem.className = `alert-item ${alert.level} fade-in`;
            alertItem.innerHTML = `
                <div class="alert-header">
                    <h5 class="alert-title">${alert.title}</h5>
                    <span class="alert-time">${this.formatTime(alert.triggered_at)}</span>
                </div>
                <div class="alert-message">${alert.message}</div>
                <div class="alert-actions">
                    <button class="alert-action-btn acknowledge" onclick="acknowledgeAlert('${alert.id}')">
                        確認
                    </button>
                    <button class="alert-action-btn dismiss" onclick="dismissAlert('${alert.id}')">
                        忽略
                    </button>
                </div>
            `;
            
            alertList.appendChild(alertItem);
        });
    }
    
    setupAnimations() {
        // 設置動畫觀察器
        if ('IntersectionObserver' in window) {
            const observer = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        entry.target.classList.add('animate');
                    }
                });
            }, { threshold: 0.1 });
            
            // 觀察所有需要動畫的元素
            document.querySelectorAll('.fade-in, .slide-up').forEach(el => {
                observer.observe(el);
            });
        }
    }
    
    // 工具方法
    getStatusText(status) {
        const statusMap = {
            'healthy': '正常',
            'warning': '警告',
            'error': '錯誤',
            'unknown': '未知'
        };
        return statusMap[status] || status;
    }
    
    formatFileSize(sizeInMB) {
        if (sizeInMB < 1) {
            return `${(sizeInMB * 1024).toFixed(1)} KB`;
        } else if (sizeInMB < 1024) {
            return `${sizeInMB.toFixed(1)} MB`;
        } else {
            return `${(sizeInMB / 1024).toFixed(1)} GB`;
        }
    }
    
    formatTime(timestamp) {
        const date = new Date(timestamp);
        const now = new Date();
        const diff = now - date;
        
        if (diff < 60000) { // 1分鐘內
            return '剛剛';
        } else if (diff < 3600000) { // 1小時內
            return `${Math.floor(diff / 60000)} 分鐘前`;
        } else if (diff < 86400000) { // 24小時內
            return `${Math.floor(diff / 3600000)} 小時前`;
        } else {
            return date.toLocaleString('zh-TW');
        }
    }
    
    updateTrendData(trendData) {
        // 更新趨勢圖表資料
        if (trendData.historical_metrics) {
            this.renderHistoricalChart(trendData.historical_metrics);
        }
        
        if (trendData.load_prediction) {
            this.updateLoadPrediction(trendData.load_prediction);
        }
        
        if (trendData.anomalies) {
            this.updateAnomalies(trendData.anomalies);
        }
    }
    
    renderHistoricalChart(historicalData) {
        const chartContainer = document.getElementById('historicalChart');
        if (!chartContainer) return;
        
        // 簡單的歷史圖表渲染（可以後續整合專業圖表庫）
        const chartHtml = `
            <div class="historical-chart">
                <h4>歷史趨勢</h4>
                <div class="chart-legend">
                    <span class="legend-item cpu">CPU</span>
                    <span class="legend-item memory">記憶體</span>
                    <span class="legend-item tasks">任務數</span>
                </div>
                <div class="chart-data">
                    ${historicalData.map((point, index) => `
                        <div class="data-point" style="left: ${(index / historicalData.length) * 100}%">
                            <div class="cpu-bar" style="height: ${point.cpu_percent || 0}%"></div>
                            <div class="memory-bar" style="height: ${point.memory_percent || 0}%"></div>
                            <div class="task-bar" style="height: ${Math.min((point.active_tasks || 0) * 2, 100)}%"></div>
                        </div>
                    `).join('')}
                </div>
                <div class="chart-timeline">
                    <span class="timeline-start">${this.formatTime(historicalData[0]?.timestamp)}</span>
                    <span class="timeline-end">${this.formatTime(historicalData[historicalData.length - 1]?.timestamp)}</span>
                </div>
            </div>
        `;
        
        chartContainer.innerHTML = chartHtml;
    }
    
    updateLoadPrediction(prediction) {
        const predictionContainer = document.getElementById('loadPredictionChart');
        if (!predictionContainer) return;
        
        predictionContainer.innerHTML = `
            <div class="load-prediction">
                <h4>負載預測</h4>
                <div class="prediction-chart">
                    <div class="prediction-line">
                        ${prediction.hourly_predictions?.map((pred, index) => `
                            <div class="prediction-point" 
                                 style="left: ${(index / 24) * 100}%; bottom: ${pred.predicted_load}%"
                                 title="時間: ${pred.hour}:00, 預測負載: ${pred.predicted_load}%">
                            </div>
                        `).join('') || ''}
                    </div>
                    <div class="prediction-threshold warning" style="bottom: 80%">
                        <span>警告閾值 (80%)</span>
                    </div>
                    <div class="prediction-threshold critical" style="bottom: 95%">
                        <span>嚴重閾值 (95%)</span>
                    </div>
                </div>
                <div class="prediction-summary">
                    <div class="prediction-metric">
                        <span class="label">預期峰值:</span>
                        <span class="value ${prediction.peak_load > 80 ? 'warning' : ''}">${prediction.peak_load}%</span>
                    </div>
                    <div class="prediction-metric">
                        <span class="label">峰值時間:</span>
                        <span class="value">${prediction.peak_time}</span>
                    </div>
                    <div class="prediction-metric">
                        <span class="label">建議:</span>
                        <span class="value">${prediction.recommendation}</span>
                    </div>
                </div>
            </div>
        `;
    }
    
    updateAnomalies(anomalies) {
        const anomaliesContainer = document.getElementById('anomaliesChart');
        if (!anomaliesContainer) return;
        
        anomaliesContainer.innerHTML = `
            <div class="anomalies-detection">
                <h4>異常檢測</h4>
                <div class="anomalies-timeline">
                    ${anomalies.map(anomaly => `
                        <div class="anomaly-point ${anomaly.severity}" 
                             title="${anomaly.description}">
                            <div class="anomaly-marker"></div>
                            <div class="anomaly-info">
                                <span class="anomaly-type">${anomaly.type}</span>
                                <span class="anomaly-time">${this.formatTime(anomaly.detected_at)}</span>
                            </div>
                        </div>
                    `).join('')}
                </div>
                <div class="anomalies-summary">
                    <div class="anomaly-count critical">
                        嚴重: ${anomalies.filter(a => a.severity === 'critical').length}
                    </div>
                    <div class="anomaly-count warning">
                        警告: ${anomalies.filter(a => a.severity === 'warning').length}
                    </div>
                    <div class="anomaly-count info">
                        資訊: ${anomalies.filter(a => a.severity === 'info').length}
                    </div>
                </div>
            </div>
        `;
    }
    
    createTaskHistoryChart(taskType, historyData) {
        // 創建任務歷史圖表
        const modal = document.createElement('div');
        modal.className = 'task-history-modal';
        modal.innerHTML = `
            <div class="modal-overlay"></div>
            <div class="modal-content large">
                <div class="modal-header">
                    <h3>${taskType} 任務歷史</h3>
                    <button class="modal-close">&times;</button>
                </div>
                <div class="modal-body">
                    <div class="history-chart">
                        <div class="chart-container">
                            <canvas id="taskHistoryCanvas" width="800" height="400"></canvas>
                        </div>
                        <div class="history-stats">
                            <div class="stat-item">
                                <span class="label">總任務數:</span>
                                <span class="value">${historyData.total_tasks || 0}</span>
                            </div>
                            <div class="stat-item">
                                <span class="label">成功率:</span>
                                <span class="value">${historyData.success_rate || 0}%</span>
                            </div>
                            <div class="stat-item">
                                <span class="label">平均執行時間:</span>
                                <span class="value">${historyData.avg_duration || 0}s</span>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button class="btn btn-secondary modal-close">關閉</button>
                </div>
            </div>
        `;
        
        // 設置關閉事件
        const closeBtn = modal.querySelector('.modal-close');
        const overlay = modal.querySelector('.modal-overlay');
        
        const closeModal = () => {
            modal.classList.remove('show');
            setTimeout(() => {
                document.body.removeChild(modal);
            }, 300);
        };
        
        closeBtn.addEventListener('click', closeModal);
        overlay.addEventListener('click', closeModal);
        
        return modal;
    }
    
    // 清理方法
    destroy() {
        if (this.animationFrameId) {
            cancelAnimationFrame(this.animationFrameId);
        }
        this.charts.clear();
        this.chartData.clear();
    }
}

// 全域圖表實例
let dashboardCharts = null;

// 初始化圖表
function initDashboardCharts() {
    if (!dashboardCharts) {
        dashboardCharts = new DashboardCharts();
    }
    return dashboardCharts;
}

// 獲取圖表實例
function getDashboardCharts() {
    return dashboardCharts || initDashboardCharts();
}

// 導出到全域作用域
window.DashboardCharts = DashboardCharts;
window.initDashboardCharts = initDashboardCharts;
window.getDashboardCharts = getDashboardCharts;