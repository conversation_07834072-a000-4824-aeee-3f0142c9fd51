#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sqlite3
import os
from pathlib import Path

def check_database_content():
    # 資料庫檔案路徑
    db_path = Path(__file__).parent / "data" / "email_inbox.db"
    
    print(f"Database path: {db_path}")
    print(f"File exists: {db_path.exists()}")
    
    if not db_path.exists():
        print("Database file not found")
        return
    
    # 檢查檔案大小
    file_size = db_path.stat().st_size
    print(f"File size: {file_size} bytes ({file_size / 1024:.2f} KB)")
    
    try:
        # 連接資料庫
        conn = sqlite3.connect(str(db_path))
        cursor = conn.cursor()
        
        # 檢查所有表格
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
        tables = cursor.fetchall()
        print(f"\nTables: {len(tables)}")
        for table in tables:
            table_name = table[0]
            cursor.execute(f"SELECT COUNT(*) FROM {table_name};")
            count = cursor.fetchone()[0]
            print(f"  - {table_name}: {count} records")
        
        # 檢查 emails 表格內容
        print("\nEmail preview:")
        cursor.execute("SELECT id, sender, subject, received_time FROM emails LIMIT 5;")
        emails = cursor.fetchall()
        if emails:
            for email in emails:
                print(f"  ID: {email[0]}")
                print(f"  Sender: {email[1]}")
                print(f"  Subject: {email[2][:50] if email[2] else 'No subject'}...")
                print(f"  Time: {email[3]}")
                print("  ---")
        else:
            print("  No email data")
        
        # 檢查郵件表格架構
        print("\nEmails table schema:")
        cursor.execute("PRAGMA table_info(emails);")
        columns = cursor.fetchall()
        for col in columns:
            print(f"  - {col[1]} ({col[2]}) {'NOT NULL' if col[3] else 'NULL'}")
        
        conn.close()
        print("\nDatabase check complete")
        
    except Exception as e:
        print(f"Database error: {e}")

if __name__ == "__main__":
    check_database_content()