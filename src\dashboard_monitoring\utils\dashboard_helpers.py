"""
統一監控儀表板 - 輔助工具函數
提供通用的工具函數和裝飾器

符合需求 11：API 端點和整合功能
"""

import time
import asyncio
import logging
import functools
from typing import Any, Callable, Dict, Optional, Union
from datetime import datetime
from fastapi import Request

logger = logging.getLogger(__name__)


def get_client_ip(request: Request) -> str:
    """獲取客戶端 IP 地址"""
    # 檢查 X-Forwarded-For 標頭（代理伺服器）
    forwarded_for = request.headers.get("X-Forwarded-For")
    if forwarded_for:
        # 取第一個 IP（原始客戶端 IP）
        return forwarded_for.split(",")[0].strip()
    
    # 檢查 X-Real-IP 標頭（Nginx 代理）
    real_ip = request.headers.get("X-Real-IP")
    if real_ip:
        return real_ip.strip()
    
    # 使用直接連接的客戶端 IP
    if request.client:
        return request.client.host
    
    return "unknown"


def is_dashboard_endpoint(path: str) -> bool:
    """檢查是否為儀表板端點"""
    dashboard_prefixes = [
        "/api/monitoring",
        "/ws/dashboard",
        "/dashboard"
    ]
    
    return any(path.startswith(prefix) for prefix in dashboard_prefixes)


def format_bytes(bytes_value: Union[int, float]) -> str:
    """格式化位元組大小"""
    if bytes_value == 0:
        return "0 B"
    
    units = ["B", "KB", "MB", "GB", "TB"]
    unit_index = 0
    size = float(bytes_value)
    
    while size >= 1024 and unit_index < len(units) - 1:
        size /= 1024
        unit_index += 1
    
    return f"{size:.2f} {units[unit_index]}"


def format_duration(seconds: Union[int, float]) -> str:
    """格式化持續時間"""
    if seconds < 1:
        return f"{seconds * 1000:.0f}ms"
    elif seconds < 60:
        return f"{seconds:.2f}s"
    elif seconds < 3600:
        minutes = int(seconds // 60)
        remaining_seconds = seconds % 60
        return f"{minutes}m {remaining_seconds:.0f}s"
    else:
        hours = int(seconds // 3600)
        remaining_minutes = int((seconds % 3600) // 60)
        return f"{hours}h {remaining_minutes}m"


def format_percentage(value: Union[int, float], decimal_places: int = 1) -> str:
    """格式化百分比"""
    return f"{value:.{decimal_places}f}%"


def safe_divide(numerator: Union[int, float], denominator: Union[int, float], default: float = 0.0) -> float:
    """安全除法，避免除零錯誤"""
    try:
        if denominator == 0:
            return default
        return float(numerator) / float(denominator)
    except (TypeError, ValueError):
        return default


def calculate_success_rate(successful: int, total: int) -> float:
    """計算成功率"""
    return safe_divide(successful, total, 0.0) * 100


def truncate_string(text: str, max_length: int = 100, suffix: str = "...") -> str:
    """截斷字串"""
    if len(text) <= max_length:
        return text
    return text[:max_length - len(suffix)] + suffix


def log_performance(func: Callable) -> Callable:
    """效能日誌記錄裝飾器"""
    @functools.wraps(func)
    async def async_wrapper(*args, **kwargs):
        start_time = time.time()
        func_name = f"{func.__module__}.{func.__name__}"
        
        try:
            result = await func(*args, **kwargs)
            elapsed_time = time.time() - start_time
            
            if elapsed_time > 1.0:  # 只記錄超過 1 秒的操作
                logger.info(f"⏱️ {func_name} 執行時間: {elapsed_time:.3f}s")
            
            return result
            
        except Exception as e:
            elapsed_time = time.time() - start_time
            logger.error(f"❌ {func_name} 執行失敗 ({elapsed_time:.3f}s): {e}")
            raise
    
    @functools.wraps(func)
    def sync_wrapper(*args, **kwargs):
        start_time = time.time()
        func_name = f"{func.__module__}.{func.__name__}"
        
        try:
            result = func(*args, **kwargs)
            elapsed_time = time.time() - start_time
            
            if elapsed_time > 1.0:  # 只記錄超過 1 秒的操作
                logger.info(f"⏱️ {func_name} 執行時間: {elapsed_time:.3f}s")
            
            return result
            
        except Exception as e:
            elapsed_time = time.time() - start_time
            logger.error(f"❌ {func_name} 執行失敗 ({elapsed_time:.3f}s): {e}")
            raise
    
    # 檢查是否為協程函數
    if hasattr(func, '__code__') and func.__code__.co_flags & 0x80:  # CO_COROUTINE
        return async_wrapper
    else:
        return sync_wrapper


def handle_dashboard_error(func: Callable) -> Callable:
    """儀表板錯誤處理裝飾器"""
    @functools.wraps(func)
    async def async_wrapper(*args, **kwargs):
        func_name = f"{func.__module__}.{func.__name__}"
        
        try:
            return await func(*args, **kwargs)
            
        except Exception as e:
            logger.error(f"❌ {func_name} 發生錯誤: {e}")
            
            # 返回預設值或重新拋出異常
            if hasattr(func, '_default_return_value'):
                return func._default_return_value
            else:
                raise
    
    @functools.wraps(func)
    def sync_wrapper(*args, **kwargs):
        func_name = f"{func.__module__}.{func.__name__}"
        
        try:
            return func(*args, **kwargs)
            
        except Exception as e:
            logger.error(f"❌ {func_name} 發生錯誤: {e}")
            
            # 返回預設值或重新拋出異常
            if hasattr(func, '_default_return_value'):
                return func._default_return_value
            else:
                raise
    
    # 檢查是否為協程函數
    if hasattr(func, '__code__') and func.__code__.co_flags & 0x80:  # CO_COROUTINE
        return async_wrapper
    else:
        return sync_wrapper


def with_default_return(default_value: Any):
    """設定預設返回值的裝飾器"""
    def decorator(func: Callable) -> Callable:
        func._default_return_value = default_value
        return func
    return decorator


def validate_time_range(time_range: str) -> bool:
    """驗證時間範圍參數"""
    valid_ranges = ["1h", "6h", "24h", "7d", "30d"]
    return time_range in valid_ranges


def parse_time_range_to_seconds(time_range: str) -> int:
    """將時間範圍轉換為秒數"""
    time_map = {
        "1h": 3600,
        "6h": 21600,
        "24h": 86400,
        "7d": 604800,
        "30d": 2592000
    }
    return time_map.get(time_range, 3600)


def get_time_range_description(time_range: str) -> str:
    """獲取時間範圍描述"""
    descriptions = {
        "1h": "過去 1 小時",
        "6h": "過去 6 小時", 
        "24h": "過去 24 小時",
        "7d": "過去 7 天",
        "30d": "過去 30 天"
    }
    return descriptions.get(time_range, "未知時間範圍")


def create_response_dict(
    status: str = "success",
    data: Any = None,
    message: str = None,
    error: str = None,
    timestamp: datetime = None
) -> Dict[str, Any]:
    """創建標準化的 API 回應字典"""
    response = {
        "status": status,
        "timestamp": (timestamp or datetime.now()).isoformat()
    }
    
    if data is not None:
        response["data"] = data
    
    if message:
        response["message"] = message
    
    if error:
        response["error"] = error
    
    return response


def sanitize_string(text: str, max_length: int = 1000) -> str:
    """清理和驗證字串輸入"""
    if not isinstance(text, str):
        return ""
    
    # 移除潛在的危險字符
    dangerous_chars = ["<", ">", "&", "\"", "'", "\x00"]
    for char in dangerous_chars:
        text = text.replace(char, "")
    
    # 截斷過長的字串
    if len(text) > max_length:
        text = text[:max_length]
    
    return text.strip()


def is_valid_metric_name(name: str) -> bool:
    """驗證指標名稱是否有效"""
    if not name or not isinstance(name, str):
        return False
    
    # 只允許字母、數字、底線和點號
    import re
    pattern = r'^[a-zA-Z0-9_\.]+$'
    return bool(re.match(pattern, name)) and len(name) <= 100


def generate_cache_key(prefix: str, *args) -> str:
    """生成快取鍵"""
    import hashlib
    
    # 將所有參數轉換為字串並連接
    key_parts = [prefix] + [str(arg) for arg in args]
    key_string = ":".join(key_parts)
    
    # 如果鍵太長，使用雜湊
    if len(key_string) > 200:
        hash_object = hashlib.md5(key_string.encode())
        return f"{prefix}:{hash_object.hexdigest()}"
    
    return key_string


def batch_process(items: list, batch_size: int = 100):
    """批次處理項目"""
    for i in range(0, len(items), batch_size):
        yield items[i:i + batch_size]


class PerformanceTimer:
    """效能計時器上下文管理器"""
    
    def __init__(self, operation_name: str, log_threshold: float = 1.0):
        self.operation_name = operation_name
        self.log_threshold = log_threshold
        self.start_time = None
        self.end_time = None
    
    def __enter__(self):
        self.start_time = time.time()
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        self.end_time = time.time()
        elapsed_time = self.end_time - self.start_time
        
        if elapsed_time > self.log_threshold:
            logger.info(f"⏱️ {self.operation_name} 執行時間: {elapsed_time:.3f}s")
    
    @property
    def elapsed_time(self) -> Optional[float]:
        """獲取已經過的時間"""
        if self.start_time is None:
            return None
        
        end_time = self.end_time or time.time()
        return end_time - self.start_time


class RateLimiter:
    """簡單的速率限制器"""
    
    def __init__(self, max_calls: int, time_window: int):
        self.max_calls = max_calls
        self.time_window = time_window
        self.calls = []
    
    def is_allowed(self) -> bool:
        """檢查是否允許調用"""
        current_time = time.time()
        
        # 清理過期的調用記錄
        self.calls = [call_time for call_time in self.calls 
                     if current_time - call_time < self.time_window]
        
        # 檢查是否超過限制
        if len(self.calls) >= self.max_calls:
            return False
        
        # 記錄這次調用
        self.calls.append(current_time)
        return True
    
    def get_reset_time(self) -> float:
        """獲取重置時間"""
        if not self.calls:
            return 0
        
        oldest_call = min(self.calls)
        return oldest_call + self.time_window


def retry_on_failure(max_retries: int = 3, delay: float = 1.0, backoff_factor: float = 2.0):
    """失敗重試裝飾器"""
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        async def async_wrapper(*args, **kwargs):
            last_exception = None
            current_delay = delay
            
            for attempt in range(max_retries + 1):
                try:
                    return await func(*args, **kwargs)
                except Exception as e:
                    last_exception = e
                    
                    if attempt < max_retries:
                        logger.warning(
                            f"⚠️ {func.__name__} 第 {attempt + 1} 次嘗試失敗: {e}, "
                            f"{current_delay}s 後重試"
                        )
                        await asyncio.sleep(current_delay)
                        current_delay *= backoff_factor
                    else:
                        logger.error(f"❌ {func.__name__} 所有重試都失敗了")
            
            raise last_exception
        
        @functools.wraps(func)
        def sync_wrapper(*args, **kwargs):
            import time
            last_exception = None
            current_delay = delay
            
            for attempt in range(max_retries + 1):
                try:
                    return func(*args, **kwargs)
                except Exception as e:
                    last_exception = e
                    
                    if attempt < max_retries:
                        logger.warning(
                            f"⚠️ {func.__name__} 第 {attempt + 1} 次嘗試失敗: {e}, "
                            f"{current_delay}s 後重試"
                        )
                        time.sleep(current_delay)
                        current_delay *= backoff_factor
                    else:
                        logger.error(f"❌ {func.__name__} 所有重試都失敗了")
            
            raise last_exception
        
        # 檢查是否為協程函數
        if hasattr(func, '__code__') and func.__code__.co_flags & 0x80:  # CO_COROUTINE
            return async_wrapper
        else:
            return sync_wrapper
    
    return decorator


def safe_execute(default_return: Any = None):
    """安全執行裝飾器，捕獲異常並返回預設值"""
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        async def async_wrapper(*args, **kwargs):
            func_name = f"{func.__module__}.{func.__name__}"
            
            try:
                return await func(*args, **kwargs)
                
            except Exception as e:
                logger.error(f"❌ {func_name} 執行失敗: {e}")
                
                # 返回預設值
                if callable(default_return):
                    return default_return()
                else:
                    return default_return
        
        @functools.wraps(func)
        def sync_wrapper(*args, **kwargs):
            func_name = f"{func.__module__}.{func.__name__}"
            
            try:
                return func(*args, **kwargs)
                
            except Exception as e:
                logger.error(f"❌ {func_name} 執行失敗: {e}")
                
                # 返回預設值
                if callable(default_return):
                    return default_return()
                else:
                    return default_return
        
        # 檢查是否為協程函數
        import asyncio
        if asyncio.iscoroutinefunction(func):
            return async_wrapper
        else:
            return sync_wrapper
    
    return decorator


def dashboard_monitor(
    fallback_value: Any = None,
    operation_name: str = None,
    suppress_exceptions: bool = True,
    log_performance: bool = True,
    performance_threshold: float = 1.0
):
    """
    綜合儀表板監控裝飾器
    
    結合錯誤處理、效能監控和回退機制
    
    Args:
        fallback_value: 發生錯誤時的回退值
        operation_name: 操作名稱（用於日誌）
        suppress_exceptions: 是否抑制異常（返回回退值）
        log_performance: 是否記錄效能信息
        performance_threshold: 效能記錄閾值（秒）
    """
    def decorator(func: Callable) -> Callable:
        operation = operation_name or f"{func.__module__}.{func.__name__}"
        
        @functools.wraps(func)
        async def async_wrapper(*args, **kwargs):
            start_time = time.time() if log_performance else None
            
            try:
                result = await func(*args, **kwargs)
                
                # 記錄效能
                if log_performance and start_time:
                    elapsed_time = time.time() - start_time
                    if elapsed_time > performance_threshold:
                        logger.info(f"⏱️ {operation} 執行時間: {elapsed_time:.3f}s")
                
                return result
                
            except Exception as e:
                # 記錄錯誤
                logger.error(f"❌ {operation} 執行失敗: {e}")
                
                if suppress_exceptions:
                    # 返回回退值
                    if callable(fallback_value):
                        return fallback_value()
                    else:
                        return fallback_value
                else:
                    # 重新拋出異常
                    raise
        
        @functools.wraps(func)
        def sync_wrapper(*args, **kwargs):
            start_time = time.time() if log_performance else None
            
            try:
                result = func(*args, **kwargs)
                
                # 記錄效能
                if log_performance and start_time:
                    elapsed_time = time.time() - start_time
                    if elapsed_time > performance_threshold:
                        logger.info(f"⏱️ {operation} 執行時間: {elapsed_time:.3f}s")
                
                return result
                
            except Exception as e:
                # 記錄錯誤
                logger.error(f"❌ {operation} 執行失敗: {e}")
                
                if suppress_exceptions:
                    # 返回回退值
                    if callable(fallback_value):
                        return fallback_value()
                    else:
                        return fallback_value
                else:
                    # 重新拋出異常
                    raise
        
        # 檢查是否為協程函數
        import asyncio
        if asyncio.iscoroutinefunction(func):
            return async_wrapper
        else:
            return sync_wrapper
    
    return decorator


def dashboard_async_monitor(
    fallback_value: Any = None,
    operation_name: str = None,
    suppress_exceptions: bool = True,
    log_performance: bool = True,
    performance_threshold: float = 1.0
):
    """
    異步儀表板監控裝飾器
    
    專門為異步函數設計的監控裝飾器
    """
    def decorator(func: Callable) -> Callable:
        operation = operation_name or f"{func.__module__}.{func.__name__}"
        
        @functools.wraps(func)
        async def async_wrapper(*args, **kwargs):
            start_time = time.time() if log_performance else None
            
            try:
                result = await func(*args, **kwargs)
                
                # 記錄效能
                if log_performance and start_time:
                    elapsed_time = time.time() - start_time
                    if elapsed_time > performance_threshold:
                        logger.info(f"⏱️ {operation} 執行時間: {elapsed_time:.3f}s")
                
                return result
                
            except Exception as e:
                # 記錄錯誤
                logger.error(f"❌ {operation} 執行失敗: {e}")
                
                if suppress_exceptions:
                    # 返回回退值
                    if callable(fallback_value):
                        return await fallback_value() if asyncio.iscoroutinefunction(fallback_value) else fallback_value()
                    else:
                        return fallback_value
                else:
                    # 重新拋出異常
                    raise
        
        return async_wrapper
    
    return decorator


def dashboard_error_handler(fallback_value: Any = None, suppress_exceptions: bool = True):
    """儀表板錯誤處理裝飾器"""
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        def sync_wrapper(*args, **kwargs):
            func_name = f"{func.__module__}.{func.__name__}"
            
            try:
                return func(*args, **kwargs)
            except Exception as e:
                logger.error(f"❌ {func_name} 錯誤: {e}")
                
                if suppress_exceptions:
                    if callable(fallback_value):
                        return fallback_value()
                    else:
                        return fallback_value
                else:
                    raise
        
        return sync_wrapper
    
    return decorator


def dashboard_async_error_handler(fallback_value: Any = None, suppress_exceptions: bool = True):
    """儀表板異步錯誤處理裝飾器"""
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        async def async_wrapper(*args, **kwargs):
            func_name = f"{func.__module__}.{func.__name__}"
            
            try:
                return await func(*args, **kwargs)
            except Exception as e:
                logger.error(f"❌ {func_name} 錯誤: {e}")
                
                if suppress_exceptions:
                    if callable(fallback_value):
                        result = fallback_value()
                        if hasattr(result, '__await__'):
                            return await result
                        return result
                    else:
                        return fallback_value
                else:
                    raise
        
        return async_wrapper
    
    return decorator


def dashboard_performance_monitor(threshold: float = 1.0, operation_name: str = None):
    """儀表板效能監控裝飾器"""
    def decorator(func: Callable) -> Callable:
        operation = operation_name or f"{func.__module__}.{func.__name__}"
        
        @functools.wraps(func)
        def sync_wrapper(*args, **kwargs):
            start_time = time.time()
            
            try:
                result = func(*args, **kwargs)
                elapsed_time = time.time() - start_time
                
                if elapsed_time > threshold:
                    logger.info(f"⏱️ {operation} 執行時間: {elapsed_time:.3f}s")
                
                return result
            except Exception as e:
                elapsed_time = time.time() - start_time
                logger.error(f"❌ {operation} 執行失敗 ({elapsed_time:.3f}s): {e}")
                raise
        
        return sync_wrapper
    
    return decorator


def dashboard_async_performance_monitor(threshold: float = 1.0, operation_name: str = None):
    """儀表板異步效能監控裝飾器"""
    def decorator(func: Callable) -> Callable:
        operation = operation_name or f"{func.__module__}.{func.__name__}"
        
        @functools.wraps(func)
        async def async_wrapper(*args, **kwargs):
            start_time = time.time()
            
            try:
                result = await func(*args, **kwargs)
                elapsed_time = time.time() - start_time
                
                if elapsed_time > threshold:
                    logger.info(f"⏱️ {operation} 執行時間: {elapsed_time:.3f}s")
                
                return result
            except Exception as e:
                elapsed_time = time.time() - start_time
                logger.error(f"❌ {operation} 執行失敗 ({elapsed_time:.3f}s): {e}")
                raise
        
        return async_wrapper
    
    return decorator


def format_timestamp(timestamp: datetime = None, format_str: str = "%Y-%m-%d %H:%M:%S") -> str:
    """格式化時間戳"""
    if timestamp is None:
        timestamp = datetime.now()
    return timestamp.strftime(format_str)


def calculate_percentage(part: Union[int, float], total: Union[int, float], decimal_places: int = 2) -> float:
    """計算百分比"""
    return safe_divide(part, total, 0.0) * 100


class DashboardPerformanceTracker:
    """儀表板效能追蹤器"""
    
    def __init__(self, operation_name: str):
        self.operation_name = operation_name
        self.start_time = None
        self.end_time = None
    
    def start(self):
        """開始追蹤"""
        self.start_time = time.time()
        return self
    
    def stop(self):
        """停止追蹤"""
        self.end_time = time.time()
        return self
    
    @property
    def elapsed_time(self) -> Optional[float]:
        """獲取已經過的時間"""
        if self.start_time is None:
            return None
        
        end_time = self.end_time or time.time()
        return end_time - self.start_time
    
    def log_if_slow(self, threshold: float = 1.0):
        """如果執行時間超過閾值則記錄"""
        if self.elapsed_time and self.elapsed_time > threshold:
            logger.info(f"⏱️ {self.operation_name} 執行時間: {self.elapsed_time:.3f}s")


def safe_batch_operation(items: list, batch_size: int = 100, operation_name: str = "批次操作"):
    """安全的批次操作處理器"""
    total_items = len(items)
    processed = 0
    errors = []
    
    for batch_start in range(0, total_items, batch_size):
        batch_end = min(batch_start + batch_size, total_items)
        batch = items[batch_start:batch_end]
        
        try:
            yield batch
            processed += len(batch)
        except Exception as e:
            error_info = {
                'batch_start': batch_start,
                'batch_end': batch_end,
                'error': str(e)
            }
            errors.append(error_info)
            logger.error(f"❌ {operation_name} 批次 {batch_start}-{batch_end} 失敗: {e}")
    
    if errors:
        logger.warning(f"⚠️ {operation_name} 完成，但有 {len(errors)} 個批次失敗")


def create_dashboard_log_context(**kwargs) -> Dict[str, Any]:
    """創建儀表板日誌上下文"""
    context = {
        'timestamp': datetime.now().isoformat(),
        'module': 'dashboard_monitoring'
    }
    context.update(kwargs)
    return context


def log_dashboard_operation(operation: str, details: Dict[str, Any] = None, level: str = "info"):
    """記錄儀表板操作"""
    context = create_dashboard_log_context(operation=operation)
    if details:
        context.update(details)
    
    message = f"📊 {operation}"
    if details:
        message += f" - {details}"
    
    if level == "info":
        logger.info(message)
    elif level == "warning":
        logger.warning(message)
    elif level == "error":
        logger.error(message)
    else:
        logger.info(message)