#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sqlite3
import os
from pathlib import Path

def check_database_content():
    # 資料庫檔案路徑
    db_path = Path(__file__).parent / "data" / "email_inbox.db"
    
    print(f"檢查資料庫: {db_path}")
    print(f"檔案存在: {db_path.exists()}")
    
    if not db_path.exists():
        print("❌ 資料庫檔案不存在")
        return
    
    # 檢查檔案大小
    file_size = db_path.stat().st_size
    print(f"檔案大小: {file_size} bytes ({file_size / 1024:.2f} KB)")
    
    try:
        # 連接資料庫
        conn = sqlite3.connect(str(db_path))
        cursor = conn.cursor()
        
        # 檢查所有表格
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
        tables = cursor.fetchall()
        print(f"\n📋 資料表: {len(tables)} 個")
        for table in tables:
            table_name = table[0]
            cursor.execute(f"SELECT COUNT(*) FROM {table_name};")
            count = cursor.fetchone()[0]
            print(f"  - {table_name}: {count} 筆記錄")
        
        # 檢查 emails 表格內容
        print("\n📧 郵件內容預覽:")
        cursor.execute("SELECT id, sender, subject, received_time FROM emails LIMIT 5;")
        emails = cursor.fetchall()
        if emails:
            for email in emails:
                print(f"  ID: {email[0]}")
                print(f"  寄件者: {email[1]}")
                print(f"  主旨: {email[2][:50]}...")
                print(f"  時間: {email[3]}")
                print("  ---")
        else:
            print("  無郵件資料")
        
        # 檢查郵件表格架構
        print("\n🏗️ emails 表格架構:")
        cursor.execute("PRAGMA table_info(emails);")
        columns = cursor.fetchall()
        for col in columns:
            print(f"  - {col[1]} ({col[2]}) {'NOT NULL' if col[3] else 'NULL'} {'DEFAULT ' + str(col[4]) if col[4] else ''}")
        
        conn.close()
        print("\n✅ 資料庫檢查完成")
        
    except Exception as e:
        print(f"❌ 資料庫操作錯誤: {e}")

if __name__ == "__main__":
    check_database_content()