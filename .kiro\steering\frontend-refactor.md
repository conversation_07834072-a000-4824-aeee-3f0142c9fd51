---
inclusion: fileMatch
filePatterns:
  - ".kiro/specs/vue-frontend-migration/**"
  - "frontend/**"
  - "start_integrated_services.py"
---

# Frontend Refactoring Guidelines

## 🎉 已完成的重構工作 (2025-01-08 至 2025-08-11)

### ✅ 任務 3.4: 程式碼審查 (已完成 - 2025-08-11)

**審查成果:**
- **Pull Request**: 檔案遷移完成後的程式碼審查已通過
- **問題修正**: 所有審查中發現的問題已修正
- **品質確認**: 代碼品質符合團隊標準
- **合併完成**: 變更已成功合併到主分支

### ✅ 任務 4.1: 建立共享模板 (已完成)

**建立成果:**
- **基礎模板**: 建立 frontend/shared/templates/base.html 基礎模板
- **共享組件**: 建立導航、佈局、模態框等共享組件
- **模板更新**: 各模組模板已更新使用共享基礎模板
- **一致性**: 確保所有頁面具有一致的外觀和行為

### ✅ 任務 4.2: 建立共享靜態資源 (已完成)

**建立成果:**
- **共享目錄**: 建立 frontend/shared/static/ 目錄結構
- **資源整合**: 移動共用 CSS/JS 檔案到共享目錄
- **全域樣式**: 建立統一的全域樣式檔案
- **資源優化**: 減少重複資源，提高載入效率

### ✅ 任務 5.1: 更新 Flask 配置 (已完成)

**配置更新成果:**
- **目錄支援**: Flask 應用程式配置已更新支援新目錄結構
- **路徑配置**: 靜態檔案和模板路徑配置已更新
- **環境配置**: 開發和生產環境配置已確認正確
- **藍圖註冊**: 所有模組藍圖已正確註冊和配置

### ✅ 任務 2: Flask 主應用程式重構 (已完成 - 2025-01-08)

**重構成果:**
- **模組化架構**: 成功將單一 Flask 應用程式重構為模組化藍圖系統
- **配置管理**: 建立統一的配置管理系統，支援開發/測試/生產環境
- **錯誤處理**: 實作全域錯誤處理機制，統一處理 Web 和 API 錯誤
- **向後相容**: 所有現有 API 端點和路由保持不變
- **可擴展性**: 為未來 Vue.js 遷移建立清晰的模組邊界

### ✅ 任務 3.1: 模板檔案遷移 (已完成 - 2025-08-09)

**遷移成果:**
- **總計遷移**: 23個模板檔案完成遷移，包含 10個原始檔案和 13個新建檔案
- **模組分配**: 6個模組各分配模板檔案，除了 monitoring 模組 3個檔案外，其他各 4個檔案
- **重要變更**: 
  - 目錄命名: 保持 `file_management/` (符合 Python 模組命名規範)
  - 檔案重命名: `ft_summary_ui.html` → `dashboard.html`
  - 檔案重命名: `scheduler_dashboard.html` → `task_scheduler.html`
- **品質評分**: 9.5/10分，符合 design.md 規範要求

### ✅ 任務 3.2: 靜態資源遷移 (已完成 - 2025-08-10)

**遷移成果:**
- **JavaScript檔案**: 37個JS檔案完成模組化分類和遷移
- **CSS檔案**: 9個CSS檔案完成模組化分類和遷移
- **路徑更新**: 所有靜態資源路徑引用已更新
- **載入驗證**: 確保所有樣式和腳本正常載入
- **品質評分**: 9.5/10分，模組化分類清晰

### ✅ 任務 3.3: 路由邏輯遷移 (已完成 - 2025-08-11)

**遷移成果:**
- **路由遷移**: 70+個路由成功遷移到6個模組的路由檔案
- **藍圖系統**: Flask藍圖系統正常運作，URL前綴隔離
- **功能保持**: 所有現有 URL 路徑保持不變
- **API端點**: 所有頁面和 API 端點正常運作
- **模組獨立**: 各模組路由邏輯完全獨立

**已建立的完整檔案結構:**
```
frontend/
├── app.py                    # Flask 主應用程式 (工廠模式)
├── config.py                 # 統一配置管理
├── __init__.py               # 前端模組初始化
├── shared/
│   ├── __init__.py
│   ├── templates/            # 共享模板 (base.html, 組件等)
│   ├── static/               # 共享靜態資源
│   └── utils/
│       ├── __init__.py
│       └── error_handler.py  # 全域錯誤處理器
├── email/                    # 郵件模組 (完整實作)
│   ├── templates/            # 郵件相關模板
│   ├── static/               # 郵件相關靜態資源
│   └── routes/
│       └── email_routes.py   # 郵件功能路由
├── analytics/                # 分析統計模組 (完整實作)
│   ├── templates/
│   ├── static/
│   └── routes/
│       └── analytics_routes.py
├── file_management/          # 檔案管理模組 (完整實作)
│   ├── templates/
│   ├── static/
│   └── routes/
│       └── file_routes.py
├── eqc/                      # EQC功能模組 (完整實作)
│   ├── templates/
│   ├── static/
│   └── routes/
│       └── eqc_routes.py
├── tasks/                    # 任務管理模組 (完整實作)
│   ├── templates/
│   ├── static/
│   └── routes/
│       └── task_routes.py
└── monitoring/               # 監控功能模組 (完整實作)
    ├── templates/
    ├── static/
    └── routes/
        └── monitoring_routes.py
```

**技術特點:**
- **藍圖系統**: 每個功能模組獨立藍圖，URL 前綴隔離
- **配置靈活**: 支援多環境配置切換
- **錯誤統一**: 統一的錯誤處理和回應格式
- **模組邊界**: 為 Vue.js 遷移建立清晰邊界
- **完整遷移**: 所有模板、靜態資源、路由邏輯已完成模組化

## Refactoring Principles

### MVP Approach
- **MUST maintain existing functionality** - No feature changes during restructuring
- **MUST preserve all current URLs** - Ensure backward compatibility
- **MUST keep same API endpoints** - Backend services remain unchanged
- **MUST maintain database connections** - No data migration required

### File Movement Strategy
- **Use `git mv` command** - Preserve file history
- **Move then delete** - Never keep duplicate files
- **Update imports immediately** - Fix all path references after moving
- **Test after each move** - Verify functionality before proceeding

### Module Organization Rules
- **One concern per module** - Clear separation of responsibilities
- **Shared resources in shared/** - Common templates, CSS, JS in shared module
- **Module independence** - Minimize cross-module dependencies
- **Consistent naming** - Use snake_case for Python, kebab-case for URLs

## Directory Structure Standards

### Frontend Module Structure
Each module MUST follow this structure:
```
frontend/{module}/
├── templates/          # HTML templates for this module
├── static/
│   ├── css/           # Module-specific styles
│   ├── js/            # Module-specific JavaScript
│   └── images/        # Module-specific images
├── components/        # Reusable HTML components
├── routes/           # Flask route definitions
└── README.md         # Module documentation
```

### Import Path Updates
When moving files, update imports following this pattern:
```python
# OLD
from email_inbox_app import some_function

# NEW
from frontend.email.routes.email_routes import some_function
```

### Template Path Updates
Update template references in Flask routes:
```python
# OLD
return render_template('inbox.html')

# NEW
return render_template('email/templates/inbox.html')
```

### Static Asset Path Updates
Update static asset references in templates:
```html
<!-- OLD -->
<link rel="stylesheet" href="{{ url_for('static', filename='css/email.css') }}">

<!-- NEW -->
<link rel="stylesheet" href="{{ url_for('email.static', filename='css/email.css') }}">
```

## Quality Assurance

### Testing Requirements
- **Test after each file move** - Verify page loads correctly
- **Check all links** - Ensure no 404 errors
- **Validate database connections** - Confirm data access works
- **Cross-browser testing** - Test in Chrome, Firefox, Edge

### Documentation Requirements
- **Update file mapping table** - Record every file movement
- **Update module README** - Document module structure and purpose
- **Commit message format** - Use standardized format for tracking

### Rollback Procedures
- **Git branch isolation** - Work in feature branches
- **Incremental commits** - Small, focused commits for easy rollback
- **Backup verification** - Ensure Git history is preserved
- **Quick rollback plan** - Document steps to revert changes

## Common Pitfalls to Avoid

### File Movement Mistakes
- **DON'T copy files** - Always move, never duplicate
- **DON'T change logic** - Only move files and update paths
- **DON'T skip testing** - Test every change immediately
- **DON'T batch too many changes** - Keep commits small and focused

### Import Path Mistakes
- **DON'T use relative imports** - Always use absolute imports from project root
- **DON'T forget __init__.py files** - Ensure Python packages are properly defined
- **DON'T mix old and new paths** - Update all references consistently

### Template and Static Asset Mistakes
- **DON'T hardcode paths** - Use Flask's url_for() function
- **DON'T forget to register blueprints** - Ensure Flask can find static files
- **DON'T break CSS/JS references** - Update all asset paths in templates

## Success Criteria

### Functional Requirements
- All existing pages load without errors
- All existing functionality works as before
- No broken links or missing assets
- Database connections remain stable

### Technical Requirements
- Clean Git history with proper commit messages
- Complete file mapping documentation
- All tests pass
- No duplicate files in repository

### Team Requirements
- All team members can run the application locally
- Development environment setup is documented
- Code review process is followed
- Knowledge transfer is complete