#!/usr/bin/env python3
"""
Debug verification test - simplified version to verify fixes
"""

import requests
import json
import time
import sys
from datetime import datetime

def run_debug_verification():
    """Execute debug verification test"""
    base_url = "http://localhost:8000"
    results = {
        'test_start_time': datetime.now().isoformat(),
        'base_url': base_url,
        'tests': [],
        'summary': {}
    }
    
    def log_test(name, success, message="", status_code=None, response_time=0):
        """Log test result"""
        test_result = {
            'name': name,
            'success': success,
            'message': message,
            'status_code': status_code,
            'response_time_ms': round(response_time * 1000, 2),
            'timestamp': datetime.now().isoformat()
        }
        results['tests'].append(test_result)
        
        status = "PASS" if success else "FAIL"
        print(f"{status} {name}")
        if message:
            print(f"    {message}")
        if response_time > 0:
            print(f"    Response time: {test_result['response_time_ms']}ms")
        return success
    
    print("Starting debug verification test...")
    print(f"Test target: {base_url}")
    print("=" * 60)
    
    session = requests.Session()
    session.timeout = 10
    
    # 1. Health check test
    print("\n1. Health check test")
    try:
        start_time = time.time()
        response = session.get(f"{base_url}/health")
        response_time = time.time() - start_time
        
        if response.status_code == 200:
            health_data = response.json()
            log_test("Health endpoint", True, f"Status: {health_data.get('status')}", 200, response_time)
            
            # Check module status
            modules = health_data.get('modules', {})
            expected_modules = ['email', 'analytics', 'file_management', 'eqc', 'tasks', 'monitoring']
            
            for module in expected_modules:
                if module in modules and modules[module]:
                    log_test(f"Module check: {module}", True, "Module status OK")
                else:
                    log_test(f"Module check: {module}", False, "Module status error or missing")
        else:
            log_test("Health endpoint", False, f"HTTP {response.status_code}", response.status_code, response_time)
    except Exception as e:
        log_test("Health endpoint", False, f"Request exception: {str(e)}")
    
    # 2. Main route test
    print("\n2. Main route test")
    main_routes = [
        ('/', 302, "Home redirect"),
        ('/health', 200, "Health check"),
    ]
    
    for route, expected_status, description in main_routes:
        try:
            start_time = time.time()
            response = session.get(f"{base_url}{route}", allow_redirects=False)
            response_time = time.time() - start_time
            
            success = response.status_code == expected_status
            log_test(description, success, f"URL: {route}", response.status_code, response_time)
        except Exception as e:
            log_test(description, False, f"Request exception: {str(e)}")
    
    # 3. Key pages test - testing the ones mentioned in the user's issue
    print("\n3. Key pages test")
    key_pages = [
        ('/email/inbox', 'Email inbox page'),
        ('/monitoring/dashboard', 'Monitoring dashboard'),
        ('/files/manager', 'Database management page'),
    ]
    
    for page, description in key_pages:
        try:
            start_time = time.time()
            response = session.get(f"{base_url}{page}")
            response_time = time.time() - start_time
            
            success = response.status_code == 200
            log_test(description, success, 
                    f"Page load {'successful' if success else 'failed'}", 
                    response.status_code, response_time)
                    
            # Check for specific error patterns
            if response.status_code == 200:
                content = response.text
                if 'CORS' in content:
                    log_test(f"{description} - CORS check", False, "CORS error found in content")
                elif 'Error' in content and 'API' in content:
                    log_test(f"{description} - API error check", False, "API error found in content") 
                else:
                    log_test(f"{description} - Content check", True, "No obvious errors in content")
            
        except Exception as e:
            log_test(description, False, f"Request exception: {str(e)}")
    
    # Test FastAPI endpoint that was mentioned in the fixes
    print("\n4. FastAPI endpoint test")
    fastapi_url = "http://localhost:8010"
    try:
        start_time = time.time()
        response = session.get(f"{fastapi_url}/docs", timeout=5)
        response_time = time.time() - start_time
        
        if response.status_code == 200:
            log_test("FastAPI docs endpoint", True, "FastAPI service accessible", 200, response_time)
        else:
            log_test("FastAPI docs endpoint", False, f"HTTP {response.status_code}", response.status_code, response_time)
    except requests.exceptions.ConnectionError:
        log_test("FastAPI docs endpoint", False, "Connection refused - service may not be running")
    except Exception as e:
        log_test("FastAPI docs endpoint", False, f"Request exception: {str(e)}")
    
    # Generate test summary
    total_tests = len(results['tests'])
    passed_tests = sum(1 for test in results['tests'] if test['success'])
    failed_tests = total_tests - passed_tests
    success_rate = (passed_tests / total_tests * 100) if total_tests > 0 else 0
    
    results['summary'] = {
        'total_tests': total_tests,
        'passed_tests': passed_tests,
        'failed_tests': failed_tests,
        'success_rate': round(success_rate, 2),
        'overall_success': failed_tests == 0,
        'test_end_time': datetime.now().isoformat()
    }
    
    # Print summary
    print("\n" + "=" * 60)
    print("Debug verification test results summary")
    print("=" * 60)
    
    overall_status = "ALL PASS" if results['summary']['overall_success'] else "SOME TESTS FAILED"
    print(f"Overall status: {overall_status}")
    print(f"Total tests: {total_tests}")
    print(f"Passed tests: {passed_tests}")
    print(f"Failed tests: {failed_tests}")
    print(f"Success rate: {success_rate:.1f}%")
    
    # List failed tests
    failed_tests_list = [test for test in results['tests'] if not test['success']]
    if failed_tests_list:
        print("\nFailed tests:")
        for test in failed_tests_list:
            print(f"  - {test['name']}: {test['message']}")
    
    # Save results to file
    try:
        with open('debug_verification_results.json', 'w', encoding='utf-8') as f:
            json.dump(results, f, ensure_ascii=False, indent=2)
        print(f"\nDetailed test results saved to: debug_verification_results.json")
    except Exception as e:
        print(f"\nFailed to save test results: {str(e)}")
    
    print("\n" + "=" * 60)
    
    return results

if __name__ == "__main__":
    results = run_debug_verification()
    success = results['summary']['overall_success']
    sys.exit(0 if success else 1)