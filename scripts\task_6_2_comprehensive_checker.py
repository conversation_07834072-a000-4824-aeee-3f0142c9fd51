#!/usr/bin/env python3
"""
Task 6.2 - 全面的路徑和連結檢查驗證工具

基於 .kiro 配置中的 Task 6.2 要求，執行全面的路徑和連結檢查驗證：
- 檢查所有內部連結是否正常運作
- 驗證靜態資源載入是否正確
- 確保沒有 404 錯誤或遺失的資源
- 符合需求 1.2 (保持功能完整性)

作者: Task 6.2 自動化檢查
版本: 1.0.0
日期: 2025-08-12
"""

import os
import sys
import re
import json
import requests
import threading
import time
from pathlib import Path
from datetime import datetime
from concurrent.futures import ThreadPoolExecutor, as_completed
from urllib.parse import urljoin, urlparse
from bs4 import BeautifulSoup
from flask import Flask

# 設定中文編碼環境變數（Windows 兼容性）
if sys.platform.startswith('win'):
    os.environ['PYTHONIOENCODING'] = 'utf-8'
    os.environ['PYTHONUTF8'] = '1'
    os.environ['LANG'] = 'zh_TW.UTF-8'

# 添加專案根目錄到 Python 路徑
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

class Task62LinkResourceChecker:
    """Task 6.2 全面的路徑和連結檢查工具"""
    
    def __init__(self, project_root):
        self.project_root = Path(project_root)
        self.frontend_path = self.project_root / 'frontend'
        self.base_url = 'http://localhost:5000'
        self.session = requests.Session()
        self.session.timeout = 10
        
        # 檢查結果存儲
        self.results = {
            'task_info': {
                'task_id': '6.2',
                'task_name': '路徑和連結檢查',
                'requirements': ['1.2 - 保持功能完整性'],
                'timestamp': datetime.now().isoformat(),
                'status': 'in_progress'
            },
            'summary': {},
            'static_resources': {
                'checked': 0,
                'found': 0,
                'missing': 0,
                'issues': []
            },
            'templates': {
                'checked': 0,
                'found': 0,
                'missing': 0,
                'issues': []
            },
            'routes': {
                'checked': 0,
                'working': 0,
                'broken': 0,
                'issues': []
            },
            'internal_links': {
                'checked': 0,
                'working': 0,
                'broken': 0,
                'issues': []
            },
            'blueprint_config': {
                'issues': []
            },
            'file_system': {
                'directories_checked': 0,
                'files_checked': 0,
                'missing_files': 0,
                'issues': []
            },
            'recommendations': [],
            'task_completion': {
                'all_links_working': False,
                'static_resources_valid': False,
                'no_404_errors': False,
                'functionality_maintained': False
            }
        }
        
        # Flask 應用模組列表
        self.modules = [
            'email', 'analytics', 'eqc', 'tasks', 
            'monitoring', 'file_management', 'shared'
        ]
        
        # 靜態資源副檔名
        self.static_extensions = {'.css', '.js', '.png', '.jpg', '.jpeg', '.gif', '.ico', '.svg'}
        
        # 重要路由測試端點（符合 Task 6.2 要求）
        self.critical_routes = [
            ('/', 'Main Page'),
            ('/email', 'Email Module'),
            ('/email/', 'Email Module Root'),
            ('/analytics', 'Analytics Module'),
            ('/analytics/', 'Analytics Module Root'),
            ('/eqc', 'EQC Module'),
            ('/eqc/', 'EQC Module Root'),
            ('/tasks', 'Tasks Module'),
            ('/tasks/', 'Tasks Module Root'),
            ('/monitoring', 'Monitoring Module'),
            ('/monitoring/', 'Monitoring Module Root'),
            ('/files', 'File Management Module'),
            ('/files/', 'File Management Module Root'),
            ('/static/shared/css/base.css', 'Shared Base CSS'),
            ('/static/shared/js/main.js', 'Shared Main JS'),
        ]
        
        print(f"🔍 Task 6.2 Link Resource Checker 初始化完成")
        print(f"   專案路徑: {self.project_root}")
        print(f"   前端路徑: {self.frontend_path}")
        print(f"   基礎 URL: {self.base_url}")

    def run_comprehensive_check(self):
        """執行符合 Task 6.2 要求的全面檢查"""
        print("\n" + "="*80)
        print("🚀 Task 6.2 - 開始執行全面的路徑和連結檢查驗證")
        print("   需求: 1.2 保持功能完整性")
        print("   目標: 檢查所有內部連結、驗證靜態資源、確保無 404 錯誤")
        print("="*80)
        
        try:
            # 1. 檢查檔案系統完整性
            print("\n📁 1. 檢查檔案系統完整性...")
            self.check_file_system_integrity()
            
            # 2. 檢查靜態資源路徑和引用
            print("\n🎨 2. 驗證靜態資源載入...")
            self.validate_static_resources()
            
            # 3. 檢查模板檔案完整性
            print("\n📄 3. 檢查模板檔案完整性...")
            self.check_template_integrity()
            
            # 4. 驗證 Flask 藍圖配置
            print("\n🧩 4. 驗證 Flask 藍圖配置...")
            self.validate_blueprint_configuration()
            
            # 5. 啟動應用並測試所有路由
            print("\n🌐 5. 測試 Flask 路由和內部連結...")
            app_thread = self.start_test_server()
            time.sleep(3)  # 等待服務器啟動
            
            try:
                self.test_critical_routes()
                self.check_internal_links_comprehensive()
                self.test_static_resource_loading()
                self.validate_form_actions()
            finally:
                self.stop_test_server(app_thread)
            
            # 6. 評估 Task 6.2 完成狀態
            print("\n📊 6. 評估 Task 6.2 完成狀態...")
            self.evaluate_task_completion()
            
            # 7. 生成詳細報告和修復建議
            print("\n📋 7. 生成詳細檢查報告...")
            self.generate_comprehensive_report()
            
            print("\n✅ Task 6.2 檢查完成！")
            return self.results

        except Exception as e:
            print(f"\n❌ Task 6.2 檢查過程中發生錯誤: {e}")
            self.results['task_info']['status'] = 'failed'
            self.results['task_info']['error'] = str(e)
            import traceback
            traceback.print_exc()
            return self.results

    def check_file_system_integrity(self):
        """檢查檔案系統完整性（Task 6.2 要求）"""
        print("   檢查目錄結構和關鍵檔案...")
        
        missing_items = []
        checked_dirs = 0
        checked_files = 0
        
        # 檢查核心目錄結構
        required_structure = {
            'directories': [
                'frontend',
                'frontend/shared',
                'frontend/shared/templates',
                'frontend/shared/static',
                'frontend/shared/static/css',
                'frontend/shared/static/js',
                'frontend/shared/static/images',
            ],
            'files': [
                'frontend/app.py',
                'frontend/config.py',
                'frontend/shared/templates/base.html',
                'frontend/shared/static/css/base.css',
            ]
        }
        
        # 為每個模組添加必需結構
        for module in self.modules:
            if module != 'shared':
                required_structure['directories'].extend([
                    f'frontend/{module}',
                    f'frontend/{module}/routes',
                    f'frontend/{module}/static',
                    f'frontend/{module}/templates',
                ])
                required_structure['files'].extend([
                    f'frontend/{module}/routes/{module}_routes.py',
                ])
        
        # 檢查目錄
        for dir_path in required_structure['directories']:
            full_path = self.project_root / dir_path
            checked_dirs += 1
            if not full_path.exists():
                missing_items.append({
                    'type': 'missing_directory',
                    'path': dir_path,
                    'critical': True
                })
        
        # 檢查檔案
        for file_path in required_structure['files']:
            full_path = self.project_root / file_path
            checked_files += 1
            if not full_path.exists():
                missing_items.append({
                    'type': 'missing_file',
                    'path': file_path,
                    'critical': True
                })
        
        # 檢查靜態資源檔案存在性
        static_files_missing = 0
        for module in self.modules:
            if module == 'shared':
                static_dir = self.frontend_path / 'shared' / 'static'
            else:
                static_dir = self.frontend_path / module / 'static'
            
            if static_dir.exists():
                for file_path in static_dir.rglob('*'):
                    if file_path.is_file():
                        checked_files += 1
                        # 檢查檔案是否可讀
                        try:
                            with open(file_path, 'rb') as f:
                                f.read(1)  # 嘗試讀取一個字節
                        except Exception as e:
                            static_files_missing += 1
                            missing_items.append({
                                'type': 'unreadable_file',
                                'path': str(file_path.relative_to(self.project_root)),
                                'error': str(e),
                                'critical': False
                            })
        
        # 更新結果
        self.results['file_system']['directories_checked'] = checked_dirs
        self.results['file_system']['files_checked'] = checked_files
        self.results['file_system']['missing_files'] = len(missing_items)
        self.results['file_system']['issues'].extend(missing_items)
        
        print(f"   ✓ 檢查了 {checked_dirs} 個目錄")
        print(f"   ✓ 檢查了 {checked_files} 個檔案")
        if missing_items:
            critical_missing = len([item for item in missing_items if item.get('critical', False)])
            print(f"   ⚠️ 發現 {len(missing_items)} 個問題（{critical_missing} 個關鍵問題）")

    def validate_static_resources(self):
        """驗證靜態資源載入（Task 6.2 核心要求）"""
        print("   驗證所有靜態資源引用...")
        
        static_files = {}
        missing_references = []
        total_references = 0
        
        # 掃描所有靜態檔案
        for module in self.modules:
            if module == 'shared':
                static_dir = self.frontend_path / 'shared' / 'static'
            else:
                static_dir = self.frontend_path / module / 'static'
            
            if static_dir.exists():
                static_files[module] = self._scan_static_files(static_dir)
                print(f"     📁 {module}: 找到 {len(static_files[module])} 個靜態檔案")
        
        # 檢查模板中的所有靜態資源引用
        for module in self.modules:
            if module == 'shared':
                template_dir = self.frontend_path / 'shared' / 'templates'
            else:
                template_dir = self.frontend_path / module / 'templates'
            
            if template_dir.exists():
                for template_file in template_dir.glob('**/*.html'):
                    references = self._extract_all_static_references(template_file)
                    total_references += len(references)
                    
                    for ref in references:
                        if not self._verify_static_reference_exists(ref, static_files, module):
                            missing_references.append({
                                'template': str(template_file.relative_to(self.project_root)),
                                'reference': ref,
                                'module': module,
                                'type': 'missing_static_resource',
                                'severity': 'high' if ref.endswith(('.css', '.js')) else 'medium'
                            })
        
        # 檢查關鍵共享資源
        critical_shared_resources = [
            'css/base.css',
            'css/global.css',
            'js/main.js',
            'js/utils.js',
            'images/favicon.ico'
        ]
        
        for resource in critical_shared_resources:
            if 'shared' in static_files:
                if resource not in static_files['shared']:
                    missing_references.append({
                        'template': 'N/A',
                        'reference': f'/static/shared/{resource}',
                        'module': 'shared',
                        'type': 'missing_critical_shared_resource',
                        'severity': 'high'
                    })
        
        # 更新結果
        total_static_files = sum(len(files) for files in static_files.values())
        self.results['static_resources']['checked'] = total_references
        self.results['static_resources']['found'] = total_static_files
        self.results['static_resources']['missing'] = len(missing_references)
        self.results['static_resources']['issues'].extend(missing_references)
        
        print(f"   ✓ 掃描到 {total_static_files} 個靜態檔案")
        print(f"   ✓ 檢查了 {total_references} 個靜態資源引用")
        if missing_references:
            high_severity = len([ref for ref in missing_references if ref.get('severity') == 'high'])
            print(f"   ❌ 發現 {len(missing_references)} 個缺少的引用（{high_severity} 個高優先級）")

    def check_template_integrity(self):
        """檢查模板檔案完整性"""
        print("   檢查模板檔案語法和完整性...")
        
        template_files = []
        template_issues = []
        
        # 掃描所有模板檔案
        for module in self.modules:
            if module == 'shared':
                template_dir = self.frontend_path / 'shared' / 'templates'
            else:
                template_dir = self.frontend_path / module / 'templates'
            
            if template_dir.exists():
                for template_file in template_dir.glob('**/*.html'):
                    template_files.append(template_file)
                    
                    # 詳細檢查模板檔案
                    issues = self._check_template_file(template_file)
                    template_issues.extend(issues)
        
        # 檢查基礎模板存在性
        base_template = self.frontend_path / 'shared' / 'templates' / 'base.html'
        if not base_template.exists():
            template_issues.append({
                'type': 'missing_base_template',
                'template': 'shared/templates/base.html',
                'severity': 'critical',
                'issue': '基礎模板檔案不存在'
            })
        
        # 更新結果
        self.results['templates']['checked'] = len(template_files)
        self.results['templates']['found'] = len(template_files)
        self.results['templates']['issues'].extend(template_issues)
        
        print(f"   ✓ 檢查了 {len(template_files)} 個模板檔案")
        if template_issues:
            critical_issues = len([issue for issue in template_issues if issue.get('severity') == 'critical'])
            print(f"   ⚠️ 發現 {len(template_issues)} 個模板問題（{critical_issues} 個關鍵問題）")

    def validate_blueprint_configuration(self):
        """驗證 Flask 藍圖配置"""
        print("   驗證 Flask 藍圖配置完整性...")
        
        config_issues = []
        
        # 檢查各模組的路由檔案
        for module in self.modules:
            if module == 'shared':
                continue
                
            route_file = self.frontend_path / module / 'routes' / f'{module}_routes.py'
            
            if not route_file.exists():
                config_issues.append({
                    'type': 'missing_route_file',
                    'module': module,
                    'expected_file': str(route_file.relative_to(self.project_root)),
                    'severity': 'high'
                })
            else:
                # 詳細檢查路由檔案
                route_issues = self._check_route_file(route_file, module)
                config_issues.extend(route_issues)
        
        # 檢查主應用檔案的藍圖註冊
        app_file = self.frontend_path / 'app.py'
        if app_file.exists():
            app_issues = self._check_app_blueprint_registration(app_file)
            config_issues.extend(app_issues)
        else:
            config_issues.append({
                'type': 'missing_app_file',
                'file': 'frontend/app.py',
                'severity': 'critical'
            })
        
        # 更新結果
        self.results['blueprint_config']['issues'].extend(config_issues)
        
        print(f"   ✓ 檢查 Flask 藍圖配置")
        if config_issues:
            high_severity = len([issue for issue in config_issues if issue.get('severity') in ['high', 'critical']])
            print(f"   ⚠️ 發現 {len(config_issues)} 個配置問題（{high_severity} 個高優先級）")

    def start_test_server(self):
        """啟動測試服務器"""
        print("   啟動測試 Flask 服務器...")
        
        def run_app():
            try:
                # 導入並創建應用
                from frontend.app import create_app
                app = create_app('development')
                app.run(host='127.0.0.1', port=5000, debug=False, use_reloader=False, threaded=True)
            except Exception as e:
                print(f"   ❌ Flask 服務器啟動失敗: {e}")
        
        app_thread = threading.Thread(target=run_app, daemon=True)
        app_thread.start()
        
        # 等待服務器完全啟動
        max_wait = 10
        for i in range(max_wait):
            try:
                response = self.session.get(f"{self.base_url}/", timeout=2)
                if response.status_code in [200, 302, 404]:  # 任何響應都表示服務器已啟動
                    print("   ✓ 測試服務器啟動成功")
                    return app_thread
            except:
                time.sleep(1)
                
        print("   ⚠️ 測試服務器可能未完全啟動")
        return app_thread

    def stop_test_server(self, app_thread):
        """停止測試服務器"""
        print("   停止測試服務器...")
        # daemon thread 會自動清理

    def test_critical_routes(self):
        """測試關鍵路由（Task 6.2 核心要求）"""
        print("   測試所有關鍵路由...")
        
        working_routes = []
        broken_routes = []
        
        for route, description in self.critical_routes:
            try:
                url = urljoin(self.base_url, route)
                response = self.session.get(url, timeout=5)
                
                if response.status_code == 200:
                    working_routes.append(route)
                    print(f"     ✓ {route} ({description}) -> {response.status_code}")
                elif response.status_code in [301, 302]:
                    working_routes.append(route)
                    print(f"     ↩️ {route} ({description}) -> {response.status_code} (重定向)")
                elif response.status_code == 404:
                    broken_routes.append({
                        'route': route,
                        'description': description,
                        'status_code': response.status_code,
                        'error': 'Page not found (404)',
                        'severity': 'high'
                    })
                    print(f"     ❌ {route} ({description}) -> {response.status_code} (404 錯誤)")
                else:
                    broken_routes.append({
                        'route': route,
                        'description': description,
                        'status_code': response.status_code,
                        'error': f'HTTP {response.status_code}',
                        'severity': 'medium'
                    })
                    print(f"     ⚠️ {route} ({description}) -> {response.status_code}")
                    
            except Exception as e:
                broken_routes.append({
                    'route': route,
                    'description': description,
                    'status_code': None,
                    'error': str(e),
                    'severity': 'high'
                })
                print(f"     ❌ {route} ({description}) -> 連接錯誤: {e}")
        
        # 更新結果
        self.results['routes']['checked'] = len(self.critical_routes)
        self.results['routes']['working'] = len(working_routes)
        self.results['routes']['broken'] = len(broken_routes)
        self.results['routes']['issues'].extend(broken_routes)
        
        print(f"   ✓ 測試了 {len(self.critical_routes)} 個關鍵路由")
        print(f"   ✓ {len(working_routes)} 個路由正常工作")
        if broken_routes:
            high_severity = len([route for route in broken_routes if route.get('severity') == 'high'])
            print(f"   ❌ {len(broken_routes)} 個路由有問題（{high_severity} 個高優先級）")

    def check_internal_links_comprehensive(self):
        """全面檢查內部連結"""
        print("   全面檢查內部連結...")
        
        working_links = []
        broken_links = []
        
        # 從主要頁面開始檢查連結
        pages_to_check = [
            '/',
            '/email',
            '/analytics',
            '/eqc',
            '/tasks',
            '/monitoring',
            '/files',
        ]
        
        for page in pages_to_check:
            try:
                response = self.session.get(urljoin(self.base_url, page), timeout=5)
                if response.status_code == 200:
                    soup = BeautifulSoup(response.text, 'html.parser')
                    
                    # 查找頁面中的所有連結
                    links = soup.find_all('a', href=True)
                    for link in links[:20]:  # 限制每頁檢查的連結數量
                        href = link['href']
                        
                        # 只檢查內部連結
                        if href.startswith('/') or href.startswith(self.base_url):
                            if href.startswith('/'):
                                test_url = urljoin(self.base_url, href)
                            else:
                                test_url = href
                            
                            try:
                                link_response = self.session.get(test_url, timeout=3)
                                if link_response.status_code in [200, 301, 302]:
                                    working_links.append(href)
                                elif link_response.status_code == 404:
                                    broken_links.append({
                                        'link': href,
                                        'source_page': page,
                                        'status_code': link_response.status_code,
                                        'error': '404 Not Found',
                                        'severity': 'high'
                                    })
                                else:
                                    broken_links.append({
                                        'link': href,
                                        'source_page': page,
                                        'status_code': link_response.status_code,
                                        'error': f'HTTP {link_response.status_code}',
                                        'severity': 'medium'
                                    })
                            except Exception as e:
                                broken_links.append({
                                    'link': href,
                                    'source_page': page,
                                    'status_code': None,
                                    'error': str(e),
                                    'severity': 'medium'
                                })
                                
            except Exception as e:
                print(f"   ⚠️ 無法檢查頁面 {page}: {e}")
        
        # 更新結果
        total_links = len(working_links) + len(broken_links)
        self.results['internal_links']['checked'] = total_links
        self.results['internal_links']['working'] = len(working_links)
        self.results['internal_links']['broken'] = len(broken_links)
        self.results['internal_links']['issues'].extend(broken_links)
        
        print(f"   ✓ 檢查了 {total_links} 個內部連結")
        if working_links:
            print(f"   ✓ {len(working_links)} 個連結正常工作")
        if broken_links:
            high_severity = len([link for link in broken_links if link.get('severity') == 'high'])
            print(f"   ❌ {len(broken_links)} 個連結有問題（{high_severity} 個高優先級）")

    def test_static_resource_loading(self):
        """測試靜態資源載入"""
        print("   測試關鍵靜態資源載入...")
        
        # 測試關鍵靜態資源
        critical_static_resources = [
            '/static/shared/css/base.css',
            '/static/shared/css/global.css',
            '/static/shared/js/main.js',
            '/static/shared/images/favicon.ico',
            '/static/email/css/inbox.css',
            '/static/analytics/css/analytics.css',
        ]
        
        working_resources = 0
        broken_resources = 0
        
        for resource in critical_static_resources:
            try:
                url = urljoin(self.base_url, resource)
                response = self.session.get(url, timeout=3)
                
                if response.status_code == 200:
                    working_resources += 1
                    print(f"     ✓ {resource} -> {response.status_code}")
                elif response.status_code == 404:
                    broken_resources += 1
                    self.results['static_resources']['issues'].append({
                        'type': 'static_404_error',
                        'resource': resource,
                        'status_code': response.status_code,
                        'severity': 'high'
                    })
                    print(f"     ❌ {resource} -> {response.status_code} (404)")
                else:
                    broken_resources += 1
                    print(f"     ⚠️ {resource} -> {response.status_code}")
                    
            except Exception as e:
                broken_resources += 1
                print(f"     ❌ {resource} -> 連接錯誤: {e}")
        
        print(f"   ✓ 測試了 {len(critical_static_resources)} 個關鍵靜態資源")
        print(f"   ✓ {working_resources} 個資源正常載入")
        if broken_resources > 0:
            print(f"   ❌ {broken_resources} 個資源載入失敗")

    def validate_form_actions(self):
        """驗證表單 action 路徑"""
        print("   驗證表單 action 路徑...")
        
        form_issues = []
        
        # 檢查主要頁面的表單
        pages_with_forms = ['/email', '/analytics', '/eqc', '/tasks', '/monitoring', '/files']
        
        for page in pages_with_forms:
            try:
                response = self.session.get(urljoin(self.base_url, page), timeout=5)
                if response.status_code == 200:
                    soup = BeautifulSoup(response.text, 'html.parser')
                    
                    # 查找所有表單
                    forms = soup.find_all('form')
                    for form in forms:
                        action = form.get('action', '')
                        if action and action.startswith('/'):
                            # 測試表單 action 路徑是否存在
                            try:
                                action_url = urljoin(self.base_url, action)
                                # 使用 HEAD 請求測試路徑存在性
                                form_response = self.session.head(action_url, timeout=3)
                                if form_response.status_code == 404:
                                    form_issues.append({
                                        'type': 'form_action_404',
                                        'page': page,
                                        'action': action,
                                        'severity': 'medium'
                                    })
                            except Exception as e:
                                form_issues.append({
                                    'type': 'form_action_error',
                                    'page': page,
                                    'action': action,
                                    'error': str(e),
                                    'severity': 'low'
                                })
                                
            except Exception as e:
                print(f"   ⚠️ 無法檢查頁面表單 {page}: {e}")
        
        if form_issues:
            self.results['internal_links']['issues'].extend(form_issues)
            print(f"   ⚠️ 發現 {len(form_issues)} 個表單路徑問題")
        else:
            print(f"   ✓ 表單 action 路徑檢查完成")

    def evaluate_task_completion(self):
        """評估 Task 6.2 完成狀態"""
        print("   評估 Task 6.2 完成狀態...")
        
        # 評估各項要求的完成狀態
        
        # 1. 所有內部連結是否正常運作
        total_links = self.results['internal_links']['checked']
        broken_links = self.results['internal_links']['broken']
        high_severity_link_issues = len([
            issue for issue in self.results['internal_links']['issues'] 
            if issue.get('severity') == 'high'
        ])
        
        self.results['task_completion']['all_links_working'] = (
            total_links > 0 and broken_links == 0
        )
        
        # 2. 靜態資源載入是否正確
        missing_static = self.results['static_resources']['missing']
        critical_static_issues = len([
            issue for issue in self.results['static_resources']['issues']
            if issue.get('severity') == 'high'
        ])
        
        self.results['task_completion']['static_resources_valid'] = (
            missing_static == 0 and critical_static_issues == 0
        )
        
        # 3. 確保沒有 404 錯誤
        total_404_errors = 0
        for category in ['routes', 'internal_links', 'static_resources']:
            for issue in self.results[category]['issues']:
                if '404' in str(issue.get('error', '')) or issue.get('status_code') == 404:
                    total_404_errors += 1
        
        self.results['task_completion']['no_404_errors'] = (total_404_errors == 0)
        
        # 4. 功能完整性維持（需求 1.2）
        critical_routes_working = self.results['routes']['working']
        total_critical_routes = self.results['routes']['checked']
        critical_issues = sum([
            len([issue for issue in self.results[category]['issues'] 
                 if issue.get('severity') == 'critical'])
            for category in ['file_system', 'templates', 'blueprint_config']
        ])
        
        self.results['task_completion']['functionality_maintained'] = (
            critical_routes_working == total_critical_routes and critical_issues == 0
        )
        
        # 計算整體完成狀態
        completion_criteria = [
            self.results['task_completion']['all_links_working'],
            self.results['task_completion']['static_resources_valid'],
            self.results['task_completion']['no_404_errors'],
            self.results['task_completion']['functionality_maintained']
        ]
        
        completed_criteria = sum(completion_criteria)
        completion_rate = (completed_criteria / len(completion_criteria)) * 100
        
        if completion_rate == 100:
            self.results['task_info']['status'] = 'completed'
        elif completion_rate >= 75:
            self.results['task_info']['status'] = 'mostly_completed'
        elif completion_rate >= 50:
            self.results['task_info']['status'] = 'partially_completed'
        else:
            self.results['task_info']['status'] = 'incomplete'
        
        print(f"   📊 Task 6.2 完成率: {completion_rate:.1f}%")
        print(f"   📊 狀態: {self.results['task_info']['status']}")

    def generate_comprehensive_report(self):
        """生成詳細的 Task 6.2 檢查報告"""
        # 計算總體統計
        total_issues = sum([
            len(self.results[category]['issues'])
            for category in ['static_resources', 'templates', 'routes', 
                           'internal_links', 'blueprint_config', 'file_system']
        ])
        
        critical_issues = sum([
            len([issue for issue in self.results[category]['issues'] 
                 if issue.get('severity') == 'critical'])
            for category in ['static_resources', 'templates', 'routes', 
                           'internal_links', 'blueprint_config', 'file_system']
        ])
        
        high_priority_issues = sum([
            len([issue for issue in self.results[category]['issues'] 
                 if issue.get('severity') == 'high'])
            for category in ['static_resources', 'templates', 'routes', 
                           'internal_links', 'blueprint_config', 'file_system']
        ])
        
        # 計算成功率
        route_success_rate = 0
        if self.results['routes']['checked'] > 0:
            route_success_rate = (self.results['routes']['working'] / 
                                self.results['routes']['checked'] * 100)
        
        link_success_rate = 0
        if self.results['internal_links']['checked'] > 0:
            link_success_rate = (self.results['internal_links']['working'] / 
                               self.results['internal_links']['checked'] * 100)
        
        static_success_rate = 100
        if self.results['static_resources']['checked'] > 0:
            static_success_rate = ((self.results['static_resources']['checked'] - 
                                  self.results['static_resources']['missing']) / 
                                 self.results['static_resources']['checked'] * 100)
        
        # 更新摘要
        self.results['summary'] = {
            'total_issues': total_issues,
            'critical_issues': critical_issues,
            'high_priority_issues': high_priority_issues,
            'route_success_rate': round(route_success_rate, 2),
            'link_success_rate': round(link_success_rate, 2),
            'static_success_rate': round(static_success_rate, 2),
            'static_resources_found': self.results['static_resources']['found'],
            'templates_found': self.results['templates']['found'],
            'files_checked': self.results['file_system']['files_checked'],
            'overall_status': self.results['task_info']['status']
        }
        
        # 生成修復建議
        self._generate_fix_recommendations()
        
        # 保存報告到檔案
        report_file = self.project_root / 'task_6_2_comprehensive_check_report.json'
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(self.results, f, indent=2, ensure_ascii=False)
        
        print(f"   📊 詳細報告已保存到: {report_file}")
        
        # 輸出摘要報告
        self._print_summary_report()

    def _generate_fix_recommendations(self):
        """生成修復建議"""
        recommendations = []
        
        # 基於檢查結果生成具體建議
        if self.results['task_completion']['no_404_errors'] == False:
            recommendations.append({
                'priority': 'high',
                'category': '404 錯誤修復',
                'description': '修復所有 404 錯誤以確保用戶體驗',
                'actions': [
                    '檢查並修復損壞的路由定義',
                    '確保所有靜態資源檔案存在',
                    '驗證內部連結指向的頁面存在'
                ]
            })
        
        if self.results['task_completion']['static_resources_valid'] == False:
            recommendations.append({
                'priority': 'high',
                'category': '靜態資源修復',
                'description': '修復靜態資源載入問題',
                'actions': [
                    '檢查模板中的靜態資源路徑引用',
                    '確保 Flask 藍圖的靜態資源配置正確',
                    '驗證所有關鍵 CSS/JS 檔案存在'
                ]
            })
        
        if self.results['task_completion']['all_links_working'] == False:
            recommendations.append({
                'priority': 'medium',
                'category': '內部連結修復',
                'description': '修復損壞的內部連結',
                'actions': [
                    '檢查導航菜單中的連結',
                    '驗證表單 action 路徑',
                    '更新過期的內部連結'
                ]
            })
        
        if len(self.results['blueprint_config']['issues']) > 0:
            recommendations.append({
                'priority': 'high',
                'category': 'Flask 配置修復',
                'description': '修復 Flask 藍圖配置問題',
                'actions': [
                    '確保所有模組的路由檔案存在',
                    '檢查藍圖定義和註冊',
                    '驗證靜態資源路徑配置'
                ]
            })
        
        if self.results['task_completion']['functionality_maintained'] == True:
            recommendations.append({
                'priority': 'low',
                'category': '準備 Vue.js 遷移',
                'description': '系統準備好進行 Vue.js 遷移',
                'actions': [
                    '開始規劃前端組件架構',
                    '準備 API 端點文檔',
                    '制定漸進式遷移計劃'
                ]
            })
        
        # 如果沒有問題，給出積極建議
        if len(recommendations) == 0 or all(r['priority'] == 'low' for r in recommendations):
            recommendations.insert(0, {
                'priority': 'info',
                'category': 'Task 6.2 完成',
                'description': '🎉 恭喜！Task 6.2 所有檢查項目都通過了',
                'actions': [
                    '系統已準備好進行 Vue.js 遷移',
                    '可以繼續 Task 7 文檔建立階段',
                    '建議進行最終的功能測試確認'
                ]
            })
        
        self.results['recommendations'] = recommendations

    def _print_summary_report(self):
        """輸出摘要報告"""
        print("\n" + "="*80)
        print("📊 Task 6.2 全面檢查摘要報告")
        print("="*80)
        print(f"任務 ID: {self.results['task_info']['task_id']}")
        print(f"任務名稱: {self.results['task_info']['task_name']}")
        print(f"需求對應: {', '.join(self.results['task_info']['requirements'])}")
        print(f"檢查時間: {self.results['task_info']['timestamp'][:19]}")
        print(f"整體狀態: {self.results['summary']['overall_status']}")
        
        print(f"\n📈 成功率統計:")
        print(f"  路由成功率: {self.results['summary']['route_success_rate']}%")
        print(f"  內部連結成功率: {self.results['summary']['link_success_rate']}%")
        print(f"  靜態資源成功率: {self.results['summary']['static_success_rate']}%")
        
        print(f"\n📊 檢查統計:")
        print(f"  靜態資源: {self.results['summary']['static_resources_found']} 個")
        print(f"  模板檔案: {self.results['summary']['templates_found']} 個")
        print(f"  檔案檢查: {self.results['summary']['files_checked']} 個")
        print(f"  總問題數: {self.results['summary']['total_issues']} 個")
        
        if self.results['summary']['critical_issues'] > 0:
            print(f"  ❌ 關鍵問題: {self.results['summary']['critical_issues']} 個")
        if self.results['summary']['high_priority_issues'] > 0:
            print(f"  ⚠️ 高優先級問題: {self.results['summary']['high_priority_issues']} 個")
        
        print(f"\n✅ Task 6.2 完成狀態檢查:")
        completion_status = self.results['task_completion']
        print(f"  內部連結正常: {'✅' if completion_status['all_links_working'] else '❌'}")
        print(f"  靜態資源正確: {'✅' if completion_status['static_resources_valid'] else '❌'}")
        print(f"  無 404 錯誤: {'✅' if completion_status['no_404_errors'] else '❌'}")
        print(f"  功能完整性: {'✅' if completion_status['functionality_maintained'] else '❌'}")
        
        print(f"\n💡 修復建議:")
        for rec in self.results['recommendations']:
            priority_icon = {
                'critical': '🔴',
                'high': '🟡',
                'medium': '🔵',
                'low': '🟢',
                'info': 'ℹ️'
            }.get(rec['priority'], '•')
            print(f"  {priority_icon} {rec['category']}: {rec['description']}")
            for action in rec['actions']:
                print(f"     - {action}")
        
        print("="*80)

    # 輔助方法
    def _scan_static_files(self, static_dir):
        """掃描靜態檔案目錄"""
        files = {}
        for file_path in static_dir.rglob('*'):
            if file_path.is_file() and file_path.suffix in self.static_extensions:
                rel_path = file_path.relative_to(static_dir)
                files[str(rel_path)] = str(file_path)
        return files

    def _extract_all_static_references(self, template_file):
        """從模板中提取所有靜態資源引用"""
        references = []
        try:
            with open(template_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 更全面的靜態資源引用模式
            patterns = [
                r'url_for\([\'"]static[\'"],\s*filename=[\'"]([^\'"]+)[\'"]',  # Flask url_for
                r'url_for\([\'"]([^.\'\"]+)\.static[\'"],\s*filename=[\'"]([^\'"]+)[\'"]',  # Module static
                r'href=[\'"]([^\'\"]+\.css)[\'"]',  # CSS 連結
                r'src=[\'"]([^\'\"]+\.js)[\'"]',   # JS 腳本
                r'src=[\'"]([^\'\"]+\.(png|jpg|jpeg|gif|ico|svg))[\'"]',  # 圖片
                r'/static/([^\'\"\\s]+)',  # 直接靜態路徑引用
                r'background-image:\s*url\([\'"]?([^\'\"\\)]+)[\'"]?\)',  # CSS 背景圖片
            ]
            
            for pattern in patterns:
                matches = re.findall(pattern, content, re.IGNORECASE)
                for match in matches:
                    if isinstance(match, tuple):
                        if len(match) == 2 and '.' in match[0]:  # Module static pattern
                            references.append(f'{match[0].split(".")[0]}/{match[1]}')
                        else:
                            references.append(match[0])
                    else:
                        references.append(match)
                        
        except Exception as e:
            self.results['templates']['issues'].append({
                'type': 'template_read_error',
                'template': str(template_file.relative_to(self.project_root)),
                'error': str(e),
                'severity': 'low'
            })
        
        return references

    def _verify_static_reference_exists(self, reference, static_files, current_module):
        """驗證靜態資源引用是否存在"""
        # 清理路徑前綴
        clean_ref = reference.lstrip('/')
        if clean_ref.startswith('static/'):
            clean_ref = clean_ref[7:]  # 移除 'static/' 前綴
        
        # 在所有模組中查找
        for module, files in static_files.items():
            if clean_ref in files:
                return True
            # 檢查模組特定路徑
            if clean_ref.startswith(f'{module}/'):
                module_ref = clean_ref[len(module)+1:]
                if module_ref in files:
                    return True
            # 檢查當前模組路徑
            if module == current_module and clean_ref in files:
                return True
        
        return False

    def _check_template_file(self, template_file):
        """檢查模板檔案的詳細問題"""
        issues = []
        
        try:
            with open(template_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            rel_path = str(template_file.relative_to(self.project_root))
            
            # 檢查基本的 Jinja2 語法錯誤
            if '{{' in content and '}}' not in content:
                issues.append({
                    'type': 'jinja_syntax_error',
                    'template': rel_path,
                    'issue': 'Unclosed Jinja2 variable',
                    'severity': 'high'
                })
            
            if '{%' in content and '%}' not in content:
                issues.append({
                    'type': 'jinja_syntax_error',
                    'template': rel_path,
                    'issue': 'Unclosed Jinja2 block',
                    'severity': 'high'
                })
            
            # 檢查基礎模板擴展
            if 'extends' not in content and template_file.name != 'base.html':
                issues.append({
                    'type': 'missing_extends',
                    'template': rel_path,
                    'issue': 'Template does not extend base template',
                    'severity': 'medium'
                })
            
            # 檢查空的模板檔案
            if len(content.strip()) == 0:
                issues.append({
                    'type': 'empty_template',
                    'template': rel_path,
                    'issue': 'Template file is empty',
                    'severity': 'medium'
                })
                
        except Exception as e:
            issues.append({
                'type': 'template_read_error',
                'template': str(template_file.relative_to(self.project_root)),
                'error': str(e),
                'severity': 'high'
            })
        
        return issues

    def _check_route_file(self, route_file, module):
        """檢查路由檔案的詳細問題"""
        issues = []
        
        try:
            with open(route_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            rel_path = str(route_file.relative_to(self.project_root))
            
            # 檢查藍圖定義
            blueprint_pattern = rf'{module}_bp\s*=\s*Blueprint'
            if not re.search(blueprint_pattern, content):
                issues.append({
                    'type': 'missing_blueprint_definition',
                    'module': module,
                    'file': rel_path,
                    'severity': 'high'
                })
            
            # 檢查基本路由定義
            route_pattern = r'@\w+_bp\.route\('
            if not re.search(route_pattern, content):
                issues.append({
                    'type': 'no_routes_defined',
                    'module': module,
                    'file': rel_path,
                    'severity': 'medium'
                })
            
            # 檢查靜態資源配置
            if 'static_folder' not in content or 'static_url_path' not in content:
                issues.append({
                    'type': 'missing_static_config',
                    'module': module,
                    'file': rel_path,
                    'severity': 'medium'
                })
                
        except Exception as e:
            issues.append({
                'type': 'route_file_read_error',
                'module': module,
                'file': str(route_file.relative_to(self.project_root)),
                'error': str(e),
                'severity': 'high'
            })
        
        return issues

    def _check_app_blueprint_registration(self, app_file):
        """檢查主應用檔案的藍圖註冊"""
        issues = []
        
        try:
            with open(app_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 檢查是否導入了所有模組的藍圖
            for module in self.modules:
                if module == 'shared':
                    continue
                    
                import_pattern = rf'from frontend\.{module}\.routes\.{module}_routes import {module}_bp'
                if not re.search(import_pattern, content):
                    issues.append({
                        'type': 'missing_blueprint_import',
                        'module': module,
                        'file': 'frontend/app.py',
                        'expected_import': f'from frontend.{module}.routes.{module}_routes import {module}_bp',
                        'severity': 'high'
                    })
                
                # 檢查藍圖註冊
                register_pattern = rf'app\.register_blueprint\({module}_bp'
                if not re.search(register_pattern, content):
                    issues.append({
                        'type': 'missing_blueprint_registration',
                        'module': module,
                        'file': 'frontend/app.py',
                        'severity': 'high'
                    })
                    
        except Exception as e:
            issues.append({
                'type': 'app_file_read_error',
                'file': 'frontend/app.py',
                'error': str(e),
                'severity': 'critical'
            })
        
        return issues


def main():
    """主函數"""
    try:
        print("🔍 Task 6.2 - 路徑和連結檢查驗證工具")
        print("   符合 .kiro 配置要求，執行全面檢查")
        print("   需求: 1.2 保持功能完整性")
        
        # 獲取專案根目錄
        project_root = Path(__file__).parent.parent
        
        # 創建檢查器並執行檢查
        checker = Task62LinkResourceChecker(project_root)
        results = checker.run_comprehensive_check()
        
        # 判斷檢查結果
        if results['task_info']['status'] in ['completed', 'mostly_completed']:
            print(f"\n🎉 Task 6.2 檢查成功完成！")
            print(f"   狀態: {results['task_info']['status']}")
            print(f"   系統準備好進行後續步驟")
            return True
        else:
            print(f"\n⚠️ Task 6.2 檢查發現問題需要修復")
            print(f"   狀態: {results['task_info']['status']}")
            print(f"   請檢查報告並修復相關問題")
            return False
        
    except KeyboardInterrupt:
        print("\n⏸️ 檢查被用戶中斷")
        return False
    except Exception as e:
        print(f"\n❌ 檢查過程中發生錯誤: {e}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == '__main__':
    main()