#!/usr/bin/env python3
"""
Playwright 測試運行器
運行整合服務的 Playwright 測試
"""

import subprocess
import sys
import time
import requests
from pathlib import Path


def check_services_running():
    """檢查服務是否運行"""
    services = [
        {'name': 'Flask', 'url': 'http://localhost:5000'},
        {'name': 'FastAPI', 'url': 'http://localhost:8010/docs'}
    ]
    
    for service in services:
        try:
            response = requests.get(service['url'], timeout=5)
            if response.status_code == 200:
                print(f"✅ {service['name']} 服務運行正常")
            else:
                print(f"❌ {service['name']} 服務返回狀態碼: {response.status_code}")
                return False
        except Exception as e:
            print(f"❌ {service['name']} 服務無法連接: {e}")
            return False
    
    return True


def run_tests():
    """運行 Playwright 測試"""
    print("🚀 開始運行 Playwright 整合測試...")
    
    # 檢查服務狀態
    if not check_services_running():
        print("❌ 服務未運行，請先啟動整合服務")
        return False
    
    # 創建測試目錄
    test_dirs = ["tests/screenshots", "tests/reports", "tests/logs"]
    for test_dir in test_dirs:
        Path(test_dir).mkdir(parents=True, exist_ok=True)
    
    # 運行測試
    try:
        cmd = [
            sys.executable, "-m", "pytest",
            "tests/playwright/test_integrated_services.py",
            "-v",
            "--tb=short",
            "--capture=no",
            "--html=tests/reports/playwright_report.html",
            "--self-contained-html"
        ]
        
        print(f"執行命令: {' '.join(cmd)}")
        result = subprocess.run(cmd, capture_output=True, text=True)
        
        print("📊 測試輸出:")
        print(result.stdout)
        
        if result.stderr:
            print("⚠️ 錯誤輸出:")
            print(result.stderr)
        
        if result.returncode == 0:
            print("✅ 所有測試通過!")
            return True
        else:
            print(f"❌ 測試失敗，返回碼: {result.returncode}")
            return False
            
    except Exception as e:
        print(f"❌ 運行測試時發生錯誤: {e}")
        return False


def main():
    """主函數"""
    print("=" * 60)
    print("🎭 Playwright 整合服務測試")
    print("=" * 60)
    
    success = run_tests()
    
    print("=" * 60)
    if success:
        print("🎉 測試完成! 檢查 tests/screenshots/ 目錄查看截圖")
        print("📊 測試報告: tests/reports/playwright_report.html")
    else:
        print("💥 測試失敗! 請檢查服務狀態和錯誤日誌")
    print("=" * 60)
    
    return 0 if success else 1


if __name__ == "__main__":
    sys.exit(main())
