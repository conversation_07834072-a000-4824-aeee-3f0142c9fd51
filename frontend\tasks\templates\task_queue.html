<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>任務隊列 - Outlook Summary</title>
    <link rel="stylesheet" href="{{ url_for('tasks.static', filename='css/tasks.css') }}?v={{ range(1000, 9999) | random }}">
    <link rel="stylesheet" href="{{ url_for('shared.static', filename='css/global.css') }}">
</head>
<body>
    <div class="task-queue-container">
        <header class="queue-header">
            <h1>任務隊列管理</h1>
            <div class="header-stats">
                <div class="stat-item">
                    <span class="stat-label">隊列總數:</span>
                    <span class="stat-value">{{ stats.total_queues or 0 }}</span>
                </div>
                <div class="stat-item">
                    <span class="stat-label">待執行:</span>
                    <span class="stat-value pending">{{ stats.pending_tasks or 0 }}</span>
                </div>
                <div class="stat-item">
                    <span class="stat-label">執行中:</span>
                    <span class="stat-value running">{{ stats.running_tasks or 0 }}</span>
                </div>
                <div class="stat-item">
                    <span class="stat-label">今日處理:</span>
                    <span class="stat-value">{{ stats.processed_today or 0 }}</span>
                </div>
            </div>
            <div class="header-actions">
                <button id="pause-all-queues-btn" class="btn btn-warning">
                    <span class="btn-icon">⏸️</span>
                    <span class="btn-text">暫停全部</span>
                </button>
                <button id="resume-all-queues-btn" class="btn btn-success">
                    <span class="btn-icon">▶️</span>
                    <span class="btn-text">恢復全部</span>
                </button>
                <button id="refresh-queues-btn" class="btn btn-secondary">
                    <span class="btn-icon">🔄</span>
                    <span class="btn-text">重新整理</span>
                </button>
                <a href="{{ url_for('tasks.dashboard') }}" class="btn btn-outline">
                    <span class="btn-icon">🏠</span>
                    <span class="btn-text">返回儀表板</span>
                </a>
            </div>
        </header>

        <div class="queue-content">
            <!-- 隊列控制面板 -->
            <div class="queue-control-section">
                <div class="control-card">
                    <h3>⚙️ 隊列控制</h3>
                    <div class="control-grid">
                        <div class="control-group">
                            <h4>全局設定</h4>
                            <div class="control-item">
                                <label for="max-concurrent-tasks">最大併發任務:</label>
                                <input type="number" id="max-concurrent-tasks" value="{{ config.max_concurrent_tasks or 5 }}" min="1" max="50">
                            </div>
                            <div class="control-item">
                                <label for="task-timeout">任務超時 (分鐘):</label>
                                <input type="number" id="task-timeout" value="{{ config.task_timeout or 30 }}" min="1" max="300">
                            </div>
                            <div class="control-item">
                                <label for="retry-attempts">重試次數:</label>
                                <input type="number" id="retry-attempts" value="{{ config.retry_attempts or 3 }}" min="0" max="10">
                            </div>
                            <div class="control-item">
                                <label>
                                    <input type="checkbox" id="auto-cleanup" {{ 'checked' if config.auto_cleanup else '' }}>
                                    自動清理已完成任務
                                </label>
                            </div>
                        </div>
                        
                        <div class="control-group">
                            <h4>優先級設定</h4>
                            <div class="priority-weights">
                                <div class="weight-item">
                                    <label>緊急:</label>
                                    <input type="range" id="critical-weight" min="1" max="10" value="{{ config.critical_weight or 8 }}">
                                    <span class="weight-value">{{ config.critical_weight or 8 }}</span>
                                </div>
                                <div class="weight-item">
                                    <label>高:</label>
                                    <input type="range" id="high-weight" min="1" max="10" value="{{ config.high_weight or 6 }}">
                                    <span class="weight-value">{{ config.high_weight or 6 }}</span>
                                </div>
                                <div class="weight-item">
                                    <label>中等:</label>
                                    <input type="range" id="medium-weight" min="1" max="10" value="{{ config.medium_weight or 4 }}">
                                    <span class="weight-value">{{ config.medium_weight or 4 }}</span>
                                </div>
                                <div class="weight-item">
                                    <label>低:</label>
                                    <input type="range" id="low-weight" min="1" max="10" value="{{ config.low_weight or 2 }}">
                                    <span class="weight-value">{{ config.low_weight or 2 }}</span>
                                </div>
                            </div>
                        </div>
                        
                        <div class="control-group">
                            <h4>隊列管理</h4>
                            <div class="queue-actions">
                                <button id="create-queue-btn" class="btn btn-primary">建立新隊列</button>
                                <button id="clear-completed-btn" class="btn btn-secondary">清除已完成</button>
                                <button id="clear-failed-btn" class="btn btn-danger">清除失敗任務</button>
                                <button id="export-queue-stats-btn" class="btn btn-outline">匯出統計</button>
                            </div>
                        </div>
                    </div>
                    <div class="control-actions">
                        <button id="apply-settings-btn" class="btn btn-primary">套用設定</button>
                        <button id="reset-settings-btn" class="btn btn-outline">重設預設</button>
                    </div>
                </div>
            </div>

            <!-- 隊列監控 -->
            <div class="queue-monitoring-section">
                <div class="monitoring-tabs">
                    <button class="tab-btn active" data-tab="realtime">即時監控</button>
                    <button class="tab-btn" data-tab="history">歷史記錄</button>
                    <button class="tab-btn" data-tab="analytics">分析報告</button>
                </div>

                <!-- 即時監控 -->
                <div class="tab-content active" id="realtime-tab">
                    <div class="realtime-grid">
                        {% for queue in queues %}
                        <div class="queue-monitor-card" data-queue-id="{{ queue.id }}">
                            <div class="queue-header">
                                <div class="queue-info">
                                    <h4 class="queue-name">{{ queue.name }}</h4>
                                    <div class="queue-status {{ queue.status }}">
                                        <span class="status-indicator"></span>
                                        {{ queue.status_display }}
                                    </div>
                                </div>
                                <div class="queue-controls">
                                    {% if queue.status == 'running' %}
                                    <button class="btn btn-sm btn-warning" onclick="pauseQueue('{{ queue.id }}')">暫停</button>
                                    {% elif queue.status == 'paused' %}
                                    <button class="btn btn-sm btn-success" onclick="resumeQueue('{{ queue.id }}')">恢復</button>
                                    {% endif %}
                                    <div class="dropdown">
                                        <button class="btn btn-sm btn-outline dropdown-toggle">⚙️</button>
                                        <div class="dropdown-menu">
                                            <a href="#" onclick="configureQueue('{{ queue.id }}')">配置</a>
                                            <a href="#" onclick="viewQueueLogs('{{ queue.id }}')">日誌</a>
                                            <a href="#" onclick="exportQueueData('{{ queue.id }}')">匯出</a>
                                            <div class="dropdown-divider"></div>
                                            <a href="#" onclick="deleteQueue('{{ queue.id }}')">刪除</a>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="queue-stats">
                                <div class="stat-row">
                                    <div class="stat">
                                        <span class="stat-label">等待中:</span>
                                        <span class="stat-value pending">{{ queue.pending_count }}</span>
                                    </div>
                                    <div class="stat">
                                        <span class="stat-label">執行中:</span>
                                        <span class="stat-value running">{{ queue.running_count }}</span>
                                    </div>
                                    <div class="stat">
                                        <span class="stat-label">已完成:</span>
                                        <span class="stat-value completed">{{ queue.completed_count }}</span>
                                    </div>
                                    <div class="stat">
                                        <span class="stat-label">失敗:</span>
                                        <span class="stat-value failed">{{ queue.failed_count }}</span>
                                    </div>
                                </div>
                            </div>

                            <div class="queue-progress">
                                <div class="progress-info">
                                    <span class="progress-label">處理進度:</span>
                                    <span class="progress-percentage">{{ '%.1f'|format(queue.completion_percentage) }}%</span>
                                </div>
                                <div class="progress-bar">
                                    <div class="progress-fill" style="width: {{ queue.completion_percentage }}%"></div>
                                </div>
                            </div>

                            <div class="queue-performance">
                                <div class="perf-metric">
                                    <span class="perf-label">處理速度:</span>
                                    <span class="perf-value">{{ queue.processing_rate }}/分鐘</span>
                                </div>
                                <div class="perf-metric">
                                    <span class="perf-label">平均耗時:</span>
                                    <span class="perf-value">{{ '%.1f'|format(queue.avg_execution_time) }}秒</span>
                                </div>
                                <div class="perf-metric">
                                    <span class="perf-label">成功率:</span>
                                    <span class="perf-value">{{ '%.1f'|format(queue.success_rate) }}%</span>
                                </div>
                            </div>

                            <div class="recent-tasks">
                                <h5>最新任務</h5>
                                <div class="task-mini-list">
                                    {% for task in queue.recent_tasks %}
                                    <div class="mini-task {{ task.status }}">
                                        <span class="task-name">{{ task.name|truncate(20) }}</span>
                                        <span class="task-status {{ task.status }}">{{ task.status_display }}</span>
                                        <span class="task-time">{{ task.updated_at.strftime('%H:%M') if task.updated_at else 'N/A' }}</span>
                                    </div>
                                    {% endfor %}
                                </div>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                </div>

                <!-- 歷史記錄 -->
                <div class="tab-content" id="history-tab">
                    <div class="history-controls">
                        <div class="filter-row">
                            <select id="history-timeframe">
                                <option value="today">今天</option>
                                <option value="week" selected>本週</option>
                                <option value="month">本月</option>
                                <option value="quarter">本季</option>
                            </select>
                            <select id="history-queue">
                                <option value="all" selected>全部隊列</option>
                                {% for queue in queues %}
                                <option value="{{ queue.id }}">{{ queue.name }}</option>
                                {% endfor %}
                            </select>
                            <select id="history-status">
                                <option value="all" selected>全部狀態</option>
                                <option value="completed">已完成</option>
                                <option value="failed">失敗</option>
                                <option value="cancelled">已取消</option>
                            </select>
                            <button id="apply-history-filters-btn" class="btn btn-sm btn-primary">套用篩選</button>
                        </div>
                    </div>

                    <div class="history-table">
                        <table>
                            <thead>
                                <tr>
                                    <th>任務名稱</th>
                                    <th>隊列</th>
                                    <th>類型</th>
                                    <th>優先級</th>
                                    <th>狀態</th>
                                    <th>開始時間</th>
                                    <th>完成時間</th>
                                    <th>執行時間</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody id="history-table-body">
                                {% for task in task_history %}
                                <tr class="history-row {{ task.status }}">
                                    <td class="task-name">{{ task.name }}</td>
                                    <td class="queue-name">{{ task.queue_name }}</td>
                                    <td class="task-type">{{ task.type_display }}</td>
                                    <td class="task-priority {{ task.priority.lower() }}">{{ task.priority }}</td>
                                    <td class="task-status {{ task.status }}">
                                        <span class="status-badge {{ task.status }}">{{ task.status_display }}</span>
                                    </td>
                                    <td class="start-time">{{ task.started_at.strftime('%Y-%m-%d %H:%M') if task.started_at else 'N/A' }}</td>
                                    <td class="end-time">{{ task.completed_at.strftime('%Y-%m-%d %H:%M') if task.completed_at else 'N/A' }}</td>
                                    <td class="duration">{{ task.execution_duration if task.execution_duration else 'N/A' }}</td>
                                    <td class="actions">
                                        <button class="btn btn-sm btn-outline" onclick="viewTaskDetails('{{ task.id }}')">詳情</button>
                                        {% if task.status == 'failed' %}
                                        <button class="btn btn-sm btn-primary" onclick="retryTask('{{ task.id }}')">重試</button>
                                        {% endif %}
                                        <button class="btn btn-sm btn-secondary" onclick="viewTaskLogs('{{ task.id }}')">日誌</button>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>

                    <div class="pagination" id="history-pagination">
                        <!-- 分頁控制會由JavaScript動態生成 -->
                    </div>
                </div>

                <!-- 分析報告 -->
                <div class="tab-content" id="analytics-tab">
                    <div class="analytics-grid">
                        <div class="analytics-card">
                            <h3>隊列效能分析</h3>
                            <div class="chart-container">
                                <canvas id="queue-performance-chart"></canvas>
                            </div>
                        </div>

                        <div class="analytics-card">
                            <h3>任務執行時間分佈</h3>
                            <div class="chart-container">
                                <canvas id="execution-distribution-chart"></canvas>
                            </div>
                        </div>

                        <div class="analytics-card">
                            <h3>失敗率趨勢</h3>
                            <div class="chart-container">
                                <canvas id="failure-trend-chart"></canvas>
                            </div>
                        </div>

                        <div class="analytics-card">
                            <h3>資源使用率</h3>
                            <div class="resource-metrics">
                                <div class="metric-item">
                                    <div class="metric-label">CPU使用率</div>
                                    <div class="metric-bar">
                                        <div class="bar-fill" style="width: {{ analytics.cpu_usage }}%"></div>
                                    </div>
                                    <div class="metric-value">{{ analytics.cpu_usage }}%</div>
                                </div>
                                <div class="metric-item">
                                    <div class="metric-label">記憶體使用率</div>
                                    <div class="metric-bar">
                                        <div class="bar-fill" style="width: {{ analytics.memory_usage }}%"></div>
                                    </div>
                                    <div class="metric-value">{{ analytics.memory_usage }}%</div>
                                </div>
                                <div class="metric-item">
                                    <div class="metric-label">隊列容量使用</div>
                                    <div class="metric-bar">
                                        <div class="bar-fill" style="width: {{ analytics.queue_capacity_usage }}%"></div>
                                    </div>
                                    <div class="metric-value">{{ analytics.queue_capacity_usage }}%</div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="analytics-summary">
                        <div class="summary-card">
                            <h4>效能建議</h4>
                            <ul class="recommendations">
                                {% for recommendation in performance_recommendations %}
                                <li class="recommendation {{ recommendation.type }}">
                                    <strong>{{ recommendation.title }}</strong>
                                    <p>{{ recommendation.description }}</p>
                                </li>
                                {% endfor %}
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 隊列配置模態框 -->
    <div class="modal" id="queue-config-modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 id="queue-config-title">隊列配置</h3>
                <button class="modal-close">&times;</button>
            </div>
            <div class="modal-body">
                <form id="queue-config-form">
                    <div class="form-group">
                        <label for="queue-name">隊列名稱:</label>
                        <input type="text" id="queue-name" required>
                    </div>
                    <div class="form-group">
                        <label for="queue-description">描述:</label>
                        <textarea id="queue-description" rows="3"></textarea>
                    </div>
                    <div class="form-row">
                        <div class="form-group">
                            <label for="queue-max-workers">最大工作執行器:</label>
                            <input type="number" id="queue-max-workers" min="1" max="20" value="3">
                        </div>
                        <div class="form-group">
                            <label for="queue-timeout">任務超時 (秒):</label>
                            <input type="number" id="queue-timeout" min="60" max="3600" value="1800">
                        </div>
                    </div>
                    <div class="form-row">
                        <div class="form-group">
                            <label for="queue-retry-count">重試次數:</label>
                            <input type="number" id="queue-retry-count" min="0" max="5" value="3">
                        </div>
                        <div class="form-group">
                            <label for="queue-priority">預設優先級:</label>
                            <select id="queue-priority">
                                <option value="LOW">低</option>
                                <option value="MEDIUM" selected>中等</option>
                                <option value="HIGH">高</option>
                                <option value="CRITICAL">緊急</option>
                            </select>
                        </div>
                    </div>
                    <div class="form-group">
                        <label>
                            <input type="checkbox" id="queue-auto-start" checked>
                            自動啟動
                        </label>
                    </div>
                    <div class="form-group">
                        <label>
                            <input type="checkbox" id="queue-persistent">
                            持久化隊列
                        </label>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button id="save-queue-config-btn" class="btn btn-primary">儲存配置</button>
                <button class="btn btn-secondary" onclick="closeModal('queue-config-modal')">取消</button>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="{{ url_for('shared.static', filename='js/main.js') }}"></script>
    <script src="{{ url_for('shared.static', filename='lib/chart.min.js') }}"></script>
    <script src="{{ url_for('tasks.static', filename='js/task-queue.js') }}"></script>
</body>
</html>