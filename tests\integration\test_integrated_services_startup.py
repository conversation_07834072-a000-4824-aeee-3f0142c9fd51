#!/usr/bin/env python3
"""
整合服務啟動端到端測試
專門測試 start_integrated_services.py 的啟動流程和新架構整合
"""

import os
import sys
import time
import json
import subprocess
import threading
import requests
import signal
from pathlib import Path
from typing import Dict, List, Any, Optional
from datetime import datetime

# 添加專案根目錄到 Python 路徑
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))


class IntegratedServicesTestRunner:
    """整合服務測試運行器"""
    
    def __init__(self):
        self.project_root = project_root
        self.flask_port = 5002  # 使用測試端口
        self.fastapi_port = 8012  # 使用測試端口
        self.service_process = None
        self.test_results = {}
        
    def test_architecture_validation(self) -> Dict[str, Any]:
        """測試 start_integrated_services.py 內建的架構驗證"""
        print("🔍 測試內建架構驗證功能...")
        
        try:
            env = os.environ.copy()
            env['PYTHONIOENCODING'] = 'utf-8'
            env['PYTHONUTF8'] = '1'
            
            # 只執行架構驗證，不啟動服務
            result = subprocess.run([
                sys.executable, 'start_integrated_services.py',
                '--validate-architecture'
            ], 
            capture_output=True, 
            text=True, 
            encoding='utf-8',
            timeout=30,
            cwd=self.project_root,
            env=env
            )
            
            success = result.returncode == 0
            output = result.stdout + result.stderr
            
            # 分析輸出內容
            validation_passed = '架構驗證通過' in output or 'validation passed' in output.lower()
            has_modules_check = '前端模組載入成功' in output or 'module' in output.lower()
            has_flask_creation = 'Flask應用創建成功' in output or 'flask' in output.lower()
            
            return {
                'success': success and validation_passed,
                'return_code': result.returncode,
                'validation_passed': validation_passed,
                'has_modules_check': has_modules_check,
                'has_flask_creation': has_flask_creation,
                'output': output
            }
            
        except subprocess.TimeoutExpired:
            return {
                'success': False,
                'error': '架構驗證超時'
            }
        except Exception as e:
            return {
                'success': False,
                'error': str(e)
            }
    
    def test_deployment_readiness_check(self) -> Dict[str, Any]:
        """測試部署就緒性檢查"""
        print("🔍 測試部署就緒性檢查...")
        
        try:
            env = os.environ.copy()
            env['PYTHONIOENCODING'] = 'utf-8'
            env['PYTHONUTF8'] = '1'
            
            # 執行部署就緒性檢查
            result = subprocess.run([
                sys.executable, 'start_integrated_services.py',
                '--check-deployment'
            ],
            capture_output=True,
            text=True,
            encoding='utf-8', 
            timeout=60,
            cwd=self.project_root,
            env=env
            )
            
            output = result.stdout + result.stderr
            
            # 分析檢查結果
            deployment_passed = '部署就緒性檢查通過' in output
            env_check = '環境變數檢查完成' in output or '環境變數檢查通過' in output
            frontend_check = '前端模組載入成功' in output or '前端架構驗證通過' in output
            db_check = '資料庫連接檢查通過' in output or 'database' in output.lower()
            
            return {
                'success': result.returncode == 0,
                'return_code': result.returncode,
                'deployment_passed': deployment_passed,
                'environment_check': env_check,
                'frontend_check': frontend_check,
                'database_check': db_check,
                'output': output
            }
            
        except subprocess.TimeoutExpired:
            return {
                'success': False,
                'error': '部署檢查超時'
            }
        except Exception as e:
            return {
                'success': False,
                'error': str(e)
            }
    
    def start_integrated_service(self, timeout: int = 45) -> bool:
        """啟動整合服務進行測試"""
        print("🚀 啟動整合服務進行端到端測試...")
        
        try:
            env = os.environ.copy()
            env['PYTHONIOENCODING'] = 'utf-8'
            env['PYTHONUTF8'] = '1'
            
            # 啟動整合服務（測試模式）
            self.service_process = subprocess.Popen([
                sys.executable, 'start_integrated_services.py',
                '--flask-port', str(self.flask_port),
                '--fastapi-port', str(self.fastapi_port),
                '--no-monitor',  # 禁用監控減少輸出
                '--mode', 'integrated'
            ],
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True,
            encoding='utf-8',
            cwd=self.project_root,
            env=env
            )
            
            # 等待服務啟動
            flask_ready = False
            fastapi_ready = False
            
            for i in range(timeout):
                try:
                    # 檢查Flask服務
                    if not flask_ready:
                        flask_response = requests.get(f"http://localhost:{self.flask_port}/health", timeout=2)
                        if flask_response.status_code == 200:
                            flask_ready = True
                            print(f"✅ Flask服務已啟動 (端口 {self.flask_port})")
                    
                    # 檢查FastAPI服務（可能需要更長時間）
                    if not fastapi_ready and i > 10:  # FastAPI通常需要更長時間啟動
                        try:
                            fastapi_response = requests.get(f"http://localhost:{self.fastapi_port}/docs", timeout=2)
                            if fastapi_response.status_code == 200:
                                fastapi_ready = True
                                print(f"✅ FastAPI服務已啟動 (端口 {self.fastapi_port})")
                        except requests.exceptions.RequestException:
                            pass  # FastAPI可能還沒啟動
                    
                    # 如果至少Flask啟動成功就可以進行基本測試
                    if flask_ready:
                        print(f"✅ 整合服務基本啟動成功")
                        return True
                        
                except requests.exceptions.RequestException:
                    pass
                
                time.sleep(1)
            
            print("❌ 整合服務啟動超時")
            return False
            
        except Exception as e:
            print(f"❌ 整合服務啟動失敗: {e}")
            return False
    
    def stop_integrated_service(self):
        """停止整合服務"""
        if self.service_process:
            try:
                self.service_process.terminate()
                self.service_process.wait(timeout=10)
                print("🛑 整合服務已停止")
            except subprocess.TimeoutExpired:
                self.service_process.kill()
                print("🛑 整合服務已強制停止")
            except Exception as e:
                print(f"⚠️ 停止服務時發生錯誤: {e}")
    
    def test_service_endpoints(self) -> Dict[str, Any]:
        """測試服務端點"""
        print("🔍 測試整合服務端點...")
        
        endpoints_to_test = [
            # Flask端點
            {
                'name': 'Flask健康檢查',
                'url': f'http://localhost:{self.flask_port}/health',
                'method': 'GET',
                'expected_status': [200],
                'service': 'flask'
            },
            {
                'name': 'Flask主頁重定向',
                'url': f'http://localhost:{self.flask_port}/',
                'method': 'GET',
                'expected_status': [302, 308],
                'service': 'flask'
            },
            {
                'name': '郵件模組',
                'url': f'http://localhost:{self.flask_port}/email/',
                'method': 'GET',
                'expected_status': [200, 302, 404],
                'service': 'flask'
            },
            # FastAPI端點（可選）
            {
                'name': 'FastAPI文檔',
                'url': f'http://localhost:{self.fastapi_port}/docs',
                'method': 'GET', 
                'expected_status': [200, 404],
                'service': 'fastapi',
                'optional': True
            }
        ]
        
        results = {}
        flask_success = 0
        fastapi_success = 0
        total_flask = 0
        total_fastapi = 0
        
        for endpoint in endpoints_to_test:
            try:
                start_time = time.time()
                response = requests.request(
                    endpoint['method'],
                    endpoint['url'],
                    timeout=10,
                    allow_redirects=False
                )
                response_time = time.time() - start_time
                
                status_ok = response.status_code in endpoint['expected_status']
                
                results[endpoint['name']] = {
                    'url': endpoint['url'],
                    'method': endpoint['method'],
                    'status_code': response.status_code,
                    'expected_status': endpoint['expected_status'],
                    'success': status_ok,
                    'response_time': response_time,
                    'service': endpoint['service'],
                    'optional': endpoint.get('optional', False)
                }
                
                # 計算成功率
                if endpoint['service'] == 'flask':
                    total_flask += 1
                    if status_ok:
                        flask_success += 1
                elif endpoint['service'] == 'fastapi':
                    total_fastapi += 1
                    if status_ok:
                        fastapi_success += 1
                
                status_icon = "✅" if status_ok else "❌"
                optional_text = " (可選)" if endpoint.get('optional') else ""
                print(f"{status_icon} {endpoint['name']}: {response.status_code}{optional_text}")
                
            except Exception as e:
                results[endpoint['name']] = {
                    'url': endpoint['url'],
                    'success': False,
                    'error': str(e),
                    'service': endpoint['service'],
                    'optional': endpoint.get('optional', False)
                }
                
                if endpoint['service'] == 'flask':
                    total_flask += 1
                elif endpoint['service'] == 'fastapi':
                    total_fastapi += 1
                
                optional_text = " (可選)" if endpoint.get('optional') else ""
                print(f"❌ {endpoint['name']}: 錯誤 - {e}{optional_text}")
        
        # Flask服務必須正常，FastAPI可選
        flask_success_rate = (flask_success / total_flask * 100) if total_flask > 0 else 0
        fastapi_success_rate = (fastapi_success / total_fastapi * 100) if total_fastapi > 0 else 100
        
        return {
            'success': flask_success_rate >= 80,  # Flask至少80%成功
            'flask_success_rate': flask_success_rate,
            'fastapi_success_rate': fastapi_success_rate,
            'flask_endpoints': f"{flask_success}/{total_flask}",
            'fastapi_endpoints': f"{fastapi_success}/{total_fastapi}",
            'details': results
        }
    
    def test_service_integration(self) -> Dict[str, Any]:
        """測試服務間整合"""
        print("🔍 測試Flask與FastAPI整合...")
        
        try:
            # 測試Flask健康檢查端點的詳細信息
            response = requests.get(f"http://localhost:{self.flask_port}/health", timeout=10)
            
            if response.status_code == 200:
                health_data = response.json()
                
                # 檢查健康檢查數據結構
                has_status = 'status' in health_data
                has_modules = 'modules' in health_data
                has_timestamp = 'timestamp' in health_data
                
                # 檢查模組狀態
                modules = health_data.get('modules', {})
                expected_modules = {'email', 'analytics', 'file_management', 'eqc', 'tasks', 'monitoring'}
                present_modules = set(modules.keys())
                
                return {
                    'success': has_status and has_modules and len(present_modules) >= 4,  # 至少4個模組
                    'health_data': health_data,
                    'has_status': has_status,
                    'has_modules': has_modules,
                    'has_timestamp': has_timestamp,
                    'expected_modules': list(expected_modules),
                    'present_modules': list(present_modules),
                    'missing_modules': list(expected_modules - present_modules)
                }
            else:
                return {
                    'success': False,
                    'error': f'健康檢查失敗，狀態碼: {response.status_code}'
                }
                
        except Exception as e:
            return {
                'success': False,
                'error': str(e)
            }
    
    def test_static_resource_serving(self) -> Dict[str, Any]:
        """測試靜態資源服務"""
        print("🔍 測試靜態資源服務...")
        
        static_resources = [
            '/favicon.ico',
            '/static/shared/css/base.css',
            '/static/email/css/inbox.css'
        ]
        
        results = {}
        success_count = 0
        
        for resource in static_resources:
            try:
                response = requests.get(f"http://localhost:{self.flask_port}{resource}", timeout=5)
                
                # 靜態資源200表示存在，404表示不存在但路由正確
                success = response.status_code in [200, 404]
                
                results[resource] = {
                    'status_code': response.status_code,
                    'success': success,
                    'content_type': response.headers.get('Content-Type', ''),
                    'content_length': len(response.content)
                }
                
                if success:
                    success_count += 1
                    if response.status_code == 200:
                        print(f"✅ {resource}: 資源存在")
                    else:
                        print(f"⚠️ {resource}: 路由正確但資源不存在")
                else:
                    print(f"❌ {resource}: 路由錯誤")
                    
            except Exception as e:
                results[resource] = {
                    'success': False,
                    'error': str(e)
                }
                print(f"❌ {resource}: 請求失敗 - {e}")
        
        return {
            'success': success_count >= len(static_resources) * 0.5,  # 至少50%成功
            'total_resources': len(static_resources),
            'successful_resources': success_count,
            'details': results
        }
    
    def run_comprehensive_test(self) -> Dict[str, Any]:
        """運行綜合測試"""
        start_time = time.time()
        
        print("🧪 開始整合服務端到端測試")
        print("="*60)
        
        results = {}
        
        try:
            # 1. 架構驗證測試
            print("\n📋 第一階段: 內建架構驗證測試")
            print("-"*40)
            results['architecture_validation'] = self.test_architecture_validation()
            
            # 2. 部署就緒性檢查
            print("\n🔧 第二階段: 部署就緒性檢查")
            print("-"*40)  
            results['deployment_check'] = self.test_deployment_readiness_check()
            
            # 3. 服務啟動測試
            print("\n🚀 第三階段: 整合服務啟動測試")
            print("-"*40)
            
            startup_success = self.start_integrated_service()
            results['service_startup'] = {'success': startup_success}
            
            if startup_success:
                # 4. 端點測試
                print("\n🔗 第四階段: 服務端點測試")
                print("-"*40)
                results['endpoint_testing'] = self.test_service_endpoints()
                
                # 5. 整合測試
                print("\n🔄 第五階段: 服務整合測試")
                print("-"*40)
                results['integration_testing'] = self.test_service_integration()
                
                # 6. 靜態資源測試
                print("\n📁 第六階段: 靜態資源測試")
                print("-"*40)
                results['static_resources'] = self.test_static_resource_serving()
                
            else:
                # 如果服務啟動失敗，記錄失敗信息
                results['endpoint_testing'] = {'success': False, 'error': '服務啟動失敗'}
                results['integration_testing'] = {'success': False, 'error': '服務啟動失敗'}
                results['static_resources'] = {'success': False, 'error': '服務啟動失敗'}
            
        finally:
            # 確保停止服務
            self.stop_integrated_service()
        
        # 計算總體結果
        total_time = time.time() - start_time
        results['summary'] = self._calculate_summary(results, total_time)
        
        self.test_results = results
        return results
    
    def _calculate_summary(self, results: Dict[str, Any], total_time: float) -> Dict[str, Any]:
        """計算測試摘要"""
        test_categories = [
            'architecture_validation',
            'deployment_check', 
            'service_startup',
            'endpoint_testing',
            'integration_testing',
            'static_resources'
        ]
        
        successful_categories = 0
        total_categories = len(test_categories)
        
        for category in test_categories:
            if results.get(category, {}).get('success', False):
                successful_categories += 1
        
        success_rate = (successful_categories / total_categories * 100) if total_categories > 0 else 0
        overall_success = success_rate >= 70  # 70%以上成功率
        
        return {
            'overall_success': overall_success,
            'success_rate': success_rate,
            'successful_categories': successful_categories,
            'total_categories': total_categories,
            'execution_time': total_time,
            'timestamp': datetime.now().isoformat()
        }
    
    def generate_report(self) -> str:
        """生成測試報告"""
        if not self.test_results:
            return "尚未執行測試"
        
        summary = self.test_results.get('summary', {})
        
        report = f"""
整合服務啟動端到端測試報告
{'='*60}

測試概要:
    整體結果: {'✅ 成功' if summary.get('overall_success') else '❌ 失敗'}
    成功率: {summary.get('success_rate', 0):.1f}%
    成功類別: {summary.get('successful_categories', 0)}/{summary.get('total_categories', 0)}
    執行時間: {summary.get('execution_time', 0):.2f}秒
    測試時間: {summary.get('timestamp', 'N/A')}

詳細結果:
{'='*60}

1. 架構驗證: {'✅ 通過' if self.test_results.get('architecture_validation', {}).get('success') else '❌ 失敗'}
2. 部署檢查: {'✅ 通過' if self.test_results.get('deployment_check', {}).get('success') else '❌ 失敗'}
3. 服務啟動: {'✅ 成功' if self.test_results.get('service_startup', {}).get('success') else '❌ 失敗'}
4. 端點測試: {'✅ 通過' if self.test_results.get('endpoint_testing', {}).get('success') else '❌ 失敗'}
5. 整合測試: {'✅ 通過' if self.test_results.get('integration_testing', {}).get('success') else '❌ 失敗'}
6. 靜態資源: {'✅ 正常' if self.test_results.get('static_resources', {}).get('success') else '❌ 異常'}

關鍵指標:
{'='*60}
"""

        # 添加詳細信息
        if 'endpoint_testing' in self.test_results:
            endpoint_result = self.test_results['endpoint_testing']
            report += f"""
端點測試詳情:
    Flask端點: {endpoint_result.get('flask_endpoints', 'N/A')}
    FastAPI端點: {endpoint_result.get('fastapi_endpoints', 'N/A')}
    Flask成功率: {endpoint_result.get('flask_success_rate', 0):.1f}%
"""

        if 'integration_testing' in self.test_results:
            integration_result = self.test_results['integration_testing']
            if integration_result.get('success'):
                present_modules = integration_result.get('present_modules', [])
                report += f"""
整合測試詳情:
    檢測到的模組: {', '.join(present_modules)}
    模組數量: {len(present_modules)}
"""

        # 如果有失敗，添加錯誤詳情
        if not summary.get('overall_success'):
            report += f"\n失敗詳情:\n{'-'*20}\n"
            for category, result in self.test_results.items():
                if category != 'summary' and isinstance(result, dict):
                    if not result.get('success', True) and 'error' in result:
                        report += f"{category}: {result['error']}\n"

        report += "\n" + "="*60
        return report
    
    def save_report(self, filename: str = None):
        """保存報告"""
        if filename is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S") 
            filename = f"integrated_services_test_{timestamp}"
        
        # 保存JSON報告
        json_file = self.project_root / f"{filename}.json"
        with open(json_file, 'w', encoding='utf-8') as f:
            json.dump(self.test_results, f, ensure_ascii=False, indent=2)
        
        # 保存文本報告
        txt_file = self.project_root / f"{filename}.txt"
        with open(txt_file, 'w', encoding='utf-8') as f:
            f.write(self.generate_report())
        
        print(f"📄 JSON報告已保存: {json_file}")
        print(f"📄 文本報告已保存: {txt_file}")


def main():
    """主函數"""
    print("🧪 整合服務啟動端到端測試")
    print("測試 start_integrated_services.py 使用新的模組化前端架構")
    print("="*60)
    
    runner = IntegratedServicesTestRunner()
    
    try:
        # 執行綜合測試
        results = runner.run_comprehensive_test()
        
        # 顯示結果
        print("\n" + "="*60)
        print("測試完成!")
        print("="*60)
        print(runner.generate_report())
        
        # 保存報告
        runner.save_report()
        
        return 0 if results['summary']['overall_success'] else 1
        
    except KeyboardInterrupt:
        print("\n❌ 測試被用戶中斷")
        runner.stop_integrated_service()
        return 1
    except Exception as e:
        print(f"\n❌ 測試執行失敗: {e}")
        import traceback
        traceback.print_exc()
        runner.stop_integrated_service()
        return 1


if __name__ == '__main__':
    exit_code = main()
    sys.exit(exit_code)