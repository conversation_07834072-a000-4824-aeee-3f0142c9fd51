"""
資料庫架構診斷工具
檢查當前資料庫的實際架構並對比模型定義
"""

import sqlite3
import sys
import os
from pathlib import Path

# 將 src 目錄加入 Python 路徑
project_root = Path(__file__).parent.parent
src_path = project_root / "src"
sys.path.insert(0, str(src_path))

def check_database_exists(db_path):
    """檢查資料庫檔案是否存在"""
    print(f"檢查資料庫檔案: {db_path}")
    if os.path.exists(db_path):
        print(f"✓ 資料庫檔案存在")
        print(f"  檔案大小: {os.path.getsize(db_path)} bytes")
        return True
    else:
        print(f"✗ 資料庫檔案不存在")
        return False

def get_table_schema(cursor, table_name):
    """取得表格架構資訊"""
    try:
        cursor.execute(f"PRAGMA table_info({table_name})")
        columns = cursor.fetchall()
        return columns
    except sqlite3.Error as e:
        print(f"✗ 取得 {table_name} 架構失敗: {e}")
        return None

def get_all_tables(cursor):
    """取得所有表格名稱"""
    try:
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
        tables = cursor.fetchall()
        return [table[0] for table in tables]
    except sqlite3.Error as e:
        print(f"✗ 取得表格列表失敗: {e}")
        return []

def check_column_exists(cursor, table_name, column_name):
    """檢查特定欄位是否存在"""
    schema = get_table_schema(cursor, table_name)
    if schema:
        columns = [col[1] for col in schema]
        return column_name in columns
    return False

def compare_with_model_definition():
    """對比模型定義中的必要欄位"""
    expected_columns = {
        'emails': [
            'id', 'message_id', 'sender', 'sender_display_name', 
            'subject', 'body', 'received_time', 'created_at', 
            'is_read', 'is_processed', 'has_attachments', 'attachment_count',
            'pd', 'lot', 'mo', 'yield_value', 'vendor_code', 
            'parsed_at', 'parse_status', 'parse_error', 'extraction_method',
            'llm_analysis_result', 'llm_analysis_timestamp', 'llm_service_used'
        ],
        'attachments': [
            'id', 'email_id', 'filename', 'content_type', 'size_bytes',
            'file_path', 'checksum', 'is_processed', 'created_at'
        ],
        'senders': [
            'id', 'email_address', 'display_name', 'total_emails',
            'last_email_time', 'first_email_time', 'created_at', 'updated_at'
        ],
        'email_process_status': [
            'id', 'email_id', 'step_name', 'status', 'started_at',
            'completed_at', 'error_message', 'output_files', 
            'progress_percentage', 'created_at', 'updated_at'
        ]
    }
    return expected_columns

def main():
    print("=" * 60)
    print("資料庫架構診斷報告")
    print("=" * 60)
    
    # 資料庫路徑
    db_path = project_root / "data" / "email_inbox.db"
    print(f"專案根目錄: {project_root}")
    print(f"資料庫路徑: {db_path}")
    print()
    
    # 檢查檔案是否存在
    if not check_database_exists(db_path):
        print("\n建議:")
        print("1. 確認 data 目錄存在")
        print("2. 執行資料庫初始化")
        return
    
    print()
    
    try:
        # 連接資料庫
        conn = sqlite3.connect(str(db_path))
        cursor = conn.cursor()
        
        print("資料庫連接成功!")
        print()
        
        # 取得所有表格
        print("現有表格:")
        tables = get_all_tables(cursor)
        for table in tables:
            print(f"  - {table}")
        print()
        
        # 對比模型定義
        expected_columns = compare_with_model_definition()
        
        print("架構對比結果:")
        print("-" * 40)
        
        missing_fields = []
        
        for table_name, expected_cols in expected_columns.items():
            print(f"\n表格: {table_name}")
            
            if table_name not in tables:
                print(f"  ✗ 表格不存在")
                missing_fields.append(f"整個表格 {table_name}")
                continue
            
            # 取得現有架構
            schema = get_table_schema(cursor, table_name)
            if schema:
                existing_cols = [col[1] for col in schema]
                print(f"  現有欄位數: {len(existing_cols)}")
                print(f"  預期欄位數: {len(expected_cols)}")
                
                # 檢查缺失的欄位
                missing_cols = []
                for col in expected_cols:
                    if col not in existing_cols:
                        missing_cols.append(col)
                        missing_fields.append(f"{table_name}.{col}")
                
                if missing_cols:
                    print(f"  ✗ 缺失欄位: {', '.join(missing_cols)}")
                else:
                    print(f"  ✓ 所有預期欄位都存在")
                
                # 顯示詳細架構
                print(f"  詳細架構:")
                for col_info in schema:
                    col_name = col_info[1]
                    col_type = col_info[2]
                    not_null = "NOT NULL" if col_info[3] else ""
                    default_val = f"DEFAULT {col_info[4]}" if col_info[4] else ""
                    pk = "PRIMARY KEY" if col_info[5] else ""
                    
                    status = "✓" if col_name in expected_cols else "?"
                    print(f"    {status} {col_name} {col_type} {not_null} {default_val} {pk}".strip())
        
        print("\n" + "=" * 60)
        print("診斷總結:")
        print("=" * 60)
        
        if missing_fields:
            print(f"✗ 發現 {len(missing_fields)} 個缺失項目:")
            for item in missing_fields:
                print(f"  - {item}")
            print()
            print("需要執行的修復動作:")
            print("1. 備份現有資料庫")
            print("2. 執行架構更新/重建")
            print("3. 恢復現有資料")
        else:
            print("✓ 資料庫架構完整，所有預期欄位都存在")
        
        # 特別檢查問題欄位 'mo'
        print("\n特別檢查問題欄位 'mo':")
        mo_exists = check_column_exists(cursor, 'emails', 'mo')
        if mo_exists:
            print("✓ emails.mo 欄位存在")
        else:
            print("✗ emails.mo 欄位不存在 - 這是造成錯誤的根本原因!")
        
        # 取得一些統計資訊
        try:
            cursor.execute("SELECT COUNT(*) FROM emails")
            email_count = cursor.fetchone()[0]
            print(f"\n統計資訊:")
            print(f"  郵件總數: {email_count}")
            
            if email_count > 0:
                cursor.execute("SELECT COUNT(*) FROM emails WHERE mo IS NOT NULL")
                mo_filled_count = cursor.fetchone()[0] if mo_exists else 0
                print(f"  有 MO 資料的郵件數: {mo_filled_count}")
        except sqlite3.Error as e:
            print(f"  統計資訊取得失敗: {e}")
        
    except sqlite3.Error as e:
        print(f"✗ 資料庫操作失敗: {e}")
        return
    except Exception as e:
        print(f"✗ 未預期的錯誤: {e}")
        return
    finally:
        if 'conn' in locals():
            conn.close()

if __name__ == "__main__":
    main()