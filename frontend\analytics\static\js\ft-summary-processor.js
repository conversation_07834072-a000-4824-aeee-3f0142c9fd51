/**
 * FT Summary Processor Module
 * FT 摘要處理器模組 JavaScript
 */

class FTSummaryProcessor {
    constructor() {
        this.isProcessing = false;
        this.processedData = null;
        this.charts = {};
        this.currentView = 'summary';
        
        this.init();
    }
    
    init() {
        console.log('FT Summary Processor: Initializing...');
        this.bindEvents();
        this.loadExistingSummaries();
    }
    
    bindEvents() {
        // 處理按鈕
        document.addEventListener('click', (e) => {
            if (e.target.matches('.process-summary-btn')) {
                this.handleProcessSummary(e);
            }
        });
        
        // 檢視切換
        document.addEventListener('click', (e) => {
            if (e.target.matches('.view-toggle-btn')) {
                this.handleViewToggle(e);
            }
        });
        
        // 匯出按鈕
        document.addEventListener('click', (e) => {
            if (e.target.matches('.export-summary-btn')) {
                this.handleExportSummary(e);
            }
        });
        
        // 重新處理按鈕
        document.addEventListener('click', (e) => {
            if (e.target.matches('.reprocess-btn')) {
                this.handleReprocess(e);
            }
        });
        
        // 篩選器
        document.addEventListener('change', (e) => {
            if (e.target.matches('.summary-filter')) {
                this.handleFilterChange(e);
            }
        });
        
        // 摘要項目點擊
        document.addEventListener('click', (e) => {
            if (e.target.closest('.summary-item')) {
                this.handleSummaryItemClick(e);
            }
        });
    }
    
    async loadExistingSummaries() {
        try {
            const response = await fetch('/analytics/api/summaries');
            
            if (response.ok) {
                const summaries = await response.json();
                this.displaySummariesList(summaries);
            }
        } catch (error) {
            console.error('Failed to load summaries:', error);
        }
    }
    
    displaySummariesList(summaries) {
        const container = document.querySelector('.summaries-list');
        if (!container) return;
        
        if (summaries.length === 0) {
            container.innerHTML = `
                <div class="no-summaries">
                    <p>尚未有處理過的摘要資料</p>
                    <p class="text-muted">點擊「開始處理」來生成第一個摘要</p>
                </div>
            `;
            return;
        }
        
        container.innerHTML = '';
        summaries.forEach(summary => {
            const item = this.createSummaryItem(summary);
            container.appendChild(item);
        });
    }
    
    createSummaryItem(summary) {
        const item = document.createElement('div');
        item.className = 'summary-item';
        item.dataset.summaryId = summary.id;
        
        const statusClass = this.getStatusClass(summary.status);
        const progressWidth = summary.progress || 0;
        
        item.innerHTML = `
            <div class="summary-header">
                <h5 class="summary-title">${summary.title}</h5>
                <span class="summary-status status-${statusClass}">${this.getStatusText(summary.status)}</span>
            </div>
            <div class="summary-info">
                <span class="summary-date">${this.formatDateTime(summary.created_at)}</span>
                <span class="summary-type">${summary.type}</span>
                <span class="summary-size">${summary.total_items || 0} 項目</span>
            </div>
            <div class="summary-progress">
                <div class="progress">
                    <div class="progress-bar" style="width: ${progressWidth}%"></div>
                </div>
                <span class="progress-text">${progressWidth}%</span>
            </div>
            <div class="summary-actions">
                <button class="btn btn-sm btn-primary view-summary-btn" data-summary-id="${summary.id}">檢視</button>
                <button class="btn btn-sm btn-secondary export-summary-btn" data-summary-id="${summary.id}">匯出</button>
                ${summary.status === 'failed' ? `<button class="btn btn-sm btn-warning reprocess-btn" data-summary-id="${summary.id}">重新處理</button>` : ''}
            </div>
        `;
        
        return item;
    }
    
    async handleProcessSummary(e) {
        if (this.isProcessing) {
            this.showWarning('目前正在處理中，請稍候...');
            return;
        }
        
        const processBtn = e.target;
        const originalText = processBtn.textContent;
        
        this.isProcessing = true;
        processBtn.textContent = '處理中...';
        processBtn.disabled = true;
        
        try {
            // 獲取處理選項
            const options = this.getProcessingOptions();
            
            const response = await fetch('/analytics/api/summaries/process', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(options)
            });
            
            if (response.ok) {
                const result = await response.json();
                
                if (result.async) {
                    // 異步處理，開始監控進度
                    this.monitorProgress(result.task_id);
                    this.showSuccess('摘要處理已開始，正在背景執行...');
                } else {
                    // 同步處理完成
                    this.processedData = result;
                    this.displayProcessingResults();
                    this.showSuccess('摘要處理完成！');
                }
                
                // 重新載入摘要列表
                this.loadExistingSummaries();
                
            } else {
                const error = await response.json();
                throw new Error(error.message || '處理失敗');
            }
            
        } catch (error) {
            console.error('Process summary error:', error);
            this.showError('處理失敗：' + error.message);
        } finally {
            this.isProcessing = false;
            processBtn.textContent = originalText;
            processBtn.disabled = false;
        }
    }
    
    getProcessingOptions() {
        return {
            'source_type': document.querySelector('#sourceType')?.value || 'email',
            'date_range': document.querySelector('#dateRange')?.value || '7d',
            'include_attachments': document.querySelector('#includeAttachments')?.checked || false,
            'group_by_sender': document.querySelector('#groupBySender')?.checked || true,
            'extract_keywords': document.querySelector('#extractKeywords')?.checked || true,
            'generate_insights': document.querySelector('#generateInsights')?.checked || true,
            'summary_length': document.querySelector('#summaryLength')?.value || 'medium'
        };
    }
    
    async monitorProgress(taskId) {
        const progressInterval = setInterval(async () => {
            try {
                const response = await fetch(`/analytics/api/summaries/progress/${taskId}`);
                
                if (response.ok) {
                    const progress = await response.json();
                    this.updateProgress(progress);
                    
                    if (progress.status === 'completed' || progress.status === 'failed') {
                        clearInterval(progressInterval);
                        
                        if (progress.status === 'completed') {
                            this.showSuccess('摘要處理完成！');
                            this.loadExistingSummaries();
                        } else {
                            this.showError('處理失敗：' + (progress.error || '未知錯誤'));
                        }
                    }
                }
            } catch (error) {
                console.error('Progress monitoring error:', error);
                clearInterval(progressInterval);
            }
        }, 2000); // 每2秒檢查一次
    }
    
    updateProgress(progress) {
        const progressBars = document.querySelectorAll('.progress-bar');
        const progressTexts = document.querySelectorAll('.progress-text');
        
        progressBars.forEach(bar => {
            bar.style.width = `${progress.percentage}%`;
        });
        
        progressTexts.forEach(text => {
            text.textContent = `${progress.percentage}%`;
        });
        
        // 更新狀態消息
        if (progress.current_step) {
            const statusElement = document.querySelector('.processing-status');
            if (statusElement) {
                statusElement.textContent = progress.current_step;
            }
        }
    }
    
    displayProcessingResults() {
        if (!this.processedData) return;
        
        // 顯示結果區域
        const resultsContainer = document.querySelector('.processing-results');
        if (resultsContainer) {
            resultsContainer.style.display = 'block';
        }
        
        // 更新摘要統計
        this.updateSummaryStatistics();
        
        // 顯示關鍵詞雲
        this.displayKeywordCloud();
        
        // 生成圖表
        this.generateSummaryCharts();
        
        // 顯示摘要內容
        this.displaySummaryContent();
    }
    
    updateSummaryStatistics() {
        const stats = this.processedData.statistics;
        if (!stats) return;
        
        this.updateElement('.total-processed', stats.total_processed);
        this.updateElement('.key-insights', stats.key_insights);
        this.updateElement('.top-keywords', stats.top_keywords?.join(', ') || '');
        this.updateElement('.processing-time', this.formatDuration(stats.processing_time));
    }
    
    displayKeywordCloud() {
        const keywords = this.processedData.keywords;
        if (!keywords) return;
        
        const container = document.querySelector('.keyword-cloud');
        if (!container) return;
        
        container.innerHTML = '';
        
        keywords.forEach(keyword => {
            const tag = document.createElement('span');
            tag.className = 'keyword-tag';
            tag.textContent = keyword.word;
            tag.style.fontSize = `${Math.min(keyword.frequency / 10 + 12, 24)}px`;
            tag.title = `出現 ${keyword.frequency} 次`;
            
            container.appendChild(tag);
        });
    }
    
    generateSummaryCharts() {
        const chartData = this.processedData.charts;
        if (!chartData) return;
        
        // 時間趨勢圖
        if (chartData.timeline) {
            this.createTimelineChart(chartData.timeline);
        }
        
        // 分類分佈圖
        if (chartData.categories) {
            this.createCategoriesChart(chartData.categories);
        }
        
        // 重要性評分圖
        if (chartData.importance) {
            this.createImportanceChart(chartData.importance);
        }
    }
    
    createTimelineChart(data) {
        const ctx = document.getElementById('timelineChart');
        if (!ctx) return;
        
        this.charts.timeline = new Chart(ctx, {
            type: 'line',
            data: {
                labels: data.labels,
                datasets: [{
                    label: '項目數量',
                    data: data.values,
                    borderColor: '#007bff',
                    backgroundColor: 'rgba(0, 123, 255, 0.1)',
                    tension: 0.4,
                    fill: true
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        beginAtZero: true
                    }
                }
            }
        });
    }
    
    createCategoriesChart(data) {
        const ctx = document.getElementById('categoriesChart');
        if (!ctx) return;
        
        this.charts.categories = new Chart(ctx, {
            type: 'doughnut',
            data: {
                labels: data.labels,
                datasets: [{
                    data: data.values,
                    backgroundColor: [
                        '#007bff', '#28a745', '#ffc107', 
                        '#dc3545', '#17a2b8', '#6c757d'
                    ]
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom'
                    }
                }
            }
        });
    }
    
    displaySummaryContent() {
        const summaryContent = this.processedData.summary;
        if (!summaryContent) return;
        
        const container = document.querySelector('.summary-content');
        if (!container) return;
        
        container.innerHTML = `
            <div class="summary-sections">
                ${summaryContent.sections.map(section => `
                    <div class="summary-section">
                        <h4>${section.title}</h4>
                        <p>${section.content}</p>
                        ${section.items ? `
                            <ul>
                                ${section.items.map(item => `<li>${item}</li>`).join('')}
                            </ul>
                        ` : ''}
                    </div>
                `).join('')}
            </div>
        `;
    }
    
    handleViewToggle(e) {
        const viewType = e.target.dataset.view;
        this.currentView = viewType;
        
        // 更新按鈕狀態
        document.querySelectorAll('.view-toggle-btn').forEach(btn => {
            btn.classList.remove('active');
        });
        e.target.classList.add('active');
        
        // 切換檢視
        this.switchView(viewType);
    }
    
    switchView(viewType) {
        const containers = document.querySelectorAll('.view-container');
        containers.forEach(container => {
            container.style.display = 'none';
        });
        
        const targetContainer = document.querySelector(`.${viewType}-view`);
        if (targetContainer) {
            targetContainer.style.display = 'block';
        }
    }
    
    handleSummaryItemClick(e) {
        const summaryId = e.target.closest('.summary-item').dataset.summaryId;
        this.loadSummaryDetail(summaryId);
    }
    
    async loadSummaryDetail(summaryId) {
        try {
            const response = await fetch(`/analytics/api/summaries/${summaryId}`);
            
            if (response.ok) {
                const summary = await response.json();
                this.displaySummaryDetail(summary);
            }
        } catch (error) {
            console.error('Failed to load summary detail:', error);
            this.showError('載入摘要詳情失敗');
        }
    }
    
    displaySummaryDetail(summary) {
        // 在這裡顯示摘要詳情
        this.processedData = summary;
        this.displayProcessingResults();
        
        // 切換到詳情檢視
        this.switchView('detail');
    }
    
    handleExportSummary(e) {
        const summaryId = e.target.dataset.summaryId;
        const format = e.target.dataset.format || 'pdf';
        
        const exportUrl = `/analytics/api/summaries/${summaryId}/export?format=${format}`;
        window.open(exportUrl, '_blank');
    }
    
    getStatusClass(status) {
        const statusMap = {
            'pending': 'pending',
            'processing': 'processing',
            'completed': 'success',
            'failed': 'danger'
        };
        
        return statusMap[status] || 'secondary';
    }
    
    getStatusText(status) {
        const statusMap = {
            'pending': '待處理',
            'processing': '處理中',
            'completed': '已完成',
            'failed': '失敗'
        };
        
        return statusMap[status] || status;
    }
    
    formatDateTime(dateString) {
        const date = new Date(dateString);
        return date.toLocaleDateString('zh-TW') + ' ' + date.toLocaleTimeString('zh-TW');
    }
    
    formatDuration(seconds) {
        if (seconds < 60) {
            return `${seconds}秒`;
        } else if (seconds < 3600) {
            return `${Math.floor(seconds / 60)}分${seconds % 60}秒`;
        } else {
            return `${Math.floor(seconds / 3600)}小時${Math.floor((seconds % 3600) / 60)}分`;
        }
    }
    
    updateElement(selector, value) {
        const element = document.querySelector(selector);
        if (element) {
            element.textContent = value;
        }
    }
    
    showSuccess(message) {
        this.showAlert(message, 'success');
    }
    
    showError(message) {
        this.showAlert(message, 'danger');
    }
    
    showWarning(message) {
        this.showAlert(message, 'warning');
    }
    
    showAlert(message, type) {
        const alert = document.createElement('div');
        alert.className = `alert alert-${type} alert-dismissible`;
        alert.innerHTML = `
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;
        
        const container = document.querySelector('.ft-summary-container');
        if (container) {
            container.insertBefore(alert, container.firstChild);
        }
        
        // 自動移除警告
        setTimeout(() => {
            if (alert.parentNode) {
                alert.remove();
            }
        }, 5000);
    }
}

// 頁面載入完成後初始化
document.addEventListener('DOMContentLoaded', function() {
    if (document.querySelector('.ft-summary-container')) {
        new FTSummaryProcessor();
    }
});