/* Tasks Module CSS - 任務模組樣式 */

/* 任務容器樣式 */
.tasks-container {
    padding: 20px;
    background: #f8f9fa;
    min-height: 100vh;
}

.tasks-header {
    background: white;
    padding: 20px;
    border-radius: 8px;
    margin-bottom: 20px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.tasks-title {
    font-size: 24px;
    font-weight: 600;
    color: #333;
    margin: 0;
}

.task-actions {
    display: flex;
    gap: 10px;
}

/* 任務儀表板樣式 */
.task-dashboard {
    display: grid;
    grid-template-columns: 1fr 300px;
    gap: 20px;
    margin-bottom: 20px;
}

.task-main-content {
    background: white;
    border-radius: 8px;
    padding: 20px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.task-sidebar {
    background: white;
    border-radius: 8px;
    padding: 20px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    height: fit-content;
}

/* 任務卡片樣式 */
.task-card {
    background: white;
    border-radius: 8px;
    padding: 15px;
    margin-bottom: 15px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    border-left: 4px solid #dee2e6;
    transition: all 0.2s ease;
}

.task-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.task-card.priority-high {
    border-left-color: #dc3545;
}

.task-card.priority-medium {
    border-left-color: #ffc107;
}

.task-card.priority-low {
    border-left-color: #28a745;
}

.task-card.status-running {
    border-left-color: #007bff;
}

.task-card.status-completed {
    border-left-color: #28a745;
    opacity: 0.8;
}

.task-card.status-failed {
    border-left-color: #dc3545;
}

/* 任務標題和內容 */
.task-title {
    font-size: 16px;
    font-weight: 600;
    color: #333;
    margin-bottom: 8px;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.task-description {
    font-size: 14px;
    color: #666;
    margin-bottom: 12px;
    line-height: 1.4;
}

.task-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 12px;
    color: #999;
}

.task-status {
    display: inline-block;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 11px;
    font-weight: 500;
    text-transform: uppercase;
}

.task-status.pending {
    background-color: #e9ecef;
    color: #6c757d;
}

.task-status.running {
    background-color: #cce5ff;
    color: #0066cc;
}

.task-status.completed {
    background-color: #d4edda;
    color: #155724;
}

.task-status.failed {
    background-color: #f8d7da;
    color: #721c24;
}

/* 任務隊列樣式 */
.task-queue {
    background: white;
    border-radius: 8px;
    padding: 20px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.queue-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 1px solid #e9ecef;
}

.queue-title {
    font-size: 18px;
    font-weight: 600;
    color: #333;
}

.queue-count {
    background-color: #007bff;
    color: white;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 500;
}

.queue-list {
    max-height: 400px;
    overflow-y: auto;
}

/* 並發任務管理器樣式 */
.concurrent-task-manager {
    background: white;
    border-radius: 8px;
    padding: 20px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    margin-bottom: 20px;
}

.concurrency-controls {
    display: flex;
    gap: 15px;
    align-items: center;
    margin-bottom: 20px;
    padding: 15px;
    background: #f8f9fa;
    border-radius: 8px;
}

.concurrency-control {
    display: flex;
    align-items: center;
    gap: 8px;
}

.concurrency-label {
    font-size: 14px;
    font-weight: 500;
    color: #333;
}

.concurrency-slider {
    width: 100px;
}

.worker-status {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: 15px;
    margin-bottom: 20px;
}

.worker-card {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 15px;
    text-align: center;
}

.worker-id {
    font-size: 14px;
    font-weight: 600;
    color: #333;
    margin-bottom: 8px;
}

.worker-state {
    font-size: 12px;
    padding: 4px 8px;
    border-radius: 12px;
    text-transform: uppercase;
    font-weight: 500;
}

.worker-state.idle {
    background-color: #d4edda;
    color: #155724;
}

.worker-state.busy {
    background-color: #fff3cd;
    color: #856404;
}

.worker-state.error {
    background-color: #f8d7da;
    color: #721c24;
}

/* 任務調度器樣式 */
.task-scheduler {
    background: white;
    border-radius: 8px;
    padding: 20px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.schedule-form {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 15px;
    margin-bottom: 20px;
    padding: 20px;
    background: #f8f9fa;
    border-radius: 8px;
}

.schedule-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px;
    background: white;
    border-radius: 6px;
    margin-bottom: 10px;
    border-left: 3px solid #007bff;
}

.schedule-info {
    flex: 1;
}

.schedule-task {
    font-size: 14px;
    font-weight: 500;
    color: #333;
}

.schedule-time {
    font-size: 12px;
    color: #666;
}

.schedule-actions {
    display: flex;
    gap: 5px;
}

.schedule-btn {
    padding: 4px 8px;
    border: none;
    border-radius: 4px;
    font-size: 12px;
    cursor: pointer;
    transition: background-color 0.2s;
}

.schedule-btn.edit {
    background-color: #17a2b8;
    color: white;
}

.schedule-btn.delete {
    background-color: #dc3545;
    color: white;
}

/* 進度條樣式 */
.task-progress {
    width: 100%;
    height: 6px;
    background-color: #e9ecef;
    border-radius: 3px;
    overflow: hidden;
    margin-top: 8px;
}

.progress-bar {
    height: 100%;
    background-color: #007bff;
    border-radius: 3px;
    transition: width 0.3s ease;
}

.progress-bar.success {
    background-color: #28a745;
}

.progress-bar.error {
    background-color: #dc3545;
}

/* 響應式設計 */
@media (max-width: 768px) {
    .task-dashboard {
        grid-template-columns: 1fr;
    }
    
    .tasks-header {
        flex-direction: column;
        gap: 15px;
    }
    
    .task-actions {
        width: 100%;
        justify-content: center;
    }
    
    .concurrency-controls {
        flex-direction: column;
        gap: 10px;
    }
    
    .schedule-form {
        grid-template-columns: 1fr;
    }
    
    .worker-status {
        grid-template-columns: 1fr;
    }
    
    .tasks-container {
        padding: 10px;
    }
}