# Windows UTF-8 編碼修復指南

## 問題描述

在 Windows 環境中，Python 腳本可能遇到以下編碼問題：

- **UnicodeEncodeError**: 'cp950' codec can't encode character
- 中文字符無法正常顯示或輸出
- 文件讀寫操作中文亂碼
- 終端輸出亂碼

## 修復方案

### 1. 腳本內修復（推薦）

在 Python 腳本開頭添加以下代碼：

```python
import os
import sys

# Windows UTF-8 編碼修復
if sys.platform.startswith('win'):
    import io
    sys.stdout = io.TextIOWrapper(sys.stdout.buffer, encoding='utf-8')
    sys.stderr = io.TextIOWrapper(sys.stderr.buffer, encoding='utf-8')
    os.environ['PYTHONIOENCODING'] = 'utf-8'
    os.environ['PYTHONUTF8'] = '1'
```

### 2. 文件讀寫優化

使用安全的文件讀取方式：

```python
# 舊方式（可能出錯）
content = file_path.read_text(encoding='utf-8')

# 新方式（安全）
with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
    content = f.read()
```

### 3. 環境變數設定

在系統環境變數中設定：

```bash
PYTHONIOENCODING=utf-8
PYTHONUTF8=1
LANG=zh_TW.UTF-8
```

## 已修復的腳本

以下腳本已經應用了編碼修復：

### 驗證腳本
- ✅ `verify_migration.py` - 靜態資源遷移驗證
- ✅ `verify_migration_en.py` - 英文版驗證腳本
- ✅ `scripts/verify_environment.py` - 環境驗證
- ✅ `scripts/verify_database_connections.py` - 數據庫驗證

### 測試腳本
- ✅ `test_encoding_fix.py` - 編碼修復測試

## 測試驗證

運行測試腳本確認修復效果：

```bash
python test_encoding_fix.py
```

預期輸出：
```
🧪 Windows UTF-8 編碼修復驗證
============================================================
作業系統: win32
預設編碼: utf-8
stdout 編碼: utf-8
stderr 編碼: utf-8

🚀 編碼修復測試
==================================================
 1. 測試中文輸出
 2. 半導體郵件處理系統
 ...
✅ 所有編碼測試完成
✅ Windows UTF-8 編碼修復有效
```

## 修復前後對比

### 修復前
```
UnicodeEncodeError: 'cp950' codec can't encode character '\u5f00' in position 0: illegal multibyte sequence
```

### 修復後
```
🚀 開始數據庫連接與完整性驗證...
============================================================
📁 檢查數據庫文件存在性...
  ✅ email_inbox: data/email_inbox.db (126,976 bytes)
```

## 最佳實踐

### 1. 新腳本開發
- 在所有新腳本中預先加入編碼修復代碼
- 使用 `with open()` 進行文件操作
- 添加 `errors='ignore'` 參數處理潛在編碼問題

### 2. 錯誤處理
```python
try:
    with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
        content = f.read()
except UnicodeDecodeError as e:
    print(f"編碼錯誤: {e}")
    # 備用處理方案
```

### 3. 跨平台兼容
```python
# 只在 Windows 平台應用修復
if sys.platform.startswith('win'):
    # Windows 特定修復
    pass
```

## 常見問題

### Q: 為什麼只在 Windows 需要修復？
A: Windows 預設使用 CP950 (Big5) 編碼，而 Linux/macOS 通常預設 UTF-8。

### Q: 修復後是否影響性能？
A: 影響極小，僅在腳本啟動時執行一次。

### Q: 是否需要修改所有現有腳本？
A: 建議修改所有涉及中文輸出的腳本，特別是用戶面向的工具腳本。

### Q: 修復後是否影響其他程序？
A: 不會，僅影響當前 Python 進程。

## 總結

通過應用本修復方案：
- ✅ 解決了 Windows 中文編碼問題
- ✅ 確保驗證腳本正常運行
- ✅ 提升了工具的跨平台兼容性
- ✅ 改善了用戶體驗

這個修復確保了所有驗證工具在 Windows 環境中的正常運行，讓開發者能夠順利使用項目提供的驗證和測試工具。