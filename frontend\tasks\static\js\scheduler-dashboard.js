/**
 * Scheduler Dashboard Module
 * 任務調度器儀表板模組 JavaScript
 */

class SchedulerDashboard {
    constructor() {
        this.scheduledTasks = [];
        this.runningTasks = [];
        this.taskHistory = [];
        this.refreshInterval = 10000; // 10秒刷新一次
        this.intervalId = null;
        
        this.init();
    }
    
    init() {
        console.log('Scheduler Dashboard: Initializing...');
        this.bindEvents();
        this.loadScheduledTasks();
        this.loadTaskHistory();
        this.startAutoRefresh();
        this.setupFormValidation();
    }
    
    bindEvents() {
        // 添加新任務
        document.addEventListener('click', (e) => {
            if (e.target.matches('.add-task-btn')) {
                this.handleAddTask();
            }
        });
        
        // 編輯任務
        document.addEventListener('click', (e) => {
            if (e.target.matches('.edit-task-btn')) {
                this.handleEditTask(e);
            }
        });
        
        // 刪除任務
        document.addEventListener('click', (e) => {
            if (e.target.matches('.delete-task-btn')) {
                this.handleDeleteTask(e);
            }
        });
        
        // 啟用/停用任務
        document.addEventListener('click', (e) => {
            if (e.target.matches('.toggle-task-btn')) {
                this.handleToggleTask(e);
            }
        });
        
        // 立即執行任務
        document.addEventListener('click', (e) => {
            if (e.target.matches('.run-now-btn')) {
                this.handleRunNow(e);
            }
        });
        
        // 任務表單提交
        document.addEventListener('submit', (e) => {
            if (e.target.matches('#taskScheduleForm')) {
                e.preventDefault();
                this.handleFormSubmit(e);
            }
        });
        
        // 時間模式切換
        document.addEventListener('change', (e) => {
            if (e.target.matches('#scheduleType')) {
                this.handleScheduleTypeChange(e);
            }
        });
        
        // 篩選器變更
        document.addEventListener('change', (e) => {
            if (e.target.matches('.task-filter')) {
                this.handleFilterChange(e);
            }
        });
        
        // 手動刷新
        document.addEventListener('click', (e) => {
            if (e.target.matches('.refresh-btn')) {
                this.handleRefresh();
            }
        });
    }
    
    async loadScheduledTasks() {
        try {
            const response = await fetch('/tasks/api/scheduler/tasks');
            
            if (response.ok) {
                this.scheduledTasks = await response.json();
                this.displayScheduledTasks();
                this.updateStatistics();
            }
        } catch (error) {
            console.error('Failed to load scheduled tasks:', error);
            this.showError('載入排程任務失敗');
        }
    }
    
    async loadTaskHistory() {
        try {
            const response = await fetch('/tasks/api/scheduler/history?limit=50');
            
            if (response.ok) {
                this.taskHistory = await response.json();
                this.displayTaskHistory();
            }
        } catch (error) {
            console.error('Failed to load task history:', error);
        }
    }
    
    displayScheduledTasks() {
        const container = document.querySelector('.scheduled-tasks-list');
        if (!container) return;
        
        container.innerHTML = '';
        
        if (this.scheduledTasks.length === 0) {
            container.innerHTML = `
                <div class="no-tasks">
                    <p>目前沒有排程任務</p>
                    <button class="btn btn-primary add-task-btn">添加第一個任務</button>
                </div>
            `;
            return;
        }
        
        this.scheduledTasks.forEach(task => {
            const taskElement = this.createTaskElement(task);
            container.appendChild(taskElement);
        });
    }
    
    createTaskElement(task) {
        const element = document.createElement('div');
        element.className = `task-item ${task.enabled ? 'enabled' : 'disabled'}`;
        element.dataset.taskId = task.id;
        
        const statusClass = this.getTaskStatusClass(task.status);
        const nextRun = task.next_run ? this.formatDateTime(task.next_run) : '未安排';
        
        element.innerHTML = `
            <div class="task-header">
                <div class="task-info">
                    <h5 class="task-name">${task.name}</h5>
                    <span class="task-type">${this.getTaskTypeText(task.type)}</span>
                    <span class="task-status status-${statusClass}">${this.getStatusText(task.status)}</span>
                </div>
                <div class="task-controls">
                    <button class="btn btn-sm btn-success run-now-btn" data-task-id="${task.id}" title="立即執行">
                        <i class="fas fa-play"></i>
                    </button>
                    <button class="btn btn-sm btn-primary edit-task-btn" data-task-id="${task.id}" title="編輯">
                        <i class="fas fa-edit"></i>
                    </button>
                    <button class="btn btn-sm ${task.enabled ? 'btn-warning' : 'btn-info'} toggle-task-btn" data-task-id="${task.id}" title="${task.enabled ? '停用' : '啟用'}">
                        <i class="fas fa-${task.enabled ? 'pause' : 'play'}"></i>
                    </button>
                    <button class="btn btn-sm btn-danger delete-task-btn" data-task-id="${task.id}" title="刪除">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
            </div>
            <div class="task-details">
                <div class="task-schedule">
                    <i class="fas fa-clock"></i>
                    <span>排程：${this.formatSchedule(task.schedule)}</span>
                </div>
                <div class="task-next-run">
                    <i class="fas fa-calendar"></i>
                    <span>下次執行：${nextRun}</span>
                </div>
                <div class="task-last-run">
                    <i class="fas fa-history"></i>
                    <span>最後執行：${task.last_run ? this.formatDateTime(task.last_run) : '從未執行'}</span>
                </div>
            </div>
            <div class="task-description">
                ${task.description || '無描述'}
            </div>
            ${task.error_message ? `
                <div class="task-error">
                    <i class="fas fa-exclamation-triangle"></i>
                    <span>${task.error_message}</span>
                </div>
            ` : ''}
        `;
        
        return element;
    }
    
    displayTaskHistory() {
        const container = document.querySelector('.task-history-list');
        if (!container) return;
        
        container.innerHTML = '';
        
        this.taskHistory.forEach(execution => {
            const historyItem = this.createHistoryItem(execution);
            container.appendChild(historyItem);
        });
    }
    
    createHistoryItem(execution) {
        const item = document.createElement('div');
        item.className = `history-item status-${execution.status}`;
        
        const duration = execution.duration ? `${execution.duration.toFixed(2)}秒` : 'N/A';
        
        item.innerHTML = `
            <div class="history-header">
                <span class="task-name">${execution.task_name}</span>
                <span class="execution-status status-${execution.status}">${this.getStatusText(execution.status)}</span>
                <span class="execution-time">${this.formatDateTime(execution.started_at)}</span>
            </div>
            <div class="history-details">
                <span class="duration">執行時間：${duration}</span>
                ${execution.output ? `<span class="output">輸出：${execution.output}</span>` : ''}
                ${execution.error ? `<span class="error">錯誤：${execution.error}</span>` : ''}
            </div>
        `;
        
        return item;
    }
    
    updateStatistics() {
        const stats = {
            total: this.scheduledTasks.length,
            enabled: this.scheduledTasks.filter(t => t.enabled).length,
            running: this.scheduledTasks.filter(t => t.status === 'running').length,
            failed: this.scheduledTasks.filter(t => t.status === 'failed').length
        };
        
        this.updateElement('.stat-total-tasks', stats.total);
        this.updateElement('.stat-enabled-tasks', stats.enabled);
        this.updateElement('.stat-running-tasks', stats.running);
        this.updateElement('.stat-failed-tasks', stats.failed);
    }
    
    handleAddTask() {
        this.resetForm();
        this.showTaskModal('新增任務');
    }
    
    async handleEditTask(e) {
        const taskId = e.target.closest('button').dataset.taskId;
        const task = this.scheduledTasks.find(t => t.id === taskId);
        
        if (!task) return;
        
        this.populateForm(task);
        this.showTaskModal('編輯任務');
    }
    
    async handleDeleteTask(e) {
        const taskId = e.target.closest('button').dataset.taskId;
        const task = this.scheduledTasks.find(t => t.id === taskId);
        
        if (!task || !confirm(`確定要刪除任務「${task.name}」嗎？`)) return;
        
        try {
            const response = await fetch(`/tasks/api/scheduler/tasks/${taskId}`, {
                method: 'DELETE'
            });
            
            if (response.ok) {
                this.showSuccess('任務已刪除');
                this.loadScheduledTasks();
            } else {
                throw new Error('刪除失敗');
            }
        } catch (error) {
            console.error('Delete task error:', error);
            this.showError('刪除任務失敗：' + error.message);
        }
    }
    
    async handleToggleTask(e) {
        const taskId = e.target.closest('button').dataset.taskId;
        const task = this.scheduledTasks.find(t => t.id === taskId);
        
        if (!task) return;
        
        try {
            const response = await fetch(`/tasks/api/scheduler/tasks/${taskId}/toggle`, {
                method: 'POST'
            });
            
            if (response.ok) {
                const action = task.enabled ? '停用' : '啟用';
                this.showSuccess(`任務已${action}`);
                this.loadScheduledTasks();
            } else {
                throw new Error('操作失敗');
            }
        } catch (error) {
            console.error('Toggle task error:', error);
            this.showError('切換任務狀態失敗：' + error.message);
        }
    }
    
    async handleRunNow(e) {
        const taskId = e.target.closest('button').dataset.taskId;
        const task = this.scheduledTasks.find(t => t.id === taskId);
        
        if (!task) return;
        
        const button = e.target.closest('button');
        const originalHTML = button.innerHTML;
        
        button.innerHTML = '<i class="fas fa-spinner fa-spin"></i>';
        button.disabled = true;
        
        try {
            const response = await fetch(`/tasks/api/scheduler/tasks/${taskId}/run`, {
                method: 'POST'
            });
            
            if (response.ok) {
                this.showSuccess('任務已開始執行');
                this.loadScheduledTasks();
                this.loadTaskHistory();
            } else {
                const error = await response.json();
                throw new Error(error.message || '執行失敗');
            }
        } catch (error) {
            console.error('Run task error:', error);
            this.showError('執行任務失敗：' + error.message);
        } finally {
            button.innerHTML = originalHTML;
            button.disabled = false;
        }
    }
    
    async handleFormSubmit(e) {
        const formData = new FormData(e.target);
        const taskData = Object.fromEntries(formData.entries());
        
        // 處理複選框
        taskData.enabled = formData.has('enabled');
        
        try {
            const url = taskData.task_id ? 
                `/tasks/api/scheduler/tasks/${taskData.task_id}` : 
                '/tasks/api/scheduler/tasks';
            
            const method = taskData.task_id ? 'PUT' : 'POST';
            
            const response = await fetch(url, {
                method: method,
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(taskData)
            });
            
            if (response.ok) {
                this.showSuccess(taskData.task_id ? '任務已更新' : '任務已創建');
                this.hideTaskModal();
                this.loadScheduledTasks();
            } else {
                const error = await response.json();
                throw new Error(error.message || '保存失敗');
            }
        } catch (error) {
            console.error('Save task error:', error);
            this.showError('保存任務失敗：' + error.message);
        }
    }
    
    handleScheduleTypeChange(e) {
        const scheduleType = e.target.value;
        const scheduleFields = document.querySelector('.schedule-fields');
        
        if (!scheduleFields) return;
        
        // 根據排程類型顯示不同的欄位
        scheduleFields.innerHTML = this.getScheduleFieldsHTML(scheduleType);
    }
    
    getScheduleFieldsHTML(type) {
        switch (type) {
            case 'interval':
                return `
                    <div class="form-group">
                        <label for="interval">間隔時間（秒）</label>
                        <input type="number" id="interval" name="interval" class="form-control" min="1" required>
                    </div>
                `;
            
            case 'cron':
                return `
                    <div class="form-group">
                        <label for="cronExpression">Cron 表達式</label>
                        <input type="text" id="cronExpression" name="cron_expression" class="form-control" placeholder="0 0 * * *" required>
                        <small class="form-text text-muted">例如：0 0 * * * (每天午夜執行)</small>
                    </div>
                `;
            
            case 'daily':
                return `
                    <div class="form-group">
                        <label for="dailyTime">執行時間</label>
                        <input type="time" id="dailyTime" name="daily_time" class="form-control" required>
                    </div>
                `;
            
            case 'weekly':
                return `
                    <div class="form-group">
                        <label for="weekday">星期</label>
                        <select id="weekday" name="weekday" class="form-control" required>
                            <option value="0">星期一</option>
                            <option value="1">星期二</option>
                            <option value="2">星期三</option>
                            <option value="3">星期四</option>
                            <option value="4">星期五</option>
                            <option value="5">星期六</option>
                            <option value="6">星期日</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="weeklyTime">執行時間</label>
                        <input type="time" id="weeklyTime" name="weekly_time" class="form-control" required>
                    </div>
                `;
            
            default:
                return '';
        }
    }
    
    setupFormValidation() {
        const form = document.getElementById('taskScheduleForm');
        if (!form) return;
        
        // 任務類型驗證
        const taskTypeSelect = form.querySelector('#taskType');
        if (taskTypeSelect) {
            taskTypeSelect.addEventListener('change', (e) => {
                this.updateTaskFields(e.target.value);
            });
        }
    }
    
    updateTaskFields(taskType) {
        const taskFields = document.querySelector('.task-fields');
        if (!taskFields) return;
        
        // 根據任務類型顯示特定欄位
        const fieldsHTML = this.getTaskFieldsHTML(taskType);
        taskFields.innerHTML = fieldsHTML;
    }
    
    getTaskFieldsHTML(type) {
        switch (type) {
            case 'email_processing':
                return `
                    <div class="form-group">
                        <label for="emailFolder">郵件資料夾</label>
                        <input type="text" id="emailFolder" name="email_folder" class="form-control" placeholder="INBOX">
                    </div>
                `;
            
            case 'data_backup':
                return `
                    <div class="form-group">
                        <label for="backupPath">備份路徑</label>
                        <input type="text" id="backupPath" name="backup_path" class="form-control" placeholder="/path/to/backup">
                    </div>
                `;
            
            case 'system_cleanup':
                return `
                    <div class="form-group">
                        <label for="cleanupDays">清理天數</label>
                        <input type="number" id="cleanupDays" name="cleanup_days" class="form-control" value="30" min="1">
                    </div>
                `;
            
            default:
                return '';
        }
    }
    
    showTaskModal(title) {
        const modal = document.getElementById('taskScheduleModal');
        if (!modal) return;
        
        const modalTitle = modal.querySelector('.modal-title');
        if (modalTitle) {
            modalTitle.textContent = title;
        }
        
        const modalInstance = new bootstrap.Modal(modal);
        modalInstance.show();
    }
    
    hideTaskModal() {
        const modal = document.getElementById('taskScheduleModal');
        if (modal) {
            const modalInstance = bootstrap.Modal.getInstance(modal);
            if (modalInstance) {
                modalInstance.hide();
            }
        }
    }
    
    resetForm() {
        const form = document.getElementById('taskScheduleForm');
        if (form) {
            form.reset();
            delete form.dataset.taskId;
        }
    }
    
    populateForm(task) {
        const form = document.getElementById('taskScheduleForm');
        if (!form) return;
        
        form.dataset.taskId = task.id;
        
        // 填充基本欄位
        form.querySelector('#taskName').value = task.name || '';
        form.querySelector('#taskDescription').value = task.description || '';
        form.querySelector('#taskType').value = task.type || '';
        form.querySelector('#scheduleType').value = task.schedule_type || '';
        form.querySelector('#enabled').checked = task.enabled;
        
        // 根據任務類型更新欄位
        this.updateTaskFields(task.type);
        
        // 根據排程類型更新欄位
        this.handleScheduleTypeChange({ target: { value: task.schedule_type } });
        
        // 填充排程特定欄位
        this.populateScheduleFields(task);
    }
    
    populateScheduleFields(task) {
        // 根據任務的排程資料填充對應欄位
        if (task.schedule_data) {
            Object.keys(task.schedule_data).forEach(key => {
                const field = document.querySelector(`[name="${key}"]`);
                if (field) {
                    field.value = task.schedule_data[key];
                }
            });
        }
    }
    
    startAutoRefresh() {
        this.intervalId = setInterval(() => {
            this.loadScheduledTasks();
            this.loadTaskHistory();
        }, this.refreshInterval);
    }
    
    stopAutoRefresh() {
        if (this.intervalId) {
            clearInterval(this.intervalId);
            this.intervalId = null;
        }
    }
    
    handleRefresh() {
        this.loadScheduledTasks();
        this.loadTaskHistory();
        this.showSuccess('資料已刷新');
    }
    
    handleFilterChange(e) {
        const filterType = e.target.dataset.filter;
        const value = e.target.value;
        
        // 實現篩選邏輯
        this.applyFilter(filterType, value);
    }
    
    applyFilter(filterType, value) {
        const tasks = document.querySelectorAll('.task-item');
        
        tasks.forEach(task => {
            let show = true;
            
            if (filterType === 'status' && value !== 'all') {
                const status = task.querySelector('.task-status').textContent;
                show = status.toLowerCase().includes(value);
            }
            
            if (filterType === 'type' && value !== 'all') {
                const type = task.querySelector('.task-type').textContent;
                show = type.includes(value);
            }
            
            task.style.display = show ? 'block' : 'none';
        });
    }
    
    formatSchedule(schedule) {
        if (!schedule) return '未設定';
        
        // 根據排程類型格式化顯示
        if (schedule.type === 'interval') {
            return `每 ${schedule.interval} 秒`;
        } else if (schedule.type === 'cron') {
            return `Cron: ${schedule.expression}`;
        } else if (schedule.type === 'daily') {
            return `每日 ${schedule.time}`;
        } else if (schedule.type === 'weekly') {
            const days = ['一', '二', '三', '四', '五', '六', '日'];
            return `每週${days[schedule.weekday]} ${schedule.time}`;
        }
        
        return schedule.description || '自訂排程';
    }
    
    formatDateTime(dateString) {
        const date = new Date(dateString);
        return date.toLocaleDateString('zh-TW') + ' ' + date.toLocaleTimeString('zh-TW');
    }
    
    getTaskStatusClass(status) {
        const statusMap = {
            'pending': 'pending',
            'running': 'running',
            'completed': 'success',
            'failed': 'danger',
            'disabled': 'secondary'
        };
        
        return statusMap[status] || 'secondary';
    }
    
    getStatusText(status) {
        const statusMap = {
            'pending': '待執行',
            'running': '執行中',
            'completed': '完成',
            'failed': '失敗',
            'disabled': '已停用'
        };
        
        return statusMap[status] || status;
    }
    
    getTaskTypeText(type) {
        const typeMap = {
            'email_processing': '郵件處理',
            'data_backup': '資料備份',
            'system_cleanup': '系統清理',
            'report_generation': '報告生成',
            'custom': '自訂任務'
        };
        
        return typeMap[type] || type;
    }
    
    updateElement(selector, value) {
        const element = document.querySelector(selector);
        if (element) {
            element.textContent = value;
        }
    }
    
    showSuccess(message) {
        this.showNotification(message, 'success');
    }
    
    showError(message) {
        this.showNotification(message, 'danger');
    }
    
    showNotification(message, type) {
        const alert = document.createElement('div');
        alert.className = `alert alert-${type} alert-dismissible`;
        alert.innerHTML = `
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;
        
        const container = document.querySelector('.scheduler-container');
        if (container) {
            container.insertBefore(alert, container.firstChild);
        }
        
        // 自動移除通知
        setTimeout(() => {
            if (alert.parentNode) {
                alert.remove();
            }
        }, 5000);
    }
    
    destroy() {
        this.stopAutoRefresh();
    }
}

// 頁面載入完成後初始化
document.addEventListener('DOMContentLoaded', function() {
    if (document.querySelector('.scheduler-dashboard-container')) {
        new SchedulerDashboard();
    }
});

// 頁面卸載時清理
window.addEventListener('beforeunload', function() {
    if (window.schedulerDashboard) {
        window.schedulerDashboard.destroy();
    }
});