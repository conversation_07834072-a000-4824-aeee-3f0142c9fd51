#!/usr/bin/env python3
"""
簡化版連結和資源檢查器
快速檢查所有內部連結和靜態資源是否正常運作
"""

import os
import sys
import json
from pathlib import Path
from typing import Dict, List, Set, Tuple, Optional
from dataclasses import dataclass, asdict

# 添加專案根目錄到 Python 路徑
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from frontend.app import create_app


@dataclass
class SimpleCheckResult:
    """簡化檢查結果"""
    url: str
    status_code: int
    error: Optional[str] = None


class SimpleLinkChecker:
    """簡化版連結檢查器"""
    
    def __init__(self):
        self.results = []
        self.static_files_checked = set()
        
    def check_all_routes(self):
        """檢查所有路由"""
        print("🔍 簡化版連結和資源檢查器")
        print("="*50)
        
        # 設定測試環境
        os.environ['FLASK_ENV'] = 'testing'
        
        # 創建測試應用程式
        app = create_app('testing')
        app.config['TESTING'] = True
        app.config['WTF_CSRF_ENABLED'] = False
        
        client = app.test_client()
        
        # 獲取所有路由
        routes = []
        with app.app_context():
            for rule in app.url_map.iter_rules():
                if rule.endpoint != 'static' and 'GET' in rule.methods:
                    if '<' not in rule.rule:  # 跳過帶參數的路由
                        routes.append(rule.rule)
        
        print(f"📋 檢查 {len(routes)} 個路由...")
        
        # 檢查每個路由
        success_count = 0
        error_count = 0
        
        for i, route in enumerate(routes, 1):
            try:
                response = client.get(route, follow_redirects=True)
                status = "✅" if response.status_code == 200 else "❌"
                print(f"  [{i:2d}/{len(routes)}] {status} {route} - HTTP {response.status_code}")
                
                if response.status_code == 200:
                    success_count += 1
                else:
                    error_count += 1
                    
                self.results.append({
                    'route': route,
                    'status_code': response.status_code,
                    'success': response.status_code == 200
                })
                
            except Exception as e:
                print(f"  [{i:2d}/{len(routes)}] ❌ {route} - 錯誤: {str(e)}")
                error_count += 1
                self.results.append({
                    'route': route,
                    'status_code': 0,
                    'success': False,
                    'error': str(e)
                })
        
        # 檢查靜態資源
        self.check_static_resources()
        
        # 生成報告
        self.generate_report(success_count, error_count, len(routes))
        
    def check_static_resources(self):
        """檢查靜態資源檔案是否存在"""
        print("\n📁 檢查靜態資源檔案...")
        
        static_dirs = [
            'frontend/shared/static',
            'frontend/email/static',
            'frontend/analytics/static',
            'frontend/file_management/static',
            'frontend/eqc/static',
            'frontend/tasks/static',
            'frontend/monitoring/static'
        ]
        
        total_files = 0
        missing_files = 0
        
        for static_dir in static_dirs:
            static_path = Path(static_dir)
            if static_path.exists():
                for file_path in static_path.rglob('*'):
                    if file_path.is_file():
                        total_files += 1
                        relative_path = file_path.relative_to('frontend')
                        print(f"  ✅ {relative_path}")
            else:
                print(f"  ⚠️  目錄不存在: {static_dir}")
        
        print(f"\n📊 靜態資源統計: {total_files} 個檔案")
        
    def check_template_files(self):
        """檢查模板檔案是否存在"""
        print("\n📄 檢查模板檔案...")
        
        template_dirs = [
            'frontend/shared/templates',
            'frontend/email/templates',
            'frontend/analytics/templates',
            'frontend/file_management/templates',
            'frontend/eqc/templates',
            'frontend/tasks/templates',
            'frontend/monitoring/templates'
        ]
        
        total_templates = 0
        
        for template_dir in template_dirs:
            template_path = Path(template_dir)
            if template_path.exists():
                for file_path in template_path.rglob('*.html'):
                    total_templates += 1
                    relative_path = file_path.relative_to('frontend')
                    print(f"  ✅ {relative_path}")
            else:
                print(f"  ⚠️  目錄不存在: {template_dir}")
        
        print(f"\n📊 模板檔案統計: {total_templates} 個檔案")
        
    def generate_report(self, success_count, error_count, total_routes):
        """生成檢查報告"""
        print("\n" + "="*60)
        print("📊 檢查結果摘要")
        print("="*60)
        
        success_rate = (success_count / total_routes * 100) if total_routes > 0 else 0
        
        print(f"✅ 成功路由: {success_count}/{total_routes} ({success_rate:.1f}%)")
        print(f"❌ 失敗路由: {error_count}/{total_routes}")
        
        if error_count > 0:
            print(f"\n❌ 失敗的路由:")
            for result in self.results:
                if not result['success']:
                    error_msg = result.get('error', f"HTTP {result['status_code']}")
                    print(f"  • {result['route']} - {error_msg}")
        
        # 檢查模板檔案
        self.check_template_files()
        
        # 保存詳細報告
        report = {
            'summary': {
                'total_routes': total_routes,
                'successful_routes': success_count,
                'failed_routes': error_count,
                'success_rate': success_rate
            },
            'results': self.results
        }
        
        with open('simple_link_check_report.json', 'w', encoding='utf-8') as f:
            json.dump(report, f, ensure_ascii=False, indent=2)
        
        print(f"\n📄 詳細報告已保存到: simple_link_check_report.json")
        
        if error_count == 0:
            print("\n🎉 所有檢查都通過！沒有發現 404 錯誤或遺失的資源。")
            return True
        else:
            print(f"\n⚠️  發現 {error_count} 個問題需要修復。")
            return False


def main():
    """主函數"""
    checker = SimpleLinkChecker()
    success = checker.check_all_routes()
    
    # 返回適當的退出碼
    sys.exit(0 if success else 1)


if __name__ == '__main__':
    main()