#!/usr/bin/env python3
"""
模組化前端架構綜合測試
驗證新的 frontend/ 架構在移除 email_inbox_app.py 後正常運作
"""

import os
import sys
import time
import json
import subprocess
import threading
import requests
from pathlib import Path
from typing import Dict, List, Any, Optional
import pytest
import asyncio
from datetime import datetime

# 添加專案根目錄到 Python 路徑
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))


class ArchitectureValidator:
    """架構驗證器"""
    
    def __init__(self):
        self.project_root = project_root
        self.test_results = {
            'architecture_validation': {},
            'module_loading': {},
            'route_testing': {},
            'integration_testing': {},
            'regression_testing': {},
            'performance_metrics': {}
        }
    
    def validate_directory_structure(self) -> Dict[str, Any]:
        """驗證目錄結構"""
        print("🔍 驗證模組化前端目錄結構...")
        
        required_paths = {
            'frontend_app': 'frontend/app.py',
            'frontend_config': 'frontend/config.py',
            'shared_templates': 'frontend/shared/templates',
            'shared_static': 'frontend/shared/static',
            'email_routes': 'frontend/email/routes/email_routes.py',
            'analytics_routes': 'frontend/analytics/routes/analytics_routes.py',
            'eqc_routes': 'frontend/eqc/routes/eqc_routes.py',
            'tasks_routes': 'frontend/tasks/routes/task_routes.py',
            'monitoring_routes': 'frontend/monitoring/routes/monitoring_routes.py',
            'file_routes': 'frontend/file_management/routes/file_routes.py'
        }
        
        results = {}
        for name, path in required_paths.items():
            full_path = self.project_root / path
            exists = full_path.exists()
            results[name] = {
                'path': str(full_path),
                'exists': exists,
                'type': 'directory' if full_path.is_dir() else 'file' if exists else 'missing'
            }
            
            if exists:
                print(f"✅ {name}: {path}")
            else:
                print(f"❌ {name}: {path} (缺失)")
        
        success = all(r['exists'] for r in results.values())
        
        # 確認舊的 email_inbox_app.py 已被移除
        old_app_path = self.project_root / 'email_inbox_app.py'
        old_app_removed = not old_app_path.exists()
        results['old_app_removed'] = {
            'path': str(old_app_path),
            'removed': old_app_removed,
            'type': 'legacy_check'
        }
        
        if old_app_removed:
            print("✅ 舊的 email_inbox_app.py 已正確移除")
        else:
            print("❌ 舊的 email_inbox_app.py 仍然存在")
            success = False
        
        return {
            'success': success,
            'details': results,
            'total_checks': len(required_paths) + 1,
            'passed_checks': sum(1 for r in results.values() if r.get('exists', r.get('removed', False)))
        }
    
    def test_module_imports(self) -> Dict[str, Any]:
        """測試模組導入"""
        print("🔍 測試所有前端模組導入...")
        
        modules_to_test = [
            'frontend.app',
            'frontend.config',
            'frontend.email.routes.email_routes',
            'frontend.analytics.routes.analytics_routes',
            'frontend.eqc.routes.eqc_routes',
            'frontend.tasks.routes.task_routes',
            'frontend.monitoring.routes.monitoring_routes',
            'frontend.file_management.routes.file_routes',
            'frontend.shared.utils.error_handler'
        ]
        
        results = {}
        failed_imports = []
        
        for module_name in modules_to_test:
            try:
                start_time = time.time()
                __import__(module_name)
                import_time = time.time() - start_time
                
                results[module_name] = {
                    'success': True,
                    'import_time': import_time,
                    'error': None
                }
                print(f"✅ {module_name} ({import_time:.3f}s)")
                
            except ImportError as e:
                failed_imports.append(f"{module_name}: {e}")
                results[module_name] = {
                    'success': False,
                    'import_time': 0,
                    'error': str(e)
                }
                print(f"❌ {module_name}: {e}")
            except Exception as e:
                failed_imports.append(f"{module_name}: {e}")
                results[module_name] = {
                    'success': False,
                    'import_time': 0,
                    'error': str(e)
                }
                print(f"❌ {module_name}: {e}")
        
        success = len(failed_imports) == 0
        total_import_time = sum(r['import_time'] for r in results.values())
        
        return {
            'success': success,
            'failed_imports': failed_imports,
            'total_modules': len(modules_to_test),
            'successful_imports': len([r for r in results.values() if r['success']]),
            'total_import_time': total_import_time,
            'details': results
        }
    
    def test_flask_app_creation(self) -> Dict[str, Any]:
        """測試 Flask 應用創建"""
        print("🔍 測試 Flask 應用創建...")
        
        try:
            start_time = time.time()
            from frontend.app import create_app
            
            # 測試不同配置環境
            environments = ['development', 'testing', 'production']
            results = {}
            
            for env in environments:
                try:
                    app = create_app(env)
                    
                    # 基本檢查
                    has_blueprints = len(app.blueprints) > 0
                    has_config = hasattr(app, 'config')
                    
                    # 測試客戶端
                    with app.test_client() as client:
                        health_response = client.get('/health')
                        health_ok = health_response.status_code == 200
                        
                        # 測試主頁重定向
                        index_response = client.get('/')
                        index_redirects = index_response.status_code in [302, 308]
                    
                    results[env] = {
                        'success': True,
                        'has_blueprints': has_blueprints,
                        'blueprint_count': len(app.blueprints),
                        'has_config': has_config,
                        'health_endpoint_ok': health_ok,
                        'index_redirects': index_redirects,
                        'error': None
                    }
                    
                    print(f"✅ {env} 環境應用創建成功 ({len(app.blueprints)} 個藍圖)")
                    
                except Exception as e:
                    results[env] = {
                        'success': False,
                        'error': str(e)
                    }
                    print(f"❌ {env} 環境應用創建失敗: {e}")
            
            creation_time = time.time() - start_time
            success = all(r['success'] for r in results.values())
            
            return {
                'success': success,
                'creation_time': creation_time,
                'environments_tested': len(environments),
                'successful_environments': len([r for r in results.values() if r['success']]),
                'details': results
            }
            
        except Exception as e:
            return {
                'success': False,
                'error': str(e),
                'creation_time': 0
            }
    
    def test_blueprint_registration(self) -> Dict[str, Any]:
        """測試藍圖註冊"""
        print("🔍 測試藍圖註冊...")
        
        try:
            from frontend.app import create_app
            app = create_app('testing')
            
            expected_blueprints = {
                'shared',
                'email',
                'analytics', 
                'eqc',
                'task',
                'monitoring',
                'file'
            }
            
            registered_blueprints = set(app.blueprints.keys())
            
            results = {}
            for bp_name in expected_blueprints:
                is_registered = bp_name in registered_blueprints
                results[bp_name] = {
                    'registered': is_registered,
                    'url_prefix': getattr(app.blueprints.get(bp_name), 'url_prefix', None) if is_registered else None
                }
                
                if is_registered:
                    print(f"✅ 藍圖 '{bp_name}' 已註冊")
                else:
                    print(f"❌ 藍圖 '{bp_name}' 未註冊")
            
            missing_blueprints = expected_blueprints - registered_blueprints
            unexpected_blueprints = registered_blueprints - expected_blueprints
            
            return {
                'success': len(missing_blueprints) == 0,
                'expected_blueprints': list(expected_blueprints),
                'registered_blueprints': list(registered_blueprints),
                'missing_blueprints': list(missing_blueprints),
                'unexpected_blueprints': list(unexpected_blueprints),
                'details': results
            }
            
        except Exception as e:
            return {
                'success': False,
                'error': str(e)
            }


class FunctionalTester:
    """功能測試器"""
    
    def __init__(self):
        self.project_root = project_root
        self.server_process = None
        self.base_url = "http://localhost:5001"  # 使用測試端口
        
    def start_test_server(self, timeout=30) -> bool:
        """啟動測試服務器"""
        print("🚀 啟動測試服務器...")
        
        try:
            # 使用測試配置啟動服務器
            env = os.environ.copy()
            env['FLASK_ENV'] = 'testing'
            env['PYTHONIOENCODING'] = 'utf-8'
            env['PYTHONUTF8'] = '1'
            
            start_script = f'''
import sys
import os
from pathlib import Path

# 添加專案根目錄到 Python 路徑
sys.path.insert(0, str(Path.cwd()))

try:
    from frontend.app import create_app
    app = create_app('testing')
    print("測試服務器正在啟動...")
    app.run(host='0.0.0.0', port=5001, debug=False, use_reloader=False)
except Exception as e:
    print(f"測試服務器啟動失敗: {{e}}")
    import traceback
    traceback.print_exc()
    sys.exit(1)
'''
            
            self.server_process = subprocess.Popen(
                [sys.executable, '-c', start_script],
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True,
                encoding='utf-8',
                env=env
            )
            
            # 等待服務器啟動
            for i in range(timeout):
                try:
                    response = requests.get(f"{self.base_url}/health", timeout=1)
                    if response.status_code == 200:
                        print("✅ 測試服務器啟動成功")
                        return True
                except requests.exceptions.RequestException:
                    pass
                time.sleep(1)
            
            print("❌ 測試服務器啟動超時")
            return False
            
        except Exception as e:
            print(f"❌ 測試服務器啟動失敗: {e}")
            return False
    
    def stop_test_server(self):
        """停止測試服務器"""
        if self.server_process:
            self.server_process.terminate()
            self.server_process.wait()
            print("🛑 測試服務器已停止")
    
    def test_routes(self) -> Dict[str, Any]:
        """測試路由功能"""
        print("🔍 測試所有模組路由...")
        
        # 定義要測試的路由
        routes_to_test = [
            # 基礎路由
            {'path': '/', 'method': 'GET', 'expected_status': [302, 308], 'name': '主頁重定向'},
            {'path': '/health', 'method': 'GET', 'expected_status': [200], 'name': '健康檢查'},
            {'path': '/favicon.ico', 'method': 'GET', 'expected_status': [200, 404], 'name': 'Favicon'},
            
            # 模組路由（基本檢查）
            {'path': '/email/', 'method': 'GET', 'expected_status': [200, 302], 'name': '郵件模組首頁'},
            {'path': '/analytics/', 'method': 'GET', 'expected_status': [200, 302], 'name': '分析模組首頁'},
            {'path': '/eqc/', 'method': 'GET', 'expected_status': [200, 302], 'name': 'EQC模組首頁'},
            {'path': '/tasks/', 'method': 'GET', 'expected_status': [200, 302], 'name': '任務模組首頁'},
            {'path': '/monitoring/', 'method': 'GET', 'expected_status': [200, 302], 'name': '監控模組首頁'},
            {'path': '/files/', 'method': 'GET', 'expected_status': [200, 302], 'name': '文件管理模組首頁'},
        ]
        
        results = {}
        success_count = 0
        
        for route in routes_to_test:
            try:
                start_time = time.time()
                response = requests.request(
                    route['method'], 
                    f"{self.base_url}{route['path']}", 
                    timeout=10,
                    allow_redirects=False
                )
                response_time = time.time() - start_time
                
                status_ok = response.status_code in route['expected_status']
                
                results[route['name']] = {
                    'path': route['path'],
                    'method': route['method'],
                    'status_code': response.status_code,
                    'expected_status': route['expected_status'],
                    'success': status_ok,
                    'response_time': response_time,
                    'content_length': len(response.content),
                    'error': None
                }
                
                if status_ok:
                    print(f"✅ {route['name']}: {route['path']} ({response.status_code}, {response_time:.3f}s)")
                    success_count += 1
                else:
                    print(f"❌ {route['name']}: {route['path']} (期望 {route['expected_status']}, 實際 {response.status_code})")
                    
            except Exception as e:
                results[route['name']] = {
                    'path': route['path'],
                    'method': route['method'],
                    'success': False,
                    'error': str(e)
                }
                print(f"❌ {route['name']}: {route['path']} - 錯誤: {e}")
        
        return {
            'success': success_count == len(routes_to_test),
            'total_routes': len(routes_to_test),
            'successful_routes': success_count,
            'details': results
        }
    
    def test_static_resources(self) -> Dict[str, Any]:
        """測試靜態資源載入"""
        print("🔍 測試靜態資源載入...")
        
        # 檢查靜態資源路由
        static_tests = [
            {'path': '/static/shared/css/base.css', 'name': '共用基礎CSS'},
            {'path': '/static/shared/js/main.js', 'name': '共用主要JS'},
            {'path': '/static/email/css/inbox.css', 'name': '郵件模組CSS'}, 
            {'path': '/static/analytics/css/analytics.css', 'name': '分析模組CSS'},
            {'path': '/static/eqc/css/eqc.css', 'name': 'EQC模組CSS'},
        ]
        
        results = {}
        success_count = 0
        
        for test in static_tests:
            try:
                response = requests.get(f"{self.base_url}{test['path']}", timeout=5)
                
                # 靜態資源可能不存在，200或404都是正常的
                success = response.status_code in [200, 404]
                
                results[test['name']] = {
                    'path': test['path'],
                    'status_code': response.status_code,
                    'success': success,
                    'content_type': response.headers.get('Content-Type', ''),
                    'content_length': len(response.content)
                }
                
                if response.status_code == 200:
                    print(f"✅ {test['name']}: 資源存在")
                    success_count += 1
                elif response.status_code == 404:
                    print(f"⚠️ {test['name']}: 資源不存在（正常，可能尚未創建）")
                    success_count += 1
                else:
                    print(f"❌ {test['name']}: 異常狀態碼 {response.status_code}")
                    
            except Exception as e:
                results[test['name']] = {
                    'path': test['path'],
                    'success': False,
                    'error': str(e)
                }
                print(f"❌ {test['name']}: 錯誤 - {e}")
        
        return {
            'success': success_count >= len(static_tests) * 0.5,  # 至少50%成功（考慮到資源可能不存在）
            'total_tests': len(static_tests),
            'successful_tests': success_count,
            'details': results
        }


class IntegrationTester:
    """整合測試器"""
    
    def __init__(self):
        self.project_root = project_root
        self.integrated_process = None
        
    def test_integrated_service_startup(self) -> Dict[str, Any]:
        """測試整合服務啟動"""
        print("🔍 測試整合服務啟動（包含FastAPI）...")
        
        try:
            # 使用更短的超時時間進行快速測試
            env = os.environ.copy()
            env['PYTHONIOENCODING'] = 'utf-8'
            env['PYTHONUTF8'] = '1'
            
            # 啟動整合服務（短時間測試）
            start_script = f'''
import sys
import os
import time
import threading
from pathlib import Path

# 添加專案根目錄到Python路徑
sys.path.insert(0, str(Path.cwd()))

def test_startup():
    try:
        # 測試Flask應用
        from frontend.app import create_app
        app = create_app('testing')
        print("Flask應用創建成功")
        
        # 測試FastAPI導入（不實際啟動）
        try:
            from src.presentation.api.ft_eqc_api import app as fastapi_app
            print("FastAPI應用導入成功")
        except ImportError as e:
            print(f"FastAPI應用導入失敗（可能正常）: {{e}}")
        
        # 簡單的健康檢查
        with app.test_client() as client:
            response = client.get('/health')
            if response.status_code == 200:
                print("健康檢查端點正常")
            else:
                print(f"健康檢查端點異常: {{response.status_code}}")
        
        print("整合測試完成")
        return True
        
    except Exception as e:
        print(f"整合測試失敗: {{e}}")
        import traceback
        traceback.print_exc()
        return False

# 執行測試
success = test_startup()
sys.exit(0 if success else 1)
'''
            
            result = subprocess.run(
                [sys.executable, '-c', start_script],
                capture_output=True,
                text=True,
                encoding='utf-8',
                timeout=30,
                env=env
            )
            
            success = result.returncode == 0
            output = result.stdout + result.stderr
            
            return {
                'success': success,
                'return_code': result.returncode,
                'output': output,
                'has_flask': 'Flask應用創建成功' in output,
                'has_fastapi_import': 'FastAPI應用導入成功' in output,
                'health_check_ok': '健康檢查端點正常' in output
            }
            
        except subprocess.TimeoutExpired:
            return {
                'success': False,
                'error': '測試超時',
                'timeout': True
            }
        except Exception as e:
            return {
                'success': False,
                'error': str(e)
            }
    
    def test_service_discovery(self) -> Dict[str, Any]:
        """測試服務發現功能"""
        print("🔍 測試服務發現功能...")
        
        try:
            from frontend.app import create_app
            app = create_app('testing')
            
            with app.test_client() as client:
                # 測試健康檢查端點
                health_response = client.get('/health')
                
                if health_response.status_code == 200:
                    health_data = health_response.json()
                    
                    expected_modules = {'email', 'analytics', 'file_management', 'eqc', 'tasks', 'monitoring'}
                    reported_modules = set(health_data.get('modules', {}).keys())
                    
                    return {
                        'success': True,
                        'health_status': health_data.get('status'),
                        'expected_modules': list(expected_modules),
                        'reported_modules': list(reported_modules),
                        'all_modules_present': expected_modules.issubset(reported_modules),
                        'response_data': health_data
                    }
                else:
                    return {
                        'success': False,
                        'error': f'健康檢查失敗，狀態碼: {health_response.status_code}'
                    }
                    
        except Exception as e:
            return {
                'success': False,
                'error': str(e)
            }


class RegressionTester:
    """回歸測試器"""
    
    def __init__(self):
        self.project_root = project_root
        
    def test_backward_compatibility(self) -> Dict[str, Any]:
        """測試向後兼容性"""
        print("🔍 測試向後兼容性...")
        
        tests = {
            'old_app_removed': self._test_old_app_removed(),
            'config_compatibility': self._test_config_compatibility(),
            'route_compatibility': self._test_route_compatibility(),
            'api_compatibility': self._test_api_compatibility()
        }
        
        success = all(test['success'] for test in tests.values())
        
        return {
            'success': success,
            'details': tests,
            'total_tests': len(tests),
            'passed_tests': sum(1 for test in tests.values() if test['success'])
        }
    
    def _test_old_app_removed(self) -> Dict[str, Any]:
        """測試舊應用已移除"""
        old_files = [
            'email_inbox_app.py',
            'email_inbox_app.py.backup'
        ]
        
        removed_files = []
        remaining_files = []
        
        for filename in old_files:
            file_path = self.project_root / filename
            if file_path.exists():
                remaining_files.append(filename)
            else:
                removed_files.append(filename)
        
        return {
            'success': len(remaining_files) == 0,
            'removed_files': removed_files,
            'remaining_files': remaining_files
        }
    
    def _test_config_compatibility(self) -> Dict[str, Any]:
        """測試配置兼容性"""
        try:
            from frontend.config import config, Config
            
            # 測試配置類別存在
            config_classes = ['development', 'testing', 'production', 'default']
            available_configs = [name for name in config_classes if name in config]
            
            return {
                'success': len(available_configs) >= 3,  # 至少要有3個配置
                'available_configs': available_configs,
                'expected_configs': config_classes
            }
            
        except Exception as e:
            return {
                'success': False,
                'error': str(e)
            }
    
    def _test_route_compatibility(self) -> Dict[str, Any]:
        """測試路由兼容性"""
        try:
            from frontend.app import create_app
            app = create_app('testing')
            
            # 檢查重要的路由是否存在
            important_routes = [
                '/',  # 主頁
                '/health',  # 健康檢查
                '/email/',  # 郵件模組
            ]
            
            with app.test_client() as client:
                route_results = {}
                for route in important_routes:
                    try:
                        response = client.get(route)
                        route_results[route] = {
                            'accessible': True,
                            'status_code': response.status_code
                        }
                    except Exception as e:
                        route_results[route] = {
                            'accessible': False,
                            'error': str(e)
                        }
                
                accessible_routes = [route for route, result in route_results.items() if result['accessible']]
                
                return {
                    'success': len(accessible_routes) >= len(important_routes) * 0.8,  # 至少80%可訪問
                    'route_results': route_results,
                    'accessible_routes': accessible_routes,
                    'total_routes': len(important_routes)
                }
                
        except Exception as e:
            return {
                'success': False,
                'error': str(e)
            }
    
    def _test_api_compatibility(self) -> Dict[str, Any]:
        """測試API兼容性"""
        try:
            from frontend.app import create_app
            app = create_app('testing')
            
            # 測試健康檢查API是否返回預期格式
            with app.test_client() as client:
                response = client.get('/health')
                
                if response.status_code == 200:
                    data = response.json()
                    
                    # 檢查必要的欄位
                    required_fields = ['status', 'timestamp', 'modules']
                    has_required_fields = all(field in data for field in required_fields)
                    
                    return {
                        'success': has_required_fields,
                        'response_data': data,
                        'required_fields': required_fields,
                        'has_required_fields': has_required_fields
                    }
                else:
                    return {
                        'success': False,
                        'error': f'健康檢查API返回狀態碼: {response.status_code}'
                    }
                    
        except Exception as e:
            return {
                'success': False,
                'error': str(e)
            }


class ComprehensiveTestRunner:
    """綜合測試運行器"""
    
    def __init__(self):
        self.start_time = None
        self.results = {}
        
    def run_all_tests(self) -> Dict[str, Any]:
        """運行所有測試"""
        self.start_time = time.time()
        
        print("🧪 開始執行模組化前端架構綜合測試")
        print("=" * 60)
        
        # 1. 架構驗證測試
        print("\n📋 第一階段: 架構驗證測試")
        print("-" * 40)
        validator = ArchitectureValidator()
        self.results['architecture'] = {
            'directory_structure': validator.validate_directory_structure(),
            'module_imports': validator.test_module_imports(),
            'flask_app_creation': validator.test_flask_app_creation(),
            'blueprint_registration': validator.test_blueprint_registration()
        }
        
        # 2. 功能測試
        print("\n🔧 第二階段: 功能測試")
        print("-" * 40)
        functional_tester = FunctionalTester()
        
        if functional_tester.start_test_server():
            try:
                self.results['functional'] = {
                    'route_testing': functional_tester.test_routes(),
                    'static_resources': functional_tester.test_static_resources()
                }
            finally:
                functional_tester.stop_test_server()
        else:
            self.results['functional'] = {
                'route_testing': {'success': False, 'error': '測試服務器啟動失敗'},
                'static_resources': {'success': False, 'error': '測試服務器啟動失敗'}
            }
        
        # 3. 整合測試
        print("\n🔗 第三階段: 整合測試")
        print("-" * 40)
        integration_tester = IntegrationTester()
        self.results['integration'] = {
            'service_startup': integration_tester.test_integrated_service_startup(),
            'service_discovery': integration_tester.test_service_discovery()
        }
        
        # 4. 回歸測試
        print("\n🔄 第四階段: 回歸測試")
        print("-" * 40)
        regression_tester = RegressionTester()
        self.results['regression'] = regression_tester.test_backward_compatibility()
        
        # 計算總體結果
        total_time = time.time() - self.start_time
        self.results['summary'] = self._calculate_summary(total_time)
        
        return self.results
    
    def _calculate_summary(self, total_time: float) -> Dict[str, Any]:
        """計算測試摘要"""
        all_tests = []
        
        # 收集所有測試結果
        def collect_results(obj, prefix=""):
            if isinstance(obj, dict):
                if 'success' in obj:
                    all_tests.append(obj['success'])
                else:
                    for key, value in obj.items():
                        collect_results(value, f"{prefix}.{key}" if prefix else key)
        
        for category, tests in self.results.items():
            if category != 'summary':
                collect_results(tests, category)
        
        successful_tests = sum(all_tests)
        total_tests = len(all_tests)
        success_rate = (successful_tests / total_tests * 100) if total_tests > 0 else 0
        
        overall_success = success_rate >= 80  # 80%以上通過率視為成功
        
        return {
            'overall_success': overall_success,
            'success_rate': success_rate,
            'total_tests': total_tests,
            'successful_tests': successful_tests,
            'failed_tests': total_tests - successful_tests,
            'execution_time': total_time,
            'timestamp': datetime.now().isoformat()
        }
    
    def generate_report(self) -> str:
        """生成詳細報告"""
        if not self.results:
            return "尚未執行測試"
        
        summary = self.results.get('summary', {})
        
        report = f"""
模組化前端架構綜合測試報告
{'='*60}

測試概要:
    整體結果: {'✅ 成功' if summary.get('overall_success') else '❌ 失敗'}
    成功率: {summary.get('success_rate', 0):.1f}%
    總測試數: {summary.get('total_tests', 0)}
    成功測試: {summary.get('successful_tests', 0)}
    失敗測試: {summary.get('failed_tests', 0)}
    執行時間: {summary.get('execution_time', 0):.2f}秒
    
詳細結果:
{'='*60}

架構驗證測試:
    目錄結構: {'✅' if self.results['architecture']['directory_structure']['success'] else '❌'}
    模組導入: {'✅' if self.results['architecture']['module_imports']['success'] else '❌'}
    Flask應用: {'✅' if self.results['architecture']['flask_app_creation']['success'] else '❌'}
    藍圖註冊: {'✅' if self.results['architecture']['blueprint_registration']['success'] else '❌'}

功能測試:
    路由測試: {'✅' if self.results['functional']['route_testing']['success'] else '❌'}
    靜態資源: {'✅' if self.results['functional']['static_resources']['success'] else '❌'}

整合測試:
    服務啟動: {'✅' if self.results['integration']['service_startup']['success'] else '❌'}
    服務發現: {'✅' if self.results['integration']['service_discovery']['success'] else '❌'}

回歸測試:
    向後兼容: {'✅' if self.results['regression']['success'] else '❌'}
"""

        # 添加失敗詳情
        if not summary.get('overall_success'):
            report += "\n失敗詳情:\n" + "-" * 20 + "\n"
            
            def report_failures(obj, prefix=""):
                if isinstance(obj, dict):
                    if 'success' in obj and not obj['success']:
                        error = obj.get('error', '未知錯誤')
                        report_temp = f"{prefix}: {error}\n"
                        return report_temp
                    else:
                        temp = ""
                        for key, value in obj.items():
                            if key not in ['success', 'error']:
                                temp += report_failures(value, f"{prefix}.{key}" if prefix else key)
                        return temp
                return ""
            
            for category, tests in self.results.items():
                if category != 'summary':
                    report += report_failures(tests, category)
        
        report += "\n" + "="*60
        return report
    
    def save_report(self, filename: str = None):
        """保存報告到文件"""
        if filename is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"modular_frontend_test_report_{timestamp}.json"
        
        # 保存JSON格式的詳細結果
        json_filename = filename.replace('.txt', '.json') if filename.endswith('.txt') else f"{filename}.json"
        with open(self.project_root / json_filename, 'w', encoding='utf-8') as f:
            json.dump(self.results, f, ensure_ascii=False, indent=2)
        
        # 保存可讀的報告
        txt_filename = filename.replace('.json', '.txt') if filename.endswith('.json') else f"{filename}.txt"
        with open(self.project_root / txt_filename, 'w', encoding='utf-8') as f:
            f.write(self.generate_report())
        
        print(f"📄 詳細報告已保存: {json_filename}")
        print(f"📄 可讀報告已保存: {txt_filename}")


def main():
    """主函數"""
    print("🧪 模組化前端架構綜合測試")
    print("此測試驗證新的 frontend/ 架構在移除 email_inbox_app.py 後正常運作")
    print("="*60)
    
    runner = ComprehensiveTestRunner()
    
    try:
        # 執行所有測試
        results = runner.run_all_tests()
        
        # 顯示摘要
        print("\n" + "="*60)
        print("測試完成!")
        print("="*60)
        print(runner.generate_report())
        
        # 保存報告
        runner.save_report("modular_frontend_architecture_test_report")
        
        # 返回適當的退出碼
        return 0 if results['summary']['overall_success'] else 1
        
    except KeyboardInterrupt:
        print("\n❌ 測試被用戶中斷")
        return 1
    except Exception as e:
        print(f"\n❌ 測試執行失敗: {e}")
        import traceback
        traceback.print_exc()
        return 1


if __name__ == '__main__':
    exit_code = main()
    sys.exit(exit_code)