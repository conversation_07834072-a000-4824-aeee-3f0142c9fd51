{"summary": {"total_routes": 50, "successful_routes": 45, "failed_routes": 5, "success_rate": 90.0}, "results": [{"route": "/email/inbox", "status_code": 200, "success": true}, {"route": "/email/", "status_code": 200, "success": true}, {"route": "/email/api/list", "status_code": 200, "success": true}, {"route": "/email/api/sync/status", "status_code": 200, "success": true}, {"route": "/email/api/statistics", "status_code": 200, "success": true}, {"route": "/email/api/senders", "status_code": 200, "success": true}, {"route": "/email/api/search", "status_code": 200, "success": true}, {"route": "/email/api/connection/test", "status_code": 200, "success": true}, {"route": "/email/api/connection/status", "status_code": 200, "success": true}, {"route": "/analytics/dashboard", "status_code": 200, "success": true}, {"route": "/analytics/", "status_code": 200, "success": true}, {"route": "/analytics/reports", "status_code": 200, "success": true}, {"route": "/analytics/vendor-analysis", "status_code": 200, "success": true}, {"route": "/analytics/api/metrics", "status_code": 200, "success": true}, {"route": "/analytics/api/reports", "status_code": 200, "success": true}, {"route": "/eqc/dashboard", "status_code": 200, "success": true}, {"route": "/eqc/", "status_code": 200, "success": true}, {"route": "/eqc/quality-check", "status_code": 200, "success": true}, {"route": "/eqc/compliance", "status_code": 200, "success": true}, {"route": "/eqc/ui", "status_code": 404, "success": false}, {"route": "/eqc/ft-eqc", "status_code": 404, "success": false}, {"route": "/eqc/ft-eqc-api", "status_code": 404, "success": false}, {"route": "/eqc/api/metrics", "status_code": 200, "success": true}, {"route": "/tasks/dashboard", "status_code": 200, "success": true}, {"route": "/tasks/", "status_code": 200, "success": true}, {"route": "/tasks/scheduler-dashboard", "status_code": 200, "success": true}, {"route": "/tasks/queue", "status_code": 200, "success": true}, {"route": "/tasks/scheduler", "status_code": 200, "success": true}, {"route": "/tasks/api/stats", "status_code": 200, "success": true}, {"route": "/tasks/api/list", "status_code": 200, "success": true}, {"route": "/monitoring/dashboard", "status_code": 200, "success": true}, {"route": "/monitoring/", "status_code": 200, "success": true}, {"route": "/monitoring/health", "status_code": 200, "success": true}, {"route": "/monitoring/api/status", "status_code": 200, "success": true}, {"route": "/monitoring/api/metrics", "status_code": 200, "success": true}, {"route": "/monitoring/api/health/all", "status_code": 200, "success": true}, {"route": "/monitoring/api/alerts", "status_code": 200, "success": true}, {"route": "/monitoring/database-manager", "status_code": 200, "success": true}, {"route": "/monitoring/api/database/info", "status_code": 200, "success": true}, {"route": "/files/manager", "status_code": 200, "success": true}, {"route": "/files/", "status_code": 200, "success": true}, {"route": "/files/upload", "status_code": 200, "success": true}, {"route": "/files/browser", "status_code": 200, "success": true}, {"route": "/files/network-browser", "status_code": 200, "success": true}, {"route": "/files/api/list", "status_code": 200, "success": true}, {"route": "/", "status_code": 200, "success": true}, {"route": "/favicon.ico", "status_code": 200, "success": true}, {"route": "/test", "status_code": 404, "success": false}, {"route": "/debug_sender_display.html", "status_code": 404, "success": false}, {"route": "/health", "status_code": 200, "success": true}]}