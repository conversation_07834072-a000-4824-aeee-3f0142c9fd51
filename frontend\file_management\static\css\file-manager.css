/* File Management Module CSS - 檔案管理模組樣式 */

/* 檔案管理容器樣式 */
.file-manager-container {
    padding: 20px;
    background: #f8f9fa;
    min-height: 100vh;
}

.file-manager-header {
    background: white;
    padding: 20px;
    border-radius: 8px;
    margin-bottom: 20px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 15px;
}

.file-manager-title {
    font-size: 24px;
    font-weight: 600;
    color: #333;
    margin: 0;
}

.file-actions {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
}

/* 檔案管理佈局 */
.file-manager-layout {
    display: grid;
    grid-template-columns: 250px 1fr;
    gap: 20px;
    margin-bottom: 20px;
}

.file-sidebar {
    background: white;
    border-radius: 8px;
    padding: 20px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    height: fit-content;
}

.file-main-content {
    background: white;
    border-radius: 8px;
    padding: 20px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    min-height: 600px;
}

/* 側邊欄樣式 */
.sidebar-section {
    margin-bottom: 25px;
}

.sidebar-title {
    font-size: 14px;
    font-weight: 600;
    color: #333;
    margin-bottom: 10px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.sidebar-list {
    list-style: none;
    padding: 0;
    margin: 0;
}

.sidebar-item {
    display: flex;
    align-items: center;
    padding: 8px 12px;
    border-radius: 6px;
    cursor: pointer;
    transition: background-color 0.2s;
    margin-bottom: 2px;
    font-size: 14px;
    color: #666;
}

.sidebar-item:hover {
    background-color: #f8f9fa;
    color: #333;
}

.sidebar-item.active {
    background-color: #007bff;
    color: white;
}

.sidebar-icon {
    width: 16px;
    height: 16px;
    margin-right: 8px;
    flex-shrink: 0;
}

.sidebar-count {
    margin-left: auto;
    font-size: 12px;
    background-color: #e9ecef;
    color: #6c757d;
    padding: 2px 6px;
    border-radius: 10px;
}

.sidebar-item.active .sidebar-count {
    background-color: rgba(255, 255, 255, 0.2);
    color: white;
}

/* 檔案列表樣式 */
.file-list-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 1px solid #e9ecef;
}

.file-list-title {
    font-size: 18px;
    font-weight: 600;
    color: #333;
}

.file-list-controls {
    display: flex;
    gap: 10px;
    align-items: center;
}

.view-toggle {
    display: flex;
    border: 1px solid #ddd;
    border-radius: 4px;
    overflow: hidden;
}

.view-btn {
    padding: 6px 10px;
    border: none;
    background: white;
    cursor: pointer;
    transition: all 0.2s;
    font-size: 14px;
}

.view-btn.active {
    background-color: #007bff;
    color: white;
}

.sort-dropdown {
    padding: 6px 10px;
    border: 1px solid #ddd;
    border-radius: 4px;
    background: white;
    font-size: 14px;
    cursor: pointer;
}

/* 檔案網格樣式 */
.file-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: 15px;
    margin-bottom: 20px;
}

.file-item {
    background: white;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    padding: 15px;
    text-align: center;
    cursor: pointer;
    transition: all 0.2s ease;
    position: relative;
}

.file-item:hover {
    border-color: #007bff;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.file-item.selected {
    border-color: #007bff;
    background-color: #f0f8ff;
}

.file-checkbox {
    position: absolute;
    top: 10px;
    left: 10px;
    cursor: pointer;
}

.file-icon {
    font-size: 48px;
    margin-bottom: 10px;
    color: #6c757d;
}

.file-icon.pdf {
    color: #dc3545;
}

.file-icon.doc {
    color: #2b579a;
}

.file-icon.xls {
    color: #217346;
}

.file-icon.img {
    color: #fd7e14;
}

.file-icon.zip {
    color: #6f42c1;
}

.file-name {
    font-size: 14px;
    font-weight: 500;
    color: #333;
    margin-bottom: 5px;
    word-break: break-word;
    line-height: 1.3;
}

.file-details {
    font-size: 12px;
    color: #666;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.file-size {
    font-weight: 500;
}

.file-date {
    opacity: 0.8;
}

/* 檔案列表樣式 */
.file-list {
    width: 100%;
    border-collapse: collapse;
    margin-bottom: 20px;
}

.file-list th,
.file-list td {
    padding: 12px;
    text-align: left;
    border-bottom: 1px solid #e9ecef;
}

.file-list th {
    background-color: #f8f9fa;
    font-weight: 600;
    font-size: 14px;
    color: #333;
    position: sticky;
    top: 0;
}

.file-list tr:hover {
    background-color: #f8f9fa;
}

.file-list-item {
    cursor: pointer;
    transition: background-color 0.2s;
}

.file-list-item.selected {
    background-color: #f0f8ff;
}

.file-list-checkbox {
    width: 30px;
    text-align: center;
}

.file-list-icon {
    width: 40px;
    text-align: center;
    font-size: 20px;
}

.file-list-name {
    font-weight: 500;
    color: #333;
}

.file-list-size,
.file-list-date,
.file-list-type {
    font-size: 13px;
    color: #666;
}

.file-list-actions {
    width: 100px;
    text-align: center;
}

/* 檔案上傳樣式 */
.file-upload-area {
    border: 2px dashed #ddd;
    border-radius: 8px;
    padding: 40px;
    text-align: center;
    margin-bottom: 20px;
    cursor: pointer;
    transition: all 0.2s ease;
    background: #fafafa;
}

.file-upload-area:hover {
    border-color: #007bff;
    background: #f0f8ff;
}

.file-upload-area.dragover {
    border-color: #28a745;
    background: #f8fff9;
}

.upload-icon {
    font-size: 64px;
    color: #ddd;
    margin-bottom: 20px;
}

.upload-text {
    font-size: 18px;
    color: #333;
    margin-bottom: 10px;
    font-weight: 500;
}

.upload-hint {
    font-size: 14px;
    color: #666;
    margin-bottom: 20px;
}

.upload-button {
    padding: 10px 20px;
    background-color: #007bff;
    color: white;
    border: none;
    border-radius: 4px;
    font-size: 14px;
    cursor: pointer;
    transition: background-color 0.2s;
}

.upload-button:hover {
    background-color: #0056b3;
}

/* 上傳進度樣式 */
.upload-progress {
    background: white;
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 20px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.progress-item {
    display: flex;
    align-items: center;
    margin-bottom: 15px;
    padding: 10px;
    background: #f8f9fa;
    border-radius: 6px;
}

.progress-icon {
    font-size: 20px;
    margin-right: 12px;
    color: #007bff;
}

.progress-info {
    flex: 1;
}

.progress-filename {
    font-size: 14px;
    font-weight: 500;
    color: #333;
    margin-bottom: 4px;
}

.progress-details {
    font-size: 12px;
    color: #666;
    display: flex;
    justify-content: space-between;
}

.progress-bar-container {
    width: 100%;
    height: 4px;
    background: #e9ecef;
    border-radius: 2px;
    margin-top: 8px;
    overflow: hidden;
}

.progress-bar {
    height: 100%;
    background: #007bff;
    transition: width 0.3s ease;
    border-radius: 2px;
}

.progress-bar.success {
    background: #28a745;
}

.progress-bar.error {
    background: #dc3545;
}

/* 附件瀏覽器樣式 */
.attachment-browser {
    background: white;
    border-radius: 8px;
    padding: 20px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.attachment-filters {
    display: flex;
    gap: 15px;
    margin-bottom: 20px;
    padding: 15px;
    background: #f8f9fa;
    border-radius: 8px;
    flex-wrap: wrap;
}

.filter-group {
    display: flex;
    flex-direction: column;
    gap: 5px;
}

.filter-label {
    font-size: 12px;
    font-weight: 500;
    color: #666;
    text-transform: uppercase;
}

.filter-select {
    padding: 6px 10px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 14px;
    background: white;
}

.attachment-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
    gap: 15px;
}

.attachment-item {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 15px;
    text-align: center;
    cursor: pointer;
    transition: all 0.2s;
    border: 2px solid transparent;
}

.attachment-item:hover {
    background: white;
    border-color: #007bff;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.attachment-icon {
    font-size: 32px;
    margin-bottom: 8px;
    color: #6c757d;
}

.attachment-name {
    font-size: 12px;
    font-weight: 500;
    color: #333;
    margin-bottom: 4px;
    word-break: break-word;
}

.attachment-info {
    font-size: 11px;
    color: #666;
}

/* 響應式設計 */
@media (max-width: 768px) {
    .file-manager-layout {
        grid-template-columns: 1fr;
    }
    
    .file-sidebar {
        order: 2;
        height: auto;
    }
    
    .file-main-content {
        order: 1;
    }
    
    .file-grid {
        grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
    }
    
    .file-manager-header {
        flex-direction: column;
        text-align: center;
    }
    
    .file-actions {
        width: 100%;
        justify-content: center;
    }
    
    .file-list-controls {
        flex-direction: column;
        gap: 10px;
        align-items: stretch;
    }
    
    .attachment-filters {
        flex-direction: column;
        gap: 10px;
    }
    
    .attachment-grid {
        grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
    }
    
    .file-manager-container {
        padding: 10px;
    }
}