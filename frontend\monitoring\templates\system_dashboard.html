<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>系統監控儀表板 - Outlook Summary</title>
    <link rel="stylesheet" href="{{ url_for('monitoring.static', filename='css/monitoring.css') }}?v={{ range(1000, 9999) | random }}">
    <link rel="stylesheet" href="{{ url_for('shared.static', filename='css/global.css') }}">
</head>
<body>
    <div class="system-dashboard-container">
        <header class="dashboard-header">
            <h1>系統監控儀表板</h1>
            <div class="header-status">
                <div class="system-health {{ 'healthy' if system_health.status == 'healthy' else 'warning' if system_health.status == 'warning' else 'critical' }}">
                    <span class="status-indicator"></span>
                    <span class="status-text">{{ system_health.status_display }}</span>
                    <span class="status-score">{{ system_health.score }}/100</span>
                </div>
                <div class="last-updated">
                    最後更新: <span id="last-updated-time">{{ last_updated.strftime('%Y-%m-%d %H:%M:%S') if last_updated else 'N/A' }}</span>
                </div>
            </div>
            <div class="header-actions">
                <button id="refresh-dashboard-btn" class="btn btn-primary">
                    <span class="btn-icon">🔄</span>
                    <span class="btn-text">重新整理</span>
                </button>
                <button id="export-report-btn" class="btn btn-secondary">
                    <span class="btn-icon">📊</span>
                    <span class="btn-text">匯出報告</span>
                </button>
                <button id="configure-alerts-btn" class="btn btn-outline">
                    <span class="btn-icon">⚠️</span>
                    <span class="btn-text">警報設定</span>
                </button>
                <a href="{{ url_for('monitoring.health') }}" class="btn btn-outline">
                    <span class="btn-icon">🏥</span>
                    <span class="btn-text">健康檢查</span>
                </a>
            </div>
        </header>

        <div class="dashboard-content">
            <!-- 系統資源監控 -->
            <div class="system-resources-section">
                <div class="resource-grid">
                    <div class="resource-card cpu">
                        <div class="card-header">
                            <h3>💻 CPU使用率</h3>
                            <div class="resource-value">{{ '%.1f'|format(resources.cpu_usage) }}%</div>
                        </div>
                        <div class="resource-meter">
                            <div class="meter-fill" style="width: {{ resources.cpu_usage }}%; background-color: {{ 'var(--success)' if resources.cpu_usage < 70 else 'var(--warning)' if resources.cpu_usage < 85 else 'var(--danger)' }}"></div>
                        </div>
                        <div class="resource-details">
                            <div class="detail-item">
                                <span class="label">核心數:</span>
                                <span class="value">{{ resources.cpu_cores }}</span>
                            </div>
                            <div class="detail-item">
                                <span class="label">負載平均:</span>
                                <span class="value">{{ '%.2f'|format(resources.load_average) }}</span>
                            </div>
                            <div class="detail-item">
                                <span class="label">頻率:</span>
                                <span class="value">{{ resources.cpu_frequency }}MHz</span>
                            </div>
                        </div>
                    </div>

                    <div class="resource-card memory">
                        <div class="card-header">
                            <h3>🧠 記憶體使用</h3>
                            <div class="resource-value">{{ '%.1f'|format(resources.memory_usage_percent) }}%</div>
                        </div>
                        <div class="resource-meter">
                            <div class="meter-fill" style="width: {{ resources.memory_usage_percent }}%; background-color: {{ 'var(--success)' if resources.memory_usage_percent < 70 else 'var(--warning)' if resources.memory_usage_percent < 85 else 'var(--danger)' }}"></div>
                        </div>
                        <div class="resource-details">
                            <div class="detail-item">
                                <span class="label">已使用:</span>
                                <span class="value">{{ resources.memory_used_gb }}GB</span>
                            </div>
                            <div class="detail-item">
                                <span class="label">總計:</span>
                                <span class="value">{{ resources.memory_total_gb }}GB</span>
                            </div>
                            <div class="detail-item">
                                <span class="label">可用:</span>
                                <span class="value">{{ resources.memory_available_gb }}GB</span>
                            </div>
                        </div>
                    </div>

                    <div class="resource-card disk">
                        <div class="card-header">
                            <h3>💾 磁碟使用</h3>
                            <div class="resource-value">{{ '%.1f'|format(resources.disk_usage_percent) }}%</div>
                        </div>
                        <div class="resource-meter">
                            <div class="meter-fill" style="width: {{ resources.disk_usage_percent }}%; background-color: {{ 'var(--success)' if resources.disk_usage_percent < 80 else 'var(--warning)' if resources.disk_usage_percent < 90 else 'var(--danger)' }}"></div>
                        </div>
                        <div class="resource-details">
                            <div class="detail-item">
                                <span class="label">已使用:</span>
                                <span class="value">{{ resources.disk_used_gb }}GB</span>
                            </div>
                            <div class="detail-item">
                                <span class="label">總容量:</span>
                                <span class="value">{{ resources.disk_total_gb }}GB</span>
                            </div>
                            <div class="detail-item">
                                <span class="label">可用:</span>
                                <span class="value">{{ resources.disk_free_gb }}GB</span>
                            </div>
                        </div>
                    </div>

                    <div class="resource-card network">
                        <div class="card-header">
                            <h3>🌐 網路活動</h3>
                            <div class="resource-value">{{ resources.network_status }}</div>
                        </div>
                        <div class="network-stats">
                            <div class="network-item">
                                <span class="direction up">↑</span>
                                <span class="speed">{{ resources.network_sent_mbps }}MB/s</span>
                                <span class="label">上傳</span>
                            </div>
                            <div class="network-item">
                                <span class="direction down">↓</span>
                                <span class="speed">{{ resources.network_recv_mbps }}MB/s</span>
                                <span class="label">下載</span>
                            </div>
                        </div>
                        <div class="resource-details">
                            <div class="detail-item">
                                <span class="label">已發送:</span>
                                <span class="value">{{ resources.network_sent_gb }}GB</span>
                            </div>
                            <div class="detail-item">
                                <span class="label">已接收:</span>
                                <span class="value">{{ resources.network_recv_gb }}GB</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 應用程式狀態 -->
            <div class="application-status-section">
                <div class="status-card">
                    <div class="card-header">
                        <h3>🚀 應用程式狀態</h3>
                        <div class="status-summary">
                            <span class="summary-item healthy">{{ app_status.healthy_services }} 正常</span>
                            <span class="summary-item warning">{{ app_status.warning_services }} 警告</span>
                            <span class="summary-item critical">{{ app_status.critical_services }} 異常</span>
                        </div>
                    </div>
                    
                    <div class="service-grid">
                        {% for service in services %}
                        <div class="service-item {{ service.status }}">
                            <div class="service-header">
                                <span class="service-icon">{{ service.icon }}</span>
                                <span class="service-name">{{ service.name }}</span>
                                <span class="service-status {{ service.status }}">{{ service.status_display }}</span>
                            </div>
                            <div class="service-metrics">
                                <div class="metric">
                                    <span class="metric-label">回應時間:</span>
                                    <span class="metric-value">{{ service.response_time }}ms</span>
                                </div>
                                <div class="metric">
                                    <span class="metric-label">可用性:</span>
                                    <span class="metric-value">{{ '%.1f'|format(service.uptime_percent) }}%</span>
                                </div>
                                <div class="metric">
                                    <span class="metric-label">最後檢查:</span>
                                    <span class="metric-value">{{ service.last_check.strftime('%H:%M:%S') if service.last_check else 'N/A' }}</span>
                                </div>
                            </div>
                            <div class="service-actions">
                                <button class="btn btn-sm btn-outline" onclick="checkService('{{ service.id }}')">檢查</button>
                                <button class="btn btn-sm btn-secondary" onclick="restartService('{{ service.id }}')">重啟</button>
                                <button class="btn btn-sm btn-primary" onclick="viewServiceLogs('{{ service.id }}')">日誌</button>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                </div>
            </div>

            <!-- 效能圖表 -->
            <div class="performance-charts-section">
                <div class="chart-row">
                    <div class="chart-card">
                        <div class="chart-header">
                            <h3>系統資源趨勢</h3>
                            <div class="chart-controls">
                                <select id="resource-timeframe">
                                    <option value="1h">過去1小時</option>
                                    <option value="6h" selected>過去6小時</option>
                                    <option value="24h">過去24小時</option>
                                    <option value="7d">過去7天</option>
                                </select>
                                <button class="btn btn-sm btn-secondary" onclick="refreshChart('resource-trend-chart')">重新整理</button>
                            </div>
                        </div>
                        <div class="chart-container">
                            <canvas id="resource-trend-chart"></canvas>
                        </div>
                    </div>

                    <div class="chart-card">
                        <div class="chart-header">
                            <h3>服務回應時間</h3>
                            <div class="chart-controls">
                                <select id="response-service">
                                    <option value="all" selected>全部服務</option>
                                    {% for service in services %}
                                    <option value="{{ service.id }}">{{ service.name }}</option>
                                    {% endfor %}
                                </select>
                            </div>
                        </div>
                        <div class="chart-container">
                            <canvas id="response-time-chart"></canvas>
                        </div>
                    </div>
                </div>

                <div class="chart-row">
                    <div class="chart-card full-width">
                        <div class="chart-header">
                            <h3>網路流量分析</h3>
                            <div class="chart-controls">
                                <select id="traffic-timeframe">
                                    <option value="1h">過去1小時</option>
                                    <option value="6h" selected>過去6小時</option>
                                    <option value="24h">過去24小時</option>
                                </select>
                                <div class="traffic-legends">
                                    <span class="legend-item"><span class="legend-color upload"></span>上傳</span>
                                    <span class="legend-item"><span class="legend-color download"></span>下載</span>
                                </div>
                            </div>
                        </div>
                        <div class="chart-container">
                            <canvas id="network-traffic-chart"></canvas>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 警報和事件 -->
            <div class="alerts-events-section">
                <div class="section-row">
                    <div class="alerts-card">
                        <div class="card-header">
                            <h3>⚠️ 活躍警報</h3>
                            <div class="alert-summary">
                                <span class="alert-count critical">{{ alerts.critical_count }} 嚴重</span>
                                <span class="alert-count warning">{{ alerts.warning_count }} 警告</span>
                                <span class="alert-count info">{{ alerts.info_count }} 資訊</span>
                            </div>
                        </div>
                        
                        <div class="alert-list" id="active-alerts-list">
                            {% for alert in active_alerts %}
                            <div class="alert-item {{ alert.severity }}">
                                <div class="alert-icon">
                                    {% if alert.severity == 'critical' %}🚨
                                    {% elif alert.severity == 'warning' %}⚠️
                                    {% elif alert.severity == 'info' %}ℹ️
                                    {% endif %}
                                </div>
                                <div class="alert-content">
                                    <div class="alert-title">{{ alert.title }}</div>
                                    <div class="alert-message">{{ alert.message }}</div>
                                    <div class="alert-meta">
                                        <span class="alert-time">{{ alert.created_at.strftime('%Y-%m-%d %H:%M') if alert.created_at else 'N/A' }}</span>
                                        <span class="alert-source">{{ alert.source }}</span>
                                    </div>
                                </div>
                                <div class="alert-actions">
                                    <button class="btn btn-sm btn-primary" onclick="acknowledgeAlert('{{ alert.id }}')">確認</button>
                                    <button class="btn btn-sm btn-outline" onclick="dismissAlert('{{ alert.id }}')">忽略</button>
                                    <button class="btn btn-sm btn-secondary" onclick="viewAlertDetails('{{ alert.id }}')">詳情</button>
                                </div>
                            </div>
                            {% endfor %}
                        </div>

                        <div class="alert-actions">
                            <button id="acknowledge-all-btn" class="btn btn-sm btn-primary">全部確認</button>
                            <button id="view-all-alerts-btn" class="btn btn-sm btn-outline">查看全部警報</button>
                        </div>
                    </div>

                    <div class="events-card">
                        <div class="card-header">
                            <h3>📋 系統事件</h3>
                            <div class="event-filters">
                                <select id="event-type-filter">
                                    <option value="all" selected>全部類型</option>
                                    <option value="system">系統</option>
                                    <option value="application">應用程式</option>
                                    <option value="security">安全</option>
                                    <option value="performance">效能</option>
                                </select>
                                <select id="event-timeframe">
                                    <option value="1h">過去1小時</option>
                                    <option value="6h" selected>過去6小時</option>
                                    <option value="24h">過去24小時</option>
                                </select>
                            </div>
                        </div>
                        
                        <div class="event-timeline" id="system-events-timeline">
                            {% for event in recent_events %}
                            <div class="event-item {{ event.type }}">
                                <div class="event-time">{{ event.timestamp.strftime('%H:%M') if event.timestamp else 'N/A' }}</div>
                                <div class="event-content">
                                    <div class="event-title">{{ event.title }}</div>
                                    <div class="event-description">{{ event.description }}</div>
                                    <div class="event-meta">
                                        <span class="event-type {{ event.type }}">{{ event.type_display }}</span>
                                        <span class="event-source">{{ event.source }}</span>
                                    </div>
                                </div>
                            </div>
                            {% endfor %}
                        </div>

                        <div class="event-actions">
                            <button id="export-events-btn" class="btn btn-sm btn-secondary">匯出事件</button>
                            <button id="clear-events-btn" class="btn btn-sm btn-danger">清除事件</button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 系統資訊 -->
            <div class="system-info-section">
                <div class="info-grid">
                    <div class="info-card">
                        <h4>💻 系統資訊</h4>
                        <div class="info-list">
                            <div class="info-item">
                                <span class="info-label">作業系統:</span>
                                <span class="info-value">{{ system_info.os_name }} {{ system_info.os_version }}</span>
                            </div>
                            <div class="info-item">
                                <span class="info-label">主機名稱:</span>
                                <span class="info-value">{{ system_info.hostname }}</span>
                            </div>
                            <div class="info-item">
                                <span class="info-label">啟動時間:</span>
                                <span class="info-value">{{ system_info.boot_time.strftime('%Y-%m-%d %H:%M:%S') if system_info.boot_time else 'N/A' }}</span>
                            </div>
                            <div class="info-item">
                                <span class="info-label">運行時間:</span>
                                <span class="info-value">{{ system_info.uptime }}</span>
                            </div>
                        </div>
                    </div>

                    <div class="info-card">
                        <h4>🐍 Python環境</h4>
                        <div class="info-list">
                            <div class="info-item">
                                <span class="info-label">Python版本:</span>
                                <span class="info-value">{{ python_info.version }}</span>
                            </div>
                            <div class="info-item">
                                <span class="info-label">虛擬環境:</span>
                                <span class="info-value">{{ python_info.virtual_env or '無' }}</span>
                            </div>
                            <div class="info-item">
                                <span class="info-label">套件數量:</span>
                                <span class="info-value">{{ python_info.package_count }}</span>
                            </div>
                            <div class="info-item">
                                <span class="info-label">工作目錄:</span>
                                <span class="info-value">{{ python_info.working_directory }}</span>
                            </div>
                        </div>
                    </div>

                    <div class="info-card">
                        <h4>📦 應用程式資訊</h4>
                        <div class="info-list">
                            <div class="info-item">
                                <span class="info-label">應用程式版本:</span>
                                <span class="info-value">{{ app_info.version }}</span>
                            </div>
                            <div class="info-item">
                                <span class="info-label">建置時間:</span>
                                <span class="info-value">{{ app_info.build_time.strftime('%Y-%m-%d %H:%M') if app_info.build_time else 'N/A' }}</span>
                            </div>
                            <div class="info-item">
                                <span class="info-label">配置模式:</span>
                                <span class="info-value">{{ app_info.config_mode }}</span>
                            </div>
                            <div class="info-item">
                                <span class="info-label">除錯模式:</span>
                                <span class="info-value">{{ '啟用' if app_info.debug_mode else '停用' }}</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 警報設定模態框 -->
    <div class="modal" id="alert-config-modal">
        <div class="modal-content large">
            <div class="modal-header">
                <h3>警報設定</h3>
                <button class="modal-close">&times;</button>
            </div>
            <div class="modal-body">
                <div class="alert-config-tabs">
                    <button class="tab-btn active" data-tab="thresholds">門檻設定</button>
                    <button class="tab-btn" data-tab="notifications">通知設定</button>
                    <button class="tab-btn" data-tab="rules">規則設定</button>
                </div>

                <div class="tab-content active" id="thresholds-tab">
                    <h4>資源使用門檻</h4>
                    <div class="threshold-grid">
                        <div class="threshold-item">
                            <label for="cpu-warning-threshold">CPU警告門檻 (%):</label>
                            <input type="number" id="cpu-warning-threshold" min="0" max="100" value="{{ thresholds.cpu_warning or 75 }}">
                        </div>
                        <div class="threshold-item">
                            <label for="cpu-critical-threshold">CPU嚴重門檻 (%):</label>
                            <input type="number" id="cpu-critical-threshold" min="0" max="100" value="{{ thresholds.cpu_critical or 90 }}">
                        </div>
                        <div class="threshold-item">
                            <label for="memory-warning-threshold">記憶體警告門檻 (%):</label>
                            <input type="number" id="memory-warning-threshold" min="0" max="100" value="{{ thresholds.memory_warning or 80 }}">
                        </div>
                        <div class="threshold-item">
                            <label for="memory-critical-threshold">記憶體嚴重門檻 (%):</label>
                            <input type="number" id="memory-critical-threshold" min="0" max="100" value="{{ thresholds.memory_critical or 95 }}">
                        </div>
                    </div>
                </div>

                <div class="tab-content" id="notifications-tab">
                    <h4>通知方式</h4>
                    <div class="notification-options">
                        <label>
                            <input type="checkbox" id="email-notifications" {{ 'checked' if notifications.email_enabled else '' }}>
                            電子郵件通知
                        </label>
                        <label>
                            <input type="checkbox" id="webhook-notifications" {{ 'checked' if notifications.webhook_enabled else '' }}>
                            Webhook通知
                        </label>
                        <label>
                            <input type="checkbox" id="desktop-notifications" {{ 'checked' if notifications.desktop_enabled else '' }}>
                            桌面通知
                        </label>
                    </div>
                </div>

                <div class="tab-content" id="rules-tab">
                    <h4>警報規則</h4>
                    <div class="rules-list">
                        <!-- 警報規則列表會由JavaScript動態生成 -->
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button id="save-alert-config-btn" class="btn btn-primary">儲存設定</button>
                <button class="btn btn-secondary" onclick="closeModal('alert-config-modal')">取消</button>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="{{ url_for('shared.static', filename='js/main.js') }}"></script>
    <script src="{{ url_for('shared.static', filename='lib/chart.min.js') }}"></script>
    <script src="{{ url_for('monitoring.static', filename='js/system-dashboard.js') }}"></script>
</body>
</html>