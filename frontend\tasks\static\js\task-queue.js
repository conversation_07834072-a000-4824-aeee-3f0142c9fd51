/**
 * Task Queue Management Module
 * 任務隊列管理模組 JavaScript
 */

class TaskQueueManager {
    constructor() {
        this.queueData = {};
        this.currentTasks = [];
        this.queueStats = {};
        this.refreshInterval = 3000; // 3秒刷新一次
        this.intervalId = null;
        this.charts = {};
        this.websocket = null;
        
        this.init();
    }
    
    init() {
        console.log('Task Queue Manager: Initializing...');
        this.bindEvents();
        this.setupCharts();
        this.loadQueueData();
        this.initWebSocket();
        this.startAutoRefresh();
    }
    
    bindEvents() {
        // 隊列控制按鈕
        document.addEventListener('click', (e) => {
            if (e.target.matches('.pause-queue-btn')) {
                this.handlePauseQueue(e);
            }
            if (e.target.matches('.resume-queue-btn')) {
                this.handleResumeQueue(e);
            }
            if (e.target.matches('.clear-queue-btn')) {
                this.handleClearQueue(e);
            }
        });
        
        // 任務操作按鈕
        document.addEventListener('click', (e) => {
            if (e.target.matches('.cancel-task-btn')) {
                this.handleCancelTask(e);
            }
            if (e.target.matches('.retry-task-btn')) {
                this.handleRetryTask(e);
            }
            if (e.target.matches('.priority-up-btn')) {
                this.handleChangePriority(e, 'up');
            }
            if (e.target.matches('.priority-down-btn')) {
                this.handleChangePriority(e, 'down');
            }
        });
        
        // 篩選器
        document.addEventListener('change', (e) => {
            if (e.target.matches('.queue-filter')) {
                this.handleFilterChange(e);
            }
        });
        
        // 批次操作
        document.addEventListener('click', (e) => {
            if (e.target.matches('.select-all-btn')) {
                this.handleSelectAll();
            }
            if (e.target.matches('.batch-cancel-btn')) {
                this.handleBatchCancel();
            }
            if (e.target.matches('.batch-retry-btn')) {
                this.handleBatchRetry();
            }
        });
        
        // 任務詳情
        document.addEventListener('click', (e) => {
            if (e.target.matches('.task-details-btn')) {
                this.handleShowTaskDetails(e);
            }
        });
        
        // 手動刷新
        document.addEventListener('click', (e) => {
            if (e.target.matches('.refresh-queue-btn')) {
                this.handleRefresh();
            }
        });
    }
    
    initWebSocket() {
        try {
            const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
            const wsUrl = `${protocol}//${window.location.host}/ws/task-queue`;
            
            this.websocket = new WebSocket(wsUrl);
            
            this.websocket.onopen = () => {
                console.log('Task Queue WebSocket connected');
                this.updateConnectionStatus(true);
            };
            
            this.websocket.onmessage = (event) => {
                const data = JSON.parse(event.data);
                this.handleWebSocketMessage(data);
            };
            
            this.websocket.onclose = () => {
                console.log('Task Queue WebSocket disconnected');
                this.updateConnectionStatus(false);
                
                // 自動重連
                setTimeout(() => {
                    this.initWebSocket();
                }, 5000);
            };
            
            this.websocket.onerror = (error) => {
                console.error('Task Queue WebSocket error:', error);
            };
            
        } catch (error) {
            console.error('Failed to initialize WebSocket:', error);
        }
    }
    
    handleWebSocketMessage(data) {
        switch (data.type) {
            case 'queue_update':
                this.updateQueueData(data.payload);
                break;
            case 'task_status':
                this.updateTaskStatus(data.payload);
                break;
            case 'queue_stats':
                this.updateQueueStats(data.payload);
                break;
            case 'worker_status':
                this.updateWorkerStatus(data.payload);
                break;
            default:
                console.log('Unknown message type:', data.type);
        }
    }
    
    async loadQueueData() {
        try {
            const response = await fetch('/tasks/api/queue/status');
            
            if (response.ok) {
                const data = await response.json();
                this.queueData = data.queues;
                this.queueStats = data.stats;
                this.displayQueueData();
                this.updateStatistics();
            }
        } catch (error) {
            console.error('Failed to load queue data:', error);
            this.showError('載入隊列資料失敗');
        }
    }
    
    displayQueueData() {
        // 顯示不同優先級的隊列
        this.displayHighPriorityQueue();
        this.displayNormalPriorityQueue();
        this.displayLowPriorityQueue();
        
        // 顯示正在執行的任務
        this.displayRunningTasks();
        
        // 顯示已完成任務
        this.displayCompletedTasks();
    }
    
    displayHighPriorityQueue() {
        const container = document.querySelector('.high-priority-queue');
        if (!container) return;
        
        const tasks = this.queueData.high || [];
        this.renderQueueTasks(container, tasks, 'high');
    }
    
    displayNormalPriorityQueue() {
        const container = document.querySelector('.normal-priority-queue');
        if (!container) return;
        
        const tasks = this.queueData.normal || [];
        this.renderQueueTasks(container, tasks, 'normal');
    }
    
    displayLowPriorityQueue() {
        const container = document.querySelector('.low-priority-queue');
        if (!container) return;
        
        const tasks = this.queueData.low || [];
        this.renderQueueTasks(container, tasks, 'low');
    }
    
    displayRunningTasks() {
        const container = document.querySelector('.running-tasks');
        if (!container) return;
        
        const tasks = this.queueData.running || [];
        this.renderRunningTasks(container, tasks);
    }
    
    displayCompletedTasks() {
        const container = document.querySelector('.completed-tasks');
        if (!container) return;
        
        const tasks = this.queueData.completed || [];
        this.renderCompletedTasks(container, tasks);
    }
    
    renderQueueTasks(container, tasks, priority) {
        container.innerHTML = '';
        
        if (tasks.length === 0) {
            container.innerHTML = `
                <div class="no-tasks">
                    <p>目前沒有${this.getPriorityText(priority)}任務</p>
                </div>
            `;
            return;
        }
        
        tasks.forEach(task => {
            const taskElement = this.createQueueTaskElement(task, priority);
            container.appendChild(taskElement);
        });
    }
    
    renderRunningTasks(container, tasks) {
        container.innerHTML = '';
        
        if (tasks.length === 0) {
            container.innerHTML = `
                <div class="no-tasks">
                    <p>目前沒有執行中的任務</p>
                </div>
            `;
            return;
        }
        
        tasks.forEach(task => {
            const taskElement = this.createRunningTaskElement(task);
            container.appendChild(taskElement);
        });
    }
    
    renderCompletedTasks(container, tasks) {
        container.innerHTML = '';
        
        if (tasks.length === 0) {
            container.innerHTML = `
                <div class="no-tasks">
                    <p>目前沒有已完成的任務</p>
                </div>
            `;
            return;
        }
        
        // 只顯示最近的20個任務
        const recentTasks = tasks.slice(0, 20);
        
        recentTasks.forEach(task => {
            const taskElement = this.createCompletedTaskElement(task);
            container.appendChild(taskElement);
        });
    }
    
    createQueueTaskElement(task, priority) {
        const element = document.createElement('div');
        element.className = `queue-task-item priority-${priority}`;
        element.dataset.taskId = task.id;
        
        const waitTime = this.calculateWaitTime(task.created_at);
        
        element.innerHTML = `
            <div class="task-checkbox">
                <input type="checkbox" class="task-select" data-task-id="${task.id}">
            </div>
            <div class="task-info">
                <div class="task-header">
                    <h6 class="task-name">${task.name}</h6>
                    <span class="task-type">${this.getTaskTypeText(task.type)}</span>
                </div>
                <div class="task-meta">
                    <span class="task-wait-time">等待時間：${waitTime}</span>
                    <span class="task-position">位置：#${task.position}</span>
                    <span class="task-created">創建時間：${this.formatDateTime(task.created_at)}</span>
                </div>
                ${task.description ? `<div class="task-description">${task.description}</div>` : ''}
            </div>
            <div class="task-actions">
                <button class="btn btn-sm btn-info task-details-btn" data-task-id="${task.id}" title="詳情">
                    <i class="fas fa-info"></i>
                </button>
                <button class="btn btn-sm btn-success priority-up-btn" data-task-id="${task.id}" title="提高優先級">
                    <i class="fas fa-arrow-up"></i>
                </button>
                <button class="btn btn-sm btn-warning priority-down-btn" data-task-id="${task.id}" title="降低優先級">
                    <i class="fas fa-arrow-down"></i>
                </button>
                <button class="btn btn-sm btn-danger cancel-task-btn" data-task-id="${task.id}" title="取消">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        `;
        
        return element;
    }
    
    createRunningTaskElement(task) {
        const element = document.createElement('div');
        element.className = 'running-task-item';
        element.dataset.taskId = task.id;
        
        const progress = task.progress || 0;
        const runtime = this.calculateRuntime(task.started_at);
        
        element.innerHTML = `
            <div class="task-info">
                <div class="task-header">
                    <h6 class="task-name">${task.name}</h6>
                    <span class="task-worker">執行者：${task.worker_id}</span>
                </div>
                <div class="task-progress">
                    <div class="progress">
                        <div class="progress-bar" style="width: ${progress}%"></div>
                    </div>
                    <span class="progress-text">${progress}%</span>
                </div>
                <div class="task-meta">
                    <span class="task-runtime">運行時間：${runtime}</span>
                    <span class="task-started">開始時間：${this.formatDateTime(task.started_at)}</span>
                </div>
                ${task.current_step ? `<div class="task-current-step">當前步驟：${task.current_step}</div>` : ''}
            </div>
            <div class="task-actions">
                <button class="btn btn-sm btn-info task-details-btn" data-task-id="${task.id}" title="詳情">
                    <i class="fas fa-info"></i>
                </button>
                <button class="btn btn-sm btn-danger cancel-task-btn" data-task-id="${task.id}" title="取消執行">
                    <i class="fas fa-stop"></i>
                </button>
            </div>
        `;
        
        return element;
    }
    
    createCompletedTaskElement(task) {
        const element = document.createElement('div');
        element.className = `completed-task-item status-${task.status}`;
        element.dataset.taskId = task.id;
        
        const duration = task.duration ? `${task.duration.toFixed(2)}秒` : 'N/A';
        const statusClass = this.getTaskStatusClass(task.status);
        
        element.innerHTML = `
            <div class="task-info">
                <div class="task-header">
                    <h6 class="task-name">${task.name}</h6>
                    <span class="task-status status-${statusClass}">${this.getStatusText(task.status)}</span>
                </div>
                <div class="task-meta">
                    <span class="task-duration">執行時間：${duration}</span>
                    <span class="task-completed">完成時間：${this.formatDateTime(task.completed_at)}</span>
                </div>
                ${task.error ? `<div class="task-error">錯誤：${task.error}</div>` : ''}
                ${task.result_summary ? `<div class="task-result">結果：${task.result_summary}</div>` : ''}
            </div>
            <div class="task-actions">
                <button class="btn btn-sm btn-info task-details-btn" data-task-id="${task.id}" title="詳情">
                    <i class="fas fa-info"></i>
                </button>
                ${task.status === 'failed' ? `
                    <button class="btn btn-sm btn-warning retry-task-btn" data-task-id="${task.id}" title="重試">
                        <i class="fas fa-redo"></i>
                    </button>
                ` : ''}
            </div>
        `;
        
        return element;
    }
    
    updateStatistics() {
        const stats = this.queueStats;
        if (!stats) return;
        
        this.updateElement('.stat-total-queued', stats.total_queued || 0);
        this.updateElement('.stat-running-tasks', stats.running_tasks || 0);
        this.updateElement('.stat-completed-today', stats.completed_today || 0);
        this.updateElement('.stat-failed-today', stats.failed_today || 0);
        this.updateElement('.stat-avg-wait-time', this.formatDuration(stats.avg_wait_time || 0));
        this.updateElement('.stat-avg-execution-time', this.formatDuration(stats.avg_execution_time || 0));
        
        // 更新工作者狀態
        this.updateWorkerStatistics();
        
        // 更新圖表
        this.updateCharts();
    }
    
    updateWorkerStatistics() {
        const workers = this.queueStats.workers || [];
        const container = document.querySelector('.worker-status-grid');
        
        if (!container) return;
        
        container.innerHTML = '';
        
        workers.forEach(worker => {
            const workerElement = this.createWorkerElement(worker);
            container.appendChild(workerElement);
        });
    }
    
    createWorkerElement(worker) {
        const element = document.createElement('div');
        element.className = `worker-item status-${worker.status}`;
        
        element.innerHTML = `
            <div class="worker-header">
                <h6 class="worker-id">${worker.id}</h6>
                <span class="worker-status status-${worker.status}">${this.getWorkerStatusText(worker.status)}</span>
            </div>
            <div class="worker-stats">
                <div class="stat">
                    <span class="stat-value">${worker.tasks_completed || 0}</span>
                    <span class="stat-label">已完成</span>
                </div>
                <div class="stat">
                    <span class="stat-value">${worker.uptime ? this.formatDuration(worker.uptime) : 'N/A'}</span>
                    <span class="stat-label">運行時間</span>
                </div>
            </div>
            ${worker.current_task ? `
                <div class="worker-current-task">
                    <small>執行中：${worker.current_task}</small>
                </div>
            ` : ''}
        `;
        
        return element;
    }
    
    setupCharts() {
        this.setupQueueTrendChart();
        this.setupTaskDistributionChart();
        this.setupPerformanceChart();
    }
    
    setupQueueTrendChart() {
        const ctx = document.getElementById('queueTrendChart');
        if (!ctx) return;
        
        this.charts.queueTrend = new Chart(ctx, {
            type: 'line',
            data: {
                labels: [],
                datasets: [
                    {
                        label: '隊列中',
                        data: [],
                        borderColor: '#007bff',
                        backgroundColor: 'rgba(0, 123, 255, 0.1)',
                        tension: 0.4
                    },
                    {
                        label: '執行中',
                        data: [],
                        borderColor: '#28a745',
                        backgroundColor: 'rgba(40, 167, 69, 0.1)',
                        tension: 0.4
                    }
                ]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                animation: { duration: 0 },
                scales: {
                    y: {
                        beginAtZero: true
                    }
                }
            }
        });
    }
    
    setupTaskDistributionChart() {
        const ctx = document.getElementById('taskDistributionChart');
        if (!ctx) return;
        
        this.charts.taskDistribution = new Chart(ctx, {
            type: 'doughnut',
            data: {
                labels: ['高優先級', '普通優先級', '低優先級'],
                datasets: [{
                    data: [],
                    backgroundColor: ['#dc3545', '#007bff', '#6c757d']
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom'
                    }
                }
            }
        });
    }
    
    updateCharts() {
        // 更新隊列趨勢圖
        if (this.charts.queueTrend && this.queueStats.trend_data) {
            const trend = this.queueStats.trend_data;
            this.charts.queueTrend.data.labels = trend.labels || [];
            this.charts.queueTrend.data.datasets[0].data = trend.queued || [];
            this.charts.queueTrend.data.datasets[1].data = trend.running || [];
            this.charts.queueTrend.update('none');
        }
        
        // 更新任務分佈圖
        if (this.charts.taskDistribution && this.queueStats.distribution) {
            const dist = this.queueStats.distribution;
            this.charts.taskDistribution.data.datasets[0].data = [
                dist.high || 0,
                dist.normal || 0,
                dist.low || 0
            ];
            this.charts.taskDistribution.update('none');
        }
    }
    
    async handlePauseQueue(e) {
        const priority = e.target.dataset.priority || 'all';
        
        try {
            const response = await fetch(`/tasks/api/queue/pause?priority=${priority}`, {
                method: 'POST'
            });
            
            if (response.ok) {
                this.showSuccess(`${this.getPriorityText(priority)}隊列已暫停`);
                this.loadQueueData();
            } else {
                throw new Error('暫停失敗');
            }
        } catch (error) {
            console.error('Pause queue error:', error);
            this.showError('暫停隊列失敗：' + error.message);
        }
    }
    
    async handleResumeQueue(e) {
        const priority = e.target.dataset.priority || 'all';
        
        try {
            const response = await fetch(`/tasks/api/queue/resume?priority=${priority}`, {
                method: 'POST'
            });
            
            if (response.ok) {
                this.showSuccess(`${this.getPriorityText(priority)}隊列已恢復`);
                this.loadQueueData();
            } else {
                throw new Error('恢復失敗');
            }
        } catch (error) {
            console.error('Resume queue error:', error);
            this.showError('恢復隊列失敗：' + error.message);
        }
    }
    
    async handleClearQueue(e) {
        const priority = e.target.dataset.priority || 'all';
        
        if (!confirm(`確定要清空${this.getPriorityText(priority)}隊列嗎？`)) return;
        
        try {
            const response = await fetch(`/tasks/api/queue/clear?priority=${priority}`, {
                method: 'POST'
            });
            
            if (response.ok) {
                this.showSuccess(`${this.getPriorityText(priority)}隊列已清空`);
                this.loadQueueData();
            } else {
                throw new Error('清空失敗');
            }
        } catch (error) {
            console.error('Clear queue error:', error);
            this.showError('清空隊列失敗：' + error.message);
        }
    }
    
    async handleCancelTask(e) {
        const taskId = e.target.closest('button').dataset.taskId;
        
        if (!confirm('確定要取消這個任務嗎？')) return;
        
        try {
            const response = await fetch(`/tasks/api/queue/tasks/${taskId}/cancel`, {
                method: 'POST'
            });
            
            if (response.ok) {
                this.showSuccess('任務已取消');
                this.loadQueueData();
            } else {
                throw new Error('取消失敗');
            }
        } catch (error) {
            console.error('Cancel task error:', error);
            this.showError('取消任務失敗：' + error.message);
        }
    }
    
    async handleRetryTask(e) {
        const taskId = e.target.closest('button').dataset.taskId;
        
        try {
            const response = await fetch(`/tasks/api/queue/tasks/${taskId}/retry`, {
                method: 'POST'
            });
            
            if (response.ok) {
                this.showSuccess('任務已重新加入隊列');
                this.loadQueueData();
            } else {
                throw new Error('重試失敗');
            }
        } catch (error) {
            console.error('Retry task error:', error);
            this.showError('重試任務失敗：' + error.message);
        }
    }
    
    async handleChangePriority(e, direction) {
        const taskId = e.target.closest('button').dataset.taskId;
        
        try {
            const response = await fetch(`/tasks/api/queue/tasks/${taskId}/priority`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ direction: direction })
            });
            
            if (response.ok) {
                this.showSuccess('優先級已更新');
                this.loadQueueData();
            } else {
                throw new Error('更新失敗');
            }
        } catch (error) {
            console.error('Change priority error:', error);
            this.showError('更新優先級失敗：' + error.message);
        }
    }
    
    handleSelectAll() {
        const checkboxes = document.querySelectorAll('.task-select');
        const allChecked = Array.from(checkboxes).every(cb => cb.checked);
        
        checkboxes.forEach(checkbox => {
            checkbox.checked = !allChecked;
        });
        
        this.updateBatchButtonsState();
    }
    
    updateBatchButtonsState() {
        const selectedTasks = document.querySelectorAll('.task-select:checked');
        const batchButtons = document.querySelectorAll('.batch-cancel-btn, .batch-retry-btn');
        
        batchButtons.forEach(button => {
            button.disabled = selectedTasks.length === 0;
        });
        
        const selectedCount = selectedTasks.length;
        const countElement = document.querySelector('.selected-count');
        if (countElement) {
            countElement.textContent = `已選擇 ${selectedCount} 個任務`;
        }
    }
    
    async handleShowTaskDetails(e) {
        const taskId = e.target.closest('button').dataset.taskId;
        
        try {
            const response = await fetch(`/tasks/api/queue/tasks/${taskId}/details`);
            
            if (response.ok) {
                const details = await response.json();
                this.showTaskDetailsModal(details);
            } else {
                throw new Error('載入失敗');
            }
        } catch (error) {
            console.error('Load task details error:', error);
            this.showError('載入任務詳情失敗：' + error.message);
        }
    }
    
    showTaskDetailsModal(details) {
        const modal = document.getElementById('taskDetailsModal');
        if (!modal) return;
        
        const content = modal.querySelector('.task-details-content');
        if (content) {
            content.innerHTML = this.renderTaskDetailsHTML(details);
        }
        
        const modalInstance = new bootstrap.Modal(modal);
        modalInstance.show();
    }
    
    renderTaskDetailsHTML(details) {
        return `
            <div class="task-details">
                <h5>${details.name}</h5>
                <div class="detail-row">
                    <span class="label">ID：</span>
                    <span class="value">${details.id}</span>
                </div>
                <div class="detail-row">
                    <span class="label">類型：</span>
                    <span class="value">${this.getTaskTypeText(details.type)}</span>
                </div>
                <div class="detail-row">
                    <span class="label">狀態：</span>
                    <span class="value">${this.getStatusText(details.status)}</span>
                </div>
                <div class="detail-row">
                    <span class="label">優先級：</span>
                    <span class="value">${this.getPriorityText(details.priority)}</span>
                </div>
                <div class="detail-row">
                    <span class="label">創建時間：</span>
                    <span class="value">${this.formatDateTime(details.created_at)}</span>
                </div>
                ${details.started_at ? `
                    <div class="detail-row">
                        <span class="label">開始時間：</span>
                        <span class="value">${this.formatDateTime(details.started_at)}</span>
                    </div>
                ` : ''}
                ${details.completed_at ? `
                    <div class="detail-row">
                        <span class="label">完成時間：</span>
                        <span class="value">${this.formatDateTime(details.completed_at)}</span>
                    </div>
                ` : ''}
                ${details.description ? `
                    <div class="detail-section">
                        <h6>描述</h6>
                        <p>${details.description}</p>
                    </div>
                ` : ''}
                ${details.parameters ? `
                    <div class="detail-section">
                        <h6>參數</h6>
                        <pre class="json-display">${JSON.stringify(details.parameters, null, 2)}</pre>
                    </div>
                ` : ''}
                ${details.error ? `
                    <div class="detail-section error">
                        <h6>錯誤訊息</h6>
                        <pre class="error-display">${details.error}</pre>
                    </div>
                ` : ''}
                ${details.result ? `
                    <div class="detail-section">
                        <h6>執行結果</h6>
                        <pre class="result-display">${JSON.stringify(details.result, null, 2)}</pre>
                    </div>
                ` : ''}
            </div>
        `;
    }
    
    startAutoRefresh() {
        this.intervalId = setInterval(() => {
            this.loadQueueData();
        }, this.refreshInterval);
    }
    
    stopAutoRefresh() {
        if (this.intervalId) {
            clearInterval(this.intervalId);
            this.intervalId = null;
        }
    }
    
    handleRefresh() {
        this.loadQueueData();
        this.showSuccess('隊列資料已刷新');
    }
    
    updateConnectionStatus(connected) {
        const indicator = document.querySelector('.websocket-status');
        if (indicator) {
            indicator.className = `websocket-status ${connected ? 'connected' : 'disconnected'}`;
            indicator.textContent = connected ? 'WebSocket 已連接' : 'WebSocket 連接中斷';
        }
    }
    
    updateQueueData(data) {
        this.queueData = data;
        this.displayQueueData();
    }
    
    updateTaskStatus(data) {
        // 更新特定任務的狀態
        const taskElement = document.querySelector(`[data-task-id="${data.task_id}"]`);
        if (taskElement) {
            // 重新載入整個列表以確保一致性
            this.loadQueueData();
        }
    }
    
    updateQueueStats(stats) {
        this.queueStats = stats;
        this.updateStatistics();
    }
    
    updateWorkerStatus(data) {
        // 更新工作者狀態
        this.updateWorkerStatistics();
    }
    
    calculateWaitTime(createdAt) {
        const created = new Date(createdAt);
        const now = new Date();
        const diff = now - created;
        
        return this.formatDuration(diff / 1000);
    }
    
    calculateRuntime(startedAt) {
        const started = new Date(startedAt);
        const now = new Date();
        const diff = now - started;
        
        return this.formatDuration(diff / 1000);
    }
    
    formatDuration(seconds) {
        if (seconds < 60) {
            return `${Math.floor(seconds)}秒`;
        } else if (seconds < 3600) {
            return `${Math.floor(seconds / 60)}分${Math.floor(seconds % 60)}秒`;
        } else {
            return `${Math.floor(seconds / 3600)}小時${Math.floor((seconds % 3600) / 60)}分`;
        }
    }
    
    formatDateTime(dateString) {
        const date = new Date(dateString);
        return date.toLocaleDateString('zh-TW') + ' ' + date.toLocaleTimeString('zh-TW');
    }
    
    getPriorityText(priority) {
        const priorityMap = {
            'high': '高優先級',
            'normal': '普通優先級',
            'low': '低優先級',
            'all': '全部'
        };
        
        return priorityMap[priority] || priority;
    }
    
    getTaskTypeText(type) {
        const typeMap = {
            'email_processing': '郵件處理',
            'data_analysis': '資料分析',
            'file_processing': '檔案處理',
            'report_generation': '報告生成',
            'system_maintenance': '系統維護',
            'backup': '備份作業',
            'custom': '自訂任務'
        };
        
        return typeMap[type] || type;
    }
    
    getStatusText(status) {
        const statusMap = {
            'pending': '等待中',
            'running': '執行中',
            'completed': '已完成',
            'failed': '失敗',
            'cancelled': '已取消',
            'retrying': '重試中'
        };
        
        return statusMap[status] || status;
    }
    
    getTaskStatusClass(status) {
        const statusMap = {
            'pending': 'warning',
            'running': 'info',
            'completed': 'success',
            'failed': 'danger',
            'cancelled': 'secondary',
            'retrying': 'warning'
        };
        
        return statusMap[status] || 'secondary';
    }
    
    getWorkerStatusText(status) {
        const statusMap = {
            'idle': '閒置',
            'busy': '忙碌',
            'offline': '離線',
            'error': '錯誤'
        };
        
        return statusMap[status] || status;
    }
    
    updateElement(selector, value) {
        const element = document.querySelector(selector);
        if (element) {
            element.textContent = value;
        }
    }
    
    showSuccess(message) {
        this.showNotification(message, 'success');
    }
    
    showError(message) {
        this.showNotification(message, 'danger');
    }
    
    showNotification(message, type) {
        const notification = document.createElement('div');
        notification.className = `alert alert-${type} alert-dismissible`;
        notification.innerHTML = `
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;
        
        const container = document.querySelector('.task-queue-container');
        if (container) {
            container.insertBefore(notification, container.firstChild);
        }
        
        // 自動移除通知
        setTimeout(() => {
            if (notification.parentNode) {
                notification.remove();
            }
        }, 5000);
    }
    
    destroy() {
        this.stopAutoRefresh();
        if (this.websocket) {
            this.websocket.close();
        }
    }
}

// 頁面載入完成後初始化
document.addEventListener('DOMContentLoaded', function() {
    if (document.querySelector('.task-queue-container')) {
        window.taskQueueManager = new TaskQueueManager();
    }
});

// 頁面卸載時清理
window.addEventListener('beforeunload', function() {
    if (window.taskQueueManager) {
        window.taskQueueManager.destroy();
    }
});