/* 資料庫管理頁面樣式 */

.db-manager-container {
    min-height: 100vh;
    background-color: #f5f7fa;
}

/* 頂部標題 */
.db-header {
    background-color: white;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    padding: 1rem 2rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.header-left {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.header-left h1 {
    margin: 0;
    color: #333;
    font-size: 1.5rem;
}

.db-info {
    display: flex;
    gap: 2rem;
    font-size: 0.9rem;
    color: #666;
}

.info-item strong {
    color: #333;
}

/* 表格選擇器 */
.table-selector {
    background-color: white;
    padding: 1.5rem 2rem;
    margin: 1rem 2rem;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    display: flex;
    align-items: center;
    gap: 1rem;
}

.form-select {
    padding: 0.5rem 1rem;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 1rem;
    min-width: 300px;
}

/* 表格資訊 */
.table-info {
    background-color: white;
    padding: 1rem 2rem;
    margin: 0 2rem 1rem;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.table-info h3 {
    margin: 0 0 0.5rem;
    color: #333;
}

.table-stats {
    display: flex;
    gap: 2rem;
    font-size: 0.9rem;
    color: #666;
}

.table-stats strong {
    color: #333;
}

/* SQL 查詢區 */
.sql-query-section {
    background-color: white;
    padding: 1.5rem 2rem;
    margin: 0 2rem 1rem;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.sql-query-section h3 {
    margin: 0 0 1rem;
    color: #333;
}

.query-container {
    display: flex;
    gap: 1rem;
    align-items: flex-start;
}

#sql-query {
    flex: 1;
    padding: 0.5rem;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-family: 'Courier New', monospace;
    resize: vertical;
    min-height: 60px;
}

#execute-query-btn {
    flex-shrink: 0;
}

.error-message {
    color: #d9534f;
    padding: 0.5rem;
    margin-top: 0.5rem;
    background-color: #f2dede;
    border: 1px solid #ebccd1;
    border-radius: 4px;
}

/* 資料容器 */
.data-container {
    background-color: white;
    padding: 1rem;
    margin: 0 2rem 2rem;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    min-height: 300px;
    overflow-x: auto;
    overflow-y: visible;
}

/* DataTable 樣式覆蓋 - 恢復到能正常工作的版本 */
.dataTable {
    font-size: 0.9rem;
    border-collapse: collapse;
    width: 100%;
}

.dataTable th {
    background-color: #f8f9fa;
    font-weight: 600;
    color: #333;
    border: 1px solid #dee2e6;
    padding: 12px 8px;
    text-align: left;
}

.dataTable td {
    vertical-align: middle;
    border: 1px solid #dee2e6;
    padding: 8px;
    background-color: #fff;
}

/* 操作按鈕欄位特殊處理 */
#data-table .action-buttons {
    white-space: nowrap;
    text-align: center;
}

/* 選擇框欄位 */
#data-table .select-checkbox {
    text-align: center;
    width: 50px !important;
    min-width: 50px !important;
    max-width: 50px !important;
}

/* 移除固定的欄位寬度設定，改用動態設定 */

/* DataTables 特定樣式改進 */
.dataTables_wrapper .dataTables_length,
.dataTables_wrapper .dataTables_filter,
.dataTables_wrapper .dataTables_info,
.dataTables_wrapper .dataTables_paginate {
    margin: 10px 0;
}

/* 表格行的懸停效果 */
#data-table tbody tr:hover {
    background-color: #f8f9fa;
}

/* 確保操作按鈕在同一行 */
.action-buttons {
    white-space: nowrap;
}

.action-buttons .btn-view-detail,
.action-buttons .btn-delete-row {
    margin: 0 2px;
    padding: 4px 8px;
    font-size: 12px;
}

/* 滑鼠懸停時顯示完整內容 */
#data-table td:hover {
    overflow: visible;
    white-space: normal;
    position: relative;
    z-index: 10;
    background-color: #f8f9fa;
    box-shadow: 0 2px 8px rgba(0,0,0,0.15);
}

/* 確保表格在容器中正確顯示 */
.dataTables_wrapper {
    width: 100%;
    margin: 0;
}

/* 改善分頁控制項的樣式 */
.dataTables_paginate .paginate_button {
    padding: 6px 12px;
    margin: 0 2px;
    border: 1px solid #ddd;
    border-radius: 4px;
}

.dataTables_paginate .paginate_button.current {
    background-color: #007bff;
    color: white !important;
    border-color: #007bff;
}

/* 載入動畫 */
.loading {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    min-height: 300px;
}

.loading-spinner {
    width: 40px;
    height: 40px;
    border: 4px solid #f3f3f3;
    border-top: 4px solid #4285f4;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* DataTable 樣式覆蓋 */
.dataTable {
    font-size: 0.85rem;
    border-collapse: collapse;
    width: 100%;
    table-layout: fixed; /* 固定表格布局確保欄位寬度穩定 */
}

/* 優化欄位寬度設置 - 提高可讀性和視覺效果 */
table.dataTable th:nth-child(1) { width: 50px; min-width: 50px; max-width: 50px; } /* 選擇框 */
table.dataTable th:nth-child(2) { width: 50px; min-width: 50px; max-width: 50px; } /* ID */
table.dataTable th:nth-child(3) { width: 220px; min-width: 220px; max-width: 220px; } /* 寄件者 */
table.dataTable th:nth-child(4) { width: 100px; min-width: 100px; max-width: 100px; } /* 寄件者名稱 */
table.dataTable th:nth-child(5) { width: 250px; min-width: 200px; max-width: 300px; } /* 主旨 */
table.dataTable th:nth-child(6) { width: 120px; min-width: 110px; max-width: 130px; } /* 接收時間 */
table.dataTable th:nth-child(7) { width: 120px; min-width: 110px; max-width: 130px; } /* 創建時間 */
table.dataTable th:nth-child(8) { width: 80px; min-width: 70px; max-width: 90px; } /* is_processed */
table.dataTable th:nth-child(9) { width: 100px; min-width: 90px; max-width: 110px; } /* 產品編號 */
table.dataTable th:nth-child(10) { width: 120px; min-width: 100px; max-width: 140px; } /* 批次編號 */
table.dataTable th:nth-child(11) { width: 130px; min-width: 110px; max-width: 150px; } /* MO */
table.dataTable th:nth-child(12) { width: 80px; min-width: 70px; max-width: 90px; } /* yield_value */
table.dataTable th:nth-child(13) { width: 80px; min-width: 70px; max-width: 90px; } /* 廠商代碼 */
table.dataTable th:nth-child(14) { width: 120px; min-width: 110px; max-width: 130px; } /* parsed_at */
table.dataTable th:nth-child(15) { width: 80px; min-width: 70px; max-width: 90px; } /* 解析狀態 */
table.dataTable th:nth-child(16) { width: 100px; min-width: 90px; max-width: 110px; } /* parse_error */
table.dataTable th:nth-child(17) { width: 80px; min-width: 70px; max-width: 90px; } /* 解析方法 */
table.dataTable th:nth-child(18) { width: 120px !important; min-width: 100px !important; max-width: 140px !important; } /* llm_analysis_result */
table.dataTable th:nth-child(19) { width: 120px; min-width: 110px; max-width: 130px; } /* llm_analysis_timestamp */
table.dataTable th:nth-child(20) { width: 100px; min-width: 90px; max-width: 110px; } /* llm_service_used */
table.dataTable th:nth-child(21) { width: 100px; min-width: 90px; max-width: 110px; } /* 操作 */

/* 對應的表格內容設置相同寬度 - 與標題保持一致 */
table.dataTable td:nth-child(1) { width: 50px; min-width: 50px; max-width: 50px; text-align: center; }
table.dataTable td:nth-child(2) { width: 50px; min-width: 50px; max-width: 50px; text-align: center; }
table.dataTable td:nth-child(3) { width: 220px; min-width: 220px; max-width: 220px; }
table.dataTable td:nth-child(4) { width: 100px; min-width: 100px; max-width: 100px; }
table.dataTable td:nth-child(5) { width: 250px; min-width: 200px; max-width: 300px; }
table.dataTable td:nth-child(6) { width: 120px; min-width: 110px; max-width: 130px; }
table.dataTable td:nth-child(7) { width: 120px; min-width: 110px; max-width: 130px; }
table.dataTable td:nth-child(8) { width: 80px; min-width: 70px; max-width: 90px; text-align: center; }
table.dataTable td:nth-child(9) { width: 100px; min-width: 90px; max-width: 110px; }
table.dataTable td:nth-child(10) { width: 120px; min-width: 100px; max-width: 140px; }
table.dataTable td:nth-child(11) { width: 130px; min-width: 110px; max-width: 150px; }
table.dataTable td:nth-child(12) { width: 80px; min-width: 70px; max-width: 90px; text-align: center; }
table.dataTable td:nth-child(13) { width: 80px; min-width: 70px; max-width: 90px; text-align: center; }
table.dataTable td:nth-child(14) { width: 120px; min-width: 110px; max-width: 130px; }
table.dataTable td:nth-child(15) { width: 80px; min-width: 70px; max-width: 90px; text-align: center; }
table.dataTable td:nth-child(16) { width: 100px; min-width: 90px; max-width: 110px; }
table.dataTable td:nth-child(17) { width: 80px; min-width: 70px; max-width: 90px; text-align: center; }
table.dataTable td:nth-child(18) { 
    width: 120px !important; 
    min-width: 100px !important; 
    max-width: 140px !important; 
    white-space: nowrap !important;
    overflow: hidden !important;
    text-overflow: ellipsis !important;
}
table.dataTable td:nth-child(19) { width: 120px; min-width: 110px; max-width: 130px; }
table.dataTable td:nth-child(20) { width: 100px; min-width: 90px; max-width: 110px; }
table.dataTable td:nth-child(21) { width: 100px; min-width: 90px; max-width: 110px; text-align: center; }

/* 文字截斷和懸停顯示 */
.dataTable td {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    position: relative;
}

/* 特定欄位允許換行 */
.dataTable td:nth-child(3),  /* 寄件者 */
.dataTable td:nth-child(5),  /* 主旨 */
.dataTable td:nth-child(10), /* 批次編號 */
.dataTable td:nth-child(11), /* MO */
.dataTable td:nth-child(16), /* parse_error */
.dataTable td:nth-child(18)  /* llm_analysis_result */ {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

/* 懸停顯示完整內容的工具提示 */
.dataTable td[title]:hover::after {
    content: attr(title);
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background-color: rgba(0, 0, 0, 0.9);
    color: white;
    padding: 12px 16px;
    border-radius: 6px;
    font-size: 13px;
    white-space: pre-wrap;
    word-wrap: break-word;
    max-width: 500px;
    max-height: 400px;
    overflow-y: auto;
    z-index: 9999;
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.3);
    pointer-events: none;
    border: 1px solid rgba(255, 255, 255, 0.2);
}

/* 讓tooltip在中心位置更容易看到 */
.dataTable td[title]:hover {
    position: relative;
    z-index: 1001;
}

/* 避免工具提示被表格容器裁切 */
.data-container {
    position: relative;
    overflow: visible;
}

.dataTable th {
    background-color: #f8f9fa;
    font-weight: 600;
    color: #333;
    border: 1px solid #dee2e6;
    padding: 8px 6px; /* 減少內邊距節省空間 */
    text-align: left;
    font-size: 0.8rem; /* 稍小的標題字體 */
    line-height: 1.2;
}

.dataTable td {
    vertical-align: middle;
    border: 1px solid #dee2e6;
    padding: 6px 4px; /* 減少內邊距 */
    background-color: #fff;
    font-size: 0.8rem; /* 統一較小字體 */
    line-height: 1.3;
}

/* 斑馬紋效果 */
.dataTable tbody tr:nth-child(even) td {
    background-color: #f9f9f9;
}

/* 滑鼠懸停效果 */
.dataTable tbody tr:hover td {
    background-color: #e3f2fd;
}

/* 移除表格標題固定效果，讓標題跟著滾動 */
.dataTable thead th {
    /* position: sticky; */
    /* top: 0; */
    z-index: 10;
    box-shadow: 0 2px 2px -1px rgba(0, 0, 0, 0.1);
}

/* 分頁控制樣式 */
.dataTables_wrapper .dataTables_paginate {
    display: block !important;
    margin-top: 1rem;
    text-align: right;
}

.dataTables_wrapper .dataTables_paginate .paginate_button {
    display: inline-block !important;
    padding: 0.5rem 0.75rem;
    margin: 0 2px;
    border: 1px solid #dee2e6;
    border-radius: 4px;
    background-color: #fff;
    color: #495057;
    cursor: pointer;
    text-decoration: none;
}

.dataTables_wrapper .dataTables_paginate .paginate_button:hover {
    background-color: #e9ecef;
    color: #495057;
    text-decoration: none;
}

.dataTables_wrapper .dataTables_paginate .paginate_button.current {
    background-color: #007bff;
    color: #fff;
    border-color: #007bff;
}

.dataTables_wrapper .dataTables_paginate .paginate_button.disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.dataTables_wrapper .dataTables_paginate .paginate_button.disabled:hover {
    background-color: #fff;
    color: #495057;
}

/* 搜尋框樣式 */
.dataTables_wrapper .dataTables_filter input {
    border: 1px solid #dee2e6;
    border-radius: 4px;
    padding: 0.375rem 0.75rem;
    font-size: 0.875rem;
}

/* 每頁顯示數量選擇框 */
.dataTables_wrapper .dataTables_length select {
    border: 1px solid #dee2e6;
    border-radius: 4px;
    padding: 0.375rem 0.75rem;
    font-size: 0.875rem;
}

/* 表格資訊顯示 */
.dataTables_wrapper .dataTables_info {
    padding-top: 0.75rem;
    color: #6c757d;
    font-size: 0.875rem;
    display: block !important;
}

/* 確保 DataTables 控制項正確顯示 */
.dataTables_wrapper {
    position: relative;
    clear: both;
    width: 100%;
}

.dataTables_wrapper .dataTables_length {
    float: left;
    margin-bottom: 1rem;
}

.dataTables_wrapper .dataTables_filter {
    float: right;
    margin-bottom: 1rem;
}

.dataTables_wrapper .dataTables_length,
.dataTables_wrapper .dataTables_filter,
.dataTables_wrapper .dataTables_info,
.dataTables_wrapper .dataTables_paginate {
    display: block !important;
    visibility: visible !important;
}

/* 清除浮動 */
.dataTables_wrapper:after {
    content: "";
    display: table;
    clear: both;
}

.null-value {
    color: #999;
    font-style: italic;
}

.btn-view-detail {
    padding: 0.25rem 0.75rem;
    background-color: #4285f4;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 0.85rem;
    margin-right: 5px;
}

.btn-view-detail:hover {
    background-color: #357ae8;
}

.btn-delete-row {
    padding: 0.25rem 0.75rem;
    background-color: #dc3545;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 0.85rem;
}

.btn-delete-row:hover {
    background-color: #c82333;
}

/* 選擇欄位樣式 */
.select-checkbox {
    text-align: center;
    width: 50px;
}

.select-checkbox input[type="checkbox"] {
    transform: scale(1.2);
    cursor: pointer;
}

/* 模態框 */
.modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0,0,0,0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
}

.modal-content {
    background-color: white;
    border-radius: 8px;
    width: 90%;
    max-width: 800px;
    max-height: 90vh;
    display: flex;
    flex-direction: column;
}

.modal-header {
    padding: 1rem 1.5rem;
    border-bottom: 1px solid #ddd;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.modal-header h3 {
    margin: 0;
    color: #333;
}

.close-btn {
    background: none;
    border: none;
    font-size: 1.5rem;
    cursor: pointer;
    color: #666;
    padding: 0;
    width: 30px;
    height: 30px;
}

.close-btn:hover {
    color: #333;
}

.modal-body {
    padding: 1.5rem;
    overflow-y: auto;
    flex: 1;
}

.modal-footer {
    padding: 1rem 1.5rem;
    border-top: 1px solid #ddd;
    text-align: right;
}

/* 詳情表格 */
.detail-table {
    width: 100%;
    border-collapse: collapse;
}

.detail-table tr {
    border-bottom: 1px solid #eee;
}

.detail-table td {
    padding: 0.75rem;
    vertical-align: top;
}

.detail-key {
    font-weight: 600;
    color: #666;
    width: 30%;
    background-color: #f8f9fa;
}

.detail-value {
    color: #333;
    word-break: break-word;
}

.detail-value pre {
    margin: 0;
    white-space: pre-wrap;
    font-family: 'Courier New', monospace;
    font-size: 0.9rem;
    background-color: #f8f9fa;
    padding: 0.5rem;
    border-radius: 4px;
}

/* LLM 分析結果樣式 */
.llm-analysis-container {
    padding: 0;
}

.llm-analysis-container .section {
    margin-bottom: 20px;
    padding: 15px;
    background-color: #f8f9fa;
    border-radius: 8px;
    border: 1px solid #dee2e6;
}

.llm-analysis-container .section h4 {
    margin: 0 0 15px 0;
    color: #333;
    font-size: 1.1em;
    font-weight: 600;
}

.llm-analysis-container .info-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 10px;
}

.llm-analysis-container .info-item {
    background-color: #fff;
    padding: 10px;
    border-radius: 4px;
    border: 1px solid #e9ecef;
}

.llm-analysis-container .info-item strong {
    color: #495057;
    font-weight: 600;
}

.llm-analysis-container .llm-status {
    display: flex;
    align-items: center;
    gap: 15px;
    margin-bottom: 15px;
}

.llm-analysis-container .status-badge {
    padding: 5px 10px;
    border-radius: 4px;
    font-size: 0.9em;
    font-weight: 500;
}

.llm-analysis-container .status-badge.success {
    background-color: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
}

.llm-analysis-container .status-badge.error {
    background-color: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
}

.llm-analysis-container .confidence-score {
    padding: 3px 8px;
    background-color: #e3f2fd;
    color: #1565c0;
    border-radius: 12px;
    font-size: 0.85em;
    font-weight: 500;
}

.llm-analysis-container .raw-response {
    background-color: #fff;
    border: 1px solid #e9ecef;
    border-radius: 4px;
    padding: 15px;
    max-height: 300px;
    overflow-y: auto;
}

.llm-analysis-container .raw-response pre {
    margin: 0;
    font-family: 'Courier New', monospace;
    font-size: 0.9em;
    line-height: 1.4;
    white-space: pre-wrap;
    word-wrap: break-word;
}

.llm-analysis-container .error-message {
    background-color: #f8d7da;
    color: #721c24;
    padding: 10px;
    border-radius: 4px;
    border: 1px solid #f5c6cb;
    margin-top: 15px;
}

/* 隱藏類 */
.hidden {
    display: none !important;
}

/* 響應式設計 */
@media (max-width: 768px) {
    .db-header {
        flex-direction: column;
        gap: 1rem;
        align-items: flex-start;
    }
    
    .table-selector {
        flex-direction: column;
        align-items: stretch;
    }
    
    .form-select {
        width: 100%;
    }
    
    .query-container {
        flex-direction: column;
    }
    
    .modal-content {
        width: 95%;
        margin: 1rem;
    }
}