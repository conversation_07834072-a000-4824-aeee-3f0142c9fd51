<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>檔案管理器 - Outlook Summary</title>
    <link rel="stylesheet" href="{{ url_for('files.static', filename='css/file-manager.css') }}?v={{ range(1000, 9999) | random }}">
    <link rel="stylesheet" href="{{ url_for('shared.static', filename='css/global.css') }}">
</head>
<body>
    <div class="file-manager-container">
        <header class="manager-header">
            <h1>檔案管理器</h1>
            <div class="header-actions">
                <button id="new-folder-btn" class="btn btn-secondary">
                    <span class="btn-icon">📁</span>
                    <span class="btn-text">新增資料夾</span>
                </button>
                <button id="upload-btn" class="btn btn-primary">
                    <span class="btn-icon">⬆️</span>
                    <span class="btn-text">上傳檔案</span>
                </button>
                <button id="refresh-btn" class="btn btn-outline">
                    <span class="btn-icon">🔄</span>
                    <span class="btn-text">重新整理</span>
                </button>
            </div>
        </header>

        <div class="manager-content">
            <!-- 工具列 -->
            <div class="toolbar">
                <div class="toolbar-left">
                    <div class="breadcrumb">
                        <span class="breadcrumb-item">
                            <a href="#" data-path="/">🏠 根目錄</a>
                        </span>
                        <span id="breadcrumb-path">
                            <!-- 路徑會由JavaScript動態生成 -->
                        </span>
                    </div>
                </div>
                <div class="toolbar-right">
                    <div class="view-controls">
                        <button class="view-btn active" data-view="list">
                            <span class="btn-icon">📋</span>
                        </button>
                        <button class="view-btn" data-view="grid">
                            <span class="btn-icon">⊞</span>
                        </button>
                    </div>
                    <div class="search-box">
                        <input type="text" id="file-search" placeholder="搜尋檔案...">
                        <button id="search-btn" class="btn btn-sm btn-outline">搜尋</button>
                    </div>
                    <div class="sort-controls">
                        <select id="sort-by">
                            <option value="name">名稱</option>
                            <option value="size">大小</option>
                            <option value="modified">修改時間</option>
                            <option value="type">類型</option>
                        </select>
                        <button id="sort-order-btn" class="btn btn-sm btn-outline" data-order="asc">↑</button>
                    </div>
                </div>
            </div>

            <!-- 側邊欄 -->
            <div class="manager-layout">
                <aside class="sidebar">
                    <div class="sidebar-section">
                        <h3>快速存取</h3>
                        <ul class="quick-access">
                            <li><a href="#" data-path="/inbox">📥 收件匣</a></li>
                            <li><a href="#" data-path="/attachments">📎 附件</a></li>
                            <li><a href="#" data-path="/downloads">💾 下載</a></li>
                            <li><a href="#" data-path="/temp">🗂️ 臨時檔案</a></li>
                            <li><a href="#" data-path="/processed">✅ 已處理</a></li>
                        </ul>
                    </div>
                    
                    <div class="sidebar-section">
                        <h3>檔案類型</h3>
                        <ul class="file-filters">
                            <li><a href="#" data-filter="all">📄 全部檔案</a></li>
                            <li><a href="#" data-filter="pdf">📕 PDF</a></li>
                            <li><a href="#" data-filter="excel">📊 Excel</a></li>
                            <li><a href="#" data-filter="word">📝 Word</a></li>
                            <li><a href="#" data-filter="image">🖼️ 圖片</a></li>
                            <li><a href="#" data-filter="archive">📦 壓縮檔</a></li>
                        </ul>
                    </div>
                    
                    <div class="sidebar-section">
                        <h3>儲存空間</h3>
                        <div class="storage-info">
                            <div class="storage-bar">
                                <div class="storage-used" style="width: {{ storage.used_percentage }}%"></div>
                            </div>
                            <div class="storage-text">
                                <small>已使用: {{ storage.used_gb }}GB / {{ storage.total_gb }}GB</small>
                            </div>
                        </div>
                    </div>
                </aside>

                <!-- 主要內容區 -->
                <main class="file-content">
                    <div class="content-header">
                        <div class="selection-info">
                            <span id="file-count">{{ file_count or 0 }} 個項目</span>
                            <span id="selection-count" style="display: none;"></span>
                        </div>
                        <div class="batch-actions" style="display: none;">
                            <button id="download-selected-btn" class="btn btn-sm btn-secondary">下載選取</button>
                            <button id="move-selected-btn" class="btn btn-sm btn-secondary">移動選取</button>
                            <button id="delete-selected-btn" class="btn btn-sm btn-danger">刪除選取</button>
                        </div>
                    </div>

                    <!-- 檔案列表視圖 -->
                    <div class="file-list-view active" id="list-view">
                        <table class="file-table">
                            <thead>
                                <tr>
                                    <th class="select-column">
                                        <input type="checkbox" id="select-all">
                                    </th>
                                    <th class="name-column">名稱</th>
                                    <th class="size-column">大小</th>
                                    <th class="modified-column">修改時間</th>
                                    <th class="actions-column">操作</th>
                                </tr>
                            </thead>
                            <tbody id="file-table-body">
                                {% for file in files %}
                                <tr class="file-row" data-path="{{ file.path }}" data-type="{{ file.type }}">
                                    <td>
                                        <input type="checkbox" class="file-select" value="{{ file.path }}">
                                    </td>
                                    <td class="file-name">
                                        <div class="file-info">
                                            <span class="file-icon">{{ file.icon }}</span>
                                            <span class="file-title">{{ file.name }}</span>
                                        </div>
                                    </td>
                                    <td class="file-size">{{ file.size_formatted }}</td>
                                    <td class="file-modified">{{ file.modified.strftime('%Y-%m-%d %H:%M') if file.modified else 'N/A' }}</td>
                                    <td class="file-actions">
                                        <div class="action-buttons">
                                            {% if file.type == 'folder' %}
                                            <button class="btn btn-sm btn-outline" onclick="openFolder('{{ file.path }}')">開啟</button>
                                            {% else %}
                                            <button class="btn btn-sm btn-outline" onclick="previewFile('{{ file.path }}')">預覽</button>
                                            <button class="btn btn-sm btn-secondary" onclick="downloadFile('{{ file.path }}')">下載</button>
                                            {% endif %}
                                            <div class="dropdown">
                                                <button class="btn btn-sm btn-outline dropdown-toggle">⋮</button>
                                                <div class="dropdown-menu">
                                                    <a href="#" onclick="renameFile('{{ file.path }}')">重新命名</a>
                                                    <a href="#" onclick="moveFile('{{ file.path }}')">移動</a>
                                                    <a href="#" onclick="copyFile('{{ file.path }}')">複製</a>
                                                    <div class="dropdown-divider"></div>
                                                    <a href="#" onclick="deleteFile('{{ file.path }}')">刪除</a>
                                                </div>
                                            </div>
                                        </div>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>

                    <!-- 檔案網格視圖 -->
                    <div class="file-grid-view" id="grid-view">
                        <div class="file-grid" id="file-grid">
                            {% for file in files %}
                            <div class="file-card" data-path="{{ file.path }}" data-type="{{ file.type }}">
                                <div class="file-select-wrapper">
                                    <input type="checkbox" class="file-select" value="{{ file.path }}">
                                </div>
                                <div class="file-preview">
                                    {% if file.type == 'image' %}
                                    <img src="{{ file.thumbnail_url }}" alt="{{ file.name }}" loading="lazy">
                                    {% else %}
                                    <div class="file-icon-large">{{ file.icon }}</div>
                                    {% endif %}
                                </div>
                                <div class="file-details">
                                    <div class="file-name" title="{{ file.name }}">{{ file.name }}</div>
                                    <div class="file-meta">
                                        <span class="file-size">{{ file.size_formatted }}</span>
                                        <span class="file-date">{{ file.modified.strftime('%Y-%m-%d') if file.modified else 'N/A' }}</span>
                                    </div>
                                </div>
                                <div class="file-actions">
                                    {% if file.type == 'folder' %}
                                    <button class="btn btn-sm btn-primary" onclick="openFolder('{{ file.path }}')">開啟</button>
                                    {% else %}
                                    <button class="btn btn-sm btn-outline" onclick="previewFile('{{ file.path }}')">預覽</button>
                                    {% endif %}
                                </div>
                            </div>
                            {% endfor %}
                        </div>
                    </div>

                    <!-- 分頁控制 -->
                    <div class="pagination" id="file-pagination">
                        <!-- 分頁控制會由JavaScript動態生成 -->
                    </div>
                </main>
            </div>
        </div>

        <!-- 檔案預覽模態框 -->
        <div class="modal" id="preview-modal">
            <div class="modal-content large">
                <div class="modal-header">
                    <h3 id="preview-title">檔案預覽</h3>
                    <button class="modal-close">&times;</button>
                </div>
                <div class="modal-body">
                    <div id="preview-content">
                        <!-- 預覽內容會由JavaScript動態載入 -->
                    </div>
                </div>
                <div class="modal-footer">
                    <button id="download-preview-btn" class="btn btn-primary">下載</button>
                    <button class="btn btn-secondary" onclick="closeModal('preview-modal')">關閉</button>
                </div>
            </div>
        </div>

        <!-- 上傳進度模態框 -->
        <div class="modal" id="upload-modal">
            <div class="modal-content">
                <div class="modal-header">
                    <h3>檔案上傳</h3>
                    <button class="modal-close">&times;</button>
                </div>
                <div class="modal-body">
                    <div id="upload-progress">
                        <!-- 上傳進度會由JavaScript動態顯示 -->
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 隱藏的檔案輸入 -->
    <input type="file" id="file-input" multiple hidden>

    <!-- Scripts -->
    <script src="{{ url_for('shared.static', filename='js/main.js') }}"></script>
    <script src="{{ url_for('files.static', filename='js/file-manager.js') }}"></script>
</body>
</html>