/**
 * Vendor Analysis Module
 * 供應商分析模組 JavaScript
 */

class VendorAnalysis {
    constructor() {
        this.data = {};
        this.charts = {};
        this.filters = {
            dateRange: '30d',
            category: 'all',
            status: 'all'
        };
        
        this.init();
    }
    
    init() {
        console.log('Vendor Analysis: Initializing...');
        this.bindEvents();
        this.loadAnalysisData();
        this.setupCharts();
    }
    
    bindEvents() {
        // 篩選器事件
        document.addEventListener('change', (e) => {
            if (e.target.matches('.vendor-filter')) {
                this.handleFilterChange(e);
            }
        });
        
        // 匯出按鈕
        document.addEventListener('click', (e) => {
            if (e.target.matches('.export-btn')) {
                this.handleExport(e);
            }
        });
        
        // 供應商卡片點擊
        document.addEventListener('click', (e) => {
            if (e.target.closest('.vendor-card')) {
                this.handleVendorClick(e);
            }
        });
    }
    
    async loadAnalysisData() {
        try {
            const response = await fetch('/analytics/api/vendor-analysis', {
                method: 'GET',
                headers: {
                    'Content-Type': 'application/json'
                }
            });
            
            if (response.ok) {
                this.data = await response.json();
                this.updateDisplay();
            }
        } catch (error) {
            console.error('Failed to load vendor analysis data:', error);
            this.showError('載入供應商分析資料失敗');
        }
    }
    
    handleFilterChange(e) {
        const filterType = e.target.dataset.filter;
        const value = e.target.value;
        
        this.filters[filterType] = value;
        this.applyFilters();
    }
    
    applyFilters() {
        // 重新載入資料並更新圖表
        this.loadAnalysisData();
        this.updateCharts();
    }
    
    setupCharts() {
        // 供應商分佈圓餅圖
        this.createVendorDistributionChart();
        
        // 成本趨勢折線圖
        this.createCostTrendChart();
        
        // 效能評分圖
        this.createPerformanceChart();
    }
    
    createVendorDistributionChart() {
        const ctx = document.getElementById('vendorDistributionChart');
        if (!ctx) return;
        
        this.charts.distribution = new Chart(ctx, {
            type: 'pie',
            data: {
                labels: [],
                datasets: [{
                    data: [],
                    backgroundColor: [
                        '#007bff', '#28a745', '#ffc107', 
                        '#dc3545', '#17a2b8', '#6c757d'
                    ]
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom'
                    }
                }
            }
        });
    }
    
    createCostTrendChart() {
        const ctx = document.getElementById('costTrendChart');
        if (!ctx) return;
        
        this.charts.costTrend = new Chart(ctx, {
            type: 'line',
            data: {
                labels: [],
                datasets: [{
                    label: '總成本',
                    data: [],
                    borderColor: '#007bff',
                    backgroundColor: 'rgba(0, 123, 255, 0.1)',
                    tension: 0.4
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        beginAtZero: true,
                        ticks: {
                            callback: function(value) {
                                return '$' + value.toLocaleString();
                            }
                        }
                    }
                }
            }
        });
    }
    
    createPerformanceChart() {
        const ctx = document.getElementById('performanceChart');
        if (!ctx) return;
        
        this.charts.performance = new Chart(ctx, {
            type: 'radar',
            data: {
                labels: ['品質', '交期', '價格', '服務', '穩定性'],
                datasets: [{
                    label: '平均評分',
                    data: [],
                    borderColor: '#28a745',
                    backgroundColor: 'rgba(40, 167, 69, 0.2)'
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    r: {
                        beginAtZero: true,
                        max: 5
                    }
                }
            }
        });
    }
    
    updateDisplay() {
        if (!this.data || !this.data.vendors) return;
        
        // 更新統計數據
        this.updateStatistics();
        
        // 更新供應商列表
        this.updateVendorList();
        
        // 更新圖表
        this.updateCharts();
    }
    
    updateStatistics() {
        const stats = this.data.statistics || {};
        
        // 更新總供應商數
        this.updateElement('.total-vendors', stats.total_vendors || 0);
        
        // 更新活躍供應商數
        this.updateElement('.active-vendors', stats.active_vendors || 0);
        
        // 更新總支出
        this.updateElement('.total-spending', this.formatCurrency(stats.total_spending || 0));
        
        // 更新平均評分
        this.updateElement('.average-rating', (stats.average_rating || 0).toFixed(1));
    }
    
    updateVendorList() {
        const container = document.querySelector('.vendor-list');
        if (!container || !this.data.vendors) return;
        
        container.innerHTML = '';
        
        this.data.vendors.forEach(vendor => {
            const vendorCard = this.createVendorCard(vendor);
            container.appendChild(vendorCard);
        });
    }
    
    createVendorCard(vendor) {
        const card = document.createElement('div');
        card.className = 'vendor-card';
        card.dataset.vendorId = vendor.id;
        
        card.innerHTML = `
            <div class="vendor-header">
                <h4 class="vendor-name">${vendor.name}</h4>
                <span class="vendor-status status-${vendor.status}">${vendor.status}</span>
            </div>
            <div class="vendor-stats">
                <div class="stat">
                    <span class="stat-value">${this.formatCurrency(vendor.total_spending)}</span>
                    <span class="stat-label">總支出</span>
                </div>
                <div class="stat">
                    <span class="stat-value">${vendor.order_count}</span>
                    <span class="stat-label">訂單數</span>
                </div>
                <div class="stat">
                    <span class="stat-value">${vendor.rating.toFixed(1)}</span>
                    <span class="stat-label">評分</span>
                </div>
            </div>
            <div class="vendor-categories">
                ${vendor.categories.map(cat => `<span class="category-tag">${cat}</span>`).join('')}
            </div>
        `;
        
        return card;
    }
    
    updateCharts() {
        if (!this.data.charts) return;
        
        // 更新分佈圖
        if (this.charts.distribution && this.data.charts.distribution) {
            const dist = this.data.charts.distribution;
            this.charts.distribution.data.labels = dist.labels;
            this.charts.distribution.data.datasets[0].data = dist.data;
            this.charts.distribution.update();
        }
        
        // 更新成本趨勢圖
        if (this.charts.costTrend && this.data.charts.cost_trend) {
            const trend = this.data.charts.cost_trend;
            this.charts.costTrend.data.labels = trend.labels;
            this.charts.costTrend.data.datasets[0].data = trend.data;
            this.charts.costTrend.update();
        }
        
        // 更新效能圖
        if (this.charts.performance && this.data.charts.performance) {
            const perf = this.data.charts.performance;
            this.charts.performance.data.datasets[0].data = perf.data;
            this.charts.performance.update();
        }
    }
    
    handleVendorClick(e) {
        const card = e.target.closest('.vendor-card');
        const vendorId = card.dataset.vendorId;
        
        // 顯示詳細資訊或導航到供應商詳情頁
        this.showVendorDetail(vendorId);
    }
    
    showVendorDetail(vendorId) {
        const vendor = this.data.vendors.find(v => v.id == vendorId);
        if (!vendor) return;
        
        // 創建詳情模態框或導航到詳情頁
        console.log('Show vendor detail:', vendor);
    }
    
    handleExport(e) {
        const format = e.target.dataset.format || 'excel';
        
        const params = new URLSearchParams({
            format: format,
            ...this.filters
        });
        
        const exportUrl = `/analytics/export/vendor-analysis?${params.toString()}`;
        window.open(exportUrl, '_blank');
    }
    
    updateElement(selector, value) {
        const element = document.querySelector(selector);
        if (element) {
            element.textContent = value;
        }
    }
    
    formatCurrency(amount) {
        return new Intl.NumberFormat('zh-TW', {
            style: 'currency',
            currency: 'TWD',
            minimumFractionDigits: 0
        }).format(amount);
    }
    
    showError(message) {
        console.error('Vendor Analysis Error:', message);
        
        const errorDiv = document.createElement('div');
        errorDiv.className = 'alert alert-danger';
        errorDiv.textContent = message;
        
        const container = document.querySelector('.vendor-analysis-container');
        if (container) {
            container.insertBefore(errorDiv, container.firstChild);
        }
    }
}

// 頁面載入完成後初始化
document.addEventListener('DOMContentLoaded', function() {
    if (document.querySelector('.vendor-analysis-container')) {
        new VendorAnalysis();
    }
});