#!/usr/bin/env python3
"""
Database Maintenance Script
Automated database backup, health checks, and maintenance tasks
"""

import sqlite3
import os
import shutil
import logging
from datetime import datetime, timedelta
from pathlib import Path
import json
import sys

# Add src to path for imports
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root / "src"))

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('logs/database_maintenance.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class DatabaseMaintenance:
    """Database maintenance operations"""
    
    def __init__(self, db_path="email_inbox.db"):
        self.db_path = Path(db_path)
        self.backup_dir = Path("backups")
        self.daily_backup_dir = self.backup_dir / "daily"
        self.weekly_backup_dir = self.backup_dir / "weekly"
        self.monthly_backup_dir = self.backup_dir / "monthly"
        
        # Create backup directories
        for dir_path in [self.daily_backup_dir, self.weekly_backup_dir, self.monthly_backup_dir]:
            dir_path.mkdir(parents=True, exist_ok=True)
    
    def create_backup(self, backup_type="daily"):
        """Create database backup"""
        try:
            if not self.db_path.exists():
                logger.error(f"Database file not found: {self.db_path}")
                return False
            
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            
            if backup_type == "daily":
                backup_dir = self.daily_backup_dir
            elif backup_type == "weekly":
                backup_dir = self.weekly_backup_dir
            elif backup_type == "monthly":
                backup_dir = self.monthly_backup_dir
            else:
                backup_dir = self.backup_dir / "manual"
                backup_dir.mkdir(exist_ok=True)
            
            backup_name = f"email_inbox_{backup_type}_{timestamp}.db"
            backup_path = backup_dir / backup_name
            
            # Create backup
            shutil.copy2(self.db_path, backup_path)
            
            # Verify backup
            if self.verify_backup(backup_path):
                file_size = backup_path.stat().st_size / (1024*1024)
                logger.info(f"Backup created successfully: {backup_name} ({file_size:.2f} MB)")
                return backup_path
            else:
                logger.error(f"Backup verification failed: {backup_name}")
                backup_path.unlink()  # Remove failed backup
                return False
                
        except Exception as e:
            logger.error(f"Backup creation failed: {str(e)}")
            return False
    
    def verify_backup(self, backup_path):
        """Verify backup integrity"""
        try:
            conn = sqlite3.connect(backup_path)
            cursor = conn.cursor()
            
            # Test basic queries
            cursor.execute("SELECT COUNT(*) FROM emails")
            email_count = cursor.fetchone()[0]
            
            cursor.execute("SELECT COUNT(*) FROM senders")
            sender_count = cursor.fetchone()[0]
            
            cursor.execute("SELECT COUNT(*) FROM attachments")
            attachment_count = cursor.fetchone()[0]
            
            # Check database integrity
            cursor.execute("PRAGMA integrity_check")
            integrity_result = cursor.fetchall()
            
            conn.close()
            
            is_valid = (
                email_count >= 0 and
                sender_count >= 0 and
                attachment_count >= 0 and
                integrity_result[0][0] == 'ok'
            )
            
            if is_valid:
                logger.info(f"Backup verified: {email_count} emails, {sender_count} senders, {attachment_count} attachments")
            
            return is_valid
            
        except Exception as e:
            logger.error(f"Backup verification failed: {str(e)}")
            return False
    
    def cleanup_old_backups(self):
        """Remove old backups based on retention policy"""
        try:
            now = datetime.now()
            
            # Daily backups: keep 30 days
            for backup_file in self.daily_backup_dir.glob("email_inbox_daily_*.db"):
                file_time = datetime.fromtimestamp(backup_file.stat().st_mtime)
                if now - file_time > timedelta(days=30):
                    backup_file.unlink()
                    logger.info(f"Removed old daily backup: {backup_file.name}")
            
            # Weekly backups: keep 12 weeks (3 months)
            for backup_file in self.weekly_backup_dir.glob("email_inbox_weekly_*.db"):
                file_time = datetime.fromtimestamp(backup_file.stat().st_mtime)
                if now - file_time > timedelta(weeks=12):
                    backup_file.unlink()
                    logger.info(f"Removed old weekly backup: {backup_file.name}")
            
            # Monthly backups: keep 12 months
            for backup_file in self.monthly_backup_dir.glob("email_inbox_monthly_*.db"):
                file_time = datetime.fromtimestamp(backup_file.stat().st_mtime)
                if now - file_time > timedelta(days=365):
                    backup_file.unlink()
                    logger.info(f"Removed old monthly backup: {backup_file.name}")
                    
        except Exception as e:
            logger.error(f"Backup cleanup failed: {str(e)}")
    
    def health_check(self):
        """Perform database health check"""
        health_report = {
            "timestamp": datetime.now().isoformat(),
            "database_exists": False,
            "database_size_mb": 0,
            "connection_test": False,
            "record_counts": {},
            "integrity_check": False,
            "issues": []
        }
        
        try:
            # Check if database exists
            if not self.db_path.exists():
                health_report["issues"].append("Database file does not exist")
                return health_report
            
            health_report["database_exists"] = True
            health_report["database_size_mb"] = self.db_path.stat().st_size / (1024*1024)
            
            # Test database connection and queries
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # Test basic connection
            cursor.execute("SELECT 1")
            health_report["connection_test"] = True
            
            # Get record counts
            tables = ["emails", "senders", "attachments"]
            for table in tables:
                try:
                    cursor.execute(f"SELECT COUNT(*) FROM {table}")
                    count = cursor.fetchone()[0]
                    health_report["record_counts"][table] = count
                except Exception as e:
                    health_report["issues"].append(f"Failed to count {table}: {str(e)}")
            
            # Check database integrity
            cursor.execute("PRAGMA integrity_check")
            integrity_result = cursor.fetchall()
            health_report["integrity_check"] = integrity_result[0][0] == 'ok'
            
            if not health_report["integrity_check"]:
                health_report["issues"].append("Database integrity check failed")
            
            # Check for recent data
            try:
                cursor.execute("SELECT MAX(received_time) FROM emails")
                latest_email = cursor.fetchone()[0]
                if latest_email:
                    latest_date = datetime.fromisoformat(latest_email.replace('Z', '+00:00'))
                    days_old = (datetime.now() - latest_date).days
                    health_report["latest_email_days_old"] = days_old
                    
                    if days_old > 7:
                        health_report["issues"].append(f"Latest email is {days_old} days old")
            except Exception as e:
                health_report["issues"].append(f"Failed to check latest email: {str(e)}")
            
            conn.close()
            
            # Overall health status
            health_report["status"] = "healthy" if not health_report["issues"] else "warning"
            
        except Exception as e:
            health_report["issues"].append(f"Health check failed: {str(e)}")
            health_report["status"] = "error"
        
        return health_report
    
    def generate_report(self):
        """Generate maintenance report"""
        report = {
            "maintenance_date": datetime.now().isoformat(),
            "health_check": self.health_check(),
            "backup_status": self.get_backup_status(),
            "recommendations": []
        }
        
        # Add recommendations based on health check
        health = report["health_check"]
        
        if health["database_size_mb"] > 100:
            report["recommendations"].append("Consider database optimization - size exceeds 100MB")
        
        if health.get("latest_email_days_old", 0) > 3:
            report["recommendations"].append("No recent emails - check email ingestion service")
        
        if health["record_counts"].get("emails", 0) < 10:
            report["recommendations"].append("Low email count - verify data integrity")
        
        if not health["integrity_check"]:
            report["recommendations"].append("URGENT: Database integrity issues detected")
        
        return report
    
    def get_backup_status(self):
        """Get backup status information"""
        backup_status = {
            "daily_backups": len(list(self.daily_backup_dir.glob("*.db"))),
            "weekly_backups": len(list(self.weekly_backup_dir.glob("*.db"))),
            "monthly_backups": len(list(self.monthly_backup_dir.glob("*.db"))),
            "latest_backup": None,
            "total_backup_size_mb": 0
        }
        
        # Find latest backup
        all_backups = []
        for backup_dir in [self.daily_backup_dir, self.weekly_backup_dir, self.monthly_backup_dir]:
            all_backups.extend(backup_dir.glob("*.db"))
        
        if all_backups:
            latest = max(all_backups, key=lambda p: p.stat().st_mtime)
            backup_status["latest_backup"] = {
                "name": latest.name,
                "age_hours": (datetime.now() - datetime.fromtimestamp(latest.stat().st_mtime)).total_seconds() / 3600,
                "size_mb": latest.stat().st_size / (1024*1024)
            }
            
            # Calculate total backup size
            backup_status["total_backup_size_mb"] = sum(
                backup.stat().st_size for backup in all_backups
            ) / (1024*1024)
        
        return backup_status

def main():
    """Main maintenance function"""
    print("Database Maintenance Script")
    print("=" * 50)
    
    maintenance = DatabaseMaintenance()
    
    # Parse command line arguments
    import argparse
    parser = argparse.ArgumentParser(description="Database Maintenance Operations")
    parser.add_argument("--backup", choices=["daily", "weekly", "monthly", "manual"], help="Create backup")
    parser.add_argument("--health-check", action="store_true", help="Perform health check")
    parser.add_argument("--cleanup", action="store_true", help="Clean up old backups")
    parser.add_argument("--report", action="store_true", help="Generate maintenance report")
    parser.add_argument("--all", action="store_true", help="Run all maintenance tasks")
    
    args = parser.parse_args()
    
    if args.all or not any(vars(args).values()):
        # Default: run all maintenance tasks
        logger.info("Running full maintenance cycle")
        
        # Health check
        health = maintenance.health_check()
        print(f"Database Status: {health['status']}")
        
        if health['issues']:
            print("Issues found:")
            for issue in health['issues']:
                print(f"  - {issue}")
        
        # Create daily backup
        backup_path = maintenance.create_backup("daily")
        if backup_path:
            print(f"Backup created: {backup_path.name}")
        
        # Cleanup old backups
        maintenance.cleanup_old_backups()
        
        # Generate report
        report = maintenance.generate_report()
        report_path = Path("logs/maintenance_report.json")
        with open(report_path, "w") as f:
            json.dump(report, f, indent=2)
        print(f"Maintenance report saved: {report_path}")
        
    else:
        # Run specific tasks
        if args.backup:
            backup_path = maintenance.create_backup(args.backup)
            if backup_path:
                print(f"Backup created: {backup_path.name}")
        
        if args.health_check:
            health = maintenance.health_check()
            print(json.dumps(health, indent=2))
        
        if args.cleanup:
            maintenance.cleanup_old_backups()
            print("Backup cleanup completed")
        
        if args.report:
            report = maintenance.generate_report()
            print(json.dumps(report, indent=2))

if __name__ == "__main__":
    main()