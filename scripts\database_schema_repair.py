"""
Database Schema Repair Tool
Add missing columns to the database to match the model definition
"""

import sqlite3
import sys
import os
import shutil
from pathlib import Path
from datetime import datetime

# Add src directory to Python path
project_root = Path(__file__).parent.parent
src_path = project_root / "src"
sys.path.insert(0, str(src_path))

def backup_database(db_path):
    """Create a backup of the database before making changes"""
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    backup_path = db_path.parent / f"email_inbox_backup_{timestamp}.db"
    
    try:
        shutil.copy2(db_path, backup_path)
        print(f"Database backed up to: {backup_path}")
        return backup_path
    except Exception as e:
        print(f"Backup failed: {e}")
        return None

def add_missing_columns(cursor):
    """Add missing columns to the emails table"""
    
    missing_columns = [
        ("mo", "VARCHAR(100)", "Nullable manufacturing order field"),
        ("extraction_method", "VARCHAR(50)", "Method used for data extraction"), 
        ("llm_analysis_result", "TEXT", "JSON format LLM analysis results"),
        ("llm_analysis_timestamp", "DATETIME", "Timestamp of LLM analysis"),
        ("llm_service_used", "VARCHAR(50)", "LLM service used (grok, ollama)")
    ]
    
    print("Adding missing columns to emails table:")
    print("-" * 50)
    
    for column_name, column_type, description in missing_columns:
        try:
            sql = f"ALTER TABLE emails ADD COLUMN {column_name} {column_type}"
            cursor.execute(sql)
            print(f"OK - Added {column_name} ({column_type}) - {description}")
        except sqlite3.Error as e:
            if "duplicate column name" in str(e).lower():
                print(f"SKIP - {column_name} already exists")
            else:
                print(f"ERROR - Failed to add {column_name}: {e}")
                raise e
    
    print()

def verify_repair(cursor):
    """Verify that all columns have been added successfully"""
    print("Verifying repair:")
    print("-" * 30)
    
    cursor.execute("PRAGMA table_info(emails)")
    columns = cursor.fetchall()
    current_columns = [col[1] for col in columns]
    
    expected_columns = [
        'id', 'message_id', 'sender', 'sender_display_name', 
        'subject', 'body', 'received_time', 'created_at', 
        'is_read', 'is_processed', 'has_attachments', 'attachment_count',
        'pd', 'lot', 'mo', 'yield_value', 'vendor_code', 
        'parsed_at', 'parse_status', 'parse_error', 'extraction_method',
        'llm_analysis_result', 'llm_analysis_timestamp', 'llm_service_used'
    ]
    
    missing = [col for col in expected_columns if col not in current_columns]
    
    if missing:
        print(f"ERROR - Still missing columns: {', '.join(missing)}")
        return False
    else:
        print(f"SUCCESS - All {len(expected_columns)} expected columns are present!")
        print(f"Current column count: {len(current_columns)}")
        return True

def test_query(cursor):
    """Test the problematic query that was causing the error"""
    print("Testing the original problematic query:")
    print("-" * 40)
    
    try:
        test_sql = """
        SELECT emails.id AS emails_id, emails.message_id AS emails_message_id, 
               emails.sender AS emails_sender, emails.sender_display_name AS emails_sender_display_name, 
               emails.subject AS emails_subject, emails.body AS emails_body, 
               emails.received_time AS emails_received_time, emails.created_at AS emails_created_at, 
               emails.is_read AS emails_is_read, emails.is_processed AS emails_is_processed, 
               emails.has_attachments AS emails_has_attachments, emails.attachment_count AS emails_attachment_count, 
               emails.pd AS emails_pd, emails.lot AS emails_lot, emails.mo AS emails_mo, 
               emails.yield_value AS emails_yield_value, emails.vendor_code AS emails_vendor_code, 
               emails.parsed_at AS emails_parsed_at, emails.parse_status AS emails_parse_status, 
               emails.parse_error AS emails_parse_error, emails.extraction_method AS emails_extraction_method, 
               emails.llm_analysis_result AS emails_llm_analysis_result, 
               emails.llm_analysis_timestamp AS emails_llm_analysis_timestamp, 
               emails.llm_service_used AS emails_llm_service_used
        FROM emails 
        LIMIT 1
        """
        
        cursor.execute(test_sql)
        result = cursor.fetchone()
        print("SUCCESS - Query executed without errors!")
        
        if result:
            print("Sample result retrieved successfully")
        else:
            print("No data in table (expected if table is empty)")
        
        return True
        
    except sqlite3.Error as e:
        print(f"ERROR - Query still fails: {e}")
        return False

def main():
    print("=" * 70)
    print("DATABASE SCHEMA REPAIR TOOL")
    print("=" * 70)
    
    db_path = project_root / "data" / "email_inbox.db"
    
    if not os.path.exists(db_path):
        print(f"ERROR - Database file not found: {db_path}")
        return
    
    print(f"Target database: {db_path}")
    print(f"Database size: {os.path.getsize(db_path)} bytes")
    print()
    
    # Create backup
    print("Step 1: Creating backup...")
    backup_path = backup_database(db_path)
    if not backup_path:
        print("Cannot proceed without backup. Exiting.")
        return
    print()
    
    try:
        # Connect to database
        conn = sqlite3.connect(str(db_path))
        cursor = conn.cursor()
        
        print("Step 2: Adding missing columns...")
        add_missing_columns(cursor)
        
        print("Step 3: Committing changes...")
        conn.commit()
        print("Changes committed successfully!")
        print()
        
        print("Step 4: Verifying repair...")
        repair_success = verify_repair(cursor)
        print()
        
        if repair_success:
            print("Step 5: Testing problematic query...")
            query_success = test_query(cursor)
            print()
            
            if query_success:
                print("=" * 70)
                print("REPAIR COMPLETED SUCCESSFULLY!")
                print("=" * 70)
                print("The database schema has been updated to match the model definition.")
                print("The original SQL error should now be resolved.")
                print(f"Backup saved at: {backup_path}")
            else:
                print("REPAIR INCOMPLETE - Query test failed")
        else:
            print("REPAIR FAILED - Schema verification failed")
        
    except Exception as e:
        print(f"ERROR during repair: {e}")
        print("Rolling back changes...")
        
        try:
            # Restore from backup
            shutil.copy2(backup_path, db_path)
            print("Database restored from backup")
        except Exception as restore_error:
            print(f"CRITICAL - Failed to restore backup: {restore_error}")
        
    finally:
        if 'conn' in locals():
            conn.close()

if __name__ == "__main__":
    main()