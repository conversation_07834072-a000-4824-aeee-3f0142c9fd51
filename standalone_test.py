#!/usr/bin/env python3
"""
獨立功能驗證測試 - 不導入任何本地模組
"""

import requests
import json
import time
import sys
from datetime import datetime

# 確保不會意外導入本地模組
sys.path = [path for path in sys.path if 'outlook_summary' not in path]

def main():
    """執行功能驗證測試"""
    base_url = "http://localhost:8000"
    
    print("🚀 開始執行功能驗證測試...")
    print(f"測試目標: {base_url}")
    print("=" * 60)
    
    session = requests.Session()
    session.timeout = 10
    
    test_results = []
    
    def test_endpoint(name, url, expected_status=200, allow_redirects=True):
        """測試端點"""
        try:
            start_time = time.time()
            response = session.get(url, allow_redirects=allow_redirects)
            response_time = time.time() - start_time
            
            success = response.status_code == expected_status
            status = "✅ PASS" if success else "❌ FAIL"
            
            print(f"{status} {name}")
            print(f"    URL: {url}")
            print(f"    狀態碼: {response.status_code} (預期: {expected_status})")
            print(f"    回應時間: {round(response_time * 1000, 2)}ms")
            print()
            
            test_results.append({
                'name': name,
                'success': success,
                'status_code': response.status_code,
                'expected_status': expected_status,
                'response_time_ms': round(response_time * 1000, 2)
            })
            
            return success, response
            
        except Exception as e:
            print(f"❌ FAIL {name}")
            print(f"    URL: {url}")
            print(f"    錯誤: {str(e)}")
            print()
            
            test_results.append({
                'name': name,
                'success': False,
                'error': str(e)
            })
            
            return False, None
    
    # 1. 健康檢查測試
    print("📋 1. 健康檢查測試")
    success, response = test_endpoint("健康檢查端點", f"{base_url}/health")
    
    if success and response:
        try:
            health_data = response.json()
            print(f"健康狀態: {health_data.get('status', 'unknown')}")
            modules = health_data.get('modules', {})
            print(f"模組狀態: {modules}")
            
            # 檢查所有模組是否正常
            expected_modules = ['email', 'analytics', 'file_management', 'eqc', 'tasks', 'monitoring']
            all_modules_ok = all(modules.get(module, False) for module in expected_modules)
            
            if all_modules_ok:
                print("✅ 所有模組狀態正常")
            else:
                print("⚠️  部分模組狀態異常")
            print()
                
        except json.JSONDecodeError:
            print("❌ 健康檢查回應格式錯誤")
            print()
    
    # 2. 主要路由測試
    print("🏠 2. 主要路由測試")
    test_endpoint("主頁重定向", f"{base_url}/", 302, allow_redirects=False)
    test_endpoint("Favicon", f"{base_url}/favicon.ico")
    
    # 3. 模組頁面測試
    print("🔧 3. 模組頁面測試")
    
    modules = [
        ('email', '/email/'),
        ('analytics', '/analytics/'),
        ('file_management', '/files/'),
        ('eqc', '/eqc/'),
        ('tasks', '/tasks/'),
        ('monitoring', '/monitoring/')
    ]
    
    for module_name, path in modules:
        test_endpoint(f"{module_name} 模組主頁", f"{base_url}{path}")
    
    # 生成測試摘要
    print("=" * 60)
    print("📊 功能驗證測試結果摘要")
    print("=" * 60)
    
    total_tests = len(test_results)
    passed_tests = sum(1 for test in test_results if test['success'])
    failed_tests = total_tests - passed_tests
    success_rate = (passed_tests / total_tests * 100) if total_tests > 0 else 0
    
    overall_success = failed_tests == 0
    overall_status = "✅ 全部通過" if overall_success else "❌ 有測試失敗"
    
    print(f"整體狀態: {overall_status}")
    print(f"總測試數: {total_tests}")
    print(f"通過測試: {passed_tests}")
    print(f"失敗測試: {failed_tests}")
    print(f"成功率: {success_rate:.1f}%")
    
    # 列出失敗的測試
    failed_tests_list = [test for test in test_results if not test['success']]
    if failed_tests_list:
        print("\n❌ 失敗的測試:")
        for test in failed_tests_list:
            error_msg = test.get('error', f"HTTP {test.get('status_code', 'unknown')}")
            print(f"  - {test['name']}: {error_msg}")
    
    # 儲存結果
    results_data = {
        'test_metadata': {
            'base_url': base_url,
            'test_time': datetime.now().isoformat(),
            'total_tests': total_tests,
            'passed_tests': passed_tests,
            'failed_tests': failed_tests,
            'success_rate': success_rate,
            'overall_success': overall_success
        },
        'test_results': test_results
    }
    
    try:
        with open('functional_verification_results.json', 'w', encoding='utf-8') as f:
            json.dump(results_data, f, ensure_ascii=False, indent=2)
        print(f"\n📄 詳細測試結果已儲存到: functional_verification_results.json")
    except Exception as e:
        print(f"\n❌ 儲存測試結果失敗: {str(e)}")
    
    print("\n" + "=" * 60)
    
    return overall_success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)