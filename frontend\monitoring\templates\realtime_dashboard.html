<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>即時狀態監控儀表板 - Outlook Summary</title>
    
    <!-- CSS -->
    <link rel="stylesheet" href="{{ url_for('shared.static', filename='css/global.css') }}">
    <link rel="stylesheet" href="{{ url_for('monitoring.static', filename='css/realtime.css') }}">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    
    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="{{ url_for('shared.static', filename='images/favicon.ico') }}">
</head>
<body>
    <!-- 連接狀態指示器 -->
    <div id="connection-status" class="connection-status disconnected">
        連接中...
    </div>
    
    <!-- 告警容器 -->
    <div id="alert-container" class="alert-container"></div>
    
    <!-- 主儀表板 -->
    <div class="realtime-dashboard">
        <!-- 儀表板標題 -->
        <header class="dashboard-header">
            <div>
                <h1 class="dashboard-title">即時狀態監控儀表板</h1>
                <p class="dashboard-subtitle">基於 WebSocket 的異步任務管理與系統監控</p>
            </div>
            <div class="dashboard-controls">
                <button class="control-button" onclick="toggleAutoRefresh()">
                    <i class="fas fa-sync-alt"></i> 自動重新整理
                </button>
                <button class="control-button" onclick="exportData()">
                    <i class="fas fa-download"></i> 匯出數據
                </button>
                <button class="control-button" onclick="wsClient.ping()">
                    <i class="fas fa-heartbeat"></i> 測試連接
                </button>
            </div>
        </header>
        
        <!-- 系統概覽 -->
        <section class="system-overview">
            <div class="metric-card">
                <div class="metric-header">
                    <span class="metric-title">CPU 使用率</span>
                    <i class="fas fa-microchip metric-icon"></i>
                </div>
                <div class="metric-value" id="cpu-usage">0%</div>
                <div class="metric-trend" id="cpu-trend">
                    <i class="fas fa-arrow-up"></i> +2.3%
                </div>
            </div>
            
            <div class="metric-card">
                <div class="metric-header">
                    <span class="metric-title">記憶體使用</span>
                    <i class="fas fa-memory metric-icon"></i>
                </div>
                <div class="metric-value" id="memory-usage">0%</div>
                <div class="metric-trend" id="memory-trend">
                    <i class="fas fa-arrow-down"></i> -1.2%
                </div>
            </div>
            
            <div class="metric-card">
                <div class="metric-header">
                    <span class="metric-title">活躍任務</span>
                    <i class="fas fa-tasks metric-icon"></i>
                </div>
                <div class="metric-value" id="active-tasks">0</div>
                <div class="metric-trend" id="tasks-trend">
                    <i class="fas fa-minus"></i> 0
                </div>
            </div>
            
            <div class="metric-card">
                <div class="metric-header">
                    <span class="metric-title">系統狀態</span>
                    <i class="fas fa-heartbeat metric-icon"></i>
                </div>
                <div class="metric-value" id="system-status">正常</div>
                <div class="metric-trend" id="status-trend">
                    <i class="fas fa-check"></i> 運行中
                </div>
            </div>
        </section>
        
        <!-- 任務監控 -->
        <section class="task-monitoring">
            <h2>任務監控</h2>
            <div class="task-list" id="task-list">
                <!-- 任務項目將動態載入 -->
                <div class="task-item">
                    <div class="task-info">
                        <span class="task-name">範例任務</span>
                        <span class="task-status status-running">執行中</span>
                    </div>
                    <div class="task-progress">
                        <div class="progress-bar">
                            <div class="progress-fill" style="width: 65%"></div>
                        </div>
                        <span class="progress-text">65%</span>
                    </div>
                </div>
            </div>
        </section>
        
        <!-- 系統日誌 -->
        <section class="system-logs">
            <h2>系統日誌</h2>
            <div class="log-container" id="log-container">
                <div class="log-entry">
                    <span class="log-time">2025-01-08 10:30:15</span>
                    <span class="log-level level-info">INFO</span>
                    <span class="log-message">系統啟動完成</span>
                </div>
            </div>
        </section>
    </div>

    <!-- 載入核心依賴 -->
    <script src="{{ url_for('shared.static', filename='js/core/utils.js') }}"></script>
    <script src="{{ url_for('shared.static', filename='js/core/api-client.js') }}"></script>
    <script src="{{ url_for('monitoring.static', filename='js/realtime.js') }}"></script>
    <script src="{{ url_for('monitoring.static', filename='js/websocket.js') }}"></script>

    <script>
        function toggleAutoRefresh() {
            console.log('切換自動重新整理');
        }

        function exportData() {
            console.log('匯出數據');
        }

        // 初始化儀表板
        document.addEventListener('DOMContentLoaded', function() {
            console.log('即時監控儀表板已載入');
        });
    </script>
</body>
</html>