<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>品質檢查 - Outlook Summary</title>
    <link rel="stylesheet" href="{{ url_for('eqc.static', filename='css/eqc.css') }}?v={{ range(1000, 9999) | random }}">
    <link rel="stylesheet" href="{{ url_for('shared.static', filename='css/global.css') }}">
</head>
<body>
    <div class="quality-check-container">
        <header class="check-header">
            <h1>品質檢查中心</h1>
            <div class="header-stats">
                <div class="stat-item">
                    <span class="stat-label">今日檢查:</span>
                    <span class="stat-value">{{ stats.today_checks or 0 }}</span>
                </div>
                <div class="stat-item">
                    <span class="stat-label">通過率:</span>
                    <span class="stat-value {{ 'success' if stats.pass_rate > 90 else 'warning' if stats.pass_rate > 70 else 'error' }}">
                        {{ '%.1f'|format(stats.pass_rate or 0) }}%
                    </span>
                </div>
                <div class="stat-item">
                    <span class="stat-label">待處理:</span>
                    <span class="stat-value">{{ stats.pending_checks or 0 }}</span>
                </div>
            </div>
            <div class="header-actions">
                <button id="start-batch-check-btn" class="btn btn-primary">
                    <span class="btn-icon">🔍</span>
                    <span class="btn-text">批次檢查</span>
                </button>
                <button id="export-report-btn" class="btn btn-secondary">
                    <span class="btn-icon">📊</span>
                    <span class="btn-text">匯出報告</span>
                </button>
                <a href="{{ url_for('eqc.dashboard') }}" class="btn btn-outline">
                    <span class="btn-icon">🏠</span>
                    <span class="btn-text">回到EQC儀表板</span>
                </a>
            </div>
        </header>

        <div class="check-content">
            <!-- 檢查配置區 -->
            <div class="check-config-section">
                <div class="config-card">
                    <h3>⚙️ 檢查配置</h3>
                    <div class="config-tabs">
                        <button class="tab-btn active" data-tab="file-check">檔案檢查</button>
                        <button class="tab-btn" data-tab="data-check">數據檢查</button>
                        <button class="tab-btn" data-tab="format-check">格式檢查</button>
                        <button class="tab-btn" data-tab="compliance-check">合規檢查</button>
                    </div>

                    <!-- 檔案檢查配置 -->
                    <div class="tab-content active" id="file-check-tab">
                        <div class="config-grid">
                            <div class="config-group">
                                <h4>📁 檔案完整性</h4>
                                <label>
                                    <input type="checkbox" id="check-file-existence" checked>
                                    檢查檔案是否存在
                                </label>
                                <label>
                                    <input type="checkbox" id="check-file-size" checked>
                                    檢查檔案大小合理性
                                </label>
                                <label>
                                    <input type="checkbox" id="check-file-corruption">
                                    檢查檔案是否損壞
                                </label>
                                <label>
                                    <input type="checkbox" id="check-file-permissions">
                                    檢查檔案權限
                                </label>
                            </div>
                            <div class="config-group">
                                <h4>📝 檔案命名</h4>
                                <label>
                                    <input type="checkbox" id="check-naming-convention" checked>
                                    檢查命名規範
                                </label>
                                <label>
                                    <input type="checkbox" id="check-special-characters">
                                    檢查特殊字元
                                </label>
                                <label>
                                    <input type="checkbox" id="check-duplicate-names">
                                    檢查重複檔名
                                </label>
                            </div>
                        </div>
                    </div>

                    <!-- 數據檢查配置 -->
                    <div class="tab-content" id="data-check-tab">
                        <div class="config-grid">
                            <div class="config-group">
                                <h4>📊 數據品質</h4>
                                <label>
                                    <input type="checkbox" id="check-data-completeness" checked>
                                    檢查數據完整性
                                </label>
                                <label>
                                    <input type="checkbox" id="check-data-accuracy">
                                    檢查數據準確性
                                </label>
                                <label>
                                    <input type="checkbox" id="check-data-consistency" checked>
                                    檢查數據一致性
                                </label>
                                <label>
                                    <input type="checkbox" id="check-data-duplicates">
                                    檢查重複數據
                                </label>
                            </div>
                            <div class="config-group">
                                <h4>🔢 數據格式</h4>
                                <label>
                                    <input type="checkbox" id="check-date-format" checked>
                                    檢查日期格式
                                </label>
                                <label>
                                    <input type="checkbox" id="check-number-format">
                                    檢查數值格式
                                </label>
                                <label>
                                    <input type="checkbox" id="check-email-format">
                                    檢查電子信箱格式
                                </label>
                            </div>
                        </div>
                    </div>

                    <!-- 格式檢查配置 -->
                    <div class="tab-content" id="format-check-tab">
                        <div class="config-grid">
                            <div class="config-group">
                                <h4>📋 文件格式</h4>
                                <label>
                                    <input type="checkbox" id="check-excel-format" checked>
                                    Excel格式檢查
                                </label>
                                <label>
                                    <input type="checkbox" id="check-pdf-format">
                                    PDF格式檢查
                                </label>
                                <label>
                                    <input type="checkbox" id="check-csv-format" checked>
                                    CSV格式檢查
                                </label>
                                <label>
                                    <input type="checkbox" id="check-xml-format">
                                    XML格式檢查
                                </label>
                            </div>
                            <div class="config-group">
                                <h4>🎨 內容格式</h4>
                                <label>
                                    <input type="checkbox" id="check-encoding" checked>
                                    檢查字元編碼
                                </label>
                                <label>
                                    <input type="checkbox" id="check-line-endings">
                                    檢查行結束符
                                </label>
                                <label>
                                    <input type="checkbox" id="check-whitespace">
                                    檢查空白字元
                                </label>
                            </div>
                        </div>
                    </div>

                    <!-- 合規檢查配置 -->
                    <div class="tab-content" id="compliance-check-tab">
                        <div class="config-grid">
                            <div class="config-group">
                                <h4>📜 規範遵循</h4>
                                <label>
                                    <input type="checkbox" id="check-industry-standards" checked>
                                    行業標準檢查
                                </label>
                                <label>
                                    <input type="checkbox" id="check-company-policies">
                                    公司政策檢查
                                </label>
                                <label>
                                    <input type="checkbox" id="check-legal-requirements">
                                    法規要求檢查
                                </label>
                            </div>
                            <div class="config-group">
                                <h4>🔒 安全性</h4>
                                <label>
                                    <input type="checkbox" id="check-sensitive-data">
                                    敏感數據檢查
                                </label>
                                <label>
                                    <input type="checkbox" id="check-access-controls">
                                    存取控制檢查
                                </label>
                                <label>
                                    <input type="checkbox" id="check-encryption">
                                    加密要求檢查
                                </label>
                            </div>
                        </div>
                    </div>

                    <div class="config-actions">
                        <button id="save-config-btn" class="btn btn-primary">儲存配置</button>
                        <button id="load-config-btn" class="btn btn-secondary">載入配置</button>
                        <button id="reset-config-btn" class="btn btn-outline">重設預設</button>
                    </div>
                </div>
            </div>

            <!-- 檢查進度區 -->
            <div class="check-progress-section" id="progress-section" style="display: none;">
                <div class="progress-card">
                    <h3>⏳ 檢查進度</h3>
                    <div class="progress-overview">
                        <div class="progress-bar">
                            <div class="progress-fill" id="check-progress-fill" style="width: 0%;"></div>
                        </div>
                        <div class="progress-text">
                            <span id="progress-percentage">0%</span>
                            <span id="progress-status">準備中...</span>
                        </div>
                    </div>
                    <div class="progress-details">
                        <div class="detail-item">
                            <span class="label">已檢查:</span>
                            <span class="value" id="checked-items">0</span>
                        </div>
                        <div class="detail-item">
                            <span class="label">待檢查:</span>
                            <span class="value" id="pending-items">0</span>
                        </div>
                        <div class="detail-item">
                            <span class="label">發現問題:</span>
                            <span class="value error" id="issues-found">0</span>
                        </div>
                        <div class="detail-item">
                            <span class="label">預估剩餘:</span>
                            <span class="value" id="estimated-remaining">-</span>
                        </div>
                    </div>
                    <div class="progress-actions">
                        <button id="pause-check-btn" class="btn btn-secondary">暫停</button>
                        <button id="stop-check-btn" class="btn btn-danger">停止</button>
                    </div>
                </div>
            </div>

            <!-- 檢查結果區 -->
            <div class="check-results-section">
                <div class="results-card">
                    <div class="results-header">
                        <h3>📋 檢查結果</h3>
                        <div class="results-summary">
                            <div class="summary-item success">
                                <span class="summary-count" id="pass-count">{{ results.pass_count or 0 }}</span>
                                <span class="summary-label">通過</span>
                            </div>
                            <div class="summary-item warning">
                                <span class="summary-count" id="warning-count">{{ results.warning_count or 0 }}</span>
                                <span class="summary-label">警告</span>
                            </div>
                            <div class="summary-item error">
                                <span class="summary-count" id="fail-count">{{ results.fail_count or 0 }}</span>
                                <span class="summary-label">失敗</span>
                            </div>
                        </div>
                        <div class="results-actions">
                            <button id="filter-results-btn" class="btn btn-sm btn-secondary">篩選結果</button>
                            <button id="export-results-btn" class="btn btn-sm btn-primary">匯出結果</button>
                        </div>
                    </div>

                    <div class="results-filters" style="display: none;">
                        <div class="filter-row">
                            <select id="result-status-filter">
                                <option value="all" selected>全部狀態</option>
                                <option value="pass">通過</option>
                                <option value="warning">警告</option>
                                <option value="fail">失敗</option>
                            </select>
                            <select id="result-category-filter">
                                <option value="all" selected>全部類別</option>
                                <option value="file">檔案檢查</option>
                                <option value="data">數據檢查</option>
                                <option value="format">格式檢查</option>
                                <option value="compliance">合規檢查</option>
                            </select>
                            <input type="text" id="result-search" placeholder="搜尋檢查項目...">
                            <button id="apply-result-filters-btn" class="btn btn-sm btn-primary">套用</button>
                        </div>
                    </div>

                    <div class="results-list" id="check-results-list">
                        {% for result in check_results %}
                        <div class="result-item {{ result.status }}">
                            <div class="result-header">
                                <div class="result-status-icon">
                                    {% if result.status == 'pass' %}✅
                                    {% elif result.status == 'warning' %}⚠️
                                    {% elif result.status == 'fail' %}❌
                                    {% endif %}
                                </div>
                                <div class="result-info">
                                    <h4 class="result-title">{{ result.check_name }}</h4>
                                    <div class="result-meta">
                                        <span class="result-category">{{ result.category }}</span>
                                        <span class="result-time">{{ result.checked_at.strftime('%Y-%m-%d %H:%M') if result.checked_at else 'N/A' }}</span>
                                        <span class="result-file">{{ result.file_name }}</span>
                                    </div>
                                </div>
                                <div class="result-actions">
                                    <button class="btn btn-sm btn-outline" onclick="toggleResultDetails('{{ result.id }}')">詳情</button>
                                    {% if result.status != 'pass' %}
                                    <button class="btn btn-sm btn-primary" onclick="fixIssue('{{ result.id }}')">修復</button>
                                    {% endif %}
                                </div>
                            </div>
                            
                            <div class="result-details" id="result-details-{{ result.id }}" style="display: none;">
                                <div class="detail-content">
                                    <p><strong>檢查描述:</strong> {{ result.description }}</p>
                                    {% if result.message %}
                                    <p><strong>結果訊息:</strong> {{ result.message }}</p>
                                    {% endif %}
                                    {% if result.recommendations %}
                                    <p><strong>建議:</strong></p>
                                    <ul>
                                        {% for recommendation in result.recommendations %}
                                        <li>{{ recommendation }}</li>
                                        {% endfor %}
                                    </ul>
                                    {% endif %}
                                    {% if result.file_path %}
                                    <p><strong>檔案路徑:</strong> <code>{{ result.file_path }}</code></p>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                        {% endfor %}
                    </div>

                    <div class="pagination" id="results-pagination">
                        <!-- 分頁控制會由JavaScript動態生成 -->
                    </div>
                </div>
            </div>

            <!-- 檢查歷史區 -->
            <div class="check-history-section">
                <div class="history-card">
                    <div class="history-header">
                        <h3>📜 檢查歷史</h3>
                        <div class="history-actions">
                            <button id="clear-history-btn" class="btn btn-sm btn-danger">清除歷史</button>
                            <button id="export-history-btn" class="btn btn-sm btn-secondary">匯出歷史</button>
                        </div>
                    </div>

                    <div class="history-list" id="check-history-list">
                        {% for history in check_history %}
                        <div class="history-item">
                            <div class="history-info">
                                <div class="history-title">{{ history.check_name }}</div>
                                <div class="history-meta">
                                    <span class="history-time">{{ history.created_at.strftime('%Y-%m-%d %H:%M') }}</span>
                                    <span class="history-duration">耗時: {{ history.duration }}秒</span>
                                    <span class="history-items">檢查項目: {{ history.total_items }}</span>
                                </div>
                            </div>
                            <div class="history-results">
                                <span class="result-count success">✅ {{ history.pass_count }}</span>
                                <span class="result-count warning">⚠️ {{ history.warning_count }}</span>
                                <span class="result-count error">❌ {{ history.fail_count }}</span>
                            </div>
                            <div class="history-actions">
                                <button class="btn btn-sm btn-outline" onclick="viewHistoryDetails('{{ history.id }}')">檢視</button>
                                <button class="btn btn-sm btn-secondary" onclick="exportHistoryReport('{{ history.id }}')">匯出</button>
                                <button class="btn btn-sm btn-danger" onclick="deleteHistory('{{ history.id }}')">刪除</button>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="{{ url_for('shared.static', filename='js/main.js') }}"></script>
    <script src="{{ url_for('eqc.static', filename='js/quality-check.js') }}"></script>
</body>
</html>