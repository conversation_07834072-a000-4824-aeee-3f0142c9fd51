#!/usr/bin/env python3
"""
內網連接性測試腳本
驗證 Systemd IP 限制配置是否允許內網訪問
"""

import ipaddress
import re
from pathlib import Path

def parse_systemd_ip_rules():
    """解析 Systemd 服務檔案中的 IP 規則"""
    project_root = Path(__file__).parent.parent
    systemd_file = project_root / "deployment" / "systemd" / "outlook-summary.service"
    
    if not systemd_file.exists():
        return None, ["❌ 找不到 Systemd 服務檔案"]
    
    try:
        content = systemd_file.read_text(encoding='utf-8')
    except UnicodeDecodeError:
        content = systemd_file.read_text(encoding='utf-8', errors='ignore')
    
    # 解析 IP 規則
    deny_rules = []
    allow_rules = []
    
    for line in content.split('\n'):
        line = line.strip()
        if line.startswith('IPAddressDeny='):
            deny_rules.append(line.split('=', 1)[1])
        elif line.startswith('IPAddressAllow='):
            allow_rules.append(line.split('=', 1)[1])
    
    return {'deny': deny_rules, 'allow': allow_rules}, []

def test_ip_access(ip_rules, test_ips):
    """測試指定 IP 是否被允許訪問"""
    if not ip_rules:
        return {}
    
    results = {}
    
    for test_ip in test_ips:
        allowed = False
        reason = "未匹配任何規則"
        
        try:
            test_addr = ipaddress.ip_address(test_ip)
            
            # 檢查 deny 規則
            for deny_rule in ip_rules['deny']:
                if deny_rule == 'any':
                    # 預設拒絕所有
                    allowed = False
                    reason = "被 IPAddressDeny=any 拒絕"
                    break
            
            # 檢查 allow 規則
            for allow_rule in ip_rules['allow']:
                if allow_rule in ['localhost', '127.0.0.1'] and test_ip in ['127.0.0.1', 'localhost']:
                    allowed = True
                    reason = f"被 {allow_rule} 規則允許"
                    break
                elif allow_rule == '::1' and test_ip == '::1':
                    allowed = True
                    reason = f"被 {allow_rule} 規則允許"
                    break
                elif '/' in allow_rule:  # CIDR 網路
                    try:
                        network = ipaddress.ip_network(allow_rule, strict=False)
                        if test_addr in network:
                            allowed = True
                            reason = f"被網路規則 {allow_rule} 允許"
                            break
                    except ValueError:
                        continue
                elif allow_rule == test_ip:
                    allowed = True
                    reason = f"被 IP 規則 {allow_rule} 允許"
                    break
            
        except ValueError:
            allowed = False
            reason = "無效的 IP 地址"
        
        results[test_ip] = {'allowed': allowed, 'reason': reason}
    
    return results

def main():
    """主要測試函數"""
    print("🌐 開始內網連接性測試...\n")
    
    # 解析 IP 規則
    ip_rules, errors = parse_systemd_ip_rules()
    
    if errors:
        for error in errors:
            print(error)
        return 1
    
    print("📋 Systemd IP 規則:")
    print(f"  拒絕規則: {ip_rules['deny']}")
    print(f"  允許規則: {ip_rules['allow']}")
    print()
    
    # 測試常見的內網 IP
    test_ips = [
        '127.0.0.1',        # 本地
        '*************',    # 常見內網 C 類
        '***********',      # 路由器常見 IP
        '**********',       # 內網 A 類
        '************',     # 內網 B 類
        '*******',          # 外部 IP (應該被拒絕)
        '*******',          # 外部 IP (應該被拒絕)
    ]
    
    # 執行測試
    results = test_ip_access(ip_rules, test_ips)
    
    print("🧪 內網連接性測試結果:")
    print("="*60)
    
    internal_ips_blocked = 0
    external_ips_allowed = 0
    
    for ip, result in results.items():
        status = "✅ 允許" if result['allowed'] else "❌ 拒絕"
        print(f"{status} {ip:15} - {result['reason']}")
        
        # 檢查是否有問題
        if ip.startswith(('192.168.', '10.', '172.16.', '172.17.', '172.18.', '172.19.', 
                         '172.20.', '172.21.', '172.22.', '172.23.', '172.24.', '172.25.',
                         '172.26.', '172.27.', '172.28.', '172.29.', '172.30.', '172.31.')) or ip == '127.0.0.1':
            if not result['allowed']:
                internal_ips_blocked += 1
        elif ip not in ['127.0.0.1', 'localhost']:
            if result['allowed']:
                external_ips_allowed += 1
    
    print("\n" + "="*60)
    print("📊 測試摘要:")
    
    if internal_ips_blocked > 0:
        print(f"❌ 發現問題: {internal_ips_blocked} 個內網 IP 被阻擋")
        print("   這將阻止內網用戶訪問服務！")
        return 1
    else:
        print("✅ 所有內網 IP 都被正確允許")
    
    if external_ips_allowed > 0:
        print(f"⚠️ 注意: {external_ips_allowed} 個外部 IP 被允許")
        print("   請確保防火牆正確配置以阻擋外部訪問")
    else:
        print("✅ 外部 IP 被正確拒絕")
    
    print("\n🎯 結論:")
    if internal_ips_blocked == 0:
        print("✅ 內網連接性配置正確，用戶可以正常訪問服務")
        return 0
    else:
        print("❌ 內網連接性配置有問題，需要修復 IP 允許規則")
        return 1

if __name__ == "__main__":
    exit(main())
