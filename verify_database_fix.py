#!/usr/bin/env python3
"""
Verify Database Configuration Fix
Test if the system now correctly reads from the main database (34 email records)
"""

import sys
from pathlib import Path

# Add src directory to Python path
project_root = Path(__file__).parent
src_path = project_root / "src"
sys.path.insert(0, str(src_path))

def test_database_connection():
    """Test database connection with new configuration"""
    print("=== Database Configuration Verification ===\n")
    
    try:
        # Test 1: Import database models
        print("1. Testing database model imports...")
        from src.infrastructure.adapters.database.models import DatabaseEngine, EmailDB
        print("   OK - Database models imported successfully")
        
        # Test 2: Initialize database engine with default config
        print("2. Initializing database engine with DEFAULT config...")
        db_engine = DatabaseEngine()  # Should use sqlite:///email_inbox.db now
        db_engine.initialize()
        print(f"   OK - Database engine initialized with URL: {db_engine.database_url}")
        
        # Test 3: Create session and test query
        print("3. Creating database session...")
        session = db_engine.get_session()
        print("   OK - Database session created")
        
        # Test 4: Query emails count
        print("4. Querying email records...")
        total_emails = session.query(EmailDB).count()
        print(f"   Total email records found: {total_emails}")
        
        if total_emails == 34:
            print("   SUCCESS! Reading from main database (34 records)")
        elif total_emails == 3:
            print("   ERROR! Still reading from old database (3 records)")
            return False
        else:
            print(f"   WARNING! Unexpected record count: {total_emails}")
        
        # Test 5: Sample recent emails
        print("5. Fetching sample recent emails...")
        recent_emails = session.query(EmailDB).order_by(EmailDB.received_time.desc()).limit(3).all()
        
        for i, email in enumerate(recent_emails, 1):
            print(f"   Email {i}: ID={email.id} | {email.received_time} | {email.sender}")
            subject_preview = email.subject[:60] + "..." if len(email.subject) > 60 else email.subject
            print(f"              Subject: {subject_preview}")
        
        # Test 6: Check data freshness
        print("6. Checking data freshness...")
        latest_email = session.query(EmailDB).order_by(EmailDB.received_time.desc()).first()
        if latest_email:
            print(f"   Latest email: {latest_email.received_time}")
            print(f"   From: {latest_email.sender}")
        
        session.close()
        db_engine.close()
        
        print("\nSUCCESS! Database configuration fix verification SUCCESSFUL!")
        print("System now correctly reads from main database with 34 email records")
        return True
        
    except Exception as e:
        print(f"   ERROR: {str(e)}")
        return False

def test_frontend_config():
    """Test frontend configuration"""
    print("\n=== Frontend Configuration Test ===\n")
    
    try:
        # Test frontend config import
        sys.path.insert(0, str(project_root / "frontend"))
        from config import Config
        
        print(f"1. Frontend DATABASE_URL: {Config.DATABASE_URL}")
        
        if "email_inbox.db" in Config.DATABASE_URL and "data/" not in Config.DATABASE_URL:
            print("   OK - Frontend config correctly points to main database")
        else:
            print("   ERROR - Frontend config still points to wrong database")
            return False
            
        return True
        
    except Exception as e:
        print(f"   ERROR - Frontend config test failed: {str(e)}")
        return False

def main():
    """Main verification function"""
    print("Database Configuration Fix Verification")
    print("="*50)
    
    # Test database connection
    db_success = test_database_connection()
    
    # Test frontend config
    frontend_success = test_frontend_config()
    
    # Final report
    print("\n" + "="*50)
    print("FINAL VERIFICATION REPORT:")
    print("="*50)
    
    if db_success and frontend_success:
        print("ALL TESTS PASSED!")
        print("Database configuration has been successfully fixed")
        print("System now reads from main database (34 email records)")
        print("Frontend configuration is correctly updated")
        print("\nThe database management page should now display all 34 email records.")
    else:
        print("SOME TESTS FAILED!")
        if not db_success:
            print("Database connection test failed")
        if not frontend_success:
            print("Frontend configuration test failed")
        print("\nPlease check the configuration and try again.")

if __name__ == "__main__":
    main()