{"timestamp": "2025-08-12 21:18:53", "base_url": "http://localhost:5000", "statistics": {"total_routes": 66, "successful_routes": 39, "failed_routes": 27, "total_resources": 55, "successful_resources": 50, "failed_resources": 5, "total_links": 16, "successful_links": 4, "failed_links": 12}, "failed_routes": [{"url": "/analytics/reports", "status_code": 500, "response_time": 0.01744699478149414, "error": "HTTP 500", "content_type": "application/json"}, {"url": "/analytics/static/analytics/test.txt", "status_code": 404, "response_time": 0.012414932250976562, "error": "HTTP 404", "content_type": "application/json"}, {"url": "/analytics/vendor-analysis", "status_code": 500, "response_time": 0.011000871658325195, "error": "HTTP 500", "content_type": "application/json"}, {"url": "/debug_sender_display.html", "status_code": 404, "response_time": 0.003243684768676758, "error": "HTTP 404", "content_type": "application/json"}, {"url": "/email/static/email/test.txt", "status_code": 404, "response_time": 0.005001068115234375, "error": "HTTP 404", "content_type": "application/json"}, {"url": "/eqc/compliance", "status_code": 500, "response_time": 0.04251694679260254, "error": "HTTP 500", "content_type": "application/json"}, {"url": "/eqc/ft-eqc", "status_code": 404, "response_time": 0.0038416385650634766, "error": "HTTP 404", "content_type": "application/json"}, {"url": "/eqc/ft-eqc-api", "status_code": 404, "response_time": 0.0025529861450195312, "error": "HTTP 404", "content_type": "application/json"}, {"url": "/eqc/quality-check", "status_code": 500, "response_time": 0.031461238861083984, "error": "HTTP 500", "content_type": "application/json"}, {"url": "/eqc/static/eqc/test.txt", "status_code": 404, "response_time": 0.0030007362365722656, "error": "HTTP 404", "content_type": "application/json"}, {"url": "/files/api/1/download", "status_code": 501, "response_time": 0.0010008811950683594, "error": "HTTP 501", "content_type": "application/json"}, {"url": "/files/browser", "status_code": 500, "response_time": 0.03267860412597656, "error": "HTTP 500", "content_type": "application/json"}, {"url": "/files/static/files/test.txt", "status_code": 404, "response_time": 0.006001710891723633, "error": "HTTP 404", "content_type": "application/json"}, {"url": "/files/upload", "status_code": 500, "response_time": 0.027166366577148438, "error": "HTTP 500", "content_type": "application/json"}, {"url": "/monitoring/health", "status_code": 500, "response_time": 0.004002094268798828, "error": "HTTP 500", "content_type": "application/json"}, {"url": "/monitoring/static/monitoring/test.txt", "status_code": 404, "response_time": 0.006015777587890625, "error": "HTTP 404", "content_type": "application/json"}, {"url": "/static/analytics/test.txt", "status_code": 404, "response_time": 0.0049970149993896484, "error": "HTTP 404", "content_type": "application/json"}, {"url": "/static/email/test.txt", "status_code": 404, "response_time": 0.004164695739746094, "error": "HTTP 404", "content_type": "application/json"}, {"url": "/static/eqc/test.txt", "status_code": 404, "response_time": 0.004536628723144531, "error": "HTTP 404", "content_type": "application/json"}, {"url": "/static/file_management/test.txt", "status_code": 404, "response_time": 0.00600433349609375, "error": "HTTP 404", "content_type": "application/json"}, {"url": "/static/monitoring/test.txt", "status_code": 404, "response_time": 0.004992008209228516, "error": "HTTP 404", "content_type": "application/json"}, {"url": "/static/shared/test.txt", "status_code": 404, "response_time": 0.005986928939819336, "error": "HTTP 404", "content_type": "application/json"}, {"url": "/static/tasks/test.txt", "status_code": 404, "response_time": 0.0041735172271728516, "error": "HTTP 404", "content_type": "application/json"}, {"url": "/static/test.txt", "status_code": 404, "response_time": 0.003985166549682617, "error": "HTTP 404", "content_type": "application/json"}, {"url": "/tasks/queue", "status_code": 500, "response_time": 0.004002809524536133, "error": "HTTP 500", "content_type": "application/json"}, {"url": "/tasks/static/tasks/test.txt", "status_code": 404, "response_time": 0.005995512008666992, "error": "HTTP 404", "content_type": "application/json"}, {"url": "/test", "status_code": 404, "response_time": 0.003002166748046875, "error": "HTTP 404", "content_type": "application/json"}, {"url": "javascript:void(0)", "status_code": 404, "response_time": 0.004003286361694336, "error": "HTTP 404", "content_type": "application/json"}, {"url": "/eqc/history", "status_code": 404, "response_time": 0.0033140182495117188, "error": "HTTP 404", "content_type": "application/json"}, {"url": "/tasks/scheduler-dashboard", "status_code": 404, "response_time": 0.0030028820037841797, "error": "HTTP 404", "content_type": "application/json"}, {"url": "/eqc/ui", "status_code": 404, "response_time": 0.002996206283569336, "error": "HTTP 404", "content_type": "application/json"}, {"url": "/files/network-browser", "status_code": 404, "response_time": 0.002996683120727539, "error": "HTTP 404", "content_type": "application/json"}], "failed_resources_by_type": {"link": [{"resource_type": "link", "url": "/eqc/ui", "source_page": "/email/", "status_code": 404, "error": "HTTP 404"}, {"resource_type": "link", "url": "/javascript:void(0)", "source_page": "/email/", "status_code": 404, "error": "HTTP 404"}, {"resource_type": "link", "url": "/tasks/scheduler-dashboard", "source_page": "/email/", "status_code": 404, "error": "HTTP 404"}, {"resource_type": "link", "url": "/files/network-browser", "source_page": "/email/", "status_code": 404, "error": "HTTP 404"}, {"resource_type": "link", "url": "/eqc/history", "source_page": "/eqc/", "status_code": 404, "error": "HTTP 404"}, {"resource_type": "link", "url": "/monitoring/health", "source_page": "/monitoring/", "status_code": 500, "error": "HTTP 500"}, {"resource_type": "link", "url": "/tasks/queue", "source_page": "/tasks/", "status_code": 500, "error": "HTTP 500"}], "js": [{"resource_type": "js", "url": "/eqc/static/eqc/js/eqc-processor.js", "source_page": "/eqc/", "status_code": 404, "error": "HTTP 404"}, {"resource_type": "js", "url": "/eqc/static/eqc/js/eqc-dashboard.js", "source_page": "/eqc/", "status_code": 404, "error": "HTTP 404"}, {"resource_type": "js", "url": "/files/static/files/js/file-manager.js", "source_page": "/files/", "status_code": 404, "error": "HTTP 404"}, {"resource_type": "js", "url": "/monitoring/static/monitoring/js/system-dashboard.js", "source_page": "/monitoring/", "status_code": 404, "error": "HTTP 404"}, {"resource_type": "js", "url": "/tasks/static/tasks/js/task-dashboard.js", "source_page": "/tasks/", "status_code": 404, "error": "HTTP 404"}]}, "summary": {"total_checks": 137, "total_failures": 44, "success_rate": 67.88}}