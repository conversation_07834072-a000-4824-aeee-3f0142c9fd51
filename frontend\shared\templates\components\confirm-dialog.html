<!-- 確認對話框 -->
<div class="dialog-overlay" id="confirm-dialog" style="display: none;">
    <div class="dialog-container">
        <div class="dialog-header">
            <h3 class="dialog-title" id="dialog-title">確認操作</h3>
            <button class="dialog-close" id="dialog-close" aria-label="關閉">
                <i class="fas fa-times"></i>
            </button>
        </div>
        
        <div class="dialog-body">
            <div class="dialog-icon" id="dialog-icon">
                <i class="fas fa-question-circle"></i>
            </div>
            <div class="dialog-content">
                <p class="dialog-message" id="dialog-message">您確定要執行此操作嗎？</p>
                <div class="dialog-details" id="dialog-details" style="display: none;"></div>
            </div>
        </div>
        
        <div class="dialog-footer">
            <button class="btn btn-secondary" id="dialog-cancel">取消</button>
            <button class="btn btn-primary" id="dialog-confirm">確認</button>
        </div>
    </div>
</div>

<!-- 確認對話框 JavaScript -->
<script>
class ConfirmDialog {
    constructor() {
        this.dialog = document.getElementById('confirm-dialog');
        this.dialogTitle = document.getElementById('dialog-title');
        this.dialogIcon = document.getElementById('dialog-icon');
        this.dialogMessage = document.getElementById('dialog-message');
        this.dialogDetails = document.getElementById('dialog-details');
        this.dialogClose = document.getElementById('dialog-close');
        this.dialogCancel = document.getElementById('dialog-cancel');
        this.dialogConfirm = document.getElementById('dialog-confirm');
        
        this.currentCallback = null;
        this.currentCancelCallback = null;
        
        this.init();
    }
    
    init() {
        if (!this.dialog) return;
        
        // 關閉按鈕事件
        if (this.dialogClose) {
            this.dialogClose.addEventListener('click', () => this.hide());
        }
        
        // 取消按鈕事件
        if (this.dialogCancel) {
            this.dialogCancel.addEventListener('click', () => {
                if (this.currentCancelCallback) {
                    this.currentCancelCallback();
                }
                this.hide();
            });
        }
        
        // 確認按鈕事件
        if (this.dialogConfirm) {
            this.dialogConfirm.addEventListener('click', () => {
                if (this.currentCallback) {
                    this.currentCallback();
                }
                this.hide();
            });
        }
        
        // 點擊背景關閉
        this.dialog.addEventListener('click', (e) => {
            if (e.target === this.dialog) {
                this.hide();
            }
        });
        
        // ESC 鍵關閉
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape' && this.isVisible()) {
                this.hide();
            }
        });
    }
    
    show(options = {}) {
        const {
            title = '確認操作',
            message = '您確定要執行此操作嗎？',
            details = null,
            confirmText = '確認',
            cancelText = '取消',
            type = 'question', // question, warning, danger, info
            onConfirm = null,
            onCancel = null
        } = options;
        
        // 設置標題
        if (this.dialogTitle) {
            this.dialogTitle.textContent = title;
        }
        
        // 設置圖示
        if (this.dialogIcon) {
            const iconElement = this.dialogIcon.querySelector('i');
            if (iconElement) {
                switch (type) {
                    case 'warning':
                        iconElement.className = 'fas fa-exclamation-triangle warning-icon';
                        break;
                    case 'danger':
                        iconElement.className = 'fas fa-exclamation-circle danger-icon';
                        break;
                    case 'info':
                        iconElement.className = 'fas fa-info-circle info-icon';
                        break;
                    default:
                        iconElement.className = 'fas fa-question-circle question-icon';
                }
            }
        }
        
        // 設置訊息
        if (this.dialogMessage) {
            this.dialogMessage.textContent = message;
        }
        
        // 設置詳細資訊
        if (this.dialogDetails) {
            if (details) {
                this.dialogDetails.innerHTML = details;
                this.dialogDetails.style.display = 'block';
            } else {
                this.dialogDetails.style.display = 'none';
            }
        }
        
        // 設置按鈕文字
        if (this.dialogConfirm) {
            this.dialogConfirm.textContent = confirmText;
            
            // 根據類型設置按鈕樣式
            this.dialogConfirm.className = 'btn';
            switch (type) {
                case 'danger':
                    this.dialogConfirm.classList.add('btn-danger');
                    break;
                case 'warning':
                    this.dialogConfirm.classList.add('btn-warning');
                    break;
                default:
                    this.dialogConfirm.classList.add('btn-primary');
            }
        }
        
        if (this.dialogCancel) {
            this.dialogCancel.textContent = cancelText;
        }
        
        // 設置回調函數
        this.currentCallback = onConfirm;
        this.currentCancelCallback = onCancel;
        
        // 顯示對話框
        this.dialog.style.display = 'flex';
        document.body.classList.add('dialog-open');
        
        // 聚焦到取消按鈕（安全選項）
        setTimeout(() => {
            if (this.dialogCancel) {
                this.dialogCancel.focus();
            }
        }, 100);
    }
    
    hide() {
        if (this.dialog) {
            this.dialog.style.display = 'none';
            document.body.classList.remove('dialog-open');
        }
        
        // 清除回調函數
        this.currentCallback = null;
        this.currentCancelCallback = null;
    }
    
    isVisible() {
        return this.dialog && this.dialog.style.display !== 'none';
    }
    
    // 便捷方法：確認刪除
    confirmDelete(itemName, onConfirm, options = {}) {
        this.show({
            title: '確認刪除',
            message: `您確定要刪除「${itemName}」嗎？`,
            details: '此操作無法復原，請謹慎操作。',
            confirmText: '刪除',
            cancelText: '取消',
            type: 'danger',
            onConfirm: onConfirm,
            ...options
        });
    }
    
    // 便捷方法：確認清空
    confirmClear(onConfirm, options = {}) {
        this.show({
            title: '確認清空',
            message: '您確定要清空所有資料嗎？',
            details: '此操作將刪除所有資料，且無法復原。',
            confirmText: '清空',
            cancelText: '取消',
            type: 'danger',
            onConfirm: onConfirm,
            ...options
        });
    }
    
    // 便捷方法：確認儲存
    confirmSave(onConfirm, options = {}) {
        this.show({
            title: '確認儲存',
            message: '您確定要儲存變更嗎？',
            confirmText: '儲存',
            cancelText: '取消',
            type: 'question',
            onConfirm: onConfirm,
            ...options
        });
    }
    
    // 便捷方法：確認離開
    confirmLeave(onConfirm, options = {}) {
        this.show({
            title: '確認離開',
            message: '您有未儲存的變更，確定要離開嗎？',
            details: '離開後未儲存的變更將會遺失。',
            confirmText: '離開',
            cancelText: '取消',
            type: 'warning',
            onConfirm: onConfirm,
            ...options
        });
    }
    
    // 便捷方法：確認重新載入
    confirmReload(onConfirm, options = {}) {
        this.show({
            title: '確認重新載入',
            message: '您確定要重新載入頁面嗎？',
            details: '重新載入後未儲存的變更將會遺失。',
            confirmText: '重新載入',
            cancelText: '取消',
            type: 'warning',
            onConfirm: onConfirm,
            ...options
        });
    }
}

// 全域確認對話框管理器
window.confirmDialog = new ConfirmDialog();
</script>