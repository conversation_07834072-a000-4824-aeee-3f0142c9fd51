#!/usr/bin/env python3
"""
錯誤處理一致性測試
驗證錯誤處理是否與原始版本一致
創建日期: 2025-08-13
作者: backend-architect agent
"""

import os
import sys
import json
import time
import requests
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Any, Tuple
import re
from urllib.parse import quote


class ErrorHandlingTester:
    """錯誤處理測試器"""
    
    def __init__(self, base_url: str = "http://localhost:8000"):
        self.base_url = base_url
        self.project_root = Path(__file__).parent
        self.session = requests.Session()
        self.session.timeout = 10
        
        self.results = {
            'test_metadata': {
                'test_start_time': datetime.now().isoformat(),
                'base_url': base_url,
                'tester': 'error-handling-tester'
            },
            'invalid_routes': {},
            'malformed_requests': {},
            'authentication_errors': {},
            'method_not_allowed': {},
            'server_errors': {},
            'api_error_responses': {},
            'error_page_consistency': {}
        }
        
        # 定義錯誤測試場景
        self.error_scenarios = {
            'invalid_routes': [
                {'path': '/nonexistent', 'expected_status': 404, 'description': '不存在的路由'},
                {'path': '/admin/secret', 'expected_status': 404, 'description': '未授權路由'},
                {'path': '/analytics/invalid', 'expected_status': 404, 'description': '分析模組無效路由'},
                {'path': '/email/nonexistent', 'expected_status': 404, 'description': '郵件模組無效路由'},
                {'path': '/eqc/invalid', 'expected_status': 404, 'description': 'EQC模組無效路由'},
                {'path': '/files/nonexistent', 'expected_status': 404, 'description': '檔案模組無效路由'},
                {'path': '/monitoring/invalid', 'expected_status': 404, 'description': '監控模組無效路由'},
                {'path': '/tasks/nonexistent', 'expected_status': 404, 'description': '任務模組無效路由'},
                {'path': '/api/invalid', 'expected_status': 404, 'description': 'API無效端點'},
                {'path': '/../../../etc/passwd', 'expected_status': 404, 'description': '路徑遍歷攻擊'},
                {'path': '/script.php', 'expected_status': 404, 'description': '非Python文件請求'}
            ],
            'malformed_requests': [
                {'path': '/analytics?param=', 'expected_status': [200, 400], 'description': '空參數值'},
                {'path': '/email?page=-1', 'expected_status': [200, 400, 422], 'description': '負數頁碼'},
                {'path': '/eqc?limit=999999', 'expected_status': [200, 400, 422], 'description': '過大限制值'},
                {'path': '/files?path=', 'expected_status': [200, 400], 'description': '空路徑參數'},
                {'path': '/api/status?format=invalid', 'expected_status': [200, 400], 'description': '無效格式參數'},
                {'path': '/monitoring?%invalid%=test', 'expected_status': [200, 400], 'description': '無效URL編碼'},
                {'path': '/tasks?' + 'x' * 1000, 'expected_status': [200, 400, 414], 'description': '過長查詢字符串'}
            ],
            'special_characters': [
                {'path': '/files/中文檔名', 'expected_status': [200, 404], 'description': '中文字符路徑'},
                {'path': '/email/test%20space', 'expected_status': [200, 404], 'description': 'URL編碼空格'},
                {'path': '/analytics/特殊字符', 'expected_status': [200, 404], 'description': '特殊字符路徑'},
                {'path': '/api/test?param=<script>', 'expected_status': [200, 400], 'description': 'HTML標籤參數'},
                {'path': '/eqc/file.txt%00.jpg', 'expected_status': [200, 400, 404], 'description': 'NULL字節注入'},
                {'path': '/monitoring/\'OR\'1\'=\'1', 'expected_status': [200, 400, 404], 'description': 'SQL注入嘗試'}
            ],
            'method_errors': [
                {'path': '/', 'method': 'POST', 'expected_status': [200, 405], 'description': 'POST到GET端點'},
                {'path': '/health', 'method': 'PUT', 'expected_status': [405], 'description': 'PUT到GET端點'},
                {'path': '/api/status', 'method': 'DELETE', 'expected_status': [405], 'description': 'DELETE到GET端點'},
                {'path': '/analytics', 'method': 'PATCH', 'expected_status': [405], 'description': 'PATCH到GET端點'}
            ],
            'api_errors': [
                {'path': '/api/nonexistent', 'expected_status': 404, 'description': '不存在的API端點'},
                {'path': '/api/analytics/invalid', 'expected_status': 404, 'description': '分析API無效端點'},
                {'path': '/api/email/nonexistent', 'expected_status': 404, 'description': '郵件API無效端點'},
                {'path': '/api/eqc/invalid', 'expected_status': 404, 'description': 'EQC API無效端點'},
                {'path': '/api/files/nonexistent', 'expected_status': 404, 'description': '檔案API無效端點'}
            ]
        }

    def log_error_test_result(self, category: str, test_name: str, 
                            actual_status: int, expected_status: Any,
                            response_data: Dict[str, Any], success: bool,
                            description: str = ""):
        """記錄錯誤測試結果"""
        if category not in self.results:
            self.results[category] = {}
            
        self.results[category][test_name] = {
            'description': description,
            'expected_status': expected_status,
            'actual_status': actual_status,
            'success': success,
            'response_data': response_data,
            'timestamp': datetime.now().isoformat()
        }
        
        status = "✅ PASS" if success else "❌ FAIL"
        expected_str = str(expected_status) if isinstance(expected_status, int) else f"[{','.join(map(str, expected_status))}]"
        print(f"{status} [{category}] {test_name}")
        print(f"    {description}")
        print(f"    期望狀態: {expected_str}, 實際狀態: {actual_status}")
        if response_data.get('error_page_quality'):
            print(f"    錯誤頁面品質: {response_data['error_page_quality']}")

    def check_service_health(self) -> bool:
        """檢查服務健康狀態"""
        try:
            response = self.session.get(f"{self.base_url}/health", timeout=5)
            return response.status_code == 200
        except:
            return False

    def analyze_error_response(self, response: requests.Response) -> Dict[str, Any]:
        """分析錯誤響應的品質"""
        analysis = {
            'status_code': response.status_code,
            'content_type': response.headers.get('content-type', ''),
            'content_length': len(response.content),
            'has_error_page': False,
            'error_page_quality': 'poor',
            'security_headers': {},
            'response_time': None
        }
        
        # 檢查是否是HTML錯誤頁面
        if 'text/html' in analysis['content_type']:
            content_lower = response.text.lower()
            
            # 檢查錯誤頁面指標
            error_indicators = [
                'error', '錯誤', 'not found', '找不到', 
                'internal server error', '內部伺服器錯誤',
                'bad request', '錯誤請求',
                'unauthorized', '未授權',
                'forbidden', '禁止'
            ]
            
            analysis['has_error_page'] = any(indicator in content_lower for indicator in error_indicators)
            
            # 評估錯誤頁面品質
            quality_indicators = {
                'has_title': '<title>' in content_lower,
                'has_message': any(word in content_lower for word in ['message', '訊息', 'error', '錯誤']),
                'has_navigation': any(word in content_lower for word in ['home', '首頁', 'back', '返回']),
                'has_styling': 'style' in content_lower or 'css' in content_lower,
                'reasonable_length': 100 < len(response.content) < 10000
            }
            
            quality_score = sum(quality_indicators.values())
            if quality_score >= 4:
                analysis['error_page_quality'] = 'good'
            elif quality_score >= 2:
                analysis['error_page_quality'] = 'fair'
            else:
                analysis['error_page_quality'] = 'poor'
            
            analysis['quality_details'] = quality_indicators
        
        # 檢查JSON錯誤響應
        elif 'application/json' in analysis['content_type']:
            try:
                json_data = response.json()
                analysis['json_response'] = json_data
                analysis['has_error_message'] = 'error' in json_data or 'message' in json_data
                analysis['error_page_quality'] = 'api_response'
            except:
                analysis['json_parse_error'] = True
        
        # 檢查安全標頭
        security_headers = ['x-content-type-options', 'x-frame-options', 'x-xss-protection']
        for header in security_headers:
            analysis['security_headers'][header] = response.headers.get(header)
        
        return analysis

    def test_invalid_routes(self) -> Dict[str, Any]:
        """測試無效路由處理"""
        print("\n🚫 測試無效路由處理...")
        
        invalid_route_results = {}
        
        for scenario in self.error_scenarios['invalid_routes']:
            path = scenario['path']
            expected_status = scenario['expected_status']
            description = scenario['description']
            
            test_name = path.replace('/', '_').replace('.', '_')
            full_url = f"{self.base_url}{path}"
            
            try:
                start_time = time.time()
                response = self.session.get(full_url)
                response_time = time.time() - start_time
                
                # 分析響應
                analysis = self.analyze_error_response(response)
                analysis['response_time'] = response_time
                
                # 檢查狀態碼是否符合預期
                status_ok = response.status_code == expected_status
                
                # 對於404錯誤，檢查是否有適當的錯誤頁面
                if response.status_code == 404:
                    error_handling_ok = (analysis['has_error_page'] or 
                                       analysis['content_length'] > 50)  # 至少有一些錯誤內容
                else:
                    error_handling_ok = True
                
                overall_success = status_ok and error_handling_ok
                
                self.log_error_test_result('invalid_routes', test_name,
                                         response.status_code, expected_status,
                                         analysis, overall_success, description)
                
                invalid_route_results[test_name] = {
                    'path': path,
                    'description': description,
                    'analysis': analysis,
                    'success': overall_success
                }
                
            except Exception as e:
                error_analysis = {'error': str(e), 'exception_occurred': True}
                
                self.log_error_test_result('invalid_routes', test_name,
                                         0, expected_status, error_analysis,
                                         False, f"{description} (異常: {str(e)})")
                
                invalid_route_results[test_name] = {
                    'path': path,
                    'description': description,
                    'error': str(e),
                    'success': False
                }
        
        return invalid_route_results

    def test_malformed_requests(self) -> Dict[str, Any]:
        """測試格式錯誤的請求處理"""
        print("\n🔧 測試格式錯誤的請求處理...")
        
        malformed_results = {}
        
        for scenario in self.error_scenarios['malformed_requests']:
            path = scenario['path']
            expected_statuses = scenario['expected_status']
            description = scenario['description']
            
            test_name = path.split('?')[0].replace('/', '_') + '_malformed'
            full_url = f"{self.base_url}{path}"
            
            try:
                start_time = time.time()
                response = self.session.get(full_url)
                response_time = time.time() - start_time
                
                # 分析響應
                analysis = self.analyze_error_response(response)
                analysis['response_time'] = response_time
                
                # 檢查狀態碼是否在預期範圍內
                if isinstance(expected_statuses, list):
                    status_ok = response.status_code in expected_statuses
                else:
                    status_ok = response.status_code == expected_statuses
                
                # 檢查是否有適當的錯誤處理
                if response.status_code >= 400:
                    error_handling_ok = (analysis.get('has_error_page', False) or 
                                       analysis.get('has_error_message', False) or
                                       analysis['content_length'] > 20)
                else:
                    error_handling_ok = True  # 200響應也是可接受的
                
                overall_success = status_ok and error_handling_ok
                
                self.log_error_test_result('malformed_requests', test_name,
                                         response.status_code, expected_statuses,
                                         analysis, overall_success, description)
                
                malformed_results[test_name] = {
                    'path': path,
                    'description': description,
                    'analysis': analysis,
                    'success': overall_success
                }
                
            except Exception as e:
                error_analysis = {'error': str(e), 'exception_occurred': True}
                
                self.log_error_test_result('malformed_requests', test_name,
                                         0, expected_statuses, error_analysis,
                                         False, f"{description} (異常: {str(e)})")
                
                malformed_results[test_name] = {
                    'path': path,
                    'description': description,
                    'error': str(e),
                    'success': False
                }
        
        return malformed_results

    def test_method_not_allowed(self) -> Dict[str, Any]:
        """測試不允許的HTTP方法處理"""
        print("\n🚷 測試HTTP方法錯誤處理...")
        
        method_error_results = {}
        
        for scenario in self.error_scenarios['method_errors']:
            path = scenario['path']
            method = scenario['method']
            expected_statuses = scenario['expected_status']
            description = scenario['description']
            
            test_name = f"{path.replace('/', '_')}_{method.lower()}"
            full_url = f"{self.base_url}{path}"
            
            try:
                start_time = time.time()
                response = self.session.request(method, full_url)
                response_time = time.time() - start_time
                
                # 分析響應
                analysis = self.analyze_error_response(response)
                analysis['response_time'] = response_time
                analysis['method_used'] = method
                
                # 檢查狀態碼
                if isinstance(expected_statuses, list):
                    status_ok = response.status_code in expected_statuses
                else:
                    status_ok = response.status_code == expected_statuses
                
                # 檢查Allow標頭（對於405錯誤）
                if response.status_code == 405:
                    analysis['allow_header'] = response.headers.get('allow')
                    has_allow_header = 'allow' in response.headers
                    analysis['has_allow_header'] = has_allow_header
                else:
                    has_allow_header = True  # 非405錯誤不需要Allow標頭
                
                overall_success = status_ok and has_allow_header
                
                self.log_error_test_result('method_not_allowed', test_name,
                                         response.status_code, expected_statuses,
                                         analysis, overall_success, description)
                
                method_error_results[test_name] = {
                    'path': path,
                    'method': method,
                    'description': description,
                    'analysis': analysis,
                    'success': overall_success
                }
                
            except Exception as e:
                error_analysis = {'error': str(e), 'exception_occurred': True, 'method_used': method}
                
                self.log_error_test_result('method_not_allowed', test_name,
                                         0, expected_statuses, error_analysis,
                                         False, f"{description} (異常: {str(e)})")
                
                method_error_results[test_name] = {
                    'path': path,
                    'method': method,
                    'description': description,
                    'error': str(e),
                    'success': False
                }
        
        return method_error_results

    def test_special_character_handling(self) -> Dict[str, Any]:
        """測試特殊字符處理"""
        print("\n🌐 測試特殊字符處理...")
        
        special_char_results = {}
        
        for scenario in self.error_scenarios['special_characters']:
            path = scenario['path']
            expected_statuses = scenario['expected_status']
            description = scenario['description']
            
            test_name = f"special_char_{len(special_char_results)}"
            
            # 對於包含特殊字符的路徑，我們需要適當編碼
            try:
                # 嘗試使用原始路徑
                full_url = f"{self.base_url}{path}"
                
                start_time = time.time()
                response = self.session.get(full_url)
                response_time = time.time() - start_time
                
                # 分析響應
                analysis = self.analyze_error_response(response)
                analysis['response_time'] = response_time
                analysis['original_path'] = path
                
                # 檢查狀態碼
                if isinstance(expected_statuses, list):
                    status_ok = response.status_code in expected_statuses
                else:
                    status_ok = response.status_code == expected_statuses
                
                # 檢查是否有適當的錯誤處理或正常處理
                if response.status_code >= 400:
                    error_handling_ok = (analysis.get('has_error_page', False) or 
                                       analysis.get('has_error_message', False) or
                                       analysis['content_length'] > 20)
                else:
                    error_handling_ok = True
                
                overall_success = status_ok and error_handling_ok
                
                self.log_error_test_result('special_characters', test_name,
                                         response.status_code, expected_statuses,
                                         analysis, overall_success, description)
                
                special_char_results[test_name] = {
                    'path': path,
                    'description': description,
                    'analysis': analysis,
                    'success': overall_success
                }
                
            except Exception as e:
                error_analysis = {'error': str(e), 'exception_occurred': True, 'original_path': path}
                
                self.log_error_test_result('special_characters', test_name,
                                         0, expected_statuses, error_analysis,
                                         False, f"{description} (異常: {str(e)})")
                
                special_char_results[test_name] = {
                    'path': path,
                    'description': description,
                    'error': str(e),
                    'success': False
                }
        
        return special_char_results

    def test_api_error_responses(self) -> Dict[str, Any]:
        """測試API錯誤響應"""
        print("\n🔌 測試API錯誤響應...")
        
        api_error_results = {}
        
        for scenario in self.error_scenarios['api_errors']:
            path = scenario['path']
            expected_status = scenario['expected_status']
            description = scenario['description']
            
            test_name = path.replace('/', '_').replace('api_', '')
            full_url = f"{self.base_url}{path}"
            
            try:
                start_time = time.time()
                response = self.session.get(full_url)
                response_time = time.time() - start_time
                
                # 分析響應
                analysis = self.analyze_error_response(response)
                analysis['response_time'] = response_time
                
                # 檢查狀態碼
                status_ok = response.status_code == expected_status
                
                # 對於API端點，期望JSON響應或至少有結構化的錯誤
                if response.status_code >= 400:
                    # 檢查是否有適當的錯誤響應
                    api_error_ok = (
                        'application/json' in analysis['content_type'] or
                        analysis.get('has_error_message', False) or
                        analysis['content_length'] > 10
                    )
                else:
                    api_error_ok = True
                
                overall_success = status_ok and api_error_ok
                
                self.log_error_test_result('api_error_responses', test_name,
                                         response.status_code, expected_status,
                                         analysis, overall_success, description)
                
                api_error_results[test_name] = {
                    'path': path,
                    'description': description,
                    'analysis': analysis,
                    'success': overall_success
                }
                
            except Exception as e:
                error_analysis = {'error': str(e), 'exception_occurred': True}
                
                self.log_error_test_result('api_error_responses', test_name,
                                         0, expected_status, error_analysis,
                                         False, f"{description} (異常: {str(e)})")
                
                api_error_results[test_name] = {
                    'path': path,
                    'description': description,
                    'error': str(e),
                    'success': False
                }
        
        return api_error_results

    def test_error_page_consistency(self) -> Dict[str, Any]:
        """測試錯誤頁面一致性"""
        print("\n📄 測試錯誤頁面一致性...")
        
        # 測試不同類型的錯誤是否有一致的錯誤頁面
        error_types = {
            '404_frontend': '/nonexistent/page',
            '404_api': '/api/nonexistent',
            '404_module': '/analytics/nonexistent'
        }
        
        consistency_results = {}
        error_page_templates = {}
        
        for error_type, test_path in error_types.items():
            full_url = f"{self.base_url}{test_path}"
            
            try:
                response = self.session.get(full_url)
                analysis = self.analyze_error_response(response)
                
                # 提取錯誤頁面模板特徵
                if 'text/html' in analysis['content_type']:
                    template_features = {
                        'has_title': analysis['quality_details'].get('has_title', False),
                        'has_styling': analysis['quality_details'].get('has_styling', False),
                        'has_navigation': analysis['quality_details'].get('has_navigation', False),
                        'content_length_range': 'small' if analysis['content_length'] < 1000 else 'large'
                    }
                    error_page_templates[error_type] = template_features
                
                consistency_results[error_type] = {
                    'path': test_path,
                    'analysis': analysis,
                    'status_code': response.status_code
                }
                
                self.log_error_test_result('error_page_consistency', error_type,
                                         response.status_code, 404, analysis,
                                         response.status_code == 404, f"測試{error_type}錯誤頁面")
                
            except Exception as e:
                consistency_results[error_type] = {
                    'path': test_path,
                    'error': str(e)
                }
        
        # 分析一致性
        if len(error_page_templates) > 1:
            template_values = list(error_page_templates.values())
            first_template = template_values[0]
            
            consistency_score = 0
            total_features = len(first_template)
            
            for feature_name in first_template:
                feature_values = [template.get(feature_name) for template in template_values]
                if len(set(feature_values)) == 1:  # 所有值相同
                    consistency_score += 1
            
            consistency_percentage = (consistency_score / total_features) * 100
            
            consistency_results['overall_consistency'] = {
                'consistency_percentage': consistency_percentage,
                'consistent_features': consistency_score,
                'total_features': total_features,
                'templates_analyzed': error_page_templates
            }
            
            self.log_error_test_result('error_page_consistency', 'overall_consistency',
                                     int(consistency_percentage), 80, 
                                     consistency_results['overall_consistency'],
                                     consistency_percentage >= 80, 
                                     f"錯誤頁面一致性分析")
        
        return consistency_results

    def run_complete_error_handling_test(self) -> Dict[str, Any]:
        """執行完整的錯誤處理測試"""
        print("=" * 80)
        print("🔬 錯誤處理一致性測試系統")
        print(f"測試目標: {self.base_url}")
        print("=" * 80)
        
        # 檢查服務是否運行
        if not self.check_service_health():
            print("❌ 服務未運行，請先啟動服務")
            return None
        
        print("✅ 服務運行正常，開始錯誤處理測試...")
        
        try:
            # 1. 測試無效路由處理
            self.results['invalid_routes'] = self.test_invalid_routes()
            
            # 2. 測試格式錯誤的請求
            self.results['malformed_requests'] = self.test_malformed_requests()
            
            # 3. 測試HTTP方法錯誤
            self.results['method_not_allowed'] = self.test_method_not_allowed()
            
            # 4. 測試特殊字符處理
            self.results['special_characters'] = self.test_special_character_handling()
            
            # 5. 測試API錯誤響應
            self.results['api_error_responses'] = self.test_api_error_responses()
            
            # 6. 測試錯誤頁面一致性
            self.results['error_page_consistency'] = self.test_error_page_consistency()
            
            # 添加測試完成時間
            self.results['test_metadata']['test_end_time'] = datetime.now().isoformat()
            self.results['test_metadata']['total_duration'] = (
                datetime.fromisoformat(self.results['test_metadata']['test_end_time']) - 
                datetime.fromisoformat(self.results['test_metadata']['test_start_time'])
            ).total_seconds()
            
            return self.results
            
        except Exception as e:
            print(f"❌ 錯誤處理測試過程中發生錯誤: {str(e)}")
            return None
        finally:
            self.session.close()

    def save_results(self, filename: str = None) -> str:
        """保存測試結果"""
        if filename is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"error_handling_report_{timestamp}.json"
        
        filepath = self.project_root / filename
        
        with open(filepath, 'w', encoding='utf-8') as f:
            json.dump(self.results, f, indent=2, ensure_ascii=False)
        
        print(f"\n📄 錯誤處理測試報告已保存: {filepath}")
        return str(filepath)

    def print_summary(self):
        """打印測試摘要"""
        print("\n" + "=" * 80)
        print("📋 錯誤處理測試摘要")
        print("=" * 80)
        
        total_tests = 0
        successful_tests = 0
        
        for category, tests in self.results.items():
            if category == 'test_metadata':
                continue
            
            category_total = 0
            category_success = 0
            
            for test_name, test_data in tests.items():
                if isinstance(test_data, dict) and 'success' in test_data:
                    category_total += 1
                    total_tests += 1
                    if test_data['success']:
                        category_success += 1
                        successful_tests += 1
            
            if category_total > 0:
                success_rate = (category_success / category_total) * 100
                print(f"{category.replace('_', ' ').title()}: {category_success}/{category_total} 通過 ({success_rate:.1f}%)")
        
        if total_tests > 0:
            overall_success_rate = (successful_tests / total_tests) * 100
            print(f"\n總體錯誤處理: {successful_tests}/{total_tests} 通過 ({overall_success_rate:.1f}%)")
            
            if overall_success_rate >= 90:
                print("✅ 錯誤處理品質: 優秀")
            elif overall_success_rate >= 75:
                print("⚠️ 錯誤處理品質: 良好")
            else:
                print("❌ 錯誤處理品質: 需要改進")
        
        # 一致性摘要
        if 'error_page_consistency' in self.results:
            consistency_data = self.results['error_page_consistency'].get('overall_consistency')
            if consistency_data:
                consistency_pct = consistency_data['consistency_percentage']
                print(f"錯誤頁面一致性: {consistency_pct:.1f}%")
        
        print("=" * 80)


def main():
    """主函數"""
    if len(sys.argv) > 1 and sys.argv[1] == '--help':
        print("""
錯誤處理一致性測試系統
用法: python error_handling_test.py [選項]

選項:
  --help          顯示此幫助信息
  --url URL       指定測試URL (默認: http://localhost:8000)

測試項目:
  1. 無效路由處理測試
  2. 格式錯誤請求處理測試
  3. HTTP方法錯誤處理測試
  4. 特殊字符處理測試
  5. API錯誤響應測試
  6. 錯誤頁面一致性測試

範例:
  python error_handling_test.py
  python error_handling_test.py --url http://localhost:5000
        """)
        return
    
    # 解析命令行參數
    base_url = "http://localhost:8000"
    
    for i, arg in enumerate(sys.argv):
        if arg == '--url' and i + 1 < len(sys.argv):
            base_url = sys.argv[i + 1]
    
    # 創建測試器並運行測試
    tester = ErrorHandlingTester(base_url)
    results = tester.run_complete_error_handling_test()
    
    if results:
        # 保存結果
        report_file = tester.save_results()
        
        # 打印摘要
        tester.print_summary()
        
        print(f"\n✅ 錯誤處理測試完成！詳細報告: {report_file}")
    else:
        print("\n❌ 錯誤處理測試失敗")
        sys.exit(1)


if __name__ == "__main__":
    main()