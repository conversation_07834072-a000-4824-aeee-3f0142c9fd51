# 半導體郵件處理系統 - 一鍵開發環境啟動腳本
# 自動化設定開發環境並啟動應用程式

param(
    [switch]$SkipVerification,  # 跳過環境驗證
    [switch]$FrontendOnly,      # 僅啟動前端
    [switch]$BackendOnly,       # 僅啟動後端
    [switch]$Help               # 顯示幫助
)

if ($Help) {
    Write-Host "🚀 半導體郵件處理系統 - 開發環境啟動腳本" -ForegroundColor Magenta
    Write-Host ""
    Write-Host "用法:" -ForegroundColor Yellow
    Write-Host "  .\start_dev.ps1                    # 完整開發環境啟動"
    Write-Host "  .\start_dev.ps1 -FrontendOnly      # 僅啟動前端"
    Write-Host "  .\start_dev.ps1 -BackendOnly       # 僅啟動後端"
    Write-Host "  .\start_dev.ps1 -SkipVerification  # 跳過環境驗證"
    Write-Host "  .\start_dev.ps1 -Help              # 顯示此幫助"
    Write-Host ""
    Write-Host "更多資訊請參考: docs/development/DEVELOPMENT_SETUP_GUIDE.md" -ForegroundColor Cyan
    exit 0
}

Write-Host "🚀 半導體郵件處理系統 - 開發環境啟動" -ForegroundColor Magenta
Write-Host "=" * 60 -ForegroundColor Gray

# 步驟 1: 載入開發環境
Write-Host ""
Write-Host "📋 步驟 1/4: 載入開發環境..." -ForegroundColor Yellow
try {
    . .\dev_env.ps1
    Write-Host "✅ 開發環境載入成功" -ForegroundColor Green
} catch {
    Write-Host "❌ 開發環境載入失敗: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "💡 請確認 dev_env.ps1 檔案存在且可執行" -ForegroundColor Cyan
    exit 1
}

# 步驟 2: 環境驗證 (可選)
if (-not $SkipVerification) {
    Write-Host ""
    Write-Host "📋 步驟 2/4: 環境驗證..." -ForegroundColor Yellow
    
    if (Test-Path "scripts/verify_environment.py") {
        try {
            $verifyResult = python scripts/verify_environment.py
            if ($LASTEXITCODE -eq 0) {
                Write-Host "✅ 環境驗證通過" -ForegroundColor Green
            } else {
                Write-Host "⚠️  環境驗證有警告，但可以繼續" -ForegroundColor Yellow
                Write-Host "💡 執行 'python scripts/verify_environment.py' 查看詳細資訊" -ForegroundColor Cyan
            }
        } catch {
            Write-Host "⚠️  環境驗證腳本執行失敗，跳過驗證" -ForegroundColor Yellow
        }
    } else {
        Write-Host "⚠️  環境驗證腳本不存在，跳過驗證" -ForegroundColor Yellow
    }
} else {
    Write-Host ""
    Write-Host "📋 步驟 2/4: 跳過環境驗證 (使用 -SkipVerification)" -ForegroundColor Yellow
}

# 步驟 3: 檢查結構
Write-Host ""
Write-Host "📋 步驟 3/4: 檢查專案結構..." -ForegroundColor Yellow

$structureOK = $true

# 檢查前端目錄
if (Test-Path "frontend/app.py") {
    Write-Host "  ✅ 前端應用程式檔案存在" -ForegroundColor Green
} else {
    Write-Host "  ❌ 前端應用程式檔案不存在" -ForegroundColor Red
    $structureOK = $false
}

# 檢查模組目錄
$modules = @('email', 'analytics', 'file_management', 'eqc', 'tasks', 'monitoring', 'shared')
foreach ($module in $modules) {
    if (Test-Path "frontend/$module") {
        Write-Host "  ✅ $module 模組存在" -ForegroundColor Green
    } else {
        Write-Host "  ⚠️  $module 模組不存在" -ForegroundColor Yellow
    }
}

if (-not $structureOK) {
    Write-Host "❌ 專案結構檢查失敗" -ForegroundColor Red
    Write-Host "💡 請確認您在正確的專案根目錄中" -ForegroundColor Cyan
    exit 1
}

# 步驟 4: 啟動服務
Write-Host ""
Write-Host "📋 步驟 4/4: 啟動服務..." -ForegroundColor Yellow

if ($FrontendOnly) {
    Write-Host "🎨 啟動前端開發模式..." -ForegroundColor Cyan
    Write-Host "💡 前端將在 http://localhost:5000 啟動" -ForegroundColor Cyan
    Write-Host ""
    python frontend/app.py
} elseif ($BackendOnly) {
    Write-Host "⚙️ 啟動後端開發模式..." -ForegroundColor Cyan
    Write-Host "💡 後端服務將在 http://localhost:8010 啟動" -ForegroundColor Cyan
    Write-Host ""
    python start_integrated_services.py
} else {
    Write-Host "🚀 完整開發模式啟動..." -ForegroundColor Cyan
    Write-Host ""
    Write-Host "選擇啟動模式:" -ForegroundColor Yellow
    Write-Host "  1. 前端開發 (Flask 應用程式)" -ForegroundColor White
    Write-Host "  2. 後端開發 (整合服務)" -ForegroundColor White
    Write-Host "  3. 前端 + 後端 (需要兩個終端)" -ForegroundColor White
    Write-Host "  4. 退出" -ForegroundColor White
    Write-Host ""
    
    do {
        $choice = Read-Host "請選擇 (1-4)"
        switch ($choice) {
            "1" {
                Write-Host "🎨 啟動前端應用程式..." -ForegroundColor Cyan
                Write-Host "💡 訪問 http://localhost:5000" -ForegroundColor Cyan
                Write-Host ""
                python frontend/app.py
                break
            }
            "2" {
                Write-Host "⚙️ 啟動後端整合服務..." -ForegroundColor Cyan
                Write-Host "💡 訪問 http://localhost:8010" -ForegroundColor Cyan
                Write-Host ""
                python start_integrated_services.py
                break
            }
            "3" {
                Write-Host "🚀 完整開發模式..." -ForegroundColor Cyan
                Write-Host ""
                Write-Host "請在兩個不同的終端視窗中執行以下命令:" -ForegroundColor Yellow
                Write-Host "終端 1: .\start_dev.ps1 -FrontendOnly" -ForegroundColor White
                Write-Host "終端 2: .\start_dev.ps1 -BackendOnly" -ForegroundColor White
                Write-Host ""
                Write-Host "或者使用 Makefile:" -ForegroundColor Yellow
                Write-Host "終端 1: make frontend-dev" -ForegroundColor White
                Write-Host "終端 2: make backend-dev" -ForegroundColor White
                break
            }
            "4" {
                Write-Host "👋 退出開發環境啟動" -ForegroundColor Yellow
                exit 0
            }
            default {
                Write-Host "❌ 無效選擇，請輸入 1-4" -ForegroundColor Red
            }
        }
    } while ($choice -notin @("1", "2", "3", "4"))
}

Write-Host ""
Write-Host "🎉 開發環境啟動完成！" -ForegroundColor Green
Write-Host "📚 更多開發指南: docs/development/" -ForegroundColor Cyan