<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>健康檢查 - Outlook Summary</title>
    <link rel="stylesheet" href="{{ url_for('monitoring.static', filename='css/monitoring.css') }}?v={{ range(1000, 9999) | random }}">
    <link rel="stylesheet" href="{{ url_for('shared.static', filename='css/global.css') }}">
</head>
<body>
    <div class="health-check-container">
        <header class="health-header">
            <h1>系統健康檢查</h1>
            <div class="health-summary">
                <div class="overall-health {{ overall_health.status }}">
                    <div class="health-icon">
                        {% if overall_health.status == 'healthy' %}✅
                        {% elif overall_health.status == 'warning' %}⚠️
                        {% elif overall_health.status == 'critical' %}🚨
                        {% endif %}
                    </div>
                    <div class="health-info">
                        <div class="health-status">{{ overall_health.status_display }}</div>
                        <div class="health-score">{{ overall_health.score }}/100</div>
                    </div>
                </div>
                <div class="check-stats">
                    <div class="stat-item">
                        <span class="stat-number">{{ stats.total_checks }}</span>
                        <span class="stat-label">檢查項目</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-number success">{{ stats.passed_checks }}</span>
                        <span class="stat-label">通過</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-number warning">{{ stats.warning_checks }}</span>
                        <span class="stat-label">警告</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-number error">{{ stats.failed_checks }}</span>
                        <span class="stat-label">失敗</span>
                    </div>
                </div>
            </div>
            <div class="header-actions">
                <button id="run-all-checks-btn" class="btn btn-primary">
                    <span class="btn-icon">🔍</span>
                    <span class="btn-text">執行全部檢查</span>
                </button>
                <button id="schedule-checks-btn" class="btn btn-secondary">
                    <span class="btn-icon">📅</span>
                    <span class="btn-text">排程檢查</span>
                </button>
                <button id="export-health-report-btn" class="btn btn-outline">
                    <span class="btn-icon">📊</span>
                    <span class="btn-text">匯出報告</span>
                </button>
                <a href="{{ url_for('monitoring.dashboard') }}" class="btn btn-outline">
                    <span class="btn-icon">📈</span>
                    <span class="btn-text">系統儀表板</span>
                </a>
            </div>
        </header>

        <div class="health-content">
            <!-- 快速檢查面板 -->
            <div class="quick-check-section">
                <div class="quick-check-card">
                    <h3>⚡ 快速檢查</h3>
                    <div class="quick-check-grid">
                        <button class="quick-check-btn" onclick="runQuickCheck('system')" data-check="system">
                            <div class="check-icon">🖥️</div>
                            <div class="check-name">系統狀態</div>
                            <div class="check-status" id="system-status">檢查中...</div>
                        </button>
                        
                        <button class="quick-check-btn" onclick="runQuickCheck('database')" data-check="database">
                            <div class="check-icon">🗄️</div>
                            <div class="check-name">資料庫連線</div>
                            <div class="check-status" id="database-status">檢查中...</div>
                        </button>
                        
                        <button class="quick-check-btn" onclick="runQuickCheck('services')" data-check="services">
                            <div class="check-icon">⚙️</div>
                            <div class="check-name">服務狀態</div>
                            <div class="check-status" id="services-status">檢查中...</div>
                        </button>
                        
                        <button class="quick-check-btn" onclick="runQuickCheck('network')" data-check="network">
                            <div class="check-icon">🌐</div>
                            <div class="check-name">網路連線</div>
                            <div class="check-status" id="network-status">檢查中...</div>
                        </button>
                        
                        <button class="quick-check-btn" onclick="runQuickCheck('storage')" data-check="storage">
                            <div class="check-icon">💾</div>
                            <div class="check-name">儲存空間</div>
                            <div class="check-status" id="storage-status">檢查中...</div>
                        </button>
                        
                        <button class="quick-check-btn" onclick="runQuickCheck('security')" data-check="security">
                            <div class="check-icon">🔒</div>
                            <div class="check-name">安全狀態</div>
                            <div class="check-status" id="security-status">檢查中...</div>
                        </button>
                    </div>
                </div>
            </div>

            <!-- 詳細健康檢查 -->
            <div class="detailed-checks-section">
                <div class="checks-tabs">
                    <button class="tab-btn active" data-tab="system-checks">系統檢查</button>
                    <button class="tab-btn" data-tab="service-checks">服務檢查</button>
                    <button class="tab-btn" data-tab="security-checks">安全檢查</button>
                    <button class="tab-btn" data-tab="performance-checks">效能檢查</button>
                    <button class="tab-btn" data-tab="custom-checks">自訂檢查</button>
                </div>

                <!-- 系統檢查 -->
                <div class="tab-content active" id="system-checks-tab">
                    <div class="checks-header">
                        <h3>🖥️ 系統檢查</h3>
                        <div class="checks-actions">
                            <button id="run-system-checks-btn" class="btn btn-sm btn-primary">執行檢查</button>
                            <button id="configure-system-checks-btn" class="btn btn-sm btn-secondary">配置</button>
                        </div>
                    </div>

                    <div class="check-list">
                        {% for check in system_checks %}
                        <div class="check-item {{ check.status }}" data-check-id="{{ check.id }}">
                            <div class="check-header">
                                <div class="check-info">
                                    <div class="check-status-icon">
                                        {% if check.status == 'pass' %}✅
                                        {% elif check.status == 'warning' %}⚠️
                                        {% elif check.status == 'fail' %}❌
                                        {% elif check.status == 'running' %}🔄
                                        {% else %}⭕
                                        {% endif %}
                                    </div>
                                    <div class="check-details">
                                        <h4 class="check-name">{{ check.name }}</h4>
                                        <p class="check-description">{{ check.description }}</p>
                                    </div>
                                </div>
                                <div class="check-meta">
                                    <div class="check-timing">
                                        <span class="last-run">最後執行: {{ check.last_run.strftime('%Y-%m-%d %H:%M') if check.last_run else '從未' }}</span>
                                        <span class="duration">耗時: {{ check.duration }}s</span>
                                    </div>
                                    <div class="check-actions">
                                        <button class="btn btn-sm btn-primary" onclick="runSingleCheck('{{ check.id }}')">執行</button>
                                        <button class="btn btn-sm btn-outline" onclick="viewCheckDetails('{{ check.id }}')">詳情</button>
                                    </div>
                                </div>
                            </div>
                            
                            {% if check.result_message %}
                            <div class="check-result">
                                <div class="result-message">{{ check.result_message }}</div>
                                {% if check.recommendations %}
                                <div class="result-recommendations">
                                    <strong>建議:</strong>
                                    <ul>
                                        {% for rec in check.recommendations %}
                                        <li>{{ rec }}</li>
                                        {% endfor %}
                                    </ul>
                                </div>
                                {% endif %}
                            </div>
                            {% endif %}
                        </div>
                        {% endfor %}
                    </div>
                </div>

                <!-- 服務檢查 -->
                <div class="tab-content" id="service-checks-tab">
                    <div class="checks-header">
                        <h3>⚙️ 服務檢查</h3>
                        <div class="checks-actions">
                            <button id="run-service-checks-btn" class="btn btn-sm btn-primary">執行檢查</button>
                            <button id="add-service-check-btn" class="btn btn-sm btn-secondary">新增服務</button>
                        </div>
                    </div>

                    <div class="service-grid">
                        {% for service in services %}
                        <div class="service-check-card {{ service.status }}" data-service-id="{{ service.id }}">
                            <div class="service-header">
                                <div class="service-icon">{{ service.icon }}</div>
                                <div class="service-info">
                                    <h4 class="service-name">{{ service.name }}</h4>
                                    <div class="service-url">{{ service.url or service.endpoint }}</div>
                                </div>
                                <div class="service-status {{ service.status }}">
                                    <div class="status-indicator"></div>
                                    <span class="status-text">{{ service.status_display }}</span>
                                </div>
                            </div>

                            <div class="service-metrics">
                                <div class="metric-row">
                                    <div class="metric">
                                        <span class="metric-label">回應時間:</span>
                                        <span class="metric-value {{ 'good' if service.response_time < 200 else 'warning' if service.response_time < 1000 else 'bad' }}">
                                            {{ service.response_time }}ms
                                        </span>
                                    </div>
                                    <div class="metric">
                                        <span class="metric-label">可用性:</span>
                                        <span class="metric-value">{{ '%.1f'|format(service.uptime_percent) }}%</span>
                                    </div>
                                </div>
                                <div class="metric-row">
                                    <div class="metric">
                                        <span class="metric-label">最後檢查:</span>
                                        <span class="metric-value">{{ service.last_check.strftime('%H:%M:%S') if service.last_check else 'N/A' }}</span>
                                    </div>
                                    <div class="metric">
                                        <span class="metric-label">狀態碼:</span>
                                        <span class="metric-value">{{ service.status_code or 'N/A' }}</span>
                                    </div>
                                </div>
                            </div>

                            <div class="service-health-chart">
                                <canvas id="service-chart-{{ service.id }}" width="200" height="60"></canvas>
                            </div>

                            <div class="service-actions">
                                <button class="btn btn-sm btn-primary" onclick="checkService('{{ service.id }}')">檢查</button>
                                <button class="btn btn-sm btn-secondary" onclick="viewServiceLogs('{{ service.id }}')">日誌</button>
                                <button class="btn btn-sm btn-outline" onclick="editService('{{ service.id }}')">編輯</button>
                                {% if service.can_restart %}
                                <button class="btn btn-sm btn-warning" onclick="restartService('{{ service.id }}')">重啟</button>
                                {% endif %}
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                </div>

                <!-- 安全檢查 -->
                <div class="tab-content" id="security-checks-tab">
                    <div class="checks-header">
                        <h3>🔒 安全檢查</h3>
                        <div class="checks-actions">
                            <button id="run-security-checks-btn" class="btn btn-sm btn-primary">執行檢查</button>
                            <button id="security-scan-btn" class="btn btn-sm btn-warning">安全掃描</button>
                        </div>
                    </div>

                    <div class="security-categories">
                        <div class="category-grid">
                            <div class="security-category">
                                <h4>🔐 驗證與授權</h4>
                                <div class="security-check-list">
                                    {% for check in security_checks.auth %}
                                    <div class="security-check {{ check.status }}">
                                        <span class="check-name">{{ check.name }}</span>
                                        <span class="check-result">{{ check.result }}</span>
                                    </div>
                                    {% endfor %}
                                </div>
                            </div>

                            <div class="security-category">
                                <h4>🛡️ 資料保護</h4>
                                <div class="security-check-list">
                                    {% for check in security_checks.data_protection %}
                                    <div class="security-check {{ check.status }}">
                                        <span class="check-name">{{ check.name }}</span>
                                        <span class="check-result">{{ check.result }}</span>
                                    </div>
                                    {% endfor %}
                                </div>
                            </div>

                            <div class="security-category">
                                <h4>🌐 網路安全</h4>
                                <div class="security-check-list">
                                    {% for check in security_checks.network %}
                                    <div class="security-check {{ check.status }}">
                                        <span class="check-name">{{ check.name }}</span>
                                        <span class="check-result">{{ check.result }}</span>
                                    </div>
                                    {% endfor %}
                                </div>
                            </div>

                            <div class="security-category">
                                <h4>📝 稽核與日誌</h4>
                                <div class="security-check-list">
                                    {% for check in security_checks.audit %}
                                    <div class="security-check {{ check.status }}">
                                        <span class="check-name">{{ check.name }}</span>
                                        <span class="check-result">{{ check.result }}</span>
                                    </div>
                                    {% endfor %}
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="security-recommendations">
                        <h4>🎯 安全建議</h4>
                        <div class="recommendation-list">
                            {% for rec in security_recommendations %}
                            <div class="recommendation-item {{ rec.priority }}">
                                <div class="rec-priority">{{ rec.priority }}</div>
                                <div class="rec-content">
                                    <h5>{{ rec.title }}</h5>
                                    <p>{{ rec.description }}</p>
                                    <div class="rec-actions">
                                        <button class="btn btn-sm btn-primary" onclick="applyRecommendation('{{ rec.id }}')">套用</button>
                                        <button class="btn btn-sm btn-outline" onclick="dismissRecommendation('{{ rec.id }}')">忽略</button>
                                    </div>
                                </div>
                            </div>
                            {% endfor %}
                        </div>
                    </div>
                </div>

                <!-- 效能檢查 -->
                <div class="tab-content" id="performance-checks-tab">
                    <div class="checks-header">
                        <h3>⚡ 效能檢查</h3>
                        <div class="checks-actions">
                            <button id="run-performance-checks-btn" class="btn btn-sm btn-primary">執行檢查</button>
                            <button id="benchmark-btn" class="btn btn-sm btn-secondary">效能基準測試</button>
                        </div>
                    </div>

                    <div class="performance-overview">
                        <div class="perf-score-card">
                            <div class="score-header">
                                <h4>整體效能評分</h4>
                                <div class="score-value {{ performance.score_class }}">{{ performance.overall_score }}/100</div>
                            </div>
                            <div class="score-breakdown">
                                <div class="score-item">
                                    <span class="score-label">回應時間:</span>
                                    <span class="score-value">{{ performance.response_score }}/100</span>
                                </div>
                                <div class="score-item">
                                    <span class="score-label">吞吐量:</span>
                                    <span class="score-value">{{ performance.throughput_score }}/100</span>
                                </div>
                                <div class="score-item">
                                    <span class="score-label">資源使用:</span>
                                    <span class="score-value">{{ performance.resource_score }}/100</span>
                                </div>
                                <div class="score-item">
                                    <span class="score-label">穩定性:</span>
                                    <span class="score-value">{{ performance.stability_score }}/100</span>
                                </div>
                            </div>
                        </div>

                        <div class="perf-trends-card">
                            <h4>效能趨勢</h4>
                            <div class="trend-chart">
                                <canvas id="performance-trend-chart"></canvas>
                            </div>
                        </div>
                    </div>

                    <div class="performance-details">
                        {% for category in performance_categories %}
                        <div class="perf-category">
                            <h4>{{ category.name }}</h4>
                            <div class="perf-metrics">
                                {% for metric in category.metrics %}
                                <div class="metric-card {{ metric.status }}">
                                    <div class="metric-header">
                                        <span class="metric-name">{{ metric.name }}</span>
                                        <span class="metric-status {{ metric.status }}">{{ metric.status_display }}</span>
                                    </div>
                                    <div class="metric-value">{{ metric.value }} {{ metric.unit }}</div>
                                    <div class="metric-target">目標: {{ metric.target }} {{ metric.unit }}</div>
                                    {% if metric.trend %}
                                    <div class="metric-trend {{ metric.trend }}">
                                        {{ '↗' if metric.trend == 'up' else '↘' if metric.trend == 'down' else '→' }}
                                    </div>
                                    {% endif %}
                                </div>
                                {% endfor %}
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                </div>

                <!-- 自訂檢查 -->
                <div class="tab-content" id="custom-checks-tab">
                    <div class="checks-header">
                        <h3>🔧 自訂檢查</h3>
                        <div class="checks-actions">
                            <button id="add-custom-check-btn" class="btn btn-sm btn-primary">新增檢查</button>
                            <button id="import-checks-btn" class="btn btn-sm btn-secondary">匯入檢查</button>
                        </div>
                    </div>

                    <div class="custom-check-list">
                        {% for check in custom_checks %}
                        <div class="custom-check-item" data-check-id="{{ check.id }}">
                            <div class="check-card">
                                <div class="check-header">
                                    <div class="check-info">
                                        <h4 class="check-name">{{ check.name }}</h4>
                                        <p class="check-description">{{ check.description }}</p>
                                        <div class="check-meta">
                                            <span class="check-type">{{ check.type_display }}</span>
                                            <span class="check-frequency">{{ check.frequency_display }}</span>
                                        </div>
                                    </div>
                                    <div class="check-status {{ check.status }}">
                                        <div class="status-indicator"></div>
                                        <span class="status-text">{{ check.status_display }}</span>
                                    </div>
                                </div>

                                <div class="check-config">
                                    <div class="config-item">
                                        <span class="config-label">檢查指令:</span>
                                        <code class="config-value">{{ check.command }}</code>
                                    </div>
                                    {% if check.expected_output %}
                                    <div class="config-item">
                                        <span class="config-label">預期輸出:</span>
                                        <code class="config-value">{{ check.expected_output }}</code>
                                    </div>
                                    {% endif %}
                                    <div class="config-item">
                                        <span class="config-label">超時時間:</span>
                                        <span class="config-value">{{ check.timeout }}秒</span>
                                    </div>
                                </div>

                                <div class="check-actions">
                                    <button class="btn btn-sm btn-primary" onclick="runCustomCheck('{{ check.id }}')">執行</button>
                                    <button class="btn btn-sm btn-secondary" onclick="editCustomCheck('{{ check.id }}')">編輯</button>
                                    <button class="btn btn-sm btn-outline" onclick="duplicateCustomCheck('{{ check.id }}')">複製</button>
                                    <button class="btn btn-sm btn-danger" onclick="deleteCustomCheck('{{ check.id }}')">刪除</button>
                                </div>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                </div>
            </div>

            <!-- 檢查歷史記錄 -->
            <div class="check-history-section">
                <div class="history-card">
                    <div class="history-header">
                        <h3>📜 檢查歷史記錄</h3>
                        <div class="history-filters">
                            <select id="history-timeframe">
                                <option value="24h" selected>過去24小時</option>
                                <option value="7d">過去7天</option>
                                <option value="30d">過去30天</option>
                            </select>
                            <select id="history-status">
                                <option value="all" selected>全部狀態</option>
                                <option value="pass">通過</option>
                                <option value="warning">警告</option>
                                <option value="fail">失敗</option>
                            </select>
                            <button id="apply-history-filters-btn" class="btn btn-sm btn-primary">套用篩選</button>
                        </div>
                    </div>

                    <div class="history-chart">
                        <canvas id="health-history-chart"></canvas>
                    </div>

                    <div class="history-table">
                        <table>
                            <thead>
                                <tr>
                                    <th>檢查項目</th>
                                    <th>類型</th>
                                    <th>狀態</th>
                                    <th>執行時間</th>
                                    <th>持續時間</th>
                                    <th>結果</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody id="history-table-body">
                                {% for record in check_history %}
                                <tr class="history-row {{ record.status }}">
                                    <td class="check-name">{{ record.check_name }}</td>
                                    <td class="check-type">{{ record.check_type }}</td>
                                    <td class="check-status">
                                        <span class="status-badge {{ record.status }}">{{ record.status_display }}</span>
                                    </td>
                                    <td class="run-time">{{ record.run_time.strftime('%Y-%m-%d %H:%M') if record.run_time else 'N/A' }}</td>
                                    <td class="duration">{{ record.duration }}s</td>
                                    <td class="result">{{ record.result_summary }}</td>
                                    <td class="actions">
                                        <button class="btn btn-sm btn-outline" onclick="viewHistoryDetails('{{ record.id }}')">詳情</button>
                                        <button class="btn btn-sm btn-secondary" onclick="rerunCheck('{{ record.check_id }}')">重新執行</button>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>

                    <div class="pagination" id="history-pagination">
                        <!-- 分頁控制會由JavaScript動態生成 -->
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 自訂檢查編輯模態框 -->
    <div class="modal" id="custom-check-modal">
        <div class="modal-content large">
            <div class="modal-header">
                <h3 id="custom-check-title">新增自訂檢查</h3>
                <button class="modal-close">&times;</button>
            </div>
            <div class="modal-body">
                <form id="custom-check-form">
                    <div class="form-row">
                        <div class="form-group">
                            <label for="check-name">檢查名稱:</label>
                            <input type="text" id="check-name" required>
                        </div>
                        <div class="form-group">
                            <label for="check-type">檢查類型:</label>
                            <select id="check-type" required>
                                <option value="">請選擇...</option>
                                <option value="command">指令執行</option>
                                <option value="http">HTTP檢查</option>
                                <option value="tcp">TCP連線</option>
                                <option value="file">檔案檢查</option>
                                <option value="process">程序檢查</option>
                            </select>
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="check-description">檢查描述:</label>
                        <textarea id="check-description" rows="2"></textarea>
                    </div>

                    <div class="form-group">
                        <label for="check-command">檢查指令/URL:</label>
                        <input type="text" id="check-command" required>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label for="check-timeout">超時時間 (秒):</label>
                            <input type="number" id="check-timeout" min="1" max="300" value="30">
                        </div>
                        <div class="form-group">
                            <label for="check-frequency">檢查頻率:</label>
                            <select id="check-frequency">
                                <option value="manual">手動</option>
                                <option value="1m">每分鐘</option>
                                <option value="5m" selected>每5分鐘</option>
                                <option value="15m">每15分鐘</option>
                                <option value="1h">每小時</option>
                                <option value="1d">每日</option>
                            </select>
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="expected-output">預期輸出 (選填):</label>
                        <textarea id="expected-output" rows="3" placeholder="留空表示只檢查執行成功"></textarea>
                    </div>

                    <div class="form-group">
                        <label>
                            <input type="checkbox" id="check-enabled" checked>
                            啟用此檢查
                        </label>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button id="save-custom-check-btn" class="btn btn-primary">儲存檢查</button>
                <button class="btn btn-secondary" onclick="closeModal('custom-check-modal')">取消</button>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="{{ url_for('shared.static', filename='js/main.js') }}"></script>
    <script src="{{ url_for('shared.static', filename='lib/chart.min.js') }}"></script>
    <script src="{{ url_for('monitoring.static', filename='js/health-check.js') }}"></script>
</body>
</html>