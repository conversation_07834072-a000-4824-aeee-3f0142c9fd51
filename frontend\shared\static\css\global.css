/* Global CSS - 全域樣式定義 */

/* 全域重置和基礎樣式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html {
    font-size: 16px;
    line-height: 1.6;
    scroll-behavior: smooth;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", <PERSON><PERSON>, "Helvetica Neue", Arial, sans-serif;
    color: #333;
    background-color: #f8f9fa;
    overflow-x: hidden;
}

/* 全域容器樣式 */
.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 15px;
}

.container-fluid {
    width: 100%;
    padding: 0 15px;
}

/* 全域按鈕樣式 */
.btn {
    display: inline-block;
    padding: 8px 16px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
    text-decoration: none;
    transition: all 0.2s ease;
}

.btn-primary {
    background-color: #007bff;
    color: white;
}

.btn-primary:hover {
    background-color: #0056b3;
}

.btn-secondary {
    background-color: #6c757d;
    color: white;
}

.btn-success {
    background-color: #28a745;
    color: white;
}

.btn-danger {
    background-color: #dc3545;
    color: white;
}

.btn-warning {
    background-color: #ffc107;
    color: #212529;
}

/* 全域表單樣式 */
.form-group {
    margin-bottom: 15px;
}

.form-control {
    width: 100%;
    padding: 8px 12px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 14px;
}

.form-control:focus {
    border-color: #007bff;
    outline: none;
    box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
}

label {
    display: block;
    margin-bottom: 5px;
    font-weight: 500;
}

/* 全域表格樣式 */
.table {
    width: 100%;
    border-collapse: collapse;
    margin-bottom: 20px;
}

.table th,
.table td {
    padding: 12px;
    text-align: left;
    border-bottom: 1px solid #ddd;
}

.table th {
    background-color: #f8f9fa;
    font-weight: 600;
}

.table-striped tbody tr:nth-child(odd) {
    background-color: #f9f9f9;
}

/* 全域卡片樣式 */
.card {
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    margin-bottom: 20px;
}

.card-header {
    padding: 15px 20px;
    border-bottom: 1px solid #e9ecef;
    background-color: #f8f9fa;
    border-radius: 8px 8px 0 0;
}

.card-body {
    padding: 20px;
}

/* 全域工具樣式 */
.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }

.mt-10 { margin-top: 10px; }
.mt-20 { margin-top: 20px; }
.mb-10 { margin-bottom: 10px; }
.mb-20 { margin-bottom: 20px; }

.d-flex { display: flex; }
.d-block { display: block; }
.d-none { display: none; }

.justify-content-between { justify-content: space-between; }
.align-items-center { align-items: center; }

/* 響應式設計 */
@media (max-width: 768px) {
    .container {
        padding: 0 10px;
    }
    
    .btn {
        width: 100%;
        margin-bottom: 10px;
    }
    
    .table {
        font-size: 12px;
    }
    
    .card-body {
        padding: 15px;
    }
}

/* 動畫和過渡效果 */
.fade-in {
    animation: fadeIn 0.3s ease-in;
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

.loading {
    opacity: 0.6;
    pointer-events: none;
}

/* 訊息提示樣式 */
.alert {
    padding: 12px 16px;
    margin-bottom: 15px;
    border: 1px solid transparent;
    border-radius: 4px;
}

.alert-success {
    color: #155724;
    background-color: #d4edda;
    border-color: #c3e6cb;
}

.alert-danger {
    color: #721c24;
    background-color: #f8d7da;
    border-color: #f5c6cb;
}

.alert-warning {
    color: #856404;
    background-color: #fff3cd;
    border-color: #ffeaa7;
}

.alert-info {
    color: #0c5460;
    background-color: #d1ecf1;
    border-color: #bee5eb;
}