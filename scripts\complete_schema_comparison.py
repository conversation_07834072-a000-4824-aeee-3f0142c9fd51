"""
Complete Schema Comparison Tool
Compare current database schema with model definition
"""

import sqlite3
import sys
import os
from pathlib import Path

# Add src directory to Python path
project_root = Path(__file__).parent.parent
src_path = project_root / "src"
sys.path.insert(0, str(src_path))

def get_expected_schema():
    """Define the expected schema based on model definition"""
    return {
        'emails': [
            'id', 'message_id', 'sender', 'sender_display_name', 
            'subject', 'body', 'received_time', 'created_at', 
            'is_read', 'is_processed', 'has_attachments', 'attachment_count',
            'pd', 'lot', 'mo', 'yield_value', 'vendor_code', 
            'parsed_at', 'parse_status', 'parse_error', 'extraction_method',
            'llm_analysis_result', 'llm_analysis_timestamp', 'llm_service_used'
        ],
        'attachments': [
            'id', 'email_id', 'filename', 'content_type', 'size_bytes',
            'file_path', 'checksum', 'is_processed', 'created_at'
        ],
        'senders': [
            'id', 'email_address', 'display_name', 'total_emails',
            'last_email_time', 'first_email_time', 'created_at', 'updated_at'
        ],
        'email_process_status': [
            'id', 'email_id', 'step_name', 'status', 'started_at',
            'completed_at', 'error_message', 'output_files', 
            'progress_percentage', 'created_at', 'updated_at'
        ]
    }

def main():
    print("=" * 70)
    print("COMPLETE SCHEMA COMPARISON REPORT")
    print("=" * 70)
    
    db_path = project_root / "data" / "email_inbox.db"
    
    try:
        conn = sqlite3.connect(str(db_path))
        cursor = conn.cursor()
        
        expected_schema = get_expected_schema()
        
        print("Comparing each table schema:")
        print("-" * 70)
        
        overall_missing = []
        
        for table_name, expected_columns in expected_schema.items():
            print(f"\nTable: {table_name}")
            print("=" * 50)
            
            # Get current schema
            cursor.execute(f"PRAGMA table_info({table_name})")
            current_columns_info = cursor.fetchall()
            current_columns = [col[1] for col in current_columns_info]
            
            print(f"Expected columns: {len(expected_columns)}")
            print(f"Current columns:  {len(current_columns)}")
            
            # Find missing columns
            missing = [col for col in expected_columns if col not in current_columns]
            extra = [col for col in current_columns if col not in expected_columns]
            
            if missing:
                print(f"MISSING COLUMNS ({len(missing)}):")
                for col in missing:
                    print(f"  - {col}")
                    overall_missing.append(f"{table_name}.{col}")
            else:
                print("OK - No missing columns")
            
            if extra:
                print(f"EXTRA COLUMNS ({len(extra)}):")
                for col in extra:
                    print(f"  + {col}")
            
            print(f"Current schema details:")
            for col_info in current_columns_info:
                col_name = col_info[1]
                col_type = col_info[2]
                not_null = "NOT NULL" if col_info[3] else "NULL"
                default_val = f"DEFAULT {col_info[4]}" if col_info[4] else "NO DEFAULT"
                pk = "PK" if col_info[5] else ""
                
                status = "OK" if col_name in expected_columns else "EXTRA"
                print(f"    [{status}] {col_name} {col_type} {not_null} {pk}")
        
        print("\n" + "=" * 70)
        print("SUMMARY")
        print("=" * 70)
        
        if overall_missing:
            print(f"TOTAL MISSING FIELDS: {len(overall_missing)}")
            for field in overall_missing:
                print(f"  - {field}")
            
            print("\nRECOMMENDED ACTIONS:")
            print("1. Backup current database")
            print("2. Add missing columns with ALTER TABLE statements")
            print("3. Test database functionality")
            print("4. Verify application works correctly")
        else:
            print("DATABASE SCHEMA IS COMPLETE!")
            print("All expected columns are present in all tables.")
        
    except Exception as e:
        print(f"Error: {e}")
        return
    finally:
        if 'conn' in locals():
            conn.close()

if __name__ == "__main__":
    main()