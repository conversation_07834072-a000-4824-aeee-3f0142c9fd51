# 半導體郵件處理系統 - 開發環境設定指南

## 📋 目錄

- [系統概述](#系統概述)
- [前置需求](#前置需求)
- [快速開始](#快速開始)
- [詳細設定步驟](#詳細設定步驟)
- [模組化架構說明](#模組化架構說明)
- [開發工作流程](#開發工作流程)
- [常見問題](#常見問題)
- [進階配置](#進階配置)

## 🎯 系統概述

本系統是一個半導體測試數據的自動化郵件處理平台，採用模組化 Flask 前端架構，支援六個主要功能領域：

- **📧 Email** - 郵件管理和處理
- **📊 Analytics** - 分析統計和報表
- **📁 File Management** - 檔案管理和上傳
- **🔍 EQC** - 設備品質控制
- **⚡ Tasks** - 任務管理和調度
- **📈 Monitoring** - 系統監控和健康檢查

## 🔧 前置需求

### 必要軟體

- **Python 3.9+** - 主要開發語言
- **Git** - 版本控制
- **PowerShell** - Windows 腳本執行環境

### 推薦工具

- **VS Code** - 程式碼編輯器
- **Windows Terminal** - 現代化終端
- **Postman** - API 測試工具

## ⚡ 快速開始

### 1. 克隆專案

```bash
git clone <repository-url>
cd outlook_summary
```

### 2. 設定開發環境

```powershell
# 方法 1: 使用 PowerShell 腳本（推薦）
. .\dev_env.ps1

# 方法 2: 使用 Makefile
make dev-setup
make activate
```

### 3. 啟動應用程式

```powershell
# 啟動前端應用程式
python frontend/app.py

# 或使用 Makefile
make run-frontend
```

### 4. 驗證安裝

開啟瀏覽器訪問：
- 前端應用程式: http://localhost:5000
- 各模組路由: http://localhost:5000/{module}/

## 📚 詳細設定步驟

### Step 1: 環境準備

1. **檢查 Python 版本**
   ```powershell
   python --version  # 應該是 3.9+
   ```

2. **建立虛擬環境**
   ```powershell
   python -m venv venv_win_3_11_12
   ```

3. **啟動虛擬環境**
   ```powershell
   .\venv_win_3_11_12\Scripts\Activate.ps1
   ```

### Step 2: 安裝依賴

```powershell
# 升級 pip
python -m pip install --upgrade pip

# 安裝生產依賴
pip install -r requirements.txt

# 安裝開發依賴（可選）
pip install -r requirements-dev.txt
```

### Step 3: 環境變數設定

開發環境腳本會自動設定以下環境變數：

```powershell
$env:PYTHONIOENCODING = "utf-8"
$env:PYTHONUTF8 = "1"
$env:FLASK_APP = "frontend.app:create_app"
$env:FLASK_ENV = "development"
$env:FLASK_DEBUG = "True"
$env:FLASK_RUN_HOST = "0.0.0.0"
$env:FLASK_RUN_PORT = "5000"
```

### Step 4: 驗證設定

```powershell
# 檢查模組化結構
make check-structure

# 驗證模組完整性
make validate-modules

# 執行測試
make test
```

## 🏗️ 模組化架構說明

### 目錄結構

```
frontend/
├── app.py                    # Flask 主應用程式
├── config.py                 # 配置管理
├── cli.py                    # CLI 工具
├── email/                    # 郵件模組
│   ├── routes/
│   ├── templates/
│   ├── static/
│   └── components/
├── analytics/                # 分析統計模組
├── file_management/          # 檔案管理模組
├── eqc/                      # EQC 模組
├── tasks/                    # 任務管理模組
├── monitoring/               # 監控模組
└── shared/                   # 共享資源
    ├── templates/
    ├── static/
    └── utils/
```

### 模組路由對應

| 模組 | URL 前綴 | 主要功能 |
|------|----------|----------|
| Email | `/email/*` | 郵件收件匣、郵件詳情、郵件設定 |
| Analytics | `/analytics/*` | 統計儀表板、報表生成、廠商分析 |
| File Management | `/files/*` | 檔案管理器、檔案上傳、附件瀏覽 |
| EQC | `/eqc/*` | EQC 儀表板、品質檢查、合規檢查 |
| Tasks | `/tasks/*` | 任務儀表板、任務隊列、任務調度 |
| Monitoring | `/monitoring/*` | 系統監控、健康檢查、即時監控 |

## 🔄 開發工作流程

### 日常開發

1. **啟動開發環境**
   ```powershell
   . .\dev_env.ps1
   ```

2. **選擇開發模式**
   ```powershell
   # 僅前端開發
   make frontend-dev
   
   # 僅後端開發
   make backend-dev
   
   # 完整開發（需要兩個終端）
   make full-dev
   ```

3. **程式碼品質檢查**
   ```powershell
   make quality-check
   ```

4. **執行測試**
   ```powershell
   # 所有測試
   make test
   
   # 前端測試
   make test-frontend
   
   # 後端測試
   make test-backend
   
   # 按模組測試
   make test-modules
   ```

### TDD 工作流程

```powershell
# Red 階段 - 寫失敗的測試
make tdd-red

# Green 階段 - 讓測試通過
make tdd-green

# Refactor 階段 - 重構程式碼
make tdd-refactor
```

## 🛠️ 可用命令

### 基本命令

| 命令 | 說明 |
|------|------|
| `make help` | 顯示所有可用命令 |
| `make dev-setup` | 設定開發環境 |
| `make quick-start` | 快速啟動開發環境 |

### 應用程式啟動

| 命令 | 說明 |
|------|------|
| `make run-frontend` | 啟動前端 Flask 應用程式 |
| `make run-flask` | 使用 Flask CLI 啟動 |
| `make run-services` | 啟動整合服務 |
| `make frontend-dev` | 前端開發模式 |
| `make backend-dev` | 後端開發模式 |

### 測試和品質

| 命令 | 說明 |
|------|------|
| `make test` | 執行所有測試 |
| `make test-frontend` | 執行前端測試 |
| `make test-backend` | 執行後端測試 |
| `make quality-check` | 程式碼品質檢查 |
| `make format` | 格式化程式碼 |

### 架構驗證

| 命令 | 說明 |
|------|------|
| `make check-structure` | 檢查前端模組化結構 |
| `make validate-modules` | 驗證所有模組完整性 |

## ❓ 常見問題

### Q1: 虛擬環境啟動失敗

**問題**: PowerShell 執行政策限制

**解決方案**:
```powershell
Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope CurrentUser
```

### Q2: 中文編碼問題

**問題**: 中文字符顯示亂碼

**解決方案**: 開發環境腳本會自動設定 UTF-8 編碼，如果仍有問題：
```powershell
$env:PYTHONIOENCODING = "utf-8"
$env:PYTHONUTF8 = "1"
```

### Q3: Flask 應用程式無法啟動

**問題**: 模組導入錯誤

**解決方案**:
1. 確認虛擬環境已啟動
2. 檢查 FLASK_APP 環境變數：`frontend.app:create_app`
3. 驗證前端目錄結構：`make check-structure`

### Q4: 靜態資源載入失敗

**問題**: CSS/JS 檔案 404 錯誤

**解決方案**:
1. 檢查模組靜態資源目錄結構
2. 確認 Flask 藍圖靜態資源配置
3. 驗證模板中的資源路徑引用

### Q5: 測試執行失敗

**問題**: 測試目錄或檔案不存在

**解決方案**:
```powershell
# 建立測試目錄結構
mkdir -p tests/frontend/{email,analytics,file_management,eqc,tasks,monitoring}
mkdir -p tests/unit tests/integration tests/e2e
```

## 🚀 進階配置

### 生產環境配置

```powershell
# 設定生產環境變數
$env:FLASK_ENV = "production"
$env:FLASK_DEBUG = "False"

# 啟動生產模式
make run-flask-prod
```

### 多環境配置

在 `frontend/config.py` 中配置不同環境：

```python
class DevelopmentConfig(Config):
    DEBUG = True
    TESTING = False

class ProductionConfig(Config):
    DEBUG = False
    TESTING = False

class TestingConfig(Config):
    DEBUG = True
    TESTING = True
```

### 自定義配置

建立 `.env` 檔案進行個人化配置：

```bash
FLASK_ENV=development
FLASK_DEBUG=True
DATABASE_URL=sqlite:///app.db
SECRET_KEY=your-secret-key
```

## 📞 支援和協助

如果遇到問題，請：

1. 檢查本指南的常見問題部分
2. 執行 `make check-structure` 驗證環境
3. 查看應用程式日誌檔案
4. 聯繫開發團隊

---

**最後更新**: 2025-01-10
**版本**: 1.0.0 (模組化架構)