<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>增強任務排程器儀表板</title>
    <link rel="stylesheet" href="{{ url_for('shared.static', filename='css/global.css') }}">
    <link rel="stylesheet" href="{{ url_for('tasks.static', filename='css/tasks.css') }}">
    <style>
        body { 
            font-family: 'Microsoft JhengHei', Arial, sans-serif; 
            margin: 0; 
            padding: 20px;
            background-color: #f5f5f5;
        }
        .dashboard-container { 
            max-width: 1400px; 
            margin: 0 auto; 
            background: white;
            border-radius: 12px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        .dashboard-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        .dashboard-header h1 {
            margin: 0;
            font-size: 2.5em;
            text-shadow: 0 2px 4px rgba(0,0,0,0.3);
        }
        .dashboard-header .subtitle {
            margin-top: 10px;
            opacity: 0.9;
            font-size: 1.2em;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            padding: 30px;
            background: #f8f9fa;
        }
        .stat-card {
            background: white;
            padding: 25px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            transition: transform 0.2s ease;
        }
        .stat-card:hover {
            transform: translateY(-2px);
        }
        .stat-card h3 {
            margin: 0 0 15px 0;
            color: #333;
            font-size: 1.3em;
        }
        .stat-value {
            font-size: 2.5em;
            font-weight: bold;
            color: #667eea;
            margin: 10px 0;
        }
        .stat-description {
            color: #666;
            font-size: 0.9em;
        }
    </style>
</head>
<body>
    <div class="dashboard-container">
        <div class="dashboard-header">
            <h1>🕐 增強任務排程器儀表板</h1>
            <div class="subtitle">企業級任務管理與監控系統</div>
        </div>
        
        <div class="stats-grid">
            <div class="stat-card">
                <h3>📊 系統狀態</h3>
                <div class="stat-value" id="systemStatus">運行中</div>
                <div class="stat-description">排程器運行狀態</div>
            </div>
            
            <div class="stat-card">
                <h3>⏰ 活躍任務</h3>
                <div class="stat-value" id="activeTasks">0</div>
                <div class="stat-description">當前執行中的任務數量</div>
            </div>
            
            <div class="stat-card">
                <h3>✅ 完成任務</h3>
                <div class="stat-value" id="completedTasks">0</div>
                <div class="stat-description">今日已完成的任務</div>
            </div>
            
            <div class="stat-card">
                <h3>❌ 失敗任務</h3>
                <div class="stat-value" id="failedTasks">0</div>
                <div class="stat-description">今日失敗的任務</div>
            </div>
        </div>
    </div>

    <!-- 載入核心依賴 -->
    <script src="{{ url_for('shared.static', filename='js/core/utils.js') }}"></script>
    <script src="{{ url_for('shared.static', filename='js/core/api-client.js') }}"></script>
    <script src="{{ url_for('tasks.static', filename='js/scheduler-dashboard.js') }}"></script>
</body>
</html>