#!/usr/bin/env python3
"""
Playwright 整合測試腳本 - 網頁功能驗證自動化
測試修復後的整合服務是否正常運作
"""

import asyncio
import json
import os
import subprocess
import sys
import time
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Optional, Tuple

import psutil
from playwright.async_api import async_playwright, <PERSON><PERSON><PERSON>, BrowserContext, Page


class Colors:
    """終端顏色代碼"""
    RED = '\033[91m'
    GREEN = '\033[92m'
    YELLOW = '\033[93m'
    BLUE = '\033[94m'
    MAGENTA = '\033[95m'
    CYAN = '\033[96m'
    WHITE = '\033[97m'
    BOLD = '\033[1m'
    END = '\033[0m'


def print_colored(text: str, color: str = Colors.WHITE):
    """印出彩色文字"""
    print(f"{color}{text}{Colors.END}")


def print_status(status: str, message: str, details: str = ""):
    """印出狀態信息"""
    timestamp = datetime.now().strftime("%H:%M:%S")
    if status == "success":
        print_colored(f"[{timestamp}] [✓] {message}", Colors.GREEN)
    elif status == "warning":
        print_colored(f"[{timestamp}] [⚠] {message}", Colors.YELLOW)
    elif status == "error":
        print_colored(f"[{timestamp}] [✗] {message}", Colors.RED)
    elif status == "info":
        print_colored(f"[{timestamp}] [ℹ] {message}", Colors.BLUE)
    
    if details:
        print_colored(f"    {details}", Colors.WHITE)


class ServiceManager:
    """整合服務管理器"""
    
    def __init__(self, flask_port: int = 5000, fastapi_port: int = 8010):
        self.flask_port = flask_port
        self.fastapi_port = fastapi_port
        self.process: Optional[subprocess.Popen] = None
        self.project_root = Path(__file__).parent
        
    async def start_service(self, timeout: int = 60) -> bool:
        """啟動整合服務"""
        try:
            print_status("info", "正在啟動整合服務...")
            
            # 檢查端口是否已被占用
            if self._is_port_in_use(self.flask_port):
                print_status("warning", f"端口 {self.flask_port} 已被占用，嘗試終止")
                self._kill_process_by_port(self.flask_port)
                time.sleep(2)
            
            if self._is_port_in_use(self.fastapi_port):
                print_status("warning", f"端口 {self.fastapi_port} 已被占用，嘗試終止")
                self._kill_process_by_port(self.fastapi_port)
                time.sleep(2)
            
            # 設置環境變數
            env = os.environ.copy()
            env['PYTHONIOENCODING'] = 'utf-8'
            env['PYTHONUTF8'] = '1'
            env['FLASK_ENV'] = 'development'
            
            # 啟動服務
            start_script = self.project_root / "start_integrated_services.py"
            
            self.process = subprocess.Popen([
                sys.executable, str(start_script),
                "--mode", "integrated",
                "--flask-port", str(self.flask_port),
                "--fastapi-port", str(self.fastapi_port),
                "--no-monitor"  # 禁用監控以避免額外輸出
            ], 
            stdout=subprocess.PIPE, 
            stderr=subprocess.STDOUT, 
            text=True,
            encoding='utf-8', 
            errors='replace', 
            env=env,
            cwd=str(self.project_root)
            )
            
            print_status("info", f"服務啟動中 (PID: {self.process.pid})")
            
            # 等待服務啟動
            start_time = time.time()
            while time.time() - start_time < timeout:
                if await self._check_service_ready():
                    print_status("success", f"整合服務啟動成功 (耗時: {time.time() - start_time:.1f}秒)")
                    return True
                
                # 檢查進程是否意外退出
                if self.process.poll() is not None:
                    print_status("error", "服務進程意外退出")
                    return False
                
                await asyncio.sleep(2)
            
            print_status("error", f"服務啟動超時 ({timeout}秒)")
            return False
            
        except Exception as e:
            print_status("error", f"啟動服務失敗: {e}")
            return False
    
    async def stop_service(self):
        """停止整合服務"""
        try:
            if self.process:
                print_status("info", "正在停止整合服務...")
                
                # 嘗試優雅停止
                self.process.terminate()
                try:
                    self.process.wait(timeout=10)
                    print_status("success", "服務已正常停止")
                except subprocess.TimeoutExpired:
                    print_status("warning", "優雅停止超時，強制終止")
                    self.process.kill()
                    self.process.wait()
                
                self.process = None
            
            # 確保端口釋放
            self._kill_process_by_port(self.flask_port)
            self._kill_process_by_port(self.fastapi_port)
            
        except Exception as e:
            print_status("error", f"停止服務失敗: {e}")
    
    async def _check_service_ready(self) -> bool:
        """檢查服務是否就緒"""
        try:
            import aiohttp
            
            async with aiohttp.ClientSession() as session:
                # 檢查 Flask 健康狀態
                try:
                    async with session.get(f"http://localhost:{self.flask_port}/health", timeout=5) as response:
                        flask_ok = response.status == 200
                except:
                    flask_ok = False
                
                # 檢查 FastAPI 健康狀態
                try:
                    async with session.get(f"http://localhost:{self.fastapi_port}/docs", timeout=5) as response:
                        fastapi_ok = response.status == 200
                except:
                    fastapi_ok = False
                
                return flask_ok and fastapi_ok
                
        except ImportError:
            # 如果沒有 aiohttp，使用簡單的端口檢查
            return (self._is_port_in_use(self.flask_port) and 
                   self._is_port_in_use(self.fastapi_port))
    
    def _is_port_in_use(self, port: int) -> bool:
        """檢查端口是否被占用"""
        try:
            for conn in psutil.net_connections():
                if conn.laddr.port == port and conn.status == psutil.CONN_LISTEN:
                    return True
            return False
        except:
            return False
    
    def _kill_process_by_port(self, port: int):
        """根據端口終止進程"""
        try:
            for proc in psutil.process_iter(['pid', 'name', 'connections']):
                try:
                    for conn in proc.info['connections'] or []:
                        if conn.laddr.port == port:
                            print_status("info", f"終止占用端口 {port} 的進程 {proc.info['pid']}")
                            proc.kill()
                            break
                except:
                    continue
        except Exception as e:
            print_status("warning", f"終止端口 {port} 進程失敗: {e}")


class PlaywrightTester:
    """Playwright 測試器"""
    
    def __init__(self, base_url: str = "http://localhost:5000"):
        self.base_url = base_url
        self.screenshot_dir = Path("test_screenshots")
        self.screenshot_dir.mkdir(exist_ok=True)
        
        # 測試結果
        self.test_results = {
            "start_time": datetime.now().isoformat(),
            "tests": [],
            "summary": {
                "total": 0,
                "passed": 0,
                "failed": 0,
                "warnings": 0
            }
        }
        
        # JavaScript 錯誤收集
        self.js_errors: List[Dict] = []
        
        # 定義要測試的頁面
        self.test_pages = [
            {"name": "首頁", "path": "/", "expected_title": "郵件收件夾"},
            {"name": "郵件模組", "path": "/email", "expected_title": "郵件"},
            {"name": "分析模組", "path": "/analytics", "expected_title": "分析"},
            {"name": "檔案管理", "path": "/file_management", "expected_title": "檔案"},
            {"name": "EQC模組", "path": "/eqc", "expected_title": "EQC"},
            {"name": "任務管理", "path": "/tasks", "expected_title": "任務"},
            {"name": "監控模組", "path": "/monitoring", "expected_title": "監控"}
        ]
        
        # API 端點測試
        self.api_endpoints = [
            {"name": "健康檢查", "path": "/health", "method": "GET"},
            {"name": "郵件狀態", "path": "/email/api/status", "method": "GET"},
            {"name": "分析狀態", "path": "/analytics/api/status", "method": "GET"},
            {"name": "檔案狀態", "path": "/file_management/api/status", "method": "GET"},
            {"name": "EQC狀態", "path": "/eqc/api/status", "method": "GET"},
            {"name": "任務狀態", "path": "/tasks/api/status", "method": "GET"},
            {"name": "監控狀態", "path": "/monitoring/api/status", "method": "GET"}
        ]
    
    async def run_all_tests(self) -> Dict:
        """執行所有測試"""
        print_colored("=" * 60, Colors.BLUE)
        print_colored("Playwright 網頁功能驗證測試開始", Colors.BOLD)
        print_colored("=" * 60, Colors.BLUE)
        
        async with async_playwright() as p:
            # 啟動瀏覽器
            browser = await p.chromium.launch(headless=False)  # 顯示瀏覽器便於觀察
            context = await browser.new_context(
                viewport={"width": 1280, "height": 720},
                user_agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36"
            )
            
            page = await context.new_page()
            
            # 設置 JavaScript 錯誤監聽
            page.on("console", self._handle_console_message)
            page.on("pageerror", self._handle_page_error)
            
            try:
                # 1. 測試頁面載入
                await self._test_page_loading(page)
                
                # 2. 測試 API 端點
                await self._test_api_endpoints(page)
                
                # 3. 測試 JavaScript 功能
                await self._test_javascript_functionality(page)
                
                # 4. 測試響應式設計
                await self._test_responsive_design(page)
                
            finally:
                await browser.close()
        
        # 生成測試報告
        self._generate_test_report()
        
        return self.test_results
    
    async def _test_page_loading(self, page: Page):
        """測試頁面載入"""
        print_status("info", "開始測試頁面載入...")
        
        for test_page in self.test_pages:
            test_name = f"頁面載入 - {test_page['name']}"
            url = f"{self.base_url}{test_page['path']}"
            
            try:
                print_status("info", f"測試頁面: {test_page['name']} ({url})")
                
                # 載入頁面
                response = await page.goto(url, wait_until="domcontentloaded", timeout=30000)
                
                # 檢查 HTTP 狀態
                if response.status >= 400:
                    self._add_test_result(test_name, False, f"HTTP {response.status}")
                    continue
                
                # 等待頁面完全載入
                await page.wait_for_load_state("networkidle", timeout=10000)
                
                # 檢查頁面標題
                title = await page.title()
                if test_page['expected_title'] not in title:
                    self._add_test_result(test_name, False, f"標題不符: 期望包含 '{test_page['expected_title']}', 實際為 '{title}'", warning=True)
                else:
                    self._add_test_result(test_name, True, f"頁面正常載入，標題: {title}")
                
                # 截圖
                screenshot_path = self.screenshot_dir / f"{test_page['name']}.png"
                await page.screenshot(path=screenshot_path, full_page=True)
                print_status("info", f"截圖已保存: {screenshot_path}")
                
                # 檢查是否有明顯的錯誤信息
                error_selectors = [
                    'text=Error', 'text=404', 'text=500', 'text=Internal Server Error',
                    '.error', '.alert-danger', '[class*="error"]'
                ]
                
                for selector in error_selectors:
                    try:
                        if await page.is_visible(selector, timeout=1000):
                            error_text = await page.text_content(selector)
                            self._add_test_result(f"{test_name} - 錯誤檢查", False, f"發現錯誤信息: {error_text}")
                            break
                    except:
                        continue
                
                # 檢查關鍵元素是否存在
                await self._check_page_elements(page, test_page['name'])
                
            except Exception as e:
                self._add_test_result(test_name, False, f"頁面載入失敗: {str(e)}")
    
    async def _test_api_endpoints(self, page: Page):
        """測試 API 端點"""
        print_status("info", "開始測試 API 端點...")
        
        for endpoint in self.api_endpoints:
            test_name = f"API測試 - {endpoint['name']}"
            url = f"{self.base_url}{endpoint['path']}"
            
            try:
                print_status("info", f"測試API: {endpoint['name']} ({url})")
                
                # 使用 page.evaluate 調用 fetch API
                result = await page.evaluate(f"""
                    async () => {{
                        try {{
                            const response = await fetch('{url}');
                            return {{
                                status: response.status,
                                ok: response.ok,
                                statusText: response.statusText,
                                contentType: response.headers.get('content-type'),
                                data: response.ok ? await response.text() : null
                            }};
                        }} catch (error) {{
                            return {{
                                error: error.message,
                                status: 0
                            }};
                        }}
                    }}
                """)
                
                if result.get('error'):
                    self._add_test_result(test_name, False, f"API呼叫失敗: {result['error']}")
                elif result['status'] >= 400:
                    self._add_test_result(test_name, False, f"API回應錯誤: HTTP {result['status']} - {result.get('statusText', 'Unknown')}")
                else:
                    self._add_test_result(test_name, True, f"API正常回應: HTTP {result['status']}")
                    
                    # 如果是 JSON 回應，嘗試解析
                    if result.get('contentType') and 'application/json' in result['contentType']:
                        try:
                            json.loads(result['data'])
                            print_status("info", f"API回應有效的JSON格式")
                        except:
                            self._add_test_result(f"{test_name} - JSON格式", False, "API回應不是有效的JSON格式", warning=True)
                
            except Exception as e:
                self._add_test_result(test_name, False, f"API測試失敗: {str(e)}")
    
    async def _test_javascript_functionality(self, page: Page):
        """測試 JavaScript 功能"""
        print_status("info", "開始測試 JavaScript 功能...")
        
        for test_page in self.test_pages:
            url = f"{self.base_url}{test_page['path']}"
            test_name = f"JavaScript測試 - {test_page['name']}"
            
            try:
                await page.goto(url, wait_until="domcontentloaded")
                await page.wait_for_load_state("networkidle", timeout=10000)
                
                # 檢查頁面是否有基本的 JavaScript 功能
                js_test_result = await page.evaluate("""
                    () => {
                        const results = {
                            jquery: typeof $ !== 'undefined',
                            domReady: document.readyState === 'complete',
                            hasScripts: document.scripts.length > 0,
                            hasEventListeners: true  // 假設有事件監聽器
                        };
                        
                        // 檢查是否有常見的錯誤
                        const errors = [];
                        
                        // 檢查 console.error 是否被調用
                        if (window.__consoleErrors && window.__consoleErrors.length > 0) {
                            errors.push(...window.__consoleErrors);
                        }
                        
                        return { ...results, errors };
                    }
                """)
                
                if js_test_result['hasScripts']:
                    self._add_test_result(test_name, True, f"頁面包含 {js_test_result.get('hasScripts', 0)} 個JavaScript腳本")
                else:
                    self._add_test_result(test_name, False, "頁面沒有JavaScript腳本", warning=True)
                
                if js_test_result['jquery']:
                    print_status("info", "檢測到 jQuery")
                
            except Exception as e:
                self._add_test_result(test_name, False, f"JavaScript測試失敗: {str(e)}")
    
    async def _test_responsive_design(self, page: Page):
        """測試響應式設計"""
        print_status("info", "開始測試響應式設計...")
        
        viewports = [
            {"name": "桌面", "width": 1280, "height": 720},
            {"name": "平板", "width": 768, "height": 1024},
            {"name": "手機", "width": 375, "height": 667}
        ]
        
        # 只測試首頁的響應式設計
        test_url = self.base_url
        
        for viewport in viewports:
            test_name = f"響應式設計 - {viewport['name']}"
            
            try:
                await page.set_viewport_size(viewport)
                await page.goto(test_url, wait_until="domcontentloaded")
                await page.wait_for_load_state("networkidle", timeout=10000)
                
                # 截圖
                screenshot_path = self.screenshot_dir / f"responsive_{viewport['name']}.png"
                await page.screenshot(path=screenshot_path, full_page=True)
                
                self._add_test_result(test_name, True, f"響應式設計測試完成，截圖: {screenshot_path}")
                
            except Exception as e:
                self._add_test_result(test_name, False, f"響應式設計測試失敗: {str(e)}")
    
    async def _check_page_elements(self, page: Page, page_name: str):
        """檢查頁面關鍵元素"""
        test_name = f"元素檢查 - {page_name}"
        
        try:
            # 檢查導航欄
            if await page.is_visible("nav, .navbar, .navigation"):
                print_status("info", "導航欄存在")
            else:
                self._add_test_result(f"{test_name} - 導航欄", False, "未找到導航欄", warning=True)
            
            # 檢查主要內容區域
            if await page.is_visible("main, .main, .content, .container"):
                print_status("info", "主要內容區域存在")
            else:
                self._add_test_result(f"{test_name} - 內容區域", False, "未找到主要內容區域", warning=True)
            
            # 檢查是否有表單
            if await page.is_visible("form, input, button"):
                print_status("info", "檢測到互動元素")
            
        except Exception as e:
            self._add_test_result(test_name, False, f"元素檢查失敗: {str(e)}")
    
    def _handle_console_message(self, msg):
        """處理控制台訊息"""
        if msg.type == "error":
            error_info = {
                "type": "console_error",
                "message": msg.text,
                "timestamp": datetime.now().isoformat(),
                "url": msg.location.get("url", "unknown") if msg.location else "unknown"
            }
            self.js_errors.append(error_info)
            print_status("warning", f"JavaScript控制台錯誤: {msg.text}")
    
    def _handle_page_error(self, error):
        """處理頁面錯誤"""
        error_info = {
            "type": "page_error",
            "message": str(error),
            "timestamp": datetime.now().isoformat()
        }
        self.js_errors.append(error_info)
        print_status("error", f"頁面JavaScript錯誤: {str(error)}")
    
    def _add_test_result(self, test_name: str, passed: bool, message: str, warning: bool = False):
        """添加測試結果"""
        result = {
            "test_name": test_name,
            "passed": passed,
            "message": message,
            "timestamp": datetime.now().isoformat(),
            "warning": warning
        }
        
        self.test_results["tests"].append(result)
        self.test_results["summary"]["total"] += 1
        
        if passed:
            self.test_results["summary"]["passed"] += 1
            print_status("success", f"{test_name}: {message}")
        elif warning:
            self.test_results["summary"]["warnings"] += 1
            print_status("warning", f"{test_name}: {message}")
        else:
            self.test_results["summary"]["failed"] += 1
            print_status("error", f"{test_name}: {message}")
    
    def _generate_test_report(self):
        """生成測試報告"""
        self.test_results["end_time"] = datetime.now().isoformat()
        self.test_results["js_errors"] = self.js_errors
        
        # 保存 JSON 報告
        report_path = Path("playwright_test_report.json")
        with open(report_path, "w", encoding="utf-8") as f:
            json.dump(self.test_results, f, ensure_ascii=False, indent=2)
        
        # 生成簡潔的文字報告
        summary = self.test_results["summary"]
        
        print_colored("\n" + "=" * 60, Colors.BLUE)
        print_colored("測試報告摘要", Colors.BOLD)
        print_colored("=" * 60, Colors.BLUE)
        
        print_status("info", f"總計測試: {summary['total']}")
        print_status("success", f"通過測試: {summary['passed']}")
        if summary['warnings'] > 0:
            print_status("warning", f"警告: {summary['warnings']}")
        if summary['failed'] > 0:
            print_status("error", f"失敗測試: {summary['failed']}")
        
        if self.js_errors:
            print_status("warning", f"JavaScript錯誤: {len(self.js_errors)} 個")
        
        print_colored(f"\n詳細報告已保存: {report_path}", Colors.CYAN)
        print_colored(f"截圖目錄: {self.screenshot_dir}", Colors.CYAN)
        
        # 計算成功率
        success_rate = (summary['passed'] / summary['total']) * 100 if summary['total'] > 0 else 0
        
        if success_rate >= 90:
            print_status("success", f"測試成功率: {success_rate:.1f}% - 優秀")
        elif success_rate >= 80:
            print_status("warning", f"測試成功率: {success_rate:.1f}% - 良好")
        else:
            print_status("error", f"測試成功率: {success_rate:.1f}% - 需要改進")


async def main():
    """主函數"""
    print_colored("Playwright 整合測試開始", Colors.BOLD)
    
    # 初始化服務管理器和測試器
    service_manager = ServiceManager()
    tester = PlaywrightTester()
    
    try:
        # 1. 啟動整合服務
        if not await service_manager.start_service():
            print_status("error", "無法啟動整合服務，測試終止")
            return
        
        # 等待服務穩定
        print_status("info", "等待服務穩定...")
        await asyncio.sleep(5)
        
        # 2. 執行測試
        test_results = await tester.run_all_tests()
        
        # 3. 輸出最終結果
        summary = test_results["summary"]
        if summary["failed"] == 0:
            print_status("success", "所有測試通過！網頁功能正常")
        else:
            print_status("warning", f"部分測試失敗，需要檢查 {summary['failed']} 個問題")
        
    except KeyboardInterrupt:
        print_status("warning", "測試被用戶中斷")
    except Exception as e:
        print_status("error", f"測試執行失敗: {e}")
    finally:
        # 4. 停止服務
        await service_manager.stop_service()
        print_status("info", "測試完成")


if __name__ == "__main__":
    # 檢查依賴
    try:
        import playwright
        import psutil
    except ImportError as e:
        print_status("error", f"缺少必要的依賴: {e}")
        print_status("info", "請安裝: pip install playwright psutil")
        print_status("info", "然後執行: playwright install chromium")
        sys.exit(1)
    
    # 執行測試
    asyncio.run(main())