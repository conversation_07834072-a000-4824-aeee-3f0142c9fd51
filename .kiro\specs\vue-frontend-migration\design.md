# 設計文件

## 概述

本設計文件詳細說明了半導體郵件處理系統的架構重構，為未來遷移到 Vue.js 做準備。當前階段專注於使用現有技術（Flask + HTML/JS）重新組織目錄結構和模組化架構，建立清晰的模組邊界和 API 介面。

重構策略採用模組化方法，將現有功能按領域分離，標準化 API 介面，並建立適合未來 Vue.js 遷移的目錄結構。

## 架構

### 整體架構圖

```mermaid
graph TB
    subgraph "重構後的 Flask 前端"
        A[Flask 主應用] --> B[模組化路由]
        A --> C[統一 API 介面]
        A --> D[WebSocket 支援]
        
        subgraph "功能模組"
            E[Email Module]
            F[Analytics Module]
            G[File Management Module]
            H[EQC Module]
            I[Tasks Module]
            J[Monitoring Module]
        end
        
        subgraph "共享資源"
            K[Shared Templates]
            L[Shared Static Assets]
            M[Shared Utils]
        end
    end
    
    subgraph "現有後端服務"
        N[Flask Email Service :5000]
        O[FastAPI EQC Service :8010]
        P[REST APIs]
        Q[WebSocket Endpoints]
        R[SQLite Database]
    end
    
    B --> P
    D --> Q
    P --> R
    Q --> R
```

### 當前技術堆疊

**前端技術：**
- Flask 2.3.3 (Web 框架)
- Jinja2 (模板引擎)
- HTML5 + CSS3 + JavaScript ES6
- Bootstrap 或類似 CSS 框架
- Chart.js (圖表庫)
- jQuery (DOM 操作)

**後端服務：**
- Flask 2.3.3 (郵件服務 :5000)
- FastAPI 0.104.1 (EQC 處理服務 :8010)
- SQLAlchemy 2.0.23 (ORM)
- WebSocket 支援

**開發工具：**
- Python >=3.9
- 現有測試框架 (Pytest)
- 現有程式碼品質工具

## 元件和介面

### 當前實際結構（已完成 - Task 1-6.1）

**✅ 已實現並正常運作的結構：**

```
D:\project\python\outlook_summary\
├── � READMEx.md                          # 專案總覽
├── 🔧 .env.example
├── 🔧 requirements.txt
├── 🔧 pyproject.toml
│
├── 📁 frontend/                          # ✅ 前端主目錄（Flask + HTML/JS）
│   ├── 📁 email/                         # ✅ 郵件功能領域
│   │   ├── 📁 templates/                 # ✅ HTML 模板
│   │   │   ├── 📄 inbox.html            # ✅ 收件匣頁面
│   │   │   ├── 📄 email_detail.html     # ✅ 郵件詳情
│   │   │   ├── 📄 email_compose.html    # ✅ 撰寫郵件
│   │   │   ├── 📄 email_settings.html   # ✅ 郵件設定
│   │   │   └── 📄 inbox_new.html        # ✅ 新收件匣介面
│   │   ├── � statics/                    # ✅ 靜態資源
│   │   │   ├── � css/
│   │   │   │   └── � inbox.css         # ✅ 郵件專用樣式
│   │   │   ├── � js/ 
│   │   │   │   └── � emaail/            # ✅ 郵件 JS 模組
│   │   │   │       ├── 📄 email-*.js   # ✅ 郵件相關邏輯
│   │   │   │       └── 📄 email-parser-ui.js # ✅ 郵件解析介面
│   │   │   └── 📁 images/               # ✅ 郵件相關圖片
│   │   ├── � compomnents/                # ✅ 可重用組件（空目錄，待擴展）
│   │   ├── 📁 routes/                    # ✅ 路由處理
│   │   │   └── 📄 email_routes.py       # ✅ 郵件相關路由
│   │   └── � READeME.md                  # ✅ 郵件模組說明
│   │
│   ├── � analytDics/                     # ✅ 分析統計功能領域
│   │   ├── 📁 templates/
│   │   │   ├── 📄 dashboard.html        # ✅ 統計儀表板
│   │   │   ├── 📄 reports.html          # ✅ 報表頁面
│   │   │   ├── 📄 vendor_analysis.html  # ✅ 廠商分析
│   │   │   └── 📄 csv_processor.html    # ✅ CSV 處理頁面
│   │   ├── 📁 static/
│   │   │   ├── �  css/ ✅
│   │   │   ├── 📁 js/ ✅
│   │   │   └── 📁 lib/ ✅               # 第三方庫
│   │   ├── 📁 components/ ✅             # 空目錄，待擴展
│   │   ├── � routes/
│   │   │   └── 📄 analytics_routes.py   # ✅ 統計路由
│   │   └── 📋 README.md ✅
│   │
│   ├── 📁 file_management/               # ✅ 檔案管理功能領域
│   │   ├── 📁 templates/
│   │   │   ├── 📄 file_manager.html     # ✅ 檔案管理器
│   │   │   ├── 📄 upload.html           # ✅ 檔案上傳
│   │   │   └── 📄 attachment_browser.html # ✅ 附件瀏覽器
│   │   ├── 📁 static/ ✅
│   │   ├── � compaonents/ ✅
│   │   ├── � Rroutes/
│   │   │   └── 📄 file_routes.py        # ✅ 檔案路由
│   │   └── 📋 README.md ✅
│   │
│   ├── 📁 eqc/                           # ✅ EQC功能領域
│   │   ├── � templates/.
│   │   │   ├── 📄 eqc_dashboard.html    # ✅ EQC儀表板
│   │   │   ├── � eqc_history.html      # ✅ EQC歷史記錄
│   │   │   ├── � qua/lity_check.html    # ✅ 品質檢查
│   │   │   └── 📄 compliance.html       # ✅ 合規檢查
│   │   ├── 📁 static/ ✅
│   │   ├── 📁 components/ ✅
│   │   ├── 📁 routes/
│   │   │   └── � eqc_routes.p.y         # ✅ EQC路由
│   │   └── � README.mtd ✅
│   │
│   ├── 📁 tasks/                         # ✅ 任務管理功能領域
│   │   ├── 📁 templates/
│   │   │   ├── 📄 task_dashboard.html   # ✅ 任務儀表板
│   │   │   ├── 📄 task_queue.html       # ✅ 任務隊列
│   │   │   ├── 📄 task_scheduler.html   # ✅ 任務調度
│   │   │   └── 📄 concurrent_task_manager.html # ✅ 並發任務管理
│   │   ├── 📁 static/ ✅
│   │   ├── � compoqnents/ ✅
│   │   ├── � rou tes/
│   │   │   └── 📄 task_routes.py        # ✅ 任務路由
│   │   └── �  README.md ✅
│   │
│   ├── 📁 monitoring/                    # ✅ 監控功能領域
│   │   ├── � templates/
│   │   │   ├── 📄 system_dashboard.html # ✅ 系統監控儀表板
│   │   │   ├── 📄 health_check.html     # ✅ 健康檢查
│   │   │   ├── 📄 database_manager.html # ✅ 資料庫管理
│   │   │   └── 📄 realtime_dashboard.html # ✅ 即時監控
│   │   ├── �  static/ ✅
│   │   ├── 📁 components/ ✅
│   │   ├── 📁 routes/
│   │   │   └── 📄 monitoring_routes.py  # ✅ 監控路由
│   │   └── � REA DME.md ✅
│   │
│   ├── 📁 shared/                        # ✅ 共享前端資源
│   │   ├── 📁 templates/
│   │   │   ├── �  base.html             # ✅ 基礎模板（主要佈局）
│   │   │   └── 📄 components/           # ✅ 共享組件
│   │   │       ├── 📄 navbar.html       # ✅ 導航列
│   │   │       ├── 📄 sidebar.html      # ✅ 側邊欄
│   │   │       ├── 📄 modal.html        # ✅ 模態框
│   │   │       ├── 📄 loading.html      # ✅ 載入動畫
│   │   │       ├── 📄 notification.html # ✅ 通知組件
│   │   │       └── 📄 confirm-dialog.html # ✅ 確認對話框
│   │   ├── 📁 static/
│   │   │   ├── 📁 css/
│   │   │   │   ├── 📄 base.css          # ✅ 基礎樣式
│   │   │   │   ├── 📄 layout.css        # ✅ 佈局樣式
│   │   │   │   ├── 📄 responsive.css    # ✅ 響應式樣式
│   │   │   │   └── 📄 special-components.css # ✅ 特殊組件樣式
│   │   │   ├── 📁 js/
│   │   │   │   ├── 📄 main.js           # ✅ 主要 JavaScript
│   │   │   │   ├── 📄 ui-components.js  # ✅ UI 組件
│   │   │   │   ├── � core/c             # ✅ 核心功能
│   │   │   │   │   ├── 📄 api-client.js # ✅ API 客戶端
│   │   │   │   │   ├── 📄 utils.js      # ✅ 工具函數
│   │   │   │   │   ├── 📄 dom-manager.js # ✅ DOM 管理
│   │   │   │   │   ├── 📄 status-manager.js # ✅ 狀態管理
│   │   │   │   │   └── 📄 error-handler.js # ✅ 錯誤處理
│   │   │   │   ├── 📁 utils/            # ✅ 工具模組
│   │   │   │   │   └── 📄 url-config.js # ✅ URL 配置
│   │   │   │   └── 📁 components/       # ✅ JavaScript 組件
│   │   │   │       └── 📄 notification.js # ✅ 通知組件
│   │   │   ├── � loib/                  # ✅ 第三方函式庫
│   │   │   │   └── 📄 chart.min.js     # ✅ 圖表庫
│   │   │   └── 📁 images/               # ✅ 共享圖片
│   │   │       └── 📄 favicon.ico      # ✅ 網站圖標
│   │   ├── 📁 utils/                    # ✅ Python 工具
│   │   │   ├── � __init__.py          # ✅
│   │   │   └── � esrror_handler.py     # ✅ 錯誤處理器
│   │   └── 📋 README.md ✅
│   │
│   ├── 📄 app.py                         # ✅ Flask 主應用（重構後）
│   ├── 📄 config.py                      # ✅ Flask 配置
│   ├── 📄 cli.py                         # ✅ CLI 工具
│   └── 📋 README.md                      # ✅ 前端開發指南
```

### 🔮 未來 Vue.js 遷移準備結構（Phase 2 - 待實現）

**🚧 為 Vue.js 遷移準備的額外檔案（當前不需要）：**

```
├── 📁 shared/                            # 共享前端資源擴展
│   ├── 📁 templates/
│   │   ├── 📄 layout.html               # 🔮 獨立佈局模板（Vue 組件化準備）
│   │   └── 📄 components/               # 🔮 更多 Vue 準備組件
│   │       ├── 📄 data-table.html       # 🔮 數據表格組件
│   │       ├── 📄 chart-widget.html     # 🔮 圖表組件
│   │       └── 📄 form-controls.html    # 🔮 表單控制組件
│   ├── 📁 static/
│   │   ├── 📁 css/
│   │   │   ├── 📄 variables.css         # 🔮 CSS 變數（Vue 主題系統準備）
│   │   │   ├── 📄 global.css            # 🔮 全域樣式
│   │   │   ├── 📄 components.css        # 🔮 組件樣式
│   │   │   └── 📄 theme.css             # 🔮 主題樣式
│   │   ├── 📁 js/
│   │   │   ├── 📄 common.js             # 🔮 統一共用函數（Vue Composables 準備）
│   │   │   ├── 📄 constants.js          # 🔮 前端常數（Vue 環境變數準備）
│   │   │   ├── 📄 websocket-client.js   # 🔮 WebSocket 客戶端
│   │   │   └── 📄 state-manager.js      # 🔮 狀態管理（Pinia 準備）
│   │   └── 📁 lib/                      # 🔮 更多第三方庫
│   │       ├── 📄 jquery.min.js         # 🔮 jQuery（過渡期使用）
│   │       ├── 📄 bootstrap.min.js      # 🔮 Bootstrap（過渡期使用）
│   │       └── 📄 vue.global.js         # 🔮 Vue.js 核心
```

### 📊 結構狀態說明

**✅ 當前已完成（Task 1-6.1）：**
- 完整的模組化 Flask 架構
- 所有 6 個功能模組正常運作
- 共享資源和組件系統
- 統一的錯誤處理和配置管理
- 70+ 個路由成功遷移
- 37 個 JS 檔案 + 9 個 CSS 檔案模組化
- **✅ 功能驗證測試完成（Task 6.1）**：
  - 所有現有頁面正常載入（100% 成功率）
  - 所有現有功能正常運作
  - 健康檢查端點正常
  - 6 個模組主頁全部可訪問
  - 數據庫連接與完整性驗證通過

**🔮 未來準備（Phase 2 - Vue.js 遷移時）：**
- 獨立的 layout.html（組件化佈局）
- 統一的 common.js（Composables 準備）
- 集中的 constants.js（環境變數管理）
- 更多 Vue.js 特定的組件和工具

**🎯 重要結論：**
當前結構已經完全滿足 Task 5.1 的需求，系統正常運作。未來的檔案是為 Vue.js 遷移做準備，不影響當前功能。

### 📊 功能完整性分析

**✅ 前端功能覆蓋率：100%**
- **Flask 模組化架構**：`email_inbox_app.py` → `frontend/app.py` (已完全移除)
- **模板和靜態資源**：23 個模板檔案 + 37 個 JS 檔案 + 9 個 CSS 檔案已完成模組化
- **路由邏輯**：70+ 個路由成功遷移到 6 個功能模組
- **Web API 層**：`src/presentation/web/api/` 已識別待整合 (Task 3.5)
- **Vue.js 原型**：`src/presentation/web/frontend/` 完整 Vue.js 應用已保留 (Task 3.6)

**🔮 Vue.js 遷移基礎**
- **現有原型**：`src/presentation/web/frontend/` 包含完整的 Vue 3 + Element Plus + Chart.js 應用
- **編譯資源**：`src/presentation/web/dist/` 包含已編譯的前端資源
- **架構準備**：模組化 Flask 架構為 Vue.js 遷移提供清晰的 API 邊界
│
├── 📁 backend/                           # 後端主目錄（保持現有架構，重新組織）
│   ├── 📁 email/                         # 郵件服務（重新組織現有代碼）
│   │   ├── 📁 api/
│   │   │   ├── 📄 routes.py             # 郵件 API 路由
│   │   │   ├── 📄 schemas.py            # 郵件 API 模式
│   │   │   └── 📄 dependencies.py       # 郵件依賴注入
│   │   ├── 📁 services/                 # 遷移現有服務
│   │   │   ├── 📄 outlook_service.py    # 來自 src/infrastructure/adapters/outlook/
│   │   │   ├── 📄 pop3_service.py       # 來自 src/infrastructure/adapters/pop3/
│   │   │   ├── 📄 email_parser.py       # 郵件解析服務
│   │   │   └── 📄 sync_service.py       # 郵件同步服務
│   │   ├── 📁 models/
│   │   │   ├── 📄 email.py              # 郵件數據模型
│   │   │   └── 📄 attachment.py         # 附件模型
│   │   ├── 📁 repositories/
│   │   │   └── 📄 email_repository.py   # 郵件數據訪問
│   │   ├── 📁 core/
│   │   │   ├── 📄 config.py             # 郵件服務配置
│   │   │   └── 📄 exceptions.py         # 郵件異常
│   │   └── 📋 README.md
│   │
│   ├── 📁 analytics/                     # 分析統計服務
│   │   ├── 📁 api/
│   │   │   ├── 📄 routes.py             # 統計 API 路由
│   │   │   └── 📄 schemas.py            # 統計 API 模式
│   │   ├── 📁 services/                 # 遷移現有分析服務
│   │   │   ├── � csv _processor.py      # 來自 batch_csv_to_excel_processor.py
│   │   │   ├── 📄 report_generator.py   # 報表生成服務
│   │   │   ├── 📄 statistics_engine.py  # 統計計算引擎
│   │   │   └── 📄 vendor_analyzer.py    # 廠商分析服務
│   │   ├── 📁 models/
│   │   │   ├── 📄 report.py             # 報表模型
│   │   │   └── 📄 statistics.py         # 統計模型
│   │   ├── 📁 repositories/
│   │   │   └── �m analytics_repository.py
│   │   └── 📋 README.md
│   │
│   ├── 📁 file_management/               # 檔案管理服務
│   │   ├── 📁 api/
│   │   │   ├── 📄 routes.py             # 檔案 API 路由
│   │   │   └── 📄 schemas.py            # 檔案 API 模式
│   │   ├── 📁 services/                 # 遷移現有檔案服務
│   │   │   ├── 📄 attachment_service.py  # 來自 src/infrastructure/adapters/attachments/
│   │   │   ├── 📄 upload_service.py     # 來自 src/infrastructure/adapters/file_upload/
│   │   │   ├── 📄 file_monitor.py       # 來自 src/services/vendor_file_monitor.py
│   │   │   └── 📄 file_cleaner.py       # 來自 src/services/file_cleaner.py
│   │   ├── 📁 models/
│   │   │   └── 📄 file.py               # 檔案模型
│   │   └── 📋 README.md
│   │
│   ├── 📁 eqc/                           # EQC服務
│   │   ├── 📁 api/
│   │   │   ├── 📄 routes.py             # EQC API 路由
│   │   │   └── 📄 schemas.py            # EQC API 模式
│   │   ├── 📁 services/
│   │   │   ├── 📄 session_manager.py    # 來自 src/services/eqc_session_manager.py
│   │   │   ├── 📄 quality_checker.py    # 品質檢查服務
│   │   │   └── 📄 compliance_validator.py # 合規驗證服務
│   │   ├── 📁 models/
│   │   │   └── 📄 eqc_data.py           # EQC數據模型
│   │   └── 📋 README.md
│   │
│   ├── 📁 tasks/                         # 任務管理服務
│   │   ├── 📁 api/
│   │   │   ├── 📄 routes.py             # 任務 API 路由
│   │   │   └── 📄 schemas.py            # 任務 API 模式
│   │   ├── 📁 services/                 # 遷移現有任務服務
│   │   │   ├── 📄 scheduler.py          # 來自 src/services/scheduler.py
│   │   │   ├── 📄 task_manager.py       # 來自 src/services/concurrent_task_manager.py
│   │   │   ├── 📄 dramatiq_service.py   # 來自 dramatiq_tasks.py
│   │   │   └── 📄 task_monitor.py       # 任務監控服務
│   │   ├── 📁 models/
│   │   │   └── 📄 task.py               # 任務模型
│   │   └── 📋 README.md
│   │
│   ├── 📁 monitoring/                    # 監控服務
│   │   ├── 📁 api/
│   │   │   ├── 📄 routes.py             # 監控 API 路由
│   │   │   └── 📄 schemas.py            # 監控 API 模式
│   │   ├── 📁 services/
│   │   │   ├── 📄 health_checker.py     # 健康檢查服務
│   │   │   ├── 📄 metrics_collector.py  # 指標收集服務
│   │   │   └── 📄 alert_manager.py      # 告警管理服務
│   │   ├── 📁 models/
│   │   │   └── 📄 metrics.py            # 指標模型
│   │   └── 📋 README.md
│   │
│   ├── 📁 shared/                        # 後端共享資源
│   │   ├── 📁 database/                 # 遷移現有數據庫代碼
│   │   │   ├── 📄 base.py               # 來自 src/infrastructure/adapters/database/
│   │   │   ├── 📄 email_database.py    # 郵件數據庫
│   │   │   └── 📄 models.py             # 共享數據模型
│   │   ├── 📁 utils/                    # 遷移現有工具
│   │   │   ├── 📄 logger.py             # 來自 src/services/unified_logger.py
│   │   │   ├── 📄 config_manager.py     # 來自 src/services/config_manager.py
│   │   │   └── 📄 helpers.py            # 工具函數
│   │   ├── 📁 middleware/               # 共享中間件
│   │   ├── 📁 exceptions/               # 共享異常
│   │   └── 📁 constants/                # 後端常數
│   │
│   ├── 📄 main.py                        # 重構後的主入口
│   ├── 📄 config.py                      # 統一配置管理
│   └── 📋 README.md                      # 後端開發指南
│
├── 📁 infrastructure/                    # 基礎設施（保持現有）
│   ├── 📁 docker/
│   ├── 📁 scripts/
│   └── 📁 deployment/
│
├── 📁 docs/                              # 文檔系統
│   ├── 📋 README.md
│   ├── 📁 migration/                     # 遷移文檔
│   │   ├── 📋 restructure-guide.md      # 重構指南
│   │   ├── 📋 file-mapping.md           # 檔案映射表
│   │   └── 📋 team-responsibilities.md  # 團隊職責
│   ├── 📁 architecture/
│   ├── 📁 development/
│   └── 📁 api/
│
└── 📁 tests/                             # 測試（按模組組織）
    ├── 📁 frontend/
    │   ├── 📁 email/
    │   ├── 📁 analytics/
    │   └── 📁 shared/
    ├── 📁 backend/
    │   ├── 📁 email/
    │   ├── 📁 analytics/
    │   └── 📁 shared/
    └── 📁 integration/
```

### API 整合介面

**統一 API 客戶端 (JavaScript)：**
```javascript
// frontend/shared/static/js/api-client.js
class ApiClient {
  constructor() {
    this.baseURL = '/api';
    this.timeout = 10000;
  }
  
  async request(method, url, data = null) {
    const config = {
      method: method,
      headers: {
        'Content-Type': 'application/json',
        'X-Requested-With': 'XMLHttpRequest'
      }
    };
    
    if (data) {
      config.body = JSON.stringify(data);
    }
    
    try {
      const response = await fetch(this.baseURL + url, config);
      const result = await response.json();
      
      if (!response.ok) {
        throw new Error(result.message || '請求失敗');
      }
      
      return result;
    } catch (error) {
      console.error('API 請求錯誤:', error);
      throw error;
    }
  }
  
  get(url) { return this.request('GET', url); }
  post(url, data) { return this.request('POST', url, data); }
  put(url, data) { return this.request('PUT', url, data); }
  delete(url) { return this.request('DELETE', url); }
}

// 全域 API 客戶端實例
window.apiClient = new ApiClient();
```

**WebSocket 整合 (JavaScript)：**
```javascript
// frontend/shared/static/js/websocket-client.js
class WebSocketClient {
  constructor() {
    this.socket = null;
    this.reconnectAttempts = 0;
    this.maxReconnectAttempts = 5;
    this.reconnectInterval = 3000;
  }
  
  connect(url) {
    try {
      this.socket = new WebSocket(url);
      this.setupEventHandlers();
    } catch (error) {
      console.error('WebSocket 連接失敗:', error);
      this.handleReconnect();
    }
  }
  
  setupEventHandlers() {
    this.socket.onopen = () => {
      console.log('WebSocket 連接成功');
      this.reconnectAttempts = 0;
    };
    
    this.socket.onmessage = (event) => {
      const data = JSON.parse(event.data);
      this.handleMessage(data);
    };
    
    this.socket.onclose = () => {
      console.log('WebSocket 連接關閉');
      this.handleReconnect();
    };
    
    this.socket.onerror = (error) => {
      console.error('WebSocket 錯誤:', error);
    };
  }
  
  send(data) {
    if (this.socket && this.socket.readyState === WebSocket.OPEN) {
      this.socket.send(JSON.stringify(data));
    }
  }
  
  handleMessage(data) {
    // 分發消息到相應的處理器
    const event = new CustomEvent('websocket-message', { detail: data });
    document.dispatchEvent(event);
  }
  
  handleReconnect() {
    if (this.reconnectAttempts < this.maxReconnectAttempts) {
      setTimeout(() => {
        this.reconnectAttempts++;
        this.connect(this.url);
      }, this.reconnectInterval);
    }
  }
}

// 全域 WebSocket 客戶端實例
window.wsClient = new WebSocketClient();
```

## 數據模型

### Flask 路由架構

**統一路由結構：**
```python
# frontend/app.py
from flask import Flask, Blueprint
from email.routes.email_routes import email_bp
from analytics.routes.analytics_routes import analytics_bp
from file_management.routes.file_routes import file_bp
from eqc.routes.eqc_routes import eqc_bp
from tasks.routes.task_routes import task_bp
from monitoring.routes.monitoring_routes import monitoring_bp

app = Flask(__name__)

# 註冊模組藍圖
app.register_blueprint(email_bp, url_prefix='/email')
app.register_blueprint(analytics_bp, url_prefix='/analytics')
app.register_blueprint(file_bp, url_prefix='/files')
app.register_blueprint(eqc_bp, url_prefix='/eqc')
app.register_blueprint(task_bp, url_prefix='/tasks')
app.register_blueprint(monitoring_bp, url_prefix='/monitoring')

# 統一 API 路由
@app.route('/api/<module>/<path:endpoint>', methods=['GET', 'POST', 'PUT', 'DELETE'])
def api_proxy(module, endpoint):
    """統一 API 代理，轉發到相應的後端服務"""
    pass
```

### 標準化 API 回應格式

**統一回應結構：**
```python
# frontend/shared/utils/api_response.py
from flask import jsonify
from typing import Any, Optional

class ApiResponse:
    @staticmethod
    def success(data: Any = None, message: str = "操作成功") -> dict:
        return {
            "status": "success",
            "message": message,
            "data": data
        }
    
    @staticmethod
    def error(message: str = "操作失敗", code: str = "ERROR", details: Any = None) -> dict:
        return {
            "status": "error",
            "message": message,
            "code": code,
            "details": details
        }
    
    @staticmethod
    def paginated(data: list, page: int, size: int, total: int) -> dict:
        return {
            "status": "success",
            "data": data,
            "pagination": {
                "page": page,
                "size": size,
                "total": total,
                "pages": (total + size - 1) // size
            }
        }
```

### 模組特定數據模型

**郵件模組數據結構：**
```python
# frontend/email/models/email_models.py
from dataclasses import dataclass
from typing import List, Optional
from datetime import datetime

@dataclass
class EmailAttachment:
    id: str
    filename: str
    size: int
    content_type: str
    download_url: str

@dataclass
class Email:
    id: str
    subject: str
    sender: str
    recipient: str
    content: str
    attachments: List[EmailAttachment]
    is_read: bool
    vendor_type: str
    created_at: datetime
    updated_at: datetime

class VendorType:
    ETD = 'etd'
    GTK = 'gtk'
    JCET = 'jcet'
    LINGSEN = 'lingsen'
    XAHT = 'xaht'
```

## 錯誤處理

### Flask 全域錯誤處理

**錯誤處理器：**
```python
# frontend/shared/utils/error_handler.py
from flask import jsonify, request
from werkzeug.exceptions import HTTPException
import logging

class ErrorHandler:
    @staticmethod
    def register_error_handlers(app):
        @app.errorhandler(404)
        def not_found(error):
            if request.path.startswith('/api/'):
                return jsonify({
                    "status": "error",
                    "message": "API 端點不存在",
                    "code": "NOT_FOUND"
                }), 404
            return render_template('shared/templates/errors/404.html'), 404
        
        @app.errorhandler(500)
        def internal_error(error):
            logging.error(f"內部伺服器錯誤: {error}")
            if request.path.startswith('/api/'):
                return jsonify({
                    "status": "error",
                    "message": "內部伺服器錯誤",
                    "code": "INTERNAL_ERROR"
                }), 500
            return render_template('shared/templates/errors/500.html'), 500
        
        @app.errorhandler(Exception)
        def handle_exception(error):
            logging.error(f"未處理的異常: {error}")
            if request.path.startswith('/api/'):
                return jsonify({
                    "status": "error",
                    "message": "發生未知錯誤",
                    "code": "UNKNOWN_ERROR"
                }), 500
            return render_template('shared/templates/errors/500.html'), 500
```

### JavaScript 錯誤處理

**前端錯誤處理：**
```javascript
// frontend/shared/static/js/error-handler.js
class ErrorHandler {
  static init() {
    // 全域錯誤處理
    window.addEventListener('error', (event) => {
      console.error('JavaScript 錯誤:', event.error);
      this.showErrorMessage('頁面發生錯誤，請重新整理頁面');
    });
    
    // Promise 錯誤處理
    window.addEventListener('unhandledrejection', (event) => {
      console.error('未處理的 Promise 錯誤:', event.reason);
      this.showErrorMessage('操作失敗，請稍後再試');
    });
  }
  
  static handleApiError(error) {
    if (error.status === 401) {
      // 處理身份驗證錯誤
      this.showErrorMessage('登入已過期，請重新登入');
      window.location.href = '/login';
    } else if (error.status >= 500) {
      // 處理伺服器錯誤
      this.showErrorMessage('伺服器錯誤，請稍後再試');
    } else {
      // 處理其他錯誤
      const message = error.message || '發生未知錯誤';
      this.showErrorMessage(message);
    }
  }
  
  static handleWebSocketError(error) {
    console.error('WebSocket 錯誤:', error);
    this.showWarningMessage('即時連接中斷，正在重新連接...');
  }
  
  static showErrorMessage(message) {
    // 顯示錯誤訊息（可以使用 toast 或 modal）
    this.showNotification(message, 'error');
  }
  
  static showWarningMessage(message) {
    this.showNotification(message, 'warning');
  }
  
  static showNotification(message, type) {
    // 實作通知顯示邏輯
    const notification = document.createElement('div');
    notification.className = `notification notification-${type}`;
    notification.textContent = message;
    document.body.appendChild(notification);
    
    setTimeout(() => {
      notification.remove();
    }, 5000);
  }
}

// 初始化錯誤處理
document.addEventListener('DOMContentLoaded', () => {
  ErrorHandler.init();
});
```

### 模組級錯誤處理

**模組錯誤處理範例：**
```html
<!-- frontend/shared/templates/components/error-boundary.html -->
<div class="error-boundary" style="display: none;" data-error-boundary>
  <div class="alert alert-danger">
    <h4>發生錯誤</h4>
    <p data-error-message></p>
    <button class="btn btn-primary" data-retry-button>重試</button>
  </div>
</div>

<script>
function setupErrorBoundary(container) {
  const errorBoundary = container.querySelector('[data-error-boundary]');
  const errorMessage = container.querySelector('[data-error-message]');
  const retryButton = container.querySelector('[data-retry-button]');
  
  function showError(message, retryCallback) {
    errorMessage.textContent = message;
    errorBoundary.style.display = 'block';
    
    retryButton.onclick = () => {
      errorBoundary.style.display = 'none';
      if (retryCallback) retryCallback();
    };
  }
  
  return { showError };
}
</script>
```

## 測試策略

### Flask 測試架構

**單元測試 (70%)：**
- 使用 Pytest 進行 Flask 路由測試
- 測試 Python 工具函數和服務
- 測試 JavaScript 函數和模組

**整合測試 (20%)：**
- 測試 Flask 與後端 API 整合
- 測試模組間路由互動
- 測試 WebSocket 連接

**端到端測試 (10%)：**
- 使用 Playwright 或 Selenium 測試完整使用者流程
- 測試跨模組工作流程
- 測試效能和可用性

### 測試配置

**Flask 路由測試範例：**
```python
# tests/test_email_routes.py
import pytest
from flask import url_for
from frontend.app import create_app

@pytest.fixture
def app():
    app = create_app(testing=True)
    return app

@pytest.fixture
def client(app):
    return app.test_client()

def test_email_inbox_route(client):
    """測試郵件收件匣路由"""
    response = client.get('/email/inbox')
    assert response.status_code == 200
    assert b'收件匣' in response.data

def test_email_api_endpoint(client):
    """測試郵件 API 端點"""
    response = client.get('/api/email/list')
    assert response.status_code == 200
    data = response.get_json()
    assert data['status'] == 'success'

def test_email_detail_route(client):
    """測試郵件詳情路由"""
    response = client.get('/email/detail/123')
    assert response.status_code == 200
```

**JavaScript 單元測試範例：**
```javascript
// tests/js/test-email-api.js
describe('Email API Client', () => {
  let apiClient;
  
  beforeEach(() => {
    apiClient = new ApiClient();
    // Mock fetch
    global.fetch = jest.fn();
  });
  
  afterEach(() => {
    jest.resetAllMocks();
  });
  
  test('should fetch email list successfully', async () => {
    const mockResponse = {
      status: 'success',
      data: [
        { id: '1', subject: '測試郵件', sender: '<EMAIL>' }
      ]
    };
    
    fetch.mockResolvedValueOnce({
      ok: true,
      json: async () => mockResponse
    });
    
    const result = await apiClient.get('/email/list');
    
    expect(fetch).toHaveBeenCalledWith('/api/email/list', expect.any(Object));
    expect(result).toEqual(mockResponse);
  });
  
  test('should handle API errors correctly', async () => {
    fetch.mockResolvedValueOnce({
      ok: false,
      json: async () => ({ message: '請求失敗' })
    });
    
    await expect(apiClient.get('/email/list')).rejects.toThrow('請求失敗');
  });
});
```

**端到端測試範例：**
```python
# tests/e2e/test_email_workflow.py
from playwright.sync_api import sync_playwright
import pytest

def test_email_processing_workflow():
    """測試完整的郵件處理工作流程"""
    with sync_playwright() as p:
        browser = p.chromium.launch()
        page = browser.new_page()
        
        # 訪問郵件頁面
        page.goto('http://localhost:5000/email')
        
        # 檢查郵件列表是否可見
        page.wait_for_selector('[data-testid="email-list"]')
        assert page.is_visible('[data-testid="email-list"]')
        
        # 點擊第一封郵件
        page.click('[data-testid="email-item"]:first-child')
        
        # 檢查郵件詳情是否顯示
        page.wait_for_selector('[data-testid="email-detail"]')
        assert page.is_visible('[data-testid="email-detail"]')
        
        # 點擊處理按鈕
        page.click('[data-testid="process-button"]')
        
        # 檢查成功訊息
        page.wait_for_selector('[data-testid="success-message"]')
        assert '處理成功' in page.text_content('[data-testid="success-message"]')
        
        browser.close()
```

### 效能監控

**效能指標：**
- 頁面載入時間 < 2s
- API 回應時間 < 500ms
- JavaScript 執行時間 < 100ms
- 記憶體使用量監控

**監控實作：**
```javascript
// frontend/shared/static/js/performance-monitor.js
class PerformanceMonitor {
  static init() {
    // 監控頁面載入效能
    window.addEventListener('load', () => {
      this.measurePageLoad();
    });
    
    // 監控 API 呼叫效能
    this.interceptApiCalls();
  }
  
  static measurePageLoad() {
    const navigation = performance.getEntriesByType('navigation')[0];
    const loadTime = navigation.loadEventEnd - navigation.loadEventStart;
    
    console.log(`頁面載入時間: ${loadTime}ms`);
    
    // 發送效能數據到監控系統
    this.sendMetrics({
      type: 'page_load',
      duration: loadTime,
      url: window.location.pathname,
      timestamp: Date.now()
    });
  }
  
  static interceptApiCalls() {
    const originalFetch = window.fetch;
    
    window.fetch = async function(...args) {
      const startTime = performance.now();
      
      try {
        const response = await originalFetch.apply(this, args);
        const endTime = performance.now();
        const duration = endTime - startTime;
        
        PerformanceMonitor.measureApiCall(args[0], duration);
        
        return response;
      } catch (error) {
        const endTime = performance.now();
        const duration = endTime - startTime;
        
        PerformanceMonitor.measureApiCall(args[0], duration, error);
        throw error;
      }
    };
  }
  
  static measureApiCall(url, duration, error = null) {
    console.log(`API 呼叫 ${url}: ${duration}ms`);
    
    this.sendMetrics({
      type: 'api_call',
      url: url,
      duration: duration,
      error: error ? error.message : null,
      timestamp: Date.now()
    });
  }
  
  static sendMetrics(metrics) {
    // 發送到監控後端
    fetch('/api/monitoring/metrics', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(metrics)
    }).catch(error => {
      console.error('發送效能指標失敗:', error);
    });
  }
}

// 初始化效能監控
document.addEventListener('DOMContentLoaded', () => {
  PerformanceMonitor.init();
});
```

### 測試自動化

**CI/CD 整合：**
```yaml
# .github/workflows/test.yml
name: 測試流水線

on: [push, pull_request]

jobs:
  test:
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v2
    
    - name: 設置 Python
      uses: actions/setup-python@v2
      with:
        python-version: '3.9'
    
    - name: 安裝依賴
      run: |
        pip install -r requirements.txt
        pip install pytest pytest-cov
    
    - name: 執行 Python 測試
      run: |
        pytest tests/ --cov=frontend/ --cov-report=xml
    
    - name: 設置 Node.js
      uses: actions/setup-node@v2
      with:
        node-version: '16'
    
    - name: 安裝 JavaScript 依賴
      run: npm install
    
    - name: 執行 JavaScript 測試
      run: npm test
    
    - name: 執行端到端測試
      run: |
        python -m flask run &
        sleep 5
        pytest tests/e2e/
```