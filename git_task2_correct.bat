@echo off
echo Creating new branch for Task 2...
git checkout -b task/2-refactor-app

echo Adding Task 2 files to Git...
git add frontend/app.py
git add frontend/config.py
git add frontend/__init__.py
git add frontend/shared/__init__.py
git add frontend/shared/utils/__init__.py
git add frontend/shared/utils/error_handler.py
git add frontend/email/routes/email_routes.py
git add frontend/analytics/routes/analytics_routes.py
git add frontend/file_management/routes/file_routes.py
git add frontend/eqc/routes/eqc_routes.py
git add frontend/tasks/routes/task_routes.py
git add frontend/monitoring/routes/monitoring_routes.py

echo Committing Task 2 completion...
git commit -m "feat: 完成任務 2 - 重構 Flask 主應用程式

- 建立模組化 Flask 應用程式架構 (frontend/app.py)
- 實作藍圖註冊系統和配置管理 (frontend/config.py)
- 創建六個功能模組的路由系統
- 建立統一錯誤處理機制
- 保持所有現有路由功能不變
- 為 Vue.js 遷移準備模組化基礎

完成需求: 1.1, 3.1
任務狀態: ✅ 已完成"

echo Task 2 committed to new branch task/2-refactor-app
echo Next: You can merge this branch back to refactor/vue-preparation when ready
pause