#!/usr/bin/env python3
"""
Quick Database Backup Script
Create immediate backup of the main database
"""

import shutil
import os
from datetime import datetime
from pathlib import Path

def quick_backup():
    """Create immediate backup of main database"""
    
    # Database and backup paths
    db_path = Path("email_inbox.db")
    backup_dir = Path("backups/manual")
    backup_dir.mkdir(parents=True, exist_ok=True)
    
    # Generate backup filename
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    backup_name = f"email_inbox_backup_{timestamp}.db"
    backup_path = backup_dir / backup_name
    
    print("Quick Database Backup")
    print("=" * 30)
    print(f"Source: {db_path.absolute()}")
    print(f"Target: {backup_path.absolute()}")
    
    # Check if source database exists
    if not db_path.exists():
        print(f"ERROR: Database file not found: {db_path}")
        return False
    
    # Get source database info
    source_size = db_path.stat().st_size / (1024*1024)  # MB
    print(f"Source size: {source_size:.2f} MB")
    
    try:
        # Create backup
        print("Creating backup...")
        shutil.copy2(db_path, backup_path)
        
        # Verify backup
        if backup_path.exists():
            backup_size = backup_path.stat().st_size / (1024*1024)  # MB
            print(f"SUCCESS: Backup created successfully!")
            print(f"Backup size: {backup_size:.2f} MB")
            print(f"Backup location: {backup_path}")
            
            # Quick verification
            try:
                import sqlite3
                conn = sqlite3.connect(backup_path)
                cursor = conn.cursor()
                cursor.execute("SELECT COUNT(*) FROM emails")
                email_count = cursor.fetchone()[0]
                conn.close()
                print(f"VERIFIED: Backup contains {email_count} email records")
                
            except Exception as e:
                print(f"WARNING: Backup created but verification failed: {e}")
            
            return True
        else:
            print("ERROR: Backup file was not created")
            return False
            
    except Exception as e:
        print(f"ERROR: Backup failed: {e}")
        return False

if __name__ == "__main__":
    success = quick_backup()
    if success:
        print("\nQuick backup completed successfully!")
        print("Your database is now safely backed up.")
    else:
        print("\nQuick backup failed!")
        print("Please check the error messages above.")