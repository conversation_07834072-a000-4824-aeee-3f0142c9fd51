/* 
 * 基礎樣式模組
 * 包含重置樣式、基礎元素樣式、表單樣式等
 */

/* ==================== CSS 重置 ==================== */
*,
*::before,
*::after {
    box-sizing: border-box;
}

html {
    font-size: 16px;
    line-height: 1.6;
    scroll-behavior: smooth;
    -webkit-text-size-adjust: 100%;
    -ms-text-size-adjust: 100%;
}

body {
    margin: 0;
    padding: 0;
    font-family: var(--font-family-primary);
    font-size: var(--font-size-md);
    line-height: 1.6;
    color: var(--secondary-color);
    background-color: var(--bg-light);
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

/* ==================== 基礎元素樣式 ==================== */
h1, h2, h3, h4, h5, h6 {
    margin: 0 0 var(--spacing-md) 0;
    font-weight: 600;
    line-height: 1.3;
    color: var(--secondary-color);
}

h1 { font-size: var(--font-size-xl); }
h2 { font-size: var(--font-size-lg); }
h3 { font-size: 1.4em; }
h4 { font-size: 1.2em; }
h5 { font-size: 1.1em; }
h6 { font-size: 1em; }

p {
    margin: 0 0 var(--spacing-md) 0;
    line-height: 1.6;
}

a {
    color: var(--primary-color);
    text-decoration: none;
    transition: var(--transition);
}

a:hover {
    color: var(--secondary-color);
    text-decoration: underline;
}

a:focus {
    outline: 2px solid var(--primary-color);
    outline-offset: 2px;
}

ul, ol {
    margin: 0 0 var(--spacing-md) 0;
    padding-left: var(--spacing-lg);
}

li {
    margin-bottom: var(--spacing-xxs);
}

img {
    max-width: 100%;
    height: auto;
    display: block;
}

hr {
    border: none;
    height: 1px;
    background-color: var(--border-color);
    margin: var(--spacing-lg) 0;
}

/* ==================== 按鈕樣式 ==================== */
.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-xxs);
    padding: var(--spacing-sm) var(--spacing-md);
    border: 1px solid transparent;
    border-radius: var(--radius-sm);
    font-size: var(--font-size-sm);
    font-weight: 500;
    text-decoration: none;
    cursor: pointer;
    transition: var(--transition);
    white-space: nowrap;
    user-select: none;
    vertical-align: middle;
}

.btn:focus {
    outline: 2px solid var(--primary-color);
    outline-offset: 2px;
}

.btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    pointer-events: none;
}

/* 按鈕變體 */
.btn-primary {
    background-color: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.btn-primary:hover {
    background-color: var(--secondary-color);
    border-color: var(--secondary-color);
    color: white;
}

.btn-secondary {
    background-color: var(--text-muted);
    color: white;
    border-color: var(--text-muted);
}

.btn-secondary:hover {
    background-color: var(--secondary-color);
    border-color: var(--secondary-color);
    color: white;
}

.btn-success {
    background-color: var(--success-color);
    color: white;
    border-color: var(--success-color);
}

.btn-success:hover {
    background-color: #218838;
    border-color: #1e7e34;
    color: white;
}

.btn-danger {
    background-color: var(--error-color);
    color: white;
    border-color: var(--error-color);
}

.btn-danger:hover {
    background-color: #c82333;
    border-color: #bd2130;
    color: white;
}

.btn-warning {
    background-color: var(--warning-color);
    color: var(--secondary-color);
    border-color: var(--warning-color);
}

.btn-warning:hover {
    background-color: #e0a800;
    border-color: #d39e00;
    color: var(--secondary-color);
}

.btn-info {
    background-color: var(--info-color);
    color: white;
    border-color: var(--info-color);
}

.btn-info:hover {
    background-color: #138496;
    border-color: #117a8b;
    color: white;
}

.btn-outline-primary {
    color: var(--primary-color);
    border-color: var(--primary-color);
    background-color: transparent;
}

.btn-outline-primary:hover {
    background-color: var(--primary-color);
    color: white;
}

/* 按鈕大小 */
.btn-sm {
    padding: var(--spacing-xxs) var(--spacing-sm);
    font-size: var(--font-size-xs);
}

.btn-lg {
    padding: var(--spacing-md) var(--spacing-lg);
    font-size: var(--font-size-md);
}

/* 按鈕圖示 */
.btn-icon {
    font-size: 1.1em;
}

.btn-close {
    background: none;
    border: none;
    font-size: 1.2em;
    color: var(--text-muted);
    cursor: pointer;
    padding: var(--spacing-xxs);
    border-radius: var(--radius-sm);
    transition: var(--transition);
}

.btn-close:hover {
    color: var(--error-color);
    background-color: rgba(220, 53, 69, 0.1);
}

/* ==================== 表單樣式 ==================== */
.form-group {
    margin-bottom: var(--spacing-md);
}

.form-label {
    display: block;
    margin-bottom: var(--spacing-xxs);
    font-weight: 500;
    color: var(--secondary-color);
}

.form-control {
    display: block;
    width: 100%;
    padding: var(--spacing-sm) var(--spacing-md);
    font-size: var(--font-size-sm);
    line-height: 1.5;
    color: var(--secondary-color);
    background-color: white;
    border: 1px solid var(--border-color);
    border-radius: var(--radius-sm);
    transition: var(--transition);
}

.form-control:focus {
    border-color: var(--primary-color);
    outline: none;
    box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.25);
}

.form-control:disabled {
    background-color: var(--bg-light);
    opacity: 0.6;
    cursor: not-allowed;
}

.form-control.is-invalid {
    border-color: var(--error-color);
}

.form-control.is-valid {
    border-color: var(--success-color);
}

/* 表單控制項變體 */
.form-control-sm {
    padding: var(--spacing-xxs) var(--spacing-sm);
    font-size: var(--font-size-xs);
}

.form-control-lg {
    padding: var(--spacing-md) var(--spacing-lg);
    font-size: var(--font-size-md);
}

/* 選擇框樣式 */
.form-select {
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16'%3e%3cpath fill='none' stroke='%23343a40' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='m1 6 7 7 7-7'/%3e%3c/svg%3e");
    background-repeat: no-repeat;
    background-position: right var(--spacing-sm) center;
    background-size: 16px 12px;
    padding-right: calc(var(--spacing-lg) + 16px);
}

/* 複選框和單選框 */
.form-check {
    display: flex;
    align-items: center;
    gap: var(--spacing-xxs);
    margin-bottom: var(--spacing-sm);
}

.form-check-input {
    width: 16px;
    height: 16px;
    margin: 0;
    cursor: pointer;
    accent-color: var(--primary-color);
}

.form-check-label {
    margin: 0;
    cursor: pointer;
    font-size: var(--font-size-sm);
}

/* 表單驗證訊息 */
.invalid-feedback {
    display: block;
    width: 100%;
    margin-top: var(--spacing-xxs);
    font-size: var(--font-size-xs);
    color: var(--error-color);
}

.valid-feedback {
    display: block;
    width: 100%;
    margin-top: var(--spacing-xxs);
    font-size: var(--font-size-xs);
    color: var(--success-color);
}

/* ==================== 表格樣式 ==================== */
.table {
    width: 100%;
    margin-bottom: var(--spacing-md);
    border-collapse: collapse;
    background-color: white;
}

.table th,
.table td {
    padding: var(--spacing-sm) var(--spacing-md);
    text-align: left;
    border-bottom: 1px solid var(--border-color);
    vertical-align: middle;
}

.table th {
    background-color: var(--bg-light);
    font-weight: 600;
    color: var(--secondary-color);
    border-top: 1px solid var(--border-color);
}

.table-striped tbody tr:nth-of-type(odd) {
    background-color: rgba(0, 0, 0, 0.02);
}

.table-hover tbody tr:hover {
    background-color: rgba(102, 126, 234, 0.05);
}

.table-bordered {
    border: 1px solid var(--border-color);
}

.table-bordered th,
.table-bordered td {
    border: 1px solid var(--border-color);
}

/* ==================== 卡片樣式 ==================== */
.card {
    background-color: white;
    border: 1px solid var(--border-color);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-sm);
    overflow: hidden;
}

.card-header {
    padding: var(--spacing-md) var(--spacing-lg);
    background-color: var(--bg-light);
    border-bottom: 1px solid var(--border-color);
    font-weight: 600;
    color: var(--secondary-color);
}

.card-body {
    padding: var(--spacing-lg);
}

.card-footer {
    padding: var(--spacing-md) var(--spacing-lg);
    background-color: var(--bg-light);
    border-top: 1px solid var(--border-color);
}

.card-title {
    margin: 0 0 var(--spacing-sm) 0;
    font-size: var(--font-size-lg);
    font-weight: 600;
    color: var(--secondary-color);
}

.card-text {
    margin-bottom: var(--spacing-md);
    color: var(--text-muted);
}

/* ==================== 警告框樣式 ==================== */
.alert {
    padding: var(--spacing-md);
    margin-bottom: var(--spacing-md);
    border: 1px solid transparent;
    border-radius: var(--radius-md);
    position: relative;
}

.alert-success {
    color: #155724;
    background-color: #d4edda;
    border-color: #c3e6cb;
}

.alert-info {
    color: #0c5460;
    background-color: #d1ecf1;
    border-color: #bee5eb;
}

.alert-warning {
    color: #856404;
    background-color: #fff3cd;
    border-color: #ffeaa7;
}

.alert-danger {
    color: #721c24;
    background-color: #f8d7da;
    border-color: #f5c6cb;
}

.alert-dismissible {
    padding-right: calc(var(--spacing-lg) + 20px);
}

.alert-dismissible .btn-close {
    position: absolute;
    top: 0;
    right: 0;
    padding: var(--spacing-md);
}

/* ==================== 徽章樣式 ==================== */
.badge {
    display: inline-block;
    padding: 0.25em 0.6em;
    font-size: var(--font-size-xs);
    font-weight: 600;
    line-height: 1;
    text-align: center;
    white-space: nowrap;
    vertical-align: baseline;
    border-radius: var(--radius-xs);
}

.badge-primary {
    color: white;
    background-color: var(--primary-color);
}

.badge-secondary {
    color: white;
    background-color: var(--text-muted);
}

.badge-success {
    color: white;
    background-color: var(--success-color);
}

.badge-danger {
    color: white;
    background-color: var(--error-color);
}

.badge-warning {
    color: var(--secondary-color);
    background-color: var(--warning-color);
}

.badge-info {
    color: white;
    background-color: var(--info-color);
}

/* ==================== 載入動畫 ==================== */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: var(--z-modal);
}

.loading-content {
    background: white;
    padding: var(--spacing-lg);
    border-radius: var(--radius-lg);
    text-align: center;
    box-shadow: var(--shadow-lg);
}

.loading-spinner {
    display: inline-block;
    position: relative;
    width: 40px;
    height: 40px;
    margin-bottom: var(--spacing-md);
}

.loading-spinner.small {
    width: 20px;
    height: 20px;
    margin-bottom: var(--spacing-xxs);
}

.loading-spinner.large {
    width: 60px;
    height: 60px;
    margin-bottom: var(--spacing-lg);
}

.spinner-ring {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    border: 3px solid transparent;
    border-top-color: var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

.spinner-ring:nth-child(2) {
    animation-delay: 0.1s;
    border-top-color: var(--secondary-color);
}

.spinner-ring:nth-child(3) {
    animation-delay: 0.2s;
    border-top-color: var(--info-color);
}

.spinner-ring:nth-child(4) {
    animation-delay: 0.3s;
    border-top-color: var(--success-color);
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* ==================== 工具類別 ==================== */
.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }
.text-justify { text-align: justify; }

.text-muted { color: var(--text-muted); }
.text-primary { color: var(--primary-color); }
.text-secondary { color: var(--secondary-color); }
.text-success { color: var(--success-color); }
.text-danger { color: var(--error-color); }
.text-warning { color: var(--warning-color); }
.text-info { color: var(--info-color); }

.bg-primary { background-color: var(--primary-color); }
.bg-secondary { background-color: var(--secondary-color); }
.bg-success { background-color: var(--success-color); }
.bg-danger { background-color: var(--error-color); }
.bg-warning { background-color: var(--warning-color); }
.bg-info { background-color: var(--info-color); }
.bg-light { background-color: var(--bg-light); }

.d-none { display: none; }
.d-block { display: block; }
.d-inline { display: inline; }
.d-inline-block { display: inline-block; }
.d-flex { display: flex; }
.d-grid { display: grid; }

.flex-row { flex-direction: row; }
.flex-column { flex-direction: column; }
.flex-wrap { flex-wrap: wrap; }
.flex-nowrap { flex-wrap: nowrap; }

.justify-content-start { justify-content: flex-start; }
.justify-content-end { justify-content: flex-end; }
.justify-content-center { justify-content: center; }
.justify-content-between { justify-content: space-between; }
.justify-content-around { justify-content: space-around; }

.align-items-start { align-items: flex-start; }
.align-items-end { align-items: flex-end; }
.align-items-center { align-items: center; }
.align-items-baseline { align-items: baseline; }
.align-items-stretch { align-items: stretch; }

.w-100 { width: 100%; }
.h-100 { height: 100%; }

.m-0 { margin: 0; }
.m-1 { margin: var(--spacing-xxs); }
.m-2 { margin: var(--spacing-xs); }
.m-3 { margin: var(--spacing-sm); }
.m-4 { margin: var(--spacing-md); }
.m-5 { margin: var(--spacing-lg); }

.p-0 { padding: 0; }
.p-1 { padding: var(--spacing-xxs); }
.p-2 { padding: var(--spacing-xs); }
.p-3 { padding: var(--spacing-sm); }
.p-4 { padding: var(--spacing-md); }
.p-5 { padding: var(--spacing-lg); }

.mt-0 { margin-top: 0; }
.mt-1 { margin-top: var(--spacing-xxs); }
.mt-2 { margin-top: var(--spacing-xs); }
.mt-3 { margin-top: var(--spacing-sm); }
.mt-4 { margin-top: var(--spacing-md); }
.mt-5 { margin-top: var(--spacing-lg); }

.mb-0 { margin-bottom: 0; }
.mb-1 { margin-bottom: var(--spacing-xxs); }
.mb-2 { margin-bottom: var(--spacing-xs); }
.mb-3 { margin-bottom: var(--spacing-sm); }
.mb-4 { margin-bottom: var(--spacing-md); }
.mb-5 { margin-bottom: var(--spacing-lg); }

.pt-0 { padding-top: 0; }
.pt-1 { padding-top: var(--spacing-xxs); }
.pt-2 { padding-top: var(--spacing-xs); }
.pt-3 { padding-top: var(--spacing-sm); }
.pt-4 { padding-top: var(--spacing-md); }
.pt-5 { padding-top: var(--spacing-lg); }

.pb-0 { padding-bottom: 0; }
.pb-1 { padding-bottom: var(--spacing-xxs); }
.pb-2 { padding-bottom: var(--spacing-xs); }
.pb-3 { padding-bottom: var(--spacing-sm); }
.pb-4 { padding-bottom: var(--spacing-md); }
.pb-5 { padding-bottom: var(--spacing-lg); }

.hidden { display: none; }
.visible { display: block; }

/* ==================== 動畫類別 ==================== */
.fade-in {
    animation: fadeIn 0.3s ease-in-out;
}

.fade-out {
    animation: fadeOut 0.3s ease-in-out;
}

.slide-in-up {
    animation: slideInUp 0.3s ease-out;
}

.slide-in-down {
    animation: slideInDown 0.3s ease-out;
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes fadeOut {
    from { opacity: 1; }
    to { opacity: 0; }
}

@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideInDown {
    from {
        opacity: 0;
        transform: translateY(-20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}