/**
 * 資料庫管理器擴展功能
 * 包含全選、進階搜尋、批次操作等功能
 */

class DatabaseManagerExtensions {
    constructor(databaseManager) {
        this.dbManager = databaseManager;
        this.initExtensions();
    }

    /**
     * 初始化擴展功能
     */
    initExtensions() {
        this.bindExtensionEvents();
    }

    /**
     * 綁定擴展事件
     */
    bindExtensionEvents() {
        // 全選搜尋結果
        $('#select-all-search-btn').on('click', () => {
            this.selectAllSearchResults();
        });
    }

    /**
     * 全選搜尋結果
     */
    selectAllSearchResults() {
        // 獲取當前可見（搜尋結果中）的行
        const visibleRows = this.dbManager.dataTable ? 
            this.dbManager.dataTable.rows({ search: 'applied' }).nodes() : 
            $('.row-select').parent().parent();
            
        const visibleCheckboxes = $(visibleRows).find('.row-select');
        const allChecked = visibleCheckboxes.length > 0 && visibleCheckboxes.filter(':checked').length === visibleCheckboxes.length;
        
        // 切換選中狀態
        visibleCheckboxes.prop('checked', !allChecked);
        
        // 更新按鈕文字
        const btn = $('#select-all-search-btn');
        if (!allChecked) {
            btn.html('☑️ 取消全選');
            this.dbManager.showBatchActionsPanel(visibleCheckboxes.length);
        } else {
            btn.html('☑️ 全選搜尋結果');
            this.dbManager.hideBatchActionsPanel();
        }
    }
    
    /**
     * 重置進階篩選（保留空方法以維持相容性）
     */
    resetAdvancedFilters() {
        this.clearAllFilters();
    }

    /**
     * 清除所有篩選（整合到主搜尋功能中）
     */
    clearAllFilters() {
        if (this.dbManager.dataTable) {
            this.dbManager.dataTable.search('').draw();
        }
        
        // 清除搜尋輸入框
        $('#search-input').val('');
        $('#search-results-info').addClass('hidden');
        
        // 重置全選按鈕狀態
        $('#select-all-search-btn').html('☑️ 全選搜尋結果');
        this.dbManager.hideBatchActionsPanel();
    }
}