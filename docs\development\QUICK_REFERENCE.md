# 🚀 快速參考卡片

## ⚡ 一鍵啟動

```powershell
# 設定環境並啟動應用程式
. .\dev_env.ps1
python frontend/app.py
```

## 🔧 常用命令

| 動作 | 命令 |
|------|------|
| 🚀 啟動前端 | `python frontend/app.py` |
| 🔧 啟動後端 | `python start_integrated_services.py` |
| 🧪 執行測試 | `pytest` 或 `make test` |
| 🎨 格式化程式碼 | `make format` |
| 🔍 品質檢查 | `make quality-check` |
| 📋 查看命令 | `make help` |

## 🌐 模組路由

| 模組 | URL | 功能 |
|------|-----|------|
| 📧 Email | `/email/` | 郵件管理 |
| 📊 Analytics | `/analytics/` | 統計分析 |
| 📁 Files | `/files/` | 檔案管理 |
| 🔍 EQC | `/eqc/` | 品質控制 |
| ⚡ Tasks | `/tasks/` | 任務管理 |
| 📈 Monitoring | `/monitoring/` | 系統監控 |

## 🛠️ 故障排除

| 問題 | 解決方案 |
|------|----------|
| 虛擬環境啟動失敗 | `Set-ExecutionPolicy RemoteSigned -Scope CurrentUser` |
| 中文亂碼 | 已自動設定 UTF-8，重新啟動 dev_env.ps1 |
| Flask 無法啟動 | 檢查 `make check-structure` |
| 靜態資源 404 | 驗證 `make validate-modules` |

## 📁 重要檔案

- `dev_env.ps1` - 開發環境啟動腳本
- `Makefile` - 開發命令集合
- `frontend/app.py` - Flask 主應用程式
- `requirements.txt` - Python 依賴套件

## 🔄 開發流程

1. `. .\dev_env.ps1` - 啟動環境
2. `make check-structure` - 檢查結構
3. `python frontend/app.py` - 啟動應用程式
4. 開發程式碼
5. `make test` - 執行測試
6. `make quality-check` - 品質檢查
7. Git 提交

---
💡 **提示**: 將此檔案加入書籤，隨時查閱！