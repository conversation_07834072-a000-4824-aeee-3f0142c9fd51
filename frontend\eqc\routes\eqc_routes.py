# -*- coding: utf-8 -*-
"""
EQC 模組路由
處理所有 EQC 相關的路由和 API 端點
"""

from flask import Blueprint, render_template, jsonify, request, redirect
import sys
from pathlib import Path

# 添加專案根目錄到 Python 路徑
project_root = Path(__file__).parent.parent.parent.parent
sys.path.insert(0, str(project_root))

from src.infrastructure.logging.logger_manager import LoggerManager

# 創建藍圖
eqc_bp = Blueprint('eqc', __name__,
                   template_folder='../templates',
                   static_folder='../static',
                   static_url_path='/static/eqc')

# 初始化日誌
logger = LoggerManager().get_logger("EQCRoutes")


@eqc_bp.route('/')
@eqc_bp.route('/dashboard')
def dashboard():
    """EQC 儀表板主頁"""
    try:
        return render_template('eqc_dashboard.html')
    except Exception as e:
        logger.error(f"載入 EQC 儀表板失敗: {e}")
        return jsonify({'error': '載入頁面失敗'}), 500


@eqc_bp.route('/quality-check')
def quality_check():
    """品質檢查頁面"""
    try:
        # 提供模板所需的統計數據
        stats = {
            'today_checks': 0,
            'pass_rate': 0.0,
            'pending_checks': 0
        }
        
        # 提供檢查結果數據
        results = {
            'pass_count': 0,
            'warning_count': 0,
            'fail_count': 0
        }
        
        # 提供檢查結果列表和歷史記錄
        check_results = []
        check_history = []
        
        return render_template('quality_check.html', 
                             stats=stats, 
                             results=results, 
                             check_results=check_results, 
                             check_history=check_history)
    except Exception as e:
        logger.error(f"載入品質檢查頁面失敗: {e}")
        return jsonify({'error': '載入頁面失敗'}), 500


@eqc_bp.route('/compliance')
def compliance():
    """合規檢查頁面"""
    try:
        # 提供模板所需的統計數據
        stats = {
            'compliance_rate': 0.0,
            'active_policies': 0,
            'violations': 0,
            'risk_level': 'LOW',
            'risk_percentage': 0
        }
        
        # 提供政策數據
        policies = {
            'data_protection': []
        }
        
        # 提供風險因子數據
        risk_factors = []
        
        # 提供合規檢查結果摘要
        summary = {
            'critical_violations': 0,
            'high_violations': 0,
            'medium_violations': 0,
            'low_violations': 0
        }
        
        # 提供合規檢查結果列表
        compliance_results = []
        
        # 提供修復計畫統計和列表
        remediation_stats = {
            'total_plans': 0,
            'in_progress': 0,
            'completed': 0,
            'overdue': 0
        }
        
        remediation_plans = []
        
        return render_template('compliance.html', 
                             stats=stats,
                             policies=policies,
                             risk_factors=risk_factors,
                             summary=summary,
                             compliance_results=compliance_results,
                             remediation_stats=remediation_stats,
                             remediation_plans=remediation_plans)
    except Exception as e:
        logger.error(f"載入合規檢查頁面失敗: {e}")
        return jsonify({'error': '載入頁面失敗'}), 500


# FastAPI 服務整合路由
@eqc_bp.route('/ui')
def eqc_ui():
    """EQC 用戶界面 - 重定向到 FastAPI 服務"""
    try:
        # 直接重定向到 FastAPI 服務
        return redirect("http://localhost:8010/ui")
    except Exception as e:
        logger.error(f"EQC UI 重定向失敗: {e}")
        # 回退到本地儀表板
        return render_template('eqc_dashboard.html',
                             title='EQC Dashboard',
                             error_message="EQC UI 服務重定向失敗，顯示本地儀表板")


@eqc_bp.route('/ft-eqc')
def ft_eqc_redirect():
    """重定向到 FT-EQC 處理介面"""
    try:
        # 直接重定向到 FastAPI 服務
        return redirect("http://localhost:8010/ui")
    except Exception as e:
        logger.error(f"FT-EQC 重定向失敗: {e}")
        return render_template('eqc_dashboard.html',
                             error_message="FT-EQC 服務重定向失敗，請稍後再試")


@eqc_bp.route('/ft-summary')
def ft_summary_redirect():
    """重定向到 FT QC Summary UI 介面"""
    try:
        # 重定向到 Flask 前端的 FT QC Summary UI
        return redirect("/analytics/dashboard")
    except Exception as e:
        logger.error(f"FT QC Summary 重定向失敗: {e}")
        return render_template('eqc_dashboard.html',
                             error_message="FT QC Summary 服務重定向失敗，請稍後再試")


@eqc_bp.route('/ft-eqc-api')
def ft_eqc_api_redirect():
    """重定向到 FT-EQC API 文檔"""
    try:
        # 直接重定向到 FastAPI 服務
        return redirect("http://localhost:8010/docs")
    except Exception as e:
        logger.error(f"FT-EQC API 重定向失敗: {e}")
        return render_template('eqc_dashboard.html',
                             error_message="FT-EQC API 文檔重定向失敗，請稍後再試")


# API 路由
@eqc_bp.route('/api/metrics')
def api_metrics():
    """獲取 EQC 關鍵指標 API"""
    try:
        # TODO: 實作 EQC 指標邏輯
        return jsonify({
            'status': 'success',
            'data': {
                'total_tests': 0,
                'passed_tests': 0,
                'failed_tests': 0,
                'pass_rate': 0.0
            }
        })
    except Exception as e:
        logger.error(f"獲取 EQC 指標失敗: {e}")
        return jsonify({'status': 'error', 'message': str(e)}), 500


@eqc_bp.route('/api/quality/run', methods=['POST'])
def api_run_quality_check():
    """執行品質檢查 API"""
    try:
        # TODO: 實作品質檢查邏輯
        return jsonify({
            'status': 'success',
            'message': '品質檢查已啟動',
            'job_id': 'placeholder_job_id'
        })
    except Exception as e:
        logger.error(f"執行品質檢查失敗: {e}")
        return jsonify({'status': 'error', 'message': str(e)}), 500


@eqc_bp.route('/api/compliance/validate', methods=['POST'])
def api_validate_compliance():
    """執行合規驗證 API"""
    try:
        # TODO: 實作合規驗證邏輯
        return jsonify({
            'status': 'success',
            'message': '合規驗證已啟動',
            'job_id': 'placeholder_job_id'
        })
    except Exception as e:
        logger.error(f"執行合規驗證失敗: {e}")
        return jsonify({'status': 'error', 'message': str(e)}), 500