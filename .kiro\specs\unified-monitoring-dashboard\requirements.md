# 需求文件

## 簡介

本文件概述了統一監控儀表板的需求，該系統提供半導體郵件處理基礎設施中所有任務處理系統的全面即時監控。系統採用**前後端分離但整合**的架構，將後端監控系統（`src/dashboard_monitoring/`）與前端展示系統（`frontend/monitoring/`）完美結合，提供統一的監控視圖和智能告警功能。

### 🏗️ 系統架構概述

#### 🎯 **後端監控系統** (`src/dashboard_monitoring/`)
- **FastAPI + WebSocket** - 提供監控 API 和即時資料推送
- **資料收集器** - 收集郵件、Dramatiq、系統、檔案、Pipeline、廠商文件等監控資料
- **告警系統** - 智能告警和通知機制
- **趨勢分析** - 歷史資料分析和預測

#### 🎨 **前端展示系統** (`frontend/monitoring/`)
- **Flask 模組化架構** - 6個功能模組的統一前端框架
- **響應式監控介面** - HTML5 + JavaScript + WebSocket
- **共享組件系統** - 統一的 UI 組件和樣式
- **Vue.js 遷移準備** - 完整的模組化架構

#### 🔄 **資料流整合**
```
前端監控頁面 (Flask Templates) 
    ↕ WebSocket/REST API
後端監控系統 (FastAPI)
    ↕ 資料收集
外部系統 (Email/Dramatiq/System/Pipeline/VendorFiles)
```

**資料儲存策略：** 系統將擴展現有的outlook.db SQLite資料庫，新增監控相關表格來儲存監控歷史資料、告警記錄和趨勢分析資料，確保系統重啟後能保持歷史資訊的連續性，同時避免管理多個資料庫檔案。

## 需求

### 需求 1

**使用者故事：** 作為系統管理員，我希望即時監控郵件處理佇列，以便追蹤自動郵件下載和code_comparison.py處理任務。

#### 驗收標準

1. 當收到郵件並排入處理佇列時，系統應在待處理郵件處理佇列中顯示該郵件
2. 當為郵件啟動code_comparison.py處理時，系統應增加活躍郵件處理計數器
3. 當郵件處理成功完成時，系統應減少活躍計數器並更新已完成任務計數器
4. 當郵件處理失敗時，系統應將任務移至失敗佇列並顯示錯誤詳情
5. 如果郵件處理佇列超過10個待處理項目，系統應顯示警告指示器

### 需求 2

**使用者故事：** 作為系統管理員，我希望監控網路UI操作的Dramatiq任務佇列，以便追蹤code_comparison.py、csv_to_summary.py、壓縮檔、解壓縮、郵件處理、資料分析、檔案處理和批次處理等任務的狀態。

#### 驗收標準

1. 當Dramatiq任務排入code_comparison.py處理佇列時，系統應在程式碼比較任務佇列區段中顯示它
2. 當Dramatiq任務排入csv_to_summary.py處理佇列時，系統應在CSV摘要任務佇列區段中顯示它
3. 當Dramatiq任務排入壓縮檔操作佇列時，系統應在壓縮任務佇列區段中顯示它
4. 當Dramatiq任務排入解壓縮操作佇列時，系統應在解壓縮任務佇列區段中顯示它
5. 當Dramatiq任務排入郵件處理佇列時，系統應在郵件處理任務區段中顯示它
6. 當Dramatiq任務排入資料分析佇列時，系統應在資料分析任務區段中顯示它
7. 當Dramatiq任務排入檔案處理佇列時，系統應在檔案處理任務區段中顯示它
8. 當Dramatiq任務排入批次處理佇列時，系統應在批次處理任務區段中顯示它
9. 當任何Dramatiq任務開始執行時，系統應將其從待處理狀態移至活躍狀態並顯示執行進度
10. 當任何Dramatiq任務完成時，系統應更新完成計數器並從活躍狀態中移除
11. 當Dramatiq任務失敗時，系統應將任務移至失敗佇列並顯示錯誤詳情和重試次數
12. 如果任何Dramatiq任務佇列超過15個待處理項目，系統應顯示警告指示器
13. 當Dramatiq工作者離線或無回應時，系統應顯示工作者狀態警告
14. 當長時間任務（壓縮、解壓縮、csv_to_summary.py）執行超過預期時間時，系統應顯示執行時間警告

### 需求 3

**使用者故事：** 作為系統管理員，我希望有一個與現有排程器儀表板整合的統一儀表板介面，以便從單一位置存取所有監控功能。

#### 驗收標準

1. 當存取監控儀表板時，系統應整合現有排程器儀表板功能（http://localhost:5555/scheduler）
2. 當存取監控儀表板時，系統應整合現有網路瀏覽器UI功能
3. 當檢視儀表板時，系統應在有組織的區段中顯示所有佇列統計資料，並有清晰的視覺分隔
4. 當儀表板載入時，系統應顯示當前時間戳和最後重新整理時間
5. 如果儀表板無法從任何受監控服務載入資料，系統應為該區段顯示服務不可用指示器

### 需求 4

**使用者故事：** 作為系統管理員，我希望對關鍵系統事件有即時更新和通知，以便快速回應問題。

#### 驗收標準

1. 當佇列計數變更時，系統應在5秒內更新顯示，無需重新整理頁面
2. 當任務失敗時，系統應顯示包含任務詳情和錯誤資訊的通知
3. 當系統資源超過閾值限制時，系統應顯示資源使用警告
4. 當任何受監控服務變為不可用時，系統應顯示服務狀態警報
5. 如果多個嚴重警報處於活躍狀態，系統應按嚴重程度等級排列優先順序

### 需求 5

**使用者故事：** 作為系統管理員，我希望有詳細的任務資訊和篩選功能，以便調查特定問題和追蹤任務效能。

#### 驗收標準

1. 當點擊任務佇列區段時，系統應顯示詳細任務資訊，包括任務ID、開始時間和參數
2. 當檢視任務詳情時，系統應提供按任務類型、狀態和時間範圍的篩選選項
3. 當選擇任務時，系統應顯示其完整執行日誌和任何錯誤訊息
4. 當檢視歷史資料時，系統應提供顯示任務完成趨勢的圖表
5. 如果任務執行時間超過正常閾值，系統應突出顯示執行緩慢的任務

### 需求 6

**使用者故事：** 作為系統管理員，我希望有系統健康監控和效能指標，以確保最佳系統運作。

#### 驗收標準

1. 當儀表板載入時，系統應顯示當前CPU、記憶體和磁碟使用指標
2. 當監控Dramatiq工作者時，系統應顯示工作者狀態、活躍任務和工作者健康狀況
3. 當監控郵件服務時，系統應顯示連線狀態和最後成功郵件檢查時間
4. 當系統效能下降時，系統應顯示效能警告和建議操作
5. 如果任何關鍵系統元件失敗，系統應顯示緊急警報和升級程序

### 需求 7

**使用者故事：** 作為系統管理員，我希望監控郵件服務和資料庫連線狀態，以確保郵件處理系統的穩定運作。

#### 驗收標準

1. 當監控POP3/Outlook郵件連線時，系統應顯示連線狀態、最後成功連線時間和失敗次數
2. 當監控SQLite/PostgreSQL資料庫時，系統應顯示連線池狀態、查詢回應時間和鎖定情況
3. 當郵件服務連線失敗時，系統應顯示錯誤詳情和重連嘗試狀態
4. 當資料庫查詢超過5秒時，系統應顯示慢查詢警告
5. 如果郵件服務超過10分鐘無法連線，系統應發送緊急通知

### 需求 8

**使用者故事：** 作為系統管理員，我希望監控檔案處理和儲存狀態，以追蹤附件下載、解析和儲存的完整流程。

#### 驗收標準

1. 當監控附件下載時，系統應顯示下載佇列、成功率和失敗的檔案清單
2. 當監控檔案解析時，系統應顯示各廠商解析器（ETD、GTK、JCET等）的處理狀態和成功率
3. 當監控檔案儲存時，系統應顯示磁碟空間使用率、檔案數量和儲存路徑狀態
4. 當檔案解析失敗時，系統應顯示失敗原因、檔案類型和建議的處理方式
5. 如果磁碟空間使用率超過85%，系統應顯示儲存空間警告

### 需求 9

**使用者故事：** 作為系統管理員，我希望監控業務指標和資料品質，以確保半導體測試資料的準確性和完整性。

#### 驗收標準

1. 當處理MO（製造訂單）資料時，系統應顯示每日處理的MO數量、重複MO和異常MO統計
2. 當處理LOT（批次）資料時，系統應顯示LOT處理狀態、良率統計和異常LOT清單
3. 當監控資料品質時，系統應顯示缺失欄位、格式錯誤和資料驗證失敗的統計
4. 當生成Excel報告時，系統應顯示報告生成狀態、檔案大小和生成時間
5. 如果發現重複或衝突的MO/LOT資料，系統應顯示資料衝突警告和建議處理方式

### 需求 10

**使用者故事：** 作為系統管理員，我希望有歷史趨勢分析和預警功能，以預測系統負載和潛在問題。

#### 驗收標準

1. 當分析歷史資料時，系統應顯示過去7天、30天的任務處理趨勢圖表
2. 當預測系統負載時，系統應根據歷史模式預測未來24小時的任務量
3. 當檢測異常模式時，系統應識別處理時間異常增長或失敗率突然上升的情況
4. 當系統負載接近容量上限時，系統應提前發出容量警告
5. 如果檢測到重複的系統問題模式，系統應提供根本原因分析建議

### 需求 11

**使用者故事：** 作為開發者，我希望有監控資料的API端點和整合功能，以便將監控功能整合到其他工具和自動化腳本中。

#### 驗收標準

1. 當透過API請求佇列統計資料時，系統應返回包含所有受監控服務當前佇列計數的JSON資料
2. 當透過API請求任務詳情時，系統應返回詳細任務資訊，包括狀態、時間和元資料
3. 當透過API請求系統健康狀況時，系統應返回當前資源使用和服務狀態資訊
4. 當API請求超過速率限制時，系統應返回適當的HTTP狀態碼和重試資訊
5. 如果需要API認證，系統應驗證憑證並返回適當的授權回應

### 需求 12

**使用者故事：** 作為系統管理員，我希望有告警和通知機制，以便及時回應系統問題和異常情況。

#### 驗收標準

1. 當系統檢測到嚴重錯誤時，系統應透過多種管道（郵件、LINE、系統通知）發送警報
2. 當任務失敗率超過10%時，系統應自動發送失敗率警報給相關人員
3. 當系統資源使用率超過90%時，系統應發送資源警報並建議優化措施
4. 當關鍵服務停止回應時，系統應立即發送服務中斷通知
5. 如果連續發生相同類型的錯誤，系統應合併警報避免通知氾濫

### 需求 13 ✅ Task 9 監控協調器已實現核心功能

**使用者故事：** 作為系統架構師，我希望監控儀表板能夠無縫整合到現有的 start_integrated_services.py 主程式中，以便統一管理所有服務。

#### 驗收標準

1. ✅ 當主程式啟動時，系統應自動初始化監控儀表板服務而不影響其他服務 - **Task 9 已實現監控協調器的完整生命週期管理**
2. ✅ 當監控儀表板初始化失敗時，系統應記錄錯誤但不阻止其他服務正常啟動 - **Task 9 已實現錯誤隔離機制**
3. 當存取管理後台時，系統應在現有的管理介面中顯示監控儀表板狀態和連結
4. ✅ 當主程式關閉時，系統應優雅地關閉監控儀表板服務並清理資源 - **Task 9 已實現優雅關閉機制**
5. ✅ 如果監控儀表板服務異常，系統應提供獨立的重啟機制而不影響其他服務 - **Task 9 已實現服務隔離和健康檢查**

**Task 9 實現狀態:** 監控協調器核心功能已完成，包括系統啟動/停止、定期收集任務、WebSocket 連接管理和指標收集廣播機制。

### 需求 14

**使用者故事：** 作為開發者，我希望監控儀表板的程式碼結構清晰且易於維護，以便後續的功能擴展和問題排查。

#### 驗收標準

1. 當檢視程式碼結構時，系統應將所有監控相關功能組織在 src/dashboard_monitoring/ 目錄下
2. 當檢視任何單一程式檔案時，系統應確保每個檔案不超過500行程式碼
3. 當需要擴展新的監控功能時，系統應提供清晰的模組介面和擴展點
4. 當發生錯誤時，系統應提供詳細的日誌記錄和錯誤追蹤資訊
5. 如果需要修改監控邏輯，系統應確保修改不會影響其他模組的正常運作

### 需求 15 🆕

**使用者故事：** 作為系統管理員，我希望監控 Pipeline 系統的執行狀態，以便追蹤管道任務的進度和效能。

#### 驗收標準

1. 當 Pipeline 管道建立時，系統應在管道監控區段中顯示新管道的狀態
2. 當管道開始執行時，系統應顯示管道狀態為 running 並追蹤任務進度
3. 當管道內任務完成時，系統應更新管道進度百分比和完成任務計數
4. 當管道執行失敗時，系統應顯示失敗原因和受影響的任務詳情
5. 當管道成功完成時，系統應更新完成統計並記錄執行時間
6. 如果活躍管道數量超過50個，系統應顯示管道積壓警告
7. 當管道失敗率超過15%時，系統應發送管道系統健康警報
8. 當 Redis 連接失敗時，系統應顯示 Pipeline 系統離線狀態

### 需求 16 🆕

**使用者故事：** 作為系統管理員，我希望監控廠商文件處理系統，以便追蹤各廠商文件的下載和處理狀態。

#### 驗收標準

1. 當開始追蹤廠商文件時，系統應在廠商文件監控區段中顯示文件狀態
2. 當文件下載進行中時，系統應顯示下載進度、速度和預估完成時間
3. 當文件處理完成時，系統應更新廠商統計並記錄處理時間
4. 當文件處理失敗時，系統應顯示錯誤類型（網路錯誤、IO錯誤、超時）和重試次數
5. 當檢視廠商統計時，系統應顯示各廠商的成功率、平均處理時間和效能排名
6. 如果文件處理失敗率超過15%，系統應發送廠商文件系統警報
7. 當同時追蹤的文件數量超過100個時，系統應顯示系統負載警告
8. 當特定廠商效能分數低於60分時，系統應發送廠商效能警報

### 需求 17 🆕

**使用者故事：** 作為系統管理員，我希望有整合的監控視圖，以便從單一介面查看所有子系統的健康狀態。

#### 驗收標準

1. 當存取整合監控儀表板時，系統應顯示郵件、Dramatiq、Pipeline、廠商文件系統的統一健康狀態
2. 當任何子系統狀態變更時，系統應更新整體系統健康分數
3. 當檢視系統摘要時，系統應顯示所有子系統的關鍵指標匯總
4. 當發生跨系統問題時，系統應識別並顯示相關聯的系統影響
5. 當系統整體健康狀態為嚴重時，系統應發送緊急通知給所有相關人員