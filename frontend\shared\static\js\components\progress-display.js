/**
 * 進度顯示組件模組
 * 處理進度條、狀態更新、動畫效果和視覺反饋
 */

class ProgressDisplayComponent {
    constructor() {
        this.currentStep = 0;
        this.totalSteps = 3;
        this.isAnimating = false;
        
        this.elements = {
            progressStep: null,
            progressDetail: null,
            progressBarContainer: null,
            progressFill: null,
            progressText: null,
            processingIndicator: null
        };
        
        this.stepTemplates = {
            1: { title: 'Step 1/3', description: '執行程式碼區間檢測與雙重搜尋...' },
            2: { title: 'Step 2/3', description: '生成程式碼對比報告...' },
            3: { title: 'Step 3/3', description: '處理完成，生成報告...' }
        };
    }
    
    /**
     * 初始化進度顯示組件
     */
    init() {
        console.log('📊 初始化進度顯示組件...');
        
        // 獲取 DOM 元素
        this.elements.progressStep = DOMManager.get('progressStep');
        this.elements.progressDetail = DOMManager.get('progressDetail');
        this.elements.progressBarContainer = DOMManager.get('progressBarContainer');
        this.elements.progressFill = DOMManager.get('progressFill');
        this.elements.progressText = DOMManager.get('progressText');
        this.elements.processingIndicator = DOMManager.get('processingIndicator');
        
        // 初始化狀態
        this.reset();
        
        console.log('✅ 進度顯示組件初始化完成');
    }
    
    /**
     * 重置進度顯示
     */
    reset() {
        this.currentStep = 0;
        this.isAnimating = false;
        
        this.updateProgress('等待開始...', '點擊「一鍵完成到程式碼對比」開始處理', 0, 'info');
        this.hideProgressBar();
        this.hideProcessingIndicator();
    }
    
    /**
     * 更新進度
     * @param {string} step - 步驟描述
     * @param {string} message - 詳細訊息
     * @param {number|null} percentage - 進度百分比
     * @param {string} status - 狀態類型
     */
    updateProgress(step, message, percentage = null, status = 'processing') {
        const icons = {
            processing: 'fas fa-spinner fa-spin',
            success: 'fas fa-check-circle',
            error: 'fas fa-times-circle',
            warning: 'fas fa-exclamation-triangle',
            info: 'fas fa-info-circle'
        };
        
        // 更新步驟顯示
        if (this.elements.progressStep) {
            this.elements.progressStep.className = `progress-step ${status}`;
            this.elements.progressStep.innerHTML = `<i class="${icons[status]}"></i> ${step}`;
        }
        
        // 更新詳細訊息
        if (this.elements.progressDetail) {
            this.elements.progressDetail.textContent = message;
        }
        
        // 更新進度條
        if (percentage !== null) {
            this.updateProgressBar(percentage);
        }
        
        console.log(`📊 進度更新: ${step} - ${message} (${percentage}%)`);
    }
    
    /**
     * 更新進度條
     * @param {number} percentage - 進度百分比
     */
    updateProgressBar(percentage) {
        if (!this.elements.progressBarContainer) return;
        
        // 顯示進度條容器
        this.showProgressBar();
        
        // 動畫更新進度條
        if (this.elements.progressFill) {
            this.animateProgressFill(percentage);
        }
        
        // 更新進度文字
        if (this.elements.progressText) {
            this.elements.progressText.textContent = `${percentage}%`;
        }
    }
    
    /**
     * 動畫更新進度條填充
     * @param {number} targetPercentage - 目標百分比
     */
    animateProgressFill(targetPercentage) {
        if (this.isAnimating) return;
        
        this.isAnimating = true;
        const progressFill = this.elements.progressFill;
        const currentWidth = parseFloat(progressFill.style.width) || 0;
        const targetWidth = Math.min(100, Math.max(0, targetPercentage));
        
        // 使用 CSS 過渡動畫
        progressFill.style.transition = 'width 0.5s ease-out';
        progressFill.style.width = `${targetWidth}%`;
        
        // 動畫完成後重置標記
        setTimeout(() => {
            this.isAnimating = false;
        }, 500);
    }
    
    /**
     * 顯示進度條
     */
    showProgressBar() {
        if (this.elements.progressBarContainer) {
            this.elements.progressBarContainer.style.display = 'block';
        }
    }
    
    /**
     * 隱藏進度條
     */
    hideProgressBar() {
        if (this.elements.progressBarContainer) {
            this.elements.progressBarContainer.style.display = 'none';
        }
    }
    
    /**
     * 顯示處理指示器
     */
    showProcessingIndicator() {
        if (this.elements.processingIndicator) {
            this.elements.processingIndicator.style.display = 'block';
        }
    }
    
    /**
     * 隱藏處理指示器
     */
    hideProcessingIndicator() {
        if (this.elements.processingIndicator) {
            this.elements.processingIndicator.style.display = 'none';
        }
    }
    
    /**
     * 開始處理流程
     */
    startProcessing() {
        this.currentStep = 1;
        this.showProcessingIndicator();
        
        const template = this.stepTemplates[1];
        this.updateProgress(template.title, template.description, 33, 'processing');
    }
    
    /**
     * 進入下一步
     */
    nextStep() {
        if (this.currentStep < this.totalSteps) {
            this.currentStep++;
            const template = this.stepTemplates[this.currentStep];
            const percentage = (this.currentStep / this.totalSteps) * 100;
            
            this.updateProgress(template.title, template.description, percentage, 'processing');
        }
    }
    
    /**
     * 完成處理
     * @param {string} message - 完成訊息
     */
    completeProcessing(message = '處理完成！') {
        this.currentStep = this.totalSteps;
        this.updateProgress('完成', message, 100, 'success');
        this.hideProcessingIndicator();
        
        // 顯示成功動畫效果
        this.showSuccessAnimation();
    }
    
    /**
     * 處理失敗
     * @param {string} message - 錯誤訊息
     */
    failProcessing(message = '處理失敗') {
        this.updateProgress('錯誤', message, null, 'error');
        this.hideProcessingIndicator();
        
        // 顯示錯誤動畫效果
        this.showErrorAnimation();
    }
    
    /**
     * 顯示成功動畫效果
     */
    showSuccessAnimation() {
        if (this.elements.progressFill) {
            // 添加成功動畫類
            this.elements.progressFill.style.background = 'linear-gradient(90deg, #28a745 0%, #20c997 100%)';
            
            // 短暫的脈衝效果
            this.elements.progressFill.style.animation = 'pulse 0.6s ease-in-out';
            
            setTimeout(() => {
                if (this.elements.progressFill) {
                    this.elements.progressFill.style.animation = '';
                }
            }, 600);
        }
    }
    
    /**
     * 顯示錯誤動畫效果
     */
    showErrorAnimation() {
        if (this.elements.progressFill) {
            // 添加錯誤動畫類
            this.elements.progressFill.style.background = 'linear-gradient(90deg, #dc3545 0%, #e74c3c 100%)';
            
            // 搖擺效果
            this.elements.progressFill.style.animation = 'shake 0.5s ease-in-out';
            
            setTimeout(() => {
                if (this.elements.progressFill) {
                    this.elements.progressFill.style.animation = '';
                }
            }, 500);
        }
    }
    
    /**
     * 設置自定義步驟模板
     * @param {Object} templates - 步驟模板對象
     */
    setStepTemplates(templates) {
        this.stepTemplates = { ...this.stepTemplates, ...templates };
        this.totalSteps = Object.keys(this.stepTemplates).length;
    }
    
    /**
     * 獲取當前進度狀態
     * @returns {Object} 當前狀態
     */
    getCurrentStatus() {
        return {
            currentStep: this.currentStep,
            totalSteps: this.totalSteps,
            isAnimating: this.isAnimating,
            percentage: this.elements.progressFill ? 
                parseFloat(this.elements.progressFill.style.width) || 0 : 0
        };
    }
    
    /**
     * 設置進度條顏色主題
     * @param {string} theme - 主題名稱 ('default', 'success', 'warning', 'error')
     */
    setProgressTheme(theme = 'default') {
        if (!this.elements.progressFill) return;
        
        const themes = {
            default: 'linear-gradient(90deg, var(--primary-color) 0%, #764ba2 100%)',
            success: 'linear-gradient(90deg, #28a745 0%, #20c997 100%)',
            warning: 'linear-gradient(90deg, #ffc107 0%, #fd7e14 100%)',
            error: 'linear-gradient(90deg, #dc3545 0%, #e74c3c 100%)'
        };
        
        this.elements.progressFill.style.background = themes[theme] || themes.default;
    }
    
    /**
     * 顯示警告狀態
     * @param {string} message - 警告訊息
     */
    showWarning(message) {
        this.updateProgress('警告', message, null, 'warning');
        this.setProgressTheme('warning');
    }
}

// 創建全局實例
const progressDisplay = new ProgressDisplayComponent();

// 導出模組
if (typeof module !== 'undefined' && module.exports) {
    module.exports = ProgressDisplayComponent;
} else if (typeof window !== 'undefined') {
    window.ProgressDisplayComponent = ProgressDisplayComponent;
    window.progressDisplay = progressDisplay;
}
