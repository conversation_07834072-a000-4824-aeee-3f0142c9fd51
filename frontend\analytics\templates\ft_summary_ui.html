<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>FT Summary 批量處理系統</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    
    <!-- 載入模組化 CSS -->
    <link rel="stylesheet" href="{{ url_for('shared.static', filename='css/variables.css') }}">
    <link rel="stylesheet" href="{{ url_for('shared.static', filename='css/base.css') }}">
    <link rel="stylesheet" href="{{ url_for('shared.static', filename='css/global.css') }}">
    <link rel="stylesheet" href="{{ url_for('analytics.static', filename='css/analytics.css') }}">
    
    <style>
        /* FT Summary 專用樣式 */
        .ft-summary-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        /* 橫向布局樣式 */
        .controls-container {
            display: flex;
            gap: 20px;
            margin-bottom: 20px;
        }

        .folder-input-card {
            flex: 1;
            background: white;
            border-radius: 12px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            padding: 25px;
        }

        .side-card {
            width: 200px;
            background: white;
            border-radius: 12px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            padding: 20px;
        }

        .side-card h4 {
            margin: 0 0 15px 0;
            font-size: 14px;
            color: var(--secondary-color);
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .folder-input {
            width: 100%;
            padding: 12px;
            border: 2px solid #dee2e6;
            border-radius: 8px;
            font-size: 14px;
            margin-bottom: 15px;
            box-sizing: border-box;
        }

        .primary-btn {
            width: 100%;
            padding: 12px;
            background: var(--primary-color, #007bff);
            color: white;
            border: none;
            border-radius: 8px;
            font-size: 16px;
            cursor: pointer;
            margin-bottom: 12px;
            transition: background-color 0.3s ease;
        }

        .primary-btn:hover {
            background: var(--primary-color-dark, #0056b3);
        }

        .primary-btn:disabled {
            background: #6c757d;
            cursor: not-allowed;
        }
        
        .ft-summary-card {
            background: white;
            border-radius: 12px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            padding: 30px;
            margin-bottom: 20px;
        }
        
        .ft-summary-input-group {
            margin-bottom: 20px;
        }
        
        .ft-summary-input-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: var(--text-color);
        }
        
        .ft-summary-path-input {
            width: 100%;
            padding: 12px 16px;
            border: 2px solid #e1e5e9;
            border-radius: 8px;
            font-size: 16px;
            transition: border-color 0.3s ease;
        }
        
        .ft-summary-path-input:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(116, 75, 162, 0.1);
        }
        
        .ft-summary-options {
            margin: 20px 0;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 8px;
        }
        
        .ft-summary-checkbox {
            display: flex;
            align-items: center;
            gap: 8px;
            margin-bottom: 10px;
        }
        
        .ft-summary-btn {
            width: 100%;
            padding: 15px 20px;
            background: linear-gradient(135deg, var(--primary-color) 0%, #764ba2 100%);
            color: white;
            border: none;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 10px;
        }
        
        .ft-summary-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 15px rgba(116, 75, 162, 0.3);
        }
        
        .ft-summary-btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }
        
        .ft-summary-result {
            margin-top: 20px;
            padding: 20px;
            border-radius: 8px;
            display: none;
        }
        
        .ft-summary-result.success {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        
        .ft-summary-result.error {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        
        .ft-summary-progress {
            margin-top: 15px;
            display: none;
        }
        
        .ft-summary-progress-bar {
            height: 8px;
            background: #e9ecef;
            border-radius: 4px;
            overflow: hidden;
            margin-bottom: 8px;
        }
        
        .ft-summary-progress-fill {
            height: 100%;
            background: linear-gradient(90deg, var(--primary-color) 0%, #764ba2 100%);
            width: 0%;
            transition: width 0.3s ease;
        }
        
        .ft-summary-stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
            gap: 15px;
            margin: 15px 0;
        }
        
        .ft-summary-stat-item {
            text-align: center;
            padding: 12px;
            background: white;
            border-radius: 6px;
            border: 1px solid #e9ecef;
        }
        
        .ft-summary-stat-value {
            font-size: 1.5em;
            font-weight: 600;
            color: var(--primary-color);
        }
        
        .ft-summary-stat-label {
            font-size: 0.9em;
            color: #6c757d;
            margin-top: 4px;
        }
        
        .ft-summary-download {
            margin-top: 15px;
            text-align: center;
        }
        
        .ft-summary-download-btn {
            display: inline-flex;
            align-items: center;
            gap: 8px;
            padding: 10px 20px;
            background: #28a745;
            color: white;
            text-decoration: none;
            border-radius: 6px;
            transition: background-color 0.3s ease;
        }
        
        .ft-summary-download-btn:hover {
            background: #218838;
            color: white;
        }
        
        /* 檔案清單樣式 */
        .ft-summary-file-lists {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin: 20px 0;
        }
        
        .ft-summary-file-list {
            background: white;
            border-radius: 8px;
            padding: 15px;
            border: 1px solid #e9ecef;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .ft-summary-file-list h5 {
            margin: 0 0 10px 0;
            color: var(--primary-color);
            font-size: 1.1em;
            font-weight: 600;
        }
        
        .ft-summary-file-items {
            max-height: 200px;
            overflow-y: auto;
        }
        
        .ft-summary-file-item {
            padding: 8px 12px;
            background: #f8f9fa;
            margin: 4px 0;
            border-radius: 4px;
            font-size: 0.9em;
            word-break: break-all;
            border-left: 3px solid var(--primary-color);
            transition: background-color 0.2s ease;
        }
        
        .ft-summary-file-item:hover {
            background: #e9ecef;
        }
        
        .ft-summary-file-item.empty {
            color: #6c757d;
            font-style: italic;
            border-left: 3px solid #dee2e6;
        }

        /* 文件上傳樣式 */
        .upload-zone.dragover {
            border-color: var(--primary-color, #007bff) !important;
            background: #e7f3ff !important;
        }

        .upload-zone.uploading {
            opacity: 0.7;
            pointer-events: none;
        }

        .upload-zone:hover {
            border-color: var(--primary-color, #007bff);
            background: #f0f8ff;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1><i class="fas fa-chart-line" aria-hidden="true"></i> FT Summary 批量處理</h1>
            <div id="modeIndicator" style="margin-top: 10px; padding: 8px 12px; border-radius: 6px; font-size: 0.9em; display: none; text-align: center;">
                <i class="fas fa-info-circle"></i>
                <span id="modeText">檢查中...</span>
            </div>
        </div>

        <div class="main-content">

            <!-- 橫向布局控制區域 -->
            <div class="controls-container" style="display: flex; gap: 20px; margin-bottom: 20px;">
                <!-- 主要路徑輸入區域 (左側一半) -->
                <div class="folder-input-card" style="flex: 1; background: white; border-radius: 12px; box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1); padding: 20px;">
                    <div class="folder-icon" style="text-align: center; margin-bottom: 10px;">
                        <i class="fas fa-folder-open" style="font-size: 1.5em; color: var(--primary-color, #007bff);"></i>
                    </div>
                    <h4 style="margin: 0 0 12px 0; color: var(--secondary-color, #6c757d); text-align: center; font-size: 14px;">📁 資料夾輸入</h4>

                    <form id="ftSummaryForm">
                        <input
                            type="text"
                            id="folderPath"
                            class="folder-input"
                            style="width: 100%; padding: 10px; border: 2px solid #dee2e6; border-radius: 6px; font-size: 13px; margin-bottom: 12px; box-sizing: border-box;"
                            placeholder="D:\folder 或 \\server\path"
                            value="D:\project\python\outlook_summary\doc\20250523"
                        >

                        <button type="submit" class="primary-btn" id="processBtn" style="width: 100%; padding: 10px; background: var(--primary-color, #007bff); color: white; border: none; border-radius: 6px; font-size: 14px; cursor: pointer; margin-bottom: 8px;">
                            <i class="fas fa-play"></i> 開始批量處理
                        </button>

                        <div style="font-size: 11px; color: #666; line-height: 1.3; text-align: center;">
                            💡 支援本地和遠端路徑
                        </div>
                    </form>
                </div>

                <!-- 檔案上傳 (右側一半) -->
                <div class="side-card" style="flex: 1; background: white; border-radius: 12px; box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1); padding: 20px;">
                    <div class="folder-icon" style="text-align: center; margin-bottom: 10px;">
                        <i class="fas fa-upload" style="font-size: 1.5em; color: var(--primary-color, #007bff);"></i>
                    </div>
                    <h4 style="margin: 0 0 12px 0; color: var(--secondary-color, #6c757d); text-align: center; font-size: 14px;"><i class="fas fa-upload" aria-hidden="true"></i> 檔案上傳</h4>
                    <div class="upload-zone" id="uploadZone" style="min-height: 80px; display: flex; flex-direction: column; justify-content: center; border: 2px dashed #dee2e6; border-radius: 6px; background: #f8f9fa; cursor: pointer; transition: all 0.3s ease;">
                        <i class="fas fa-cloud-upload-alt" aria-hidden="true" style="font-size: 1.5em; color: var(--primary-color); margin-bottom: 8px;"></i>
                        <div style="font-size: 12px; text-align: center;">
                            拖放檔案或點擊上傳<br>
                            <small style="color: #666;">支援 ZIP, 7Z, RAR<br>(最大 <span id="maxSizeDisplay">1000</span>MB)</small>
                        </div>
                        <input type="file" id="fileInput" accept=".zip,.7z,.rar,.tar,.gz" style="display: none;">
                    </div>
                    <div id="uploadStatus" style="margin-top: 8px; font-size: 11px; display: none;"></div>
                </div>
            </div>

            <!-- 處理模式選擇 -->
            <div class="ft-summary-card" style="background: white; border-radius: 12px; box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1); padding: 20px; margin-bottom: 20px;">
                <div class="ft-summary-input-group">
                    <label>
                        <i class="fas fa-cogs"></i> 處理模式
                    </label>
                    <div class="mode-selection" style="display: flex; gap: 20px; margin-top: 8px; justify-content: center;">
                        <div style="display: flex; align-items: center; gap: 6px;">
                            <input type="radio" id="modeXlsSummary" name="processing_mode" value="full">
                            <label for="modeXlsSummary" style="margin: 0; font-weight: normal;">
                                完整模式（Excel + Summary）
                            </label>
                        </div>
                        <div style="display: flex; align-items: center; gap: 6px;">
                            <input type="radio" id="modeSummaryOnly" name="processing_mode" value="summary_only" checked>
                            <label for="modeSummaryOnly" style="margin: 0; font-weight: normal;">
                                快速模式（僅 Summary）
                            </label>
                        </div>
                    </div>
                    <small style="color: #6c757d; margin-top: 8px; display: block; text-align: center;">
                        💡 快速模式：跳過Excel轉換，僅生成Summary和最終整併檔案，處理速度更快
                    </small>
                </div>
            </div>

            <!-- 7zip 路徑設定 -->
            <div class="ft-summary-card" style="background: white; border-radius: 12px; box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1); padding: 20px; margin-bottom: 20px;">
                <div class="ft-summary-input-group">
                    <label for="sevenZipPath">
                        <i class="fas fa-archive"></i> 7zip 路徑設定
                    </label>
                    <div style="display: flex; gap: 10px; margin-top: 8px;">
                        <input
                            type="text"
                            id="sevenZipPath"
                            class="folder-input"
                            style="flex: 1; padding: 8px; border: 2px solid #dee2e6; border-radius: 6px; font-size: 13px; box-sizing: border-box;"
                            placeholder="C:\Program Files\7-Zip"
                            value="C:\Program Files\7-Zip"
                        >
                        <button
                            type="button"
                            id="testSevenZipBtn"
                            style="padding: 8px 12px; background: #28a745; color: white; border: none; border-radius: 6px; font-size: 12px; cursor: pointer; white-space: nowrap;"
                        >
                            <i class="fas fa-check"></i> 測試
                        </button>
                        <button
                            type="button"
                            id="saveSevenZipBtn"
                            style="padding: 8px 12px; background: #007bff; color: white; border: none; border-radius: 6px; font-size: 12px; cursor: pointer; white-space: nowrap;"
                        >
                            <i class="fas fa-save"></i> 儲存
                        </button>
                    </div>
                    <div id="sevenZipStatus" style="margin-top: 8px; font-size: 11px; display: none;"></div>
                    <small style="color: #6c757d; margin-top: 8px; display: block;">
                        💡 設定 7zip 安裝路徑以支援 7Z/RAR 檔案解壓縮。預設路徑：C:\Program Files\7-Zip
                    </small>
                </div>
            </div>

            <div class="ft-summary-progress" id="progressContainer">
                <div class="ft-summary-progress-bar">
                    <div class="ft-summary-progress-fill" id="progressFill"></div>
                </div>
                <div id="progressText" style="text-align: center; font-size: 0.9em; color: #6c757d;">
                    處理中...
                </div>
            </div>

            <div class="ft-summary-result" id="resultContainer">
                <div id="resultMessage"></div>
                <div class="ft-summary-stats" id="statsContainer" style="display: none;">
                    <div class="ft-summary-stat-item">
                        <div class="ft-summary-stat-value" id="totalFiles">0</div>
                        <div class="ft-summary-stat-label">總檔案數</div>
                    </div>
                    <div class="ft-summary-stat-item">
                        <div class="ft-summary-stat-value" id="processedFiles">0</div>
                        <div class="ft-summary-stat-label">處理成功</div>
                    </div>
                    <div class="ft-summary-stat-item">
                        <div class="ft-summary-stat-value" id="skippedFiles">0</div>
                        <div class="ft-summary-stat-label">跳過檔案</div>
                    </div>
                    <div class="ft-summary-stat-item">
                        <div class="ft-summary-stat-value" id="ftSummaryFiles">0</div>
                        <div class="ft-summary-stat-label">FT Summary</div>
                    </div>
                    <div class="ft-summary-stat-item">
                        <div class="ft-summary-stat-value" id="eqcSummaryFiles">0</div>
                        <div class="ft-summary-stat-label">EQC Summary</div>
                    </div>
                    <div class="ft-summary-stat-item">
                        <div class="ft-summary-stat-value" id="processingTime">0s</div>
                        <div class="ft-summary-stat-label">處理時間</div>
                    </div>
                </div>

                <!-- 檔案清單顯示區域 -->
                <div class="ft-summary-file-lists" id="fileListContainer" style="display: none;">
                    <div class="ft-summary-file-list">
                        <h5><i class="fas fa-file-csv"></i> FT 檔案清單</h5>
                        <div class="ft-summary-file-items" id="ftFileList">
                            <div class="ft-summary-file-item">無 FT 檔案</div>
                        </div>
                    </div>
                    <div class="ft-summary-file-list">
                        <h5><i class="fas fa-file-csv"></i> EQC 檔案清單</h5>
                        <div class="ft-summary-file-items" id="eqcFileList">
                            <div class="ft-summary-file-item">無 EQC 檔案</div>
                        </div>
                    </div>
                </div>
                <div class="ft-summary-download" id="downloadContainer" style="display: none;">
                    <a href="#" class="ft-summary-download-btn" id="downloadBtn">
                        <i class="fas fa-download"></i>
                        下載 FT_SUMMARY.csv
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- 載入核心依賴 -->
    <script src="{{ url_for('shared.static', filename='js/core/utils.js') }}"></script>
    <script src="{{ url_for('shared.static', filename='js/core/api-client.js') }}"></script>

    <!-- 載入工具模組 -->
    <script src="{{ url_for('shared.static', filename='js/core/dom-manager.js') }}"></script>
    <script src="{{ url_for('shared.static', filename='js/core/status-manager.js') }}"></script>

    <!-- 載入文件上傳組件 -->
    <script src="{{ url_for('shared.static', filename='js/components/file-upload.js') }}"></script>

    <!-- 載入主處理器 -->
    <script src="{{ url_for('analytics.static', filename='js/ft-summary-processor.js') }}"></script>
</body>
</html>
