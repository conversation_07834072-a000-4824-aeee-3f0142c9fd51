#!/usr/bin/env python3
"""
Simple Architecture Migration Test
Verify the migration from email_inbox_app.py to modular frontend architecture
"""

import os
import sys
import json
import time
from pathlib import Path
from datetime import datetime
from typing import Dict, Any

# Add project root to Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))


def run_simple_verification() -> Dict[str, Any]:
    """Run simple architecture verification"""
    results = {
        'timestamp': datetime.now().isoformat(),
        'tests': {},
        'summary': {}
    }
    
    print("=== Architecture Migration Verification ===")
    
    # Test 1: Check old file removal
    print("\n1. Checking old file removal...")
    old_files = ['email_inbox_app.py', 'email_inbox_app.py.backup']
    legacy_removed = True
    
    for filename in old_files:
        file_path = project_root / filename
        if file_path.exists():
            print(f"   FAIL: {filename} still exists")
            legacy_removed = False
        else:
            print(f"   PASS: {filename} removed")
    
    results['tests']['legacy_cleanup'] = {'success': legacy_removed}
    
    # Test 2: Check new structure
    print("\n2. Checking new directory structure...")
    required_paths = [
        'frontend/app.py',
        'frontend/config.py', 
        'frontend/email/routes/email_routes.py',
        'frontend/analytics/routes/analytics_routes.py',
        'frontend/eqc/routes/eqc_routes.py',
        'frontend/tasks/routes/task_routes.py',
        'frontend/monitoring/routes/monitoring_routes.py',
        'frontend/file_management/routes/file_routes.py'
    ]
    
    structure_complete = True
    for path in required_paths:
        full_path = project_root / path
        if full_path.exists():
            print(f"   PASS: {path}")
        else:
            print(f"   FAIL: {path} missing")
            structure_complete = False
    
    results['tests']['structure'] = {'success': structure_complete}
    
    # Test 3: Test Flask app creation
    print("\n3. Testing Flask app creation...")
    try:
        from frontend.app import create_app
        app = create_app('testing')
        blueprints = len(app.blueprints)
        print(f"   PASS: Flask app created with {blueprints} blueprints")
        results['tests']['flask_app'] = {'success': True, 'blueprints': blueprints}
    except Exception as e:
        print(f"   FAIL: Flask app creation failed: {e}")
        results['tests']['flask_app'] = {'success': False, 'error': str(e)}
    
    # Test 4: Test module imports
    print("\n4. Testing module imports...")
    modules = [
        'frontend.email.routes.email_routes',
        'frontend.analytics.routes.analytics_routes',
        'frontend.eqc.routes.eqc_routes',
        'frontend.tasks.routes.task_routes',
        'frontend.monitoring.routes.monitoring_routes',
        'frontend.file_management.routes.file_routes'
    ]
    
    successful_imports = 0
    for module in modules:
        try:
            __import__(module)
            print(f"   PASS: {module}")
            successful_imports += 1
        except Exception as e:
            print(f"   FAIL: {module} - {e}")
    
    import_success = successful_imports == len(modules)
    results['tests']['imports'] = {
        'success': import_success,
        'successful': successful_imports,
        'total': len(modules)
    }
    
    # Test 5: Check start script
    print("\n5. Checking start_integrated_services.py...")
    start_script = project_root / 'start_integrated_services.py'
    if start_script.exists():
        try:
            content = start_script.read_text(encoding='utf-8')
            uses_new_arch = 'frontend.app' in content and 'create_app' in content
            no_old_refs = 'email_inbox_app' not in content
            
            if uses_new_arch and no_old_refs:
                print("   PASS: Uses new architecture, no old references")
                results['tests']['start_script'] = {'success': True}
            else:
                print("   FAIL: Still has old references or not using new architecture")
                results['tests']['start_script'] = {'success': False}
        except Exception as e:
            print(f"   FAIL: Error reading start script: {e}")
            results['tests']['start_script'] = {'success': False, 'error': str(e)}
    else:
        print("   FAIL: start_integrated_services.py not found")
        results['tests']['start_script'] = {'success': False, 'error': 'File not found'}
    
    # Calculate summary
    test_results = [results['tests'][test]['success'] for test in results['tests']]
    successful_tests = sum(test_results)
    total_tests = len(test_results)
    success_rate = (successful_tests / total_tests * 100) if total_tests > 0 else 0
    
    results['summary'] = {
        'overall_success': successful_tests == total_tests,
        'success_rate': success_rate,
        'successful_tests': successful_tests,
        'total_tests': total_tests
    }
    
    return results


def generate_report(results: Dict[str, Any]) -> str:
    """Generate test report"""
    summary = results['summary']
    
    report = f"""
Architecture Migration Test Report
{'='*50}

Test Time: {results['timestamp']}
Overall Result: {'SUCCESS' if summary['overall_success'] else 'FAILURE'}
Success Rate: {summary['success_rate']:.1f}%
Passed Tests: {summary['successful_tests']}/{summary['total_tests']}

Detailed Results:
{'='*50}

1. Legacy Cleanup: {'PASS' if results['tests']['legacy_cleanup']['success'] else 'FAIL'}
   - Old files removed from project

2. Directory Structure: {'PASS' if results['tests']['structure']['success'] else 'FAIL'}
   - New modular frontend structure in place

3. Flask App Creation: {'PASS' if results['tests']['flask_app']['success'] else 'FAIL'}
"""
    
    if results['tests']['flask_app']['success']:
        report += f"   - Created with {results['tests']['flask_app']['blueprints']} blueprints\n"
    
    report += f"""
4. Module Imports: {'PASS' if results['tests']['imports']['success'] else 'FAIL'}
   - Successfully imported {results['tests']['imports']['successful']}/{results['tests']['imports']['total']} modules

5. Start Script Update: {'PASS' if results['tests']['start_script']['success'] else 'FAIL'}
   - start_integrated_services.py updated for new architecture
"""
    
    if summary['overall_success']:
        report += f"""
CONCLUSION:
{'='*50}

SUCCESS: Architecture migration is complete!

✓ Old email_inbox_app.py successfully removed
✓ New modular frontend architecture in place  
✓ All 6 frontend modules loading correctly
✓ Flask application factory working
✓ start_integrated_services.py updated
✓ Ready for production use

NEXT STEPS:
- Can safely remove any backup files
- Update deployment documentation
- Begin Vue.js migration phase
"""
    else:
        failed_tests = [test for test, result in results['tests'].items() 
                       if not result['success']]
        report += f"""
ISSUES FOUND:
{'='*50}

The following tests failed:
"""
        for test in failed_tests:
            report += f"- {test}\n"
        
        report += """
RECOMMENDED ACTIONS:
1. Review and fix failed tests
2. Re-run verification
3. Ensure all tests pass before proceeding
"""
    
    report += "\n" + "="*50
    return report


def main():
    """Main function"""
    print("Architecture Migration Verification Test")
    print("Checking migration from email_inbox_app.py to modular frontend/")
    print("="*60)
    
    try:
        # Run verification
        results = run_simple_verification()
        
        # Generate report
        report = generate_report(results)
        
        # Display results
        print("\n" + "="*60)
        print("VERIFICATION COMPLETE")
        print("="*60)
        print(report)
        
        # Save reports
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        # Save JSON report
        json_file = project_root / f"architecture_test_{timestamp}.json"
        with open(json_file, 'w', encoding='utf-8') as f:
            json.dump(results, f, indent=2, ensure_ascii=False)
        
        # Save text report
        txt_file = project_root / f"architecture_test_{timestamp}.txt"
        with open(txt_file, 'w', encoding='utf-8') as f:
            f.write(report)
        
        print(f"\nReports saved:")
        print(f"- JSON: {json_file.name}")
        print(f"- Text: {txt_file.name}")
        
        return 0 if results['summary']['overall_success'] else 1
        
    except Exception as e:
        print(f"\nERROR: Verification failed: {e}")
        import traceback
        traceback.print_exc()
        return 1


if __name__ == '__main__':
    sys.exit(main())