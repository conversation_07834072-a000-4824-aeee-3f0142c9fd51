"""
Simple Database Architecture Diagnosis Tool
Check current database schema and compare with model definition
"""

import sqlite3
import sys
import os
from pathlib import Path

# Add src directory to Python path
project_root = Path(__file__).parent.parent
src_path = project_root / "src"
sys.path.insert(0, str(src_path))

def main():
    print("=" * 60)
    print("Database Architecture Diagnosis Report")
    print("=" * 60)
    
    # Database path
    db_path = project_root / "data" / "email_inbox.db"
    print(f"Project root: {project_root}")
    print(f"Database path: {db_path}")
    print()
    
    # Check if file exists
    print(f"Checking database file: {db_path}")
    if os.path.exists(db_path):
        print(f"OK - Database file exists")
        print(f"  File size: {os.path.getsize(db_path)} bytes")
    else:
        print(f"ERROR - Database file does not exist")
        return
    
    print()
    
    try:
        # Connect to database
        conn = sqlite3.connect(str(db_path))
        cursor = conn.cursor()
        
        print("Database connection successful!")
        print()
        
        # Get all tables
        print("Existing tables:")
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
        tables = cursor.fetchall()
        table_names = [table[0] for table in tables]
        for table in table_names:
            print(f"  - {table}")
        print()
        
        # Check emails table schema
        print("Emails table schema:")
        if 'emails' in table_names:
            cursor.execute("PRAGMA table_info(emails)")
            columns = cursor.fetchall()
            print(f"  Total columns: {len(columns)}")
            
            # Check for specific columns
            column_names = [col[1] for col in columns]
            
            required_columns = ['id', 'message_id', 'sender', 'subject', 'mo', 'pd', 'lot']
            missing_columns = []
            
            print("  Column check:")
            for col in required_columns:
                if col in column_names:
                    print(f"    OK - {col} exists")
                else:
                    print(f"    ERROR - {col} missing")
                    missing_columns.append(col)
            
            print()
            print("  All columns in emails table:")
            for col_info in columns:
                col_name = col_info[1]
                col_type = col_info[2]
                print(f"    {col_name} ({col_type})")
            
            if missing_columns:
                print(f"\nMISSING COLUMNS: {', '.join(missing_columns)}")
                print("This explains the SQL error!")
            else:
                print("\nAll required columns exist.")
        else:
            print("  ERROR - emails table does not exist")
        
        # Get some statistics
        print("\nDatabase statistics:")
        try:
            cursor.execute("SELECT COUNT(*) FROM emails")
            email_count = cursor.fetchone()[0]
            print(f"  Total emails: {email_count}")
        except sqlite3.Error as e:
            print(f"  Cannot get email count: {e}")
        
    except sqlite3.Error as e:
        print(f"Database operation failed: {e}")
        return
    except Exception as e:
        print(f"Unexpected error: {e}")
        return
    finally:
        if 'conn' in locals():
            conn.close()

if __name__ == "__main__":
    main()