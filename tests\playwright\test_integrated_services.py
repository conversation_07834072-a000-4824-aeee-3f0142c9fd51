"""
Playwright 整合測試套件
測試所有整合服務的功能，確保 Vue 前端遷移需求得到滿足
"""

import pytest
import asyncio
import time
from playwright.async_api import async_playwright, <PERSON>, Browser, BrowserContext
from typing import Dict, Any, List
import requests
import json


class TestIntegratedServices:
    """整合服務測試類"""
    
    @pytest.fixture(scope="class")
    async def browser_setup(self):
        """設置瀏覽器環境"""
        playwright = await async_playwright().start()
        browser = await playwright.chromium.launch(headless=False)
        context = await browser.new_context()
        page = await context.new_page()
        
        yield {
            'playwright': playwright,
            'browser': browser,
            'context': context,
            'page': page
        }
        
        await context.close()
        await browser.close()
        await playwright.stop()
    
    @pytest.mark.asyncio
    async def test_flask_service_availability(self, browser_setup):
        """測試 Flask 服務可用性 - 需求 1.1"""
        page = browser_setup['page']
        
        # 導航到 Flask 主頁
        await page.goto('http://localhost:5000')
        
        # 等待頁面載入
        await page.wait_for_load_state('networkidle')
        
        # 檢查頁面標題
        title = await page.title()
        assert title is not None, "Flask 服務應該返回有效的頁面標題"
        
        # 檢查頁面內容
        content = await page.content()
        assert len(content) > 0, "Flask 服務應該返回有效的頁面內容"
        
        # 截圖記錄
        await page.screenshot(path='tests/screenshots/flask_homepage.png', full_page=True)
    
    @pytest.mark.asyncio
    async def test_fastapi_service_availability(self, browser_setup):
        """測試 FastAPI 服務可用性 - 需求 1.2"""
        page = browser_setup['page']
        
        # 導航到 FastAPI 文檔頁面
        await page.goto('http://localhost:8010/docs')
        
        # 等待頁面載入
        await page.wait_for_load_state('networkidle')
        
        # 檢查 Swagger UI 是否載入
        swagger_ui = await page.locator('.swagger-ui').count()
        assert swagger_ui > 0, "FastAPI Swagger UI 應該正確載入"
        
        # 檢查 API 標題
        api_title = await page.locator('h2.title').text_content()
        assert 'FT-EQC' in api_title, "API 標題應該包含 FT-EQC"
        
        # 截圖記錄
        await page.screenshot(path='tests/screenshots/fastapi_docs.png', full_page=True)
    
    @pytest.mark.asyncio
    async def test_email_module_functionality(self, browser_setup):
        """測試郵件模組功能 - 需求 2.1"""
        page = browser_setup['page']
        
        # 導航到郵件收件夾
        await page.goto('http://localhost:5000/email/inbox')
        
        # 等待頁面載入
        await page.wait_for_load_state('networkidle')
        
        # 檢查郵件列表是否存在
        email_list = await page.locator('.email-list, #email-list, table').count()
        assert email_list > 0, "郵件列表應該存在"
        
        # 檢查同步狀態 API
        sync_status = await page.evaluate("""
            fetch('/api/sync/status')
                .then(response => response.json())
                .then(data => data)
                .catch(error => null)
        """)
        assert sync_status is not None, "同步狀態 API 應該可用"
        
        # 截圖記錄
        await page.screenshot(path='tests/screenshots/email_inbox.png', full_page=True)
    
    @pytest.mark.asyncio
    async def test_analytics_module_functionality(self, browser_setup):
        """測試分析統計模組功能 - 需求 2.2"""
        page = browser_setup['page']
        
        # 導航到分析統計頁面
        await page.goto('http://localhost:5000/analytics/dashboard')
        
        # 等待頁面載入
        await page.wait_for_load_state('networkidle')
        
        # 檢查圖表或統計元素
        charts = await page.locator('canvas, .chart, .graph, .statistics').count()
        assert charts >= 0, "分析頁面應該載入成功"
        
        # 截圖記錄
        await page.screenshot(path='tests/screenshots/analytics_dashboard.png', full_page=True)
    
    @pytest.mark.asyncio
    async def test_file_management_functionality(self, browser_setup):
        """測試檔案管理模組功能 - 需求 2.3"""
        page = browser_setup['page']
        
        # 導航到檔案管理頁面
        await page.goto('http://localhost:5000/file_management/dashboard')
        
        # 等待頁面載入
        await page.wait_for_load_state('networkidle')
        
        # 檢查檔案列表或管理介面
        file_interface = await page.locator('.file-list, .file-manager, table').count()
        assert file_interface >= 0, "檔案管理介面應該載入成功"
        
        # 截圖記錄
        await page.screenshot(path='tests/screenshots/file_management.png', full_page=True)
    
    @pytest.mark.asyncio
    async def test_eqc_module_functionality(self, browser_setup):
        """測試 EQC 模組功能 - 需求 2.4"""
        page = browser_setup['page']
        
        # 導航到 EQC 儀表板
        await page.goto('http://localhost:5000/eqc/dashboard')
        
        # 等待頁面載入
        await page.wait_for_load_state('networkidle')
        
        # 檢查 EQC 相關元素
        eqc_elements = await page.locator('.eqc, .dashboard, .metrics').count()
        assert eqc_elements >= 0, "EQC 儀表板應該載入成功"
        
        # 測試 FastAPI EQC 端點
        await page.goto('http://localhost:8010/ui')
        await page.wait_for_load_state('networkidle')
        
        # 截圖記錄
        await page.screenshot(path='tests/screenshots/eqc_dashboard.png', full_page=True)
    
    @pytest.mark.asyncio
    async def test_tasks_module_functionality(self, browser_setup):
        """測試任務管理模組功能 - 需求 2.5"""
        page = browser_setup['page']
        
        # 導航到任務管理頁面
        await page.goto('http://localhost:5000/tasks/dashboard')
        
        # 等待頁面載入
        await page.wait_for_load_state('networkidle')
        
        # 檢查任務相關元素
        task_elements = await page.locator('.task, .scheduler, table').count()
        assert task_elements >= 0, "任務管理頁面應該載入成功"
        
        # 截圖記錄
        await page.screenshot(path='tests/screenshots/tasks_dashboard.png', full_page=True)
    
    @pytest.mark.asyncio
    async def test_monitoring_module_functionality(self, browser_setup):
        """測試監控模組功能 - 需求 2.6"""
        page = browser_setup['page']
        
        # 導航到監控頁面
        await page.goto('http://localhost:5000/monitoring/dashboard')
        
        # 等待頁面載入
        await page.wait_for_load_state('networkidle')
        
        # 檢查監控相關元素
        monitoring_elements = await page.locator('.monitoring, .metrics, .health').count()
        assert monitoring_elements >= 0, "監控頁面應該載入成功"
        
        # 截圖記錄
        await page.screenshot(path='tests/screenshots/monitoring_dashboard.png', full_page=True)
    
    @pytest.mark.asyncio
    async def test_api_endpoints_functionality(self, browser_setup):
        """測試 API 端點功能 - 需求 3"""
        page = browser_setup['page']
        
        # 測試關鍵 API 端點
        api_tests = [
            {'url': '/api/sync/status', 'description': '同步狀態 API'},
            {'url': '/api/emails/count', 'description': '郵件計數 API'},
            {'url': '/api/system/health', 'description': '系統健康檢查 API'},
        ]
        
        for api_test in api_tests:
            try:
                response = await page.evaluate(f"""
                    fetch('http://localhost:5000{api_test['url']}')
                        .then(response => {{
                            return {{
                                status: response.status,
                                ok: response.ok,
                                url: response.url
                            }}
                        }})
                        .catch(error => {{
                            return {{
                                status: 0,
                                ok: false,
                                error: error.message
                            }}
                        }})
                """)
                
                print(f"API 測試 - {api_test['description']}: {response}")
                
            except Exception as e:
                print(f"API 測試失敗 - {api_test['description']}: {e}")
    
    @pytest.mark.asyncio
    async def test_navigation_between_modules(self, browser_setup):
        """測試模組間導航 - 需求 4"""
        page = browser_setup['page']
        
        # 測試主要模組導航
        modules = [
            {'path': '/email/inbox', 'name': '郵件模組'},
            {'path': '/analytics/dashboard', 'name': '分析模組'},
            {'path': '/file_management/dashboard', 'name': '檔案管理模組'},
            {'path': '/eqc/dashboard', 'name': 'EQC模組'},
            {'path': '/tasks/dashboard', 'name': '任務模組'},
            {'path': '/monitoring/dashboard', 'name': '監控模組'},
        ]
        
        for module in modules:
            try:
                await page.goto(f'http://localhost:5000{module["path"]}')
                await page.wait_for_load_state('networkidle', timeout=5000)
                
                # 檢查頁面是否成功載入
                title = await page.title()
                print(f"導航測試 - {module['name']}: 成功 (標題: {title})")
                
            except Exception as e:
                print(f"導航測試 - {module['name']}: 失敗 ({e})")
    
    def test_service_health_check(self):
        """測試服務健康檢查 - 需求 7"""
        # 測試 Flask 服務
        try:
            flask_response = requests.get('http://localhost:5000', timeout=5)
            assert flask_response.status_code == 200, "Flask 服務應該返回 200 狀態碼"
            print("Flask 服務健康檢查: 通過")
        except Exception as e:
            pytest.fail(f"Flask 服務健康檢查失敗: {e}")
        
        # 測試 FastAPI 服務
        try:
            fastapi_response = requests.get('http://localhost:8010/docs', timeout=5)
            assert fastapi_response.status_code == 200, "FastAPI 服務應該返回 200 狀態碼"
            print("FastAPI 服務健康檢查: 通過")
        except Exception as e:
            pytest.fail(f"FastAPI 服務健康檢查失敗: {e}")


if __name__ == "__main__":
    # 運行測試
    pytest.main([__file__, "-v", "--tb=short"])
