/**
 * 下載對話框組件
 * 提供檔案處理完成後的下載功能
 * 包含EQCTOTALDATA.xlsx和EQCTOTALDATA_RAW.csv的下載
 */

// 顯示下載按鈕功能 (支援完整下載路徑)
function showDownloadButtons(folderPath, eqctotaldataPath = null, eqctotaldataRawPath = null) {
    // 檢查按鈕容器是否已存在，避免重複創建
    let buttonContainer = document.getElementById('downloadButtonContainer');
    if (buttonContainer) {
        buttonContainer.remove();
    }
    
    // 創建下載按鈕容器
    buttonContainer = document.createElement('div');
    buttonContainer.id = 'downloadButtonContainer';
    buttonContainer.style.cssText = `
        position: fixed;
        bottom: 30px;
        right: 30px;
        background: white;
        padding: 20px;
        border-radius: 15px;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
        border: 1px solid #e9ecef;
        z-index: 1000;
        font-family: 'Microsoft JhengHei', Arial, sans-serif;
        min-width: 300px;
        opacity: 0;
        transform: translateX(100%);
        transition: all 0.3s ease;
    `;
    
    buttonContainer.innerHTML = `
        <div style="display: flex; align-items: center; margin-bottom: 15px; color: #2c3e50;">
            <i class="fas fa-download" style="font-size: 18px; color: #667eea; margin-right: 8px;"></i>
            <h4 style="margin: 0; font-size: 16px;">處理完成，可下載結果檔案</h4>
            <button onclick="closeDownloadButtons()" style="
                background: none;
                border: none;
                color: #6c757d;
                cursor: pointer;
                font-size: 16px;
                margin-left: auto;
                padding: 2px;
            ">
                <i class="fas fa-times"></i>
            </button>
        </div>
        
        <div style="display: flex; flex-direction: column; gap: 10px;">
            ${generateDownloadButton(
                eqctotaldataPath, 
                folderPath, 
                'EQCTOTALDATA.xlsx', 
                'fas fa-file-excel', 
                'linear-gradient(135deg, #28a745, #20c997)'
            )}
            
            ${generateDownloadButton(
                eqctotaldataRawPath, 
                folderPath, 
                'EQCTOTALDATA_RAW.csv', 
                'fas fa-file-csv', 
                'linear-gradient(135deg, #17a2b8, #138496)'
            )}
        </div>
        
        <div style="font-size: 12px; color: #6c757d; margin-top: 10px; text-align: center;">
            <i class="fas fa-info-circle"></i> 檔案將在24小時後自動清理
        </div>
    `;
    
    document.body.appendChild(buttonContainer);
    
    // 添加進入動畫
    setTimeout(() => {
        buttonContainer.style.transform = 'translateX(0)';
        buttonContainer.style.opacity = '1';
    }, 100);
    
    console.log('✅ 下載按鈕已顯示');
}

// 關閉下載按鈕
function closeDownloadButtons() {
    const buttonContainer = document.getElementById('downloadButtonContainer');
    if (buttonContainer) {
        buttonContainer.style.transform = 'translateX(100%)';
        buttonContainer.style.opacity = '0';
        setTimeout(() => {
            if (buttonContainer && buttonContainer.parentNode) {
                buttonContainer.parentNode.removeChild(buttonContainer);
            }
        }, 300);
    }
}

// 生成下載按鈕HTML
function generateDownloadButton(fullPath, folderPath, fileName, iconClass, gradient) {
    // 使用唯一 ID 和數據屬性來避免反斜線轉義問題
    const buttonId = `download-btn-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
    
    // 決定使用哪個下載函數
    const downloadFunction = fullPath 
        ? `downloadFileByDataAttr('${buttonId}')`
        : `downloadFile('${folderPath}', '${fileName}')`;
    
    return `
        <button id="${buttonId}" 
                data-full-path="${fullPath || ''}" 
                data-folder-path="${folderPath || ''}" 
                data-file-name="${fileName}" 
                onclick="${downloadFunction}" style="
            background: ${gradient};
            color: white;
            border: none;
            padding: 12px 16px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
            display: flex;
            align-items: center;
            gap: 8px;
            transition: all 0.3s ease;
        " onmouseover="this.style.transform='translateY(-2px)'" onmouseout="this.style.transform='translateY(0px)'">
            <i class="${iconClass}"></i>
            <span>下載 ${fileName}</span>
        </button>
    `;
}

// 下載檔案功能
async function downloadFile(folderPath, fileName) {
    try {
        console.log(`🔽 開始下載檔案: ${fileName} from ${folderPath}`);
        
        // 構建完整檔案路徑
        const fullFilePath = `${folderPath}/${fileName}`;
        
        // 創建下載連結
        const downloadUrl = ApiClient.createDownloadUrl(fullFilePath);
        
        // 檢查檔案是否存在
        const checkResponse = await fetch(`${ApiClient.baseUrl}/check_file_exists`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                file_path: fullFilePath
            })
        });
        
        if (!checkResponse.ok) {
            throw new Error('檔案檢查請求失敗');
        }
        
        const checkResult = await checkResponse.json();
        
        if (!checkResult.exists) {
            alert(`檔案不存在：${fileName}\n\n請確認 EQC 處理流程已完成。`);
            return;
        }
        
        // 創建隱藏的下載連結
        const link = document.createElement('a');
        link.href = downloadUrl;
        link.download = fileName;
        link.style.display = 'none';
        document.body.appendChild(link);
        
        // 觸發下載
        link.click();
        
        // 清理連結
        document.body.removeChild(link);
        
        console.log(`✅ ${fileName} 下載已啟動`);
        showDownloadSuccessMessage(fileName);
        
    } catch (error) {
        console.error(`❌ 下載 ${fileName} 失敗:`, error);
        alert(`下載失敗：${error.message}`);
    }
}

// 通過完整路徑下載檔案
async function downloadFileByFullPath(fullFilePath, displayName) {
    try {
        console.log(`🔽 開始下載檔案: ${displayName} from ${fullFilePath}`);
        
        // 創建下載連結
        const downloadUrl = ApiClient.createDownloadUrl(fullFilePath);
        
        // 檢查檔案是否存在
        const checkResponse = await fetch(`${ApiClient.baseUrl}/check_file_exists`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                file_path: fullFilePath
            })
        });
        
        if (!checkResponse.ok) {
            throw new Error('檔案檢查請求失敗');
        }
        
        const checkResult = await checkResponse.json();
        
        if (!checkResult.exists) {
            alert(`檔案不存在：${displayName}\n\n請確認 EQC 處理流程已完成。`);
            return;
        }
        
        // 創建隱藏的下載連結
        const link = document.createElement('a');
        link.href = downloadUrl;
        link.download = displayName;
        link.style.display = 'none';
        document.body.appendChild(link);
        
        // 觸發下載
        link.click();
        
        // 清理連結
        document.body.removeChild(link);
        
        console.log(`✅ ${displayName} 下載已啟動`);
        showDownloadSuccessMessage(displayName);
        
    } catch (error) {
        console.error(`❌ 下載 ${displayName} 失敗:`, error);
        alert(`下載失敗：${error.message}`);
    }
}

// 通過數據屬性下載檔案
async function downloadFileByDataAttr(buttonId) {
    try {
        const button = document.getElementById(buttonId);
        if (!button) {
            throw new Error('找不到下載按鈕');
        }
        
        const fullPath = button.getAttribute('data-full-path');
        const fileName = button.getAttribute('data-file-name');
        const folderPath = button.getAttribute('data-folder-path');
        
        // 檢查是否有有效的完整路徑
        if (fullPath && fullPath !== 'undefined' && fullPath !== 'null') {
            console.log(`🔽 通過數據屬性下載: ${fileName}`);
            console.log(`📁 完整路徑: ${fullPath}`);
            await downloadFileByFullPath(fullPath, fileName);
        } else if (folderPath && folderPath !== 'undefined') {
            console.log(`🔽 備用下載方式: ${fileName} from folder: ${folderPath}`);
            await downloadFile(folderPath, fileName);
        } else {
            // 最後備用方案：嘗試從當前結果中獲取路徑資訊
            console.log(`🔽 智能路徑構建: ${fileName}`);
            await downloadFileWithSmartPath(fileName);
        }
        
    } catch (error) {
        console.error(`❌ 數據屬性下載失敗:`, error);
        alert(`下載失敗：${error.message}`);
    }
}

/**
 * 智能路徑構建下載 - 從當前處理結果中獲取路徑
 */
async function downloadFileWithSmartPath(fileName) {
    try {
        // 嘗試從全局的 EQC 處理器獲取當前結果
        const eqcProcessor = window.mainController?.modules?.eqcProcessor;
        if (!eqcProcessor || !eqcProcessor.currentResults) {
            throw new Error('無法獲取當前處理結果');
        }
        
        const results = eqcProcessor.currentResults;
        console.log('📊 當前處理結果:', results);
        
        // 從 Step 1 結果中獲取路徑資訊
        const step1Data = results.step1_online_eqc?.data;
        if (!step1Data) {
            throw new Error('無法獲取 Step 1 處理結果');
        }
        
        let downloadPath = null;
        
        // 根據檔案名稱匹配對應的下載路徑
        if (fileName === 'EQCTOTALDATA.xlsx') {
            downloadPath = step1Data.eqctotaldata_download_path;
        } else if (fileName === 'EQCTOTALDATA_RAW.csv') {
            downloadPath = step1Data.eqctotaldata_raw_download_path;
        } else if (fileName === 'EQCTOTALDATA.csv') {
            // 構建 CSV 路徑（通常在同一資料夾下）
            const basePath = step1Data.eqctotaldata_download_path;
            if (basePath) {
                downloadPath = basePath.replace('EQCTOTALDATA.xlsx', 'EQCTOTALDATA.csv');
            }
        }
        
        if (!downloadPath) {
            throw new Error(`無法確定檔案 ${fileName} 的下載路徑`);
        }
        
        console.log(`🎯 智能構建路徑: ${downloadPath}`);
        await downloadFileByFullPath(downloadPath, fileName);
        
    } catch (error) {
        console.error(`❌ 智能路徑下載失敗:`, error);
        throw error;
    }
}

// 顯示下載成功提示
function showDownloadSuccessMessage(fileName) {
    const toast = document.createElement('div');
    toast.style.cssText = `
        position: fixed;
        top: 30px;
        right: 30px;
        background: #28a745;
        color: white;
        padding: 15px 20px;
        border-radius: 8px;
        box-shadow: 0 4px 12px rgba(40, 167, 69, 0.3);
        z-index: 10001;
        font-family: 'Microsoft JhengHei', Arial, sans-serif;
        opacity: 0;
        transform: translateX(100%);
        transition: all 0.3s ease;
    `;
    
    toast.innerHTML = `
        <div style="display: flex; align-items: center; gap: 8px;">
            <i class="fas fa-check-circle"></i>
            <span>${fileName} 下載完成</span>
        </div>
    `;
    
    document.body.appendChild(toast);
    
    // 顯示動畫
    setTimeout(() => {
        toast.style.opacity = '1';
        toast.style.transform = 'translateX(0)';
    }, 100);
    
    // 自動隱藏
    setTimeout(() => {
        toast.style.opacity = '0';
        toast.style.transform = 'translateX(100%)';
        setTimeout(() => {
            if (toast && toast.parentNode) {
                toast.parentNode.removeChild(toast);
            }
        }, 300);
    }, 3000);
}