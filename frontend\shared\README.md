# Shared Module - 共享前端資源

## 概述

共享模組包含所有功能模組共用的前端資源，包括基礎模板、全域樣式、共用 JavaScript 函數和第三方函式庫。

**Vue.js 遷移準備**: 本模組是 Vue.js 遷移的核心基礎，提供統一的設計系統、API 客戶端和工具函數，所有共享資源都已準備好轉換為 Vue.js 的全域組件、Composables 和工具庫。

## 功能特性

### 核心功能
- **基礎模板** - 提供統一的頁面佈局和結構
- **共享組件** - 可重用的 UI 組件
- **全域樣式** - 統一的視覺風格和主題
- **API 客戶端** - 統一的 API 調用介面
- **工具函數** - 共用的 JavaScript 工具函數

### 設計系統
- **一致性** - 確保所有模組的視覺和交互一致性
- **可重用性** - 提供高度可重用的組件和樣式
- **可維護性** - 集中管理共用資源，便於維護
- **擴展性** - 支援主題定制和組件擴展

## 目錄結構

```
shared/
├── templates/               # 共享模板
│   ├── base.html           # 基礎模板
│   ├── layout.html         # 主要佈局
│   └── components/         # 共享組件
│       ├── navbar.html     # 導航列
│       ├── sidebar.html    # 側邊欄
│       ├── modal.html      # 模態框
│       ├── loading.html    # 載入動畫
│       ├── pagination.html # 分頁組件
│       ├── breadcrumb.html # 麵包屑導航
│       └── alert.html      # 警報組件
├── static/                 # 靜態資源
│   ├── css/                # 樣式檔案
│   │   ├── global.css      # 全域樣式
│   │   ├── components.css  # 共享組件樣式
│   │   ├── theme.css       # 主題樣式
│   │   └── utilities.css   # 工具樣式
│   ├── js/                 # JavaScript 檔案
│   │   ├── common.js       # 共用 JavaScript
│   │   ├── api-client.js   # 統一 API 客戶端
│   │   ├── websocket-client.js # WebSocket 客戶端
│   │   ├── utils.js        # 工具函數
│   │   ├── constants.js    # 前端常數
│   │   └── error-handler.js # 錯誤處理
│   ├── lib/                # 第三方函式庫
│   │   ├── jquery.min.js   # jQuery
│   │   ├── bootstrap.min.js # Bootstrap
│   │   ├── chart.min.js    # Chart.js
│   │   └── moment.min.js   # Moment.js
│   └── images/             # 共享圖片
│       ├── logo.png        # 系統標誌
│       ├── favicon.ico     # 網站圖示
│       └── icons/          # 圖示集
└── README.md               # 本檔案
```

## 基礎模板

### base.html
提供所有頁面的基礎結構，包括：
- HTML5 文檔結構
- Meta 標籤和 SEO 設定
- 共用 CSS 和 JavaScript 引用
- 基本的頁面區塊定義

### layout.html
提供主要的頁面佈局，包括：
- 頂部導航列
- 側邊欄導航
- 主內容區域
- 頁腳資訊

## 共享組件

### 導航組件
- **navbar.html** - 頂部導航列，包含系統標誌、主選單和用戶資訊
- **sidebar.html** - 側邊欄導航，包含功能模組導航連結
- **breadcrumb.html** - 麵包屑導航，顯示當前頁面位置

### 互動組件
- **modal.html** - 通用模態框組件，支援自定義內容和操作
- **alert.html** - 警報組件，支援不同類型的訊息顯示
- **loading.html** - 載入動畫組件，提供統一的載入指示

### 數據組件
- **pagination.html** - 分頁組件，支援大數據集的分頁顯示
- **data-table.html** - 數據表格組件，支援排序、篩選和分頁
- **chart-container.html** - 圖表容器組件，統一圖表顯示樣式

## 全域樣式

### global.css
- **重置樣式** - 統一不同瀏覽器的預設樣式
- **基礎樣式** - 定義基本的字體、顏色和間距
- **佈局樣式** - 定義頁面佈局和網格系統

### components.css
- **組件樣式** - 所有共享組件的樣式定義
- **狀態樣式** - 組件的不同狀態樣式 (hover, active, disabled)
- **響應式樣式** - 組件的響應式設計樣式

### theme.css
- **顏色系統** - 定義主題顏色變數
- **字體系統** - 定義字體大小和權重變數
- **間距系統** - 定義統一的間距變數
- **陰影系統** - 定義統一的陰影效果

## JavaScript 模組

### api-client.js
統一的 API 客戶端，提供：
- HTTP 請求封裝 (GET, POST, PUT, DELETE)
- 錯誤處理和重試機制
- 請求攔截器和回應攔截器
- 身份驗證令牌管理

### websocket-client.js
WebSocket 客戶端，提供：
- WebSocket 連接管理
- 自動重連機制
- 訊息分發和處理
- 連接狀態監控

### utils.js
工具函數庫，包含：
- 日期時間格式化函數
- 數據驗證函數
- DOM 操作輔助函數
- 字串處理函數

### error-handler.js
錯誤處理模組，提供：
- 全域錯誤捕獲
- 錯誤訊息顯示
- 錯誤日誌記錄
- 錯誤恢復機制

## 第三方函式庫

### 核心函式庫
- **jQuery 3.6.0** - DOM 操作和事件處理
- **Bootstrap 5.3.0** - CSS 框架和 UI 組件
- **Chart.js 4.0.0** - 圖表繪製函式庫
- **Moment.js 2.29.0** - 日期時間處理

### 可選函式庫
- **DataTables** - 高級表格功能
- **Select2** - 增強的選擇框組件
- **Toastr** - 通知訊息顯示
- **SweetAlert2** - 美化的警報對話框

## API 介面標準

### 請求格式
```javascript
// GET 請求
const response = await apiClient.get('/api/module/endpoint');

// POST 請求
const response = await apiClient.post('/api/module/endpoint', {
  key: 'value'
});

// 帶參數的請求
const response = await apiClient.get('/api/module/endpoint', {
  params: { page: 1, size: 10 }
});
```

### 回應格式
```javascript
// 成功回應
{
  "status": "success",
  "message": "操作成功",
  "data": { ... }
}

// 錯誤回應
{
  "status": "error",
  "message": "操作失敗",
  "code": "ERROR_CODE",
  "details": { ... }
}

// 分頁回應
{
  "status": "success",
  "data": [...],
  "pagination": {
    "page": 1,
    "size": 10,
    "total": 100,
    "pages": 10
  }
}
```

## WebSocket 事件

### 系統事件
- `system.status` - 系統狀態更新
- `system.alert` - 系統警報通知
- `system.maintenance` - 系統維護通知

### 業務事件
- `email.new` - 新郵件通知
- `task.status` - 任務狀態更新
- `data.update` - 數據更新通知

## 開發指南

### 使用共享組件
```html
<!-- 引入基礎模板 -->
{% extends "shared/templates/base.html" %}

<!-- 使用共享組件 -->
{% include "shared/templates/components/navbar.html" %}

<!-- 使用模態框 -->
{% include "shared/templates/components/modal.html" with context %}
```

### 使用 API 客戶端
```javascript
// 在模組 JavaScript 中使用
document.addEventListener('DOMContentLoaded', async () => {
  try {
    const data = await apiClient.get('/api/email/list');
    renderEmailList(data.data);
  } catch (error) {
    ErrorHandler.handleApiError(error);
  }
});
```

### 使用 WebSocket
```javascript
// 監聽 WebSocket 事件
document.addEventListener('websocket-message', (event) => {
  const { type, data } = event.detail;
  
  switch (type) {
    case 'email.new':
      updateEmailCount(data.count);
      break;
    case 'task.status':
      updateTaskStatus(data.task_id, data.status);
      break;
  }
});
```

## Vue.js 遷移準備

### 已完成的準備工作 ✅
- **統一設計系統**: 完整的組件庫和樣式系統，易於轉換為 Vue 組件
- **API 客戶端標準化**: 統一的 API 調用介面，Vue.js 可直接使用
- **WebSocket 客戶端**: 完整的 WebSocket 管理，適合 Vue.js 整合
- **工具函數庫**: 豐富的工具函數，可轉換為 Vue 3 Composables

### Vue.js 遷移優勢
- **設計系統轉換**: 現有組件可直接轉換為 Vue 全域組件
- **API 兼容性**: API 客戶端與 Vue.js 的 HTTP 庫完全兼容
- **狀態管理準備**: WebSocket 和錯誤處理適合 Vue.js 狀態管理
- **工具函數重用**: 現有工具函數可轉換為 Vue 3 Composables

### 未來 Vue.js 架構
```
shared/ (Vue.js 版本)
├── components/              # Vue 全域組件
│   ├── BaseLayout.vue       # 基礎佈局組件
│   ├── AppNavbar.vue        # 導航列組件
│   ├── AppSidebar.vue       # 側邊欄組件
│   ├── AppModal.vue         # 模態框組件
│   ├── AppAlert.vue         # 警報組件
│   ├── AppLoading.vue       # 載入動畫組件
│   └── AppPagination.vue    # 分頁組件
├── composables/             # Vue 3 Composition API
│   ├── useApi.js            # API 調用邏輯
│   ├── useWebSocket.js      # WebSocket 邏輯
│   ├── useAuth.js           # 身份驗證邏輯
│   ├── useUtils.js          # 工具函數邏輯
│   └── useErrorHandler.js   # 錯誤處理邏輯
├── stores/                  # Pinia 全域狀態
│   ├── auth.js              # 身份驗證狀態
│   ├── ui.js                # UI 狀態管理
│   └── websocket.js         # WebSocket 狀態
├── styles/                  # 全域樣式
│   ├── variables.scss       # SCSS 變數
│   ├── global.scss          # 全域樣式
│   ├── components.scss      # 組件樣式
│   └── utilities.scss       # 工具樣式
├── plugins/                 # Vue 插件
│   ├── api.js               # API 插件
│   ├── websocket.js         # WebSocket 插件
│   └── errorHandler.js      # 錯誤處理插件
└── utils/                   # 純函數工具
    ├── constants.js         # 常數定義
    ├── validators.js        # 驗證函數
    └── formatters.js        # 格式化函數
```

### 遷移對照表

| 當前 (Flask) | 未來 (Vue.js) | 說明 |
|-------------|---------------|------|
| `templates/components/` | `components/` | HTML 組件 → Vue 組件 |
| `static/js/api-client.js` | `composables/useApi.js` | API 客戶端 → Composable |
| `static/js/websocket-client.js` | `composables/useWebSocket.js` | WebSocket → Composable |
| `static/js/utils.js` | `composables/useUtils.js` | 工具函數 → Composable |
| `static/js/error-handler.js` | `composables/useErrorHandler.js` | 錯誤處理 → Composable |
| `static/css/` | `styles/` | CSS → SCSS 模組化 |

## 開發注意事項

### 當前階段 (Flask)
- 所有共享資源都應該保持向後相容性
- 新增共享組件時需要提供完整的文檔和範例
- 修改全域樣式時需要測試所有模組的影響
- API 客戶端的修改需要通知所有模組開發者
- 確保第三方函式庫的版本相容性和安全性

### Vue.js 遷移準備
- 設計組件時考慮 Vue.js 的 props 和 events 模式
- API 客戶端保持 Promise-based 設計，兼容 Vue.js
- WebSocket 客戶端考慮 Vue.js 的生命週期整合
- 工具函數設計為純函數，便於轉換為 Composables
- 樣式系統準備 SCSS 變數和模組化結構