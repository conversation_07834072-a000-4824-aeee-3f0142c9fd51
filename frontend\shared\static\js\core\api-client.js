/**
 * API 客戶端模組
 * 統一管理所有 API 調用，提供一致的錯誤處理和請求格式
 */

class ApiClient {
    // 動態獲取 API 基礎 URL
    static get baseUrl() {
        // 確保 UrlConfig 已載入
        if (typeof UrlConfig !== 'undefined') {
            return UrlConfig.getApiBaseUrl();
        }
        
        // 回退到靜態配置（如果 UrlConfig 未載入）
        const hostname = window.location.hostname || 'localhost';
        const protocol = window.location.protocol || 'http:';
        return `${protocol}//${hostname}:8010/ft-eqc/api`;
    }
    
    /**
     * 通用 API 請求方法
     * @param {string} endpoint - API 端點
     * @param {Object} options - 請求選項
     * @returns {Promise<Object>} API 響應數據
     */
    static async request(endpoint, options = {}) {
        const url = `${this.baseUrl}/${endpoint}`;
        const defaultOptions = {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            ...options
        };
        
        try {
            const response = await fetch(url, defaultOptions);
            if (!response.ok) {
                const errorText = await response.text();
                throw new Error(`HTTP ${response.status}: ${errorText}`);
            }
            return await response.json();
        } catch (error) {
            console.error(`API 請求失敗 (${url}):`, error);
            throw error;
        }
    }
    
    /**
     * POST 請求
     * @param {string} endpoint - API 端點
     * @param {Object} data - 請求數據
     * @returns {Promise<Object>} API 響應數據
     */
    static async post(endpoint, data) {
        return this.request(endpoint, {
            method: 'POST',
            body: JSON.stringify(data)
        });
    }
    
    /**
     * GET 請求
     * @param {string} endpoint - API 端點
     * @returns {Promise<Object>} API 響應數據
     */
    static async get(endpoint) {
        return this.request(endpoint, {
            method: 'GET',
            headers: {}
        });
    }
    
    /**
     * 檔案上傳請求
     * @param {string} endpoint - API 端點
     * @param {FormData} formData - 表單數據
     * @returns {Promise<Object>} API 響應數據
     */
    static async upload(endpoint, formData) {
        const url = `${this.baseUrl}/${endpoint}`;
        
        try {
            const response = await fetch(url, {
                method: 'POST',
                body: formData
            });
            
            if (!response.ok) {
                const errorData = await response.json();
                throw new Error(errorData.message || `HTTP ${response.status}`);
            }
            
            return await response.json();
        } catch (error) {
            console.error(`檔案上傳失敗 (${url}):`, error);
            throw error;
        }
    }
    
    // ==================== 專用 API 方法 ====================
    
    /**
     * EQC 標準處理
     * @param {string} folderPath - 資料夾路徑
     * @param {boolean} includeStep123 - 是否包含步驟1-3
     * @returns {Promise<Object>} 處理結果
     */
    static async processEqcStandard(folderPath, includeStep123 = true) {
        return this.post('process_eqc_standard', {
            folder_path: folderPath,
            include_step123: includeStep123
        });
    }
    
    /**
     * 分析真實 EQC 數據
     * @param {string} folderPath - 資料夾路徑
     * @returns {Promise<Object>} 分析結果
     */
    static async analyzeEqcRealData(folderPath) {
        return this.post('analyze_eqc_real_data', {
            folder_path: folderPath
        });
    }

    /**
     * EQC BIN=1 掃描
     * @param {string} folderPath - 資料夾路徑
     * @returns {Promise<Object>} 掃描結果
     */
    static async scanEqcBin1(folderPath) {
        return this.post('scan_eqc_bin1', {
            folder_path: folderPath
        });
    }

    /**
     * Online EQC 處理
     * @param {string} folderPath - 資料夾路徑
     * @param {string} mode - 處理模式
     * @returns {Promise<Object>} 處理結果
     */
    static async processOnlineEqc(folderPath, mode) {
        return this.post('process_online_eqc', {
            folder_path: folderPath,
            processing_mode: mode.toString() // 確保是字符串格式
        });
    }

    /**
     * EQC 進階處理 (CODE 區間檢測 + 雙重搜尋)
     * @param {string} folderPath - 資料夾路徑
     * @param {Object} options - 處理選項
     * @returns {Promise<Object>} 處理結果
     */
    static async processEqcAdvanced(folderPath, options = {}) {
        return this.post('process_eqc_advanced', {
            folder_path: folderPath,
            ...options
        });
    }
    
    /**
     * 檔案上傳和解壓縮
     * @param {File} file - 要上傳的檔案
     * @returns {Promise<Object>} 上傳結果
     */
    static async uploadArchive(file) {
        const formData = new FormData();
        formData.append('file', file);
        return this.upload('upload_archive', formData);
    }
    
    /**
     * 清除重複上傳快取
     * @returns {Promise<Object>} 清除結果
     */
    static async clearDuplicateCache() {
        return this.post('clear_duplicate_cache', {});
    }
    
    /**
     * 檢查檔案是否存在
     * @param {string} filePath - 檔案路徑
     * @returns {Promise<Object>} 檢查結果
     */
    static async checkFileExists(filePath) {
        return this.post('check_file_exists', {
            file_path: filePath
        });
    }
    
    /**
     * 讀取報告內容
     * @param {string} reportPath - 報告路徑
     * @returns {Promise<Object>} 報告內容
     */
    static async readReport(reportPath) {
        return this.post('read_report', {
            report_path: reportPath
        });
    }
    
    /**
     * 獲取上傳配置
     * @returns {Promise<Object>} 配置信息
     */
    static async getUploadConfig() {
        return this.get('upload_config');
    }
    
    /**
     * 獲取今日處理記錄
     * @returns {Promise<Object>} 處理記錄
     */
    static async getTodayProcessedFiles() {
        console.log('📅 調用今日處理記錄 API...');
        try {
            const result = await this.get('today_processed_files');
            console.log('📅 今日處理記錄 API 響應:', result);
            return result;
        } catch (error) {
            console.error('❌ 今日處理記錄 API 調用失敗:', error);
            throw error;
        }
    }
    
    /**
     * 創建下載 URL
     * @param {string} filePath - 檔案路徑
     * @returns {string} 下載 URL
     */
    static createDownloadUrl(filePath) {
        // 使用 UrlConfig 如果可用
        if (typeof UrlConfig !== 'undefined') {
            return UrlConfig.createDownloadUrl(filePath);
        }
        return `${this.baseUrl}/download_file?file_path=${encodeURIComponent(filePath)}`;
    }
    
    /**
     * 創建報告下載 URL
     * @param {string} reportPath - 報告路徑
     * @returns {string} 下載 URL
     */
    static createReportDownloadUrl(reportPath) {
        return `${this.baseUrl}/download_report?report_path=${encodeURIComponent(reportPath)}`;
    }
}

// 導出 ApiClient 類
if (typeof module !== 'undefined' && module.exports) {
    module.exports = ApiClient;
} else if (typeof window !== 'undefined') {
    window.ApiClient = ApiClient;
}
