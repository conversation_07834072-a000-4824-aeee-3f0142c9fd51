<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>報表頁面 - Outlook Summary</title>
    <link rel="stylesheet" href="{{ url_for('analytics.static', filename='css/analytics.css') }}?v={{ range(1000, 9999) | random }}">
    <link rel="stylesheet" href="{{ url_for('shared.static', filename='css/global.css') }}">
</head>
<body>
    <div class="reports-container">
        <header class="reports-header">
            <h1>報表中心</h1>
            <div class="header-actions">
                <button id="refresh-reports-btn" class="btn btn-primary">
                    <span class="btn-icon">🔄</span>
                    <span class="btn-text">重新整理</span>
                </button>
                <button id="export-all-btn" class="btn btn-secondary">
                    <span class="btn-icon">📊</span>
                    <span class="btn-text">匯出全部</span>
                </button>
                <a href="{{ url_for('analytics.dashboard') }}" class="btn btn-outline">
                    <span class="btn-icon">🏠</span>
                    <span class="btn-text">回到儀表板</span>
                </a>
            </div>
        </header>

        <div class="reports-content">
            <div class="report-filters">
                <div class="filter-group">
                    <label for="date-range">日期範圍:</label>
                    <select id="date-range">
                        <option value="7">近7天</option>
                        <option value="30" selected>近30天</option>
                        <option value="90">近3個月</option>
                        <option value="365">近1年</option>
                        <option value="custom">自訂範圍</option>
                    </select>
                </div>
                <div class="filter-group">
                    <label for="report-type">報表類型:</label>
                    <select id="report-type">
                        <option value="all" selected>全部</option>
                        <option value="email">郵件統計</option>
                        <option value="vendor">廠商分析</option>
                        <option value="eqc">品質管理</option>
                        <option value="performance">效能分析</option>
                    </select>
                </div>
                <div class="filter-group">
                    <label for="format">匯出格式:</label>
                    <select id="format">
                        <option value="excel">Excel</option>
                        <option value="pdf">PDF</option>
                        <option value="csv">CSV</option>
                    </select>
                </div>
                <button id="apply-filters-btn" class="btn btn-primary">套用篩選</button>
            </div>

            <div class="reports-grid">
                <!-- 郵件統計報表 -->
                <div class="report-card" data-type="email">
                    <div class="report-header">
                        <h3>📧 郵件統計報表</h3>
                        <div class="report-actions">
                            <button class="btn btn-sm btn-secondary" onclick="generateReport('email')">產生</button>
                            <button class="btn btn-sm btn-outline" onclick="exportReport('email')">匯出</button>
                        </div>
                    </div>
                    <div class="report-preview">
                        <div class="metric">
                            <span class="metric-label">總郵件數</span>
                            <span class="metric-value" id="total-emails">{{ stats.total_emails or 0 }}</span>
                        </div>
                        <div class="metric">
                            <span class="metric-label">處理完成</span>
                            <span class="metric-value" id="processed-emails">{{ stats.processed_emails or 0 }}</span>
                        </div>
                        <div class="metric">
                            <span class="metric-label">處理率</span>
                            <span class="metric-value" id="processing-rate">{{ '%.1f'|format(stats.processing_rate or 0) }}%</span>
                        </div>
                    </div>
                </div>

                <!-- 廠商分析報表 -->
                <div class="report-card" data-type="vendor">
                    <div class="report-header">
                        <h3>🏢 廠商分析報表</h3>
                        <div class="report-actions">
                            <button class="btn btn-sm btn-secondary" onclick="generateReport('vendor')">產生</button>
                            <button class="btn btn-sm btn-outline" onclick="exportReport('vendor')">匯出</button>
                        </div>
                    </div>
                    <div class="report-preview">
                        <div class="metric">
                            <span class="metric-label">廠商數量</span>
                            <span class="metric-value" id="total-vendors">{{ stats.total_vendors or 0 }}</span>
                        </div>
                        <div class="metric">
                            <span class="metric-label">活躍廠商</span>
                            <span class="metric-value" id="active-vendors">{{ stats.active_vendors or 0 }}</span>
                        </div>
                        <div class="metric">
                            <span class="metric-label">合規率</span>
                            <span class="metric-value" id="compliance-rate">{{ '%.1f'|format(stats.compliance_rate or 0) }}%</span>
                        </div>
                    </div>
                </div>

                <!-- EQC品質報表 -->
                <div class="report-card" data-type="eqc">
                    <div class="report-header">
                        <h3>✅ EQC品質報表</h3>
                        <div class="report-actions">
                            <button class="btn btn-sm btn-secondary" onclick="generateReport('eqc')">產生</button>
                            <button class="btn btn-sm btn-outline" onclick="exportReport('eqc')">匯出</button>
                        </div>
                    </div>
                    <div class="report-preview">
                        <div class="metric">
                            <span class="metric-label">檢查項目</span>
                            <span class="metric-value" id="total-checks">{{ stats.total_checks or 0 }}</span>
                        </div>
                        <div class="metric">
                            <span class="metric-label">通過率</span>
                            <span class="metric-value" id="pass-rate">{{ '%.1f'|format(stats.pass_rate or 0) }}%</span>
                        </div>
                        <div class="metric">
                            <span class="metric-label">異常項目</span>
                            <span class="metric-value" id="failed-checks">{{ stats.failed_checks or 0 }}</span>
                        </div>
                    </div>
                </div>

                <!-- 效能分析報表 -->
                <div class="report-card" data-type="performance">
                    <div class="report-header">
                        <h3>⚡ 效能分析報表</h3>
                        <div class="report-actions">
                            <button class="btn btn-sm btn-secondary" onclick="generateReport('performance')">產生</button>
                            <button class="btn btn-sm btn-outline" onclick="exportReport('performance')">匯出</button>
                        </div>
                    </div>
                    <div class="report-preview">
                        <div class="metric">
                            <span class="metric-label">平均處理時間</span>
                            <span class="metric-value" id="avg-processing-time">{{ '%.2f'|format(stats.avg_processing_time or 0) }}s</span>
                        </div>
                        <div class="metric">
                            <span class="metric-label">系統負載</span>
                            <span class="metric-value" id="system-load">{{ '%.1f'|format(stats.system_load or 0) }}%</span>
                        </div>
                        <div class="metric">
                            <span class="metric-label">錯誤率</span>
                            <span class="metric-value" id="error-rate">{{ '%.2f'|format(stats.error_rate or 0) }}%</span>
                        </div>
                    </div>
                </div>
            </div>

            <div class="report-history">
                <h3>報表歷史記錄</h3>
                <div class="history-table">
                    <table>
                        <thead>
                            <tr>
                                <th>報表名稱</th>
                                <th>類型</th>
                                <th>產生時間</th>
                                <th>狀態</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody id="report-history-body">
                            {% for report in report_history %}
                            <tr>
                                <td>{{ report.name }}</td>
                                <td>{{ report.type }}</td>
                                <td>{{ report.created_at.strftime('%Y-%m-%d %H:%M') }}</td>
                                <td>
                                    <span class="status {{ 'success' if report.status == 'completed' else 'pending' }}">
                                        {{ '完成' if report.status == 'completed' else '處理中' }}
                                    </span>
                                </td>
                                <td>
                                    {% if report.status == 'completed' %}
                                    <a href="{{ report.download_url }}" class="btn btn-sm btn-outline">下載</a>
                                    {% endif %}
                                    <button class="btn btn-sm btn-danger" onclick="deleteReport('{{ report.id }}')">刪除</button>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="{{ url_for('shared.static', filename='js/main.js') }}"></script>
    <script src="{{ url_for('analytics.static', filename='js/reports.js') }}"></script>
</body>
</html>