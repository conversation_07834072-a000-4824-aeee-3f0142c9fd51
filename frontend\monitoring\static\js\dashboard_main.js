/**
 * 統一監控儀表板 - 主要功能
 * 負責整合所有儀表板功能和用戶互動
 */

class DashboardMain {
    constructor() {
        this.websocket = null;
        this.charts = null;
        this.refreshInterval = null;
        this.isInitialized = false;
        this.currentData = {
            email: {},
            dramatiq: {},
            system: {},
            file: {},
            business: {},
            alerts: []
        };
        
        this.init();
    }
    
    async init() {
        try {
            console.log('初始化統一監控儀表板...');
            
            // 顯示載入畫面
            showLoadingOverlay('初始化儀表板...');
            
            // 初始化 WebSocket 連接
            this.websocket = initDashboardWebSocket();
            
            // 初始化圖表系統
            this.charts = initDashboardCharts();
            
            // 設置事件監聽器
            this.setupEventListeners();
            
            // 設置 WebSocket 訊息處理器
            this.setupWebSocketHandlers();
            
            // 初始載入資料
            await this.loadInitialData();
            
            // 設置定期刷新
            this.setupRefreshInterval();
            
            this.isInitialized = true;
            console.log('✅ 統一監控儀表板初始化完成');
            
        } catch (error) {
            console.error('❌ 儀表板初始化失敗:', error);
            this.showError('儀表板初始化失敗，請重新整理頁面');
        }
    }
    
    setupEventListeners() {
        // 監聽 WebSocket 指標更新事件
        window.addEventListener('metricsUpdate', (event) => {
            this.handleMetricsUpdate(event.detail);
        });
        
        // 監聽新告警事件
        window.addEventListener('newAlert', (event) => {
            this.handleNewAlert(event.detail);
        });
        
        // 監聽頁面可見性變化
        document.addEventListener('visibilitychange', () => {
            if (!document.hidden && this.isInitialized) {
                // 頁面重新可見時刷新資料
                this.refreshAllData();
            }
        });
        
        // 設置刷新按鈕事件
        this.setupRefreshButtons();
        
        // 設置面板展開/收縮功能
        this.setupPanelControls();
    }
    
    setupWebSocketHandlers() {
        if (!this.websocket) return;
        
        // 設置指標更新處理器
        this.websocket.addMessageHandler('metrics_update', (data) => {
            this.updateAllMetrics(data);
        });
        
        // 設置告警處理器
        this.websocket.addMessageHandler('alert', (alert) => {
            this.addAlert(alert);
        });
        
        // 設置系統狀態處理器
        this.websocket.addMessageHandler('system_status', (status) => {
            this.updateSystemStatus(status);
        });
        
        // 設置任務詳情處理器
        this.websocket.addMessageHandler('task_details', (taskDetails) => {
            this.showTaskDetails(taskDetails);
        });
        
        // 設置趨勢資料處理器
        this.websocket.addMessageHandler('trend_data', (trendData) => {
            this.updateTrendCharts(trendData);
        });
    }
    
    async loadInitialData() {
        try {
            // 載入當前指標
            const response = await fetch(`${window.location.origin}/dashboard/api/metrics/current`);
            if (response.ok) {
                const result = await response.json();
                if (result.status === 'success') {
                    this.updateAllMetrics(result.data);
                    // 更新系統狀態為正常
                    this.updateSystemStatus({ level: 'healthy', text: '系統正常' });
                }
            }
            
            // 載入活躍告警
            const alertResponse = await fetch(`${window.location.origin}/dashboard/api/alerts/active`);
            if (alertResponse.ok) {
                const alertResult = await alertResponse.json();
                if (alertResult.status === 'success') {
                    this.currentData.alerts = alertResult.data.alerts || [];
                    this.charts.updateAlertStats(this.currentData.alerts);
                }
            }
            
        } catch (error) {
            console.error('載入初始資料失敗:', error);
            this.updateSystemStatus({ level: 'error', text: '載入失敗' });
        }
    }
    
    setupRefreshInterval() {
        // 每30秒自動刷新一次（如果 WebSocket 未連接）
        this.refreshInterval = setInterval(() => {
            if (!this.websocket?.isConnected) {
                this.refreshAllData();
            }
        }, 30000);
    }
    
    setupRefreshButtons() {
        // 全域刷新按鈕
        const refreshButtons = document.querySelectorAll('.refresh-btn');
        refreshButtons.forEach(btn => {
            btn.addEventListener('click', (e) => {
                e.preventDefault();
                this.refreshAllData();
            });
        });
    }
    
    setupPanelControls() {
        // 面板展開/收縮按鈕
        const expandButtons = document.querySelectorAll('.expand-btn');
        expandButtons.forEach(btn => {
            btn.addEventListener('click', (e) => {
                e.preventDefault();
                const panel = btn.closest('.monitoring-panel');
                if (panel) {
                    panel.classList.toggle('expanded');
                    btn.textContent = panel.classList.contains('expanded') ? '⛶' : '⛶';
                }
            });
        });
    }
    
    handleMetricsUpdate(metrics) {
        console.log('收到指標更新:', metrics);
        this.updateAllMetrics(metrics);
    }
    
    handleNewAlert(alert) {
        console.log('收到新告警:', alert);
        this.addAlert(alert);
    }
    
    updateAllMetrics(data) {
        try {
            // 更新概覽卡片
            this.updateOverviewCards(data);
            
            // 更新詳細監控面板
            this.updateDetailedPanels(data);
            
            // 更新系統整體狀態
            this.updateSystemStatus({ level: 'healthy', text: '系統正常' });
            
            // 儲存當前資料
            this.currentData = { ...this.currentData, ...data };
            
            // 隱藏載入畫面
            hideLoadingOverlay();
            
        } catch (error) {
            console.error('更新指標失敗:', error);
        }
    }
    
    updateOverviewCards(data) {
        // 更新郵件處理概覽 - 修復數據結構匹配
        if (data.email_processing) {
            this.updateElement('emailPending', data.email_processing.pending || 0);
            this.updateElement('emailProcessing', data.email_processing.processing || 0);
            this.updateElement('emailCompleted', data.email_processing.completed || 0);
            this.updateElement('emailFailed', data.email_processing.failed || 0);
            this.updateCardStatus('emailStatus', this.getEmailStatus(data.email_processing));
        }
        
        // 更新 Dramatiq 任務概覽 - 修復數據結構匹配
        if (data.dramatiq_tasks) {
            this.updateElement('dramatiqActive', data.dramatiq_tasks.active || 0);
            this.updateElement('dramatiqPending', data.dramatiq_tasks.pending || 0);
            this.updateElement('dramatiqWorkers', data.dramatiq_tasks.workers || 0);
            this.updateElement('dramatiqSuccessRate', `${Math.round(data.dramatiq_tasks.success_rate || 0)}%`);
            this.updateCardStatus('dramatiqStatus', this.getDramatiqStatus(data.dramatiq_tasks));
        }
        
        // 更新系統資源概覽 - 修復數據結構匹配
        if (data.system_resources) {
            this.updateElement('systemCpu', `${Math.round(data.system_resources.cpu || 0)}%`);
            this.updateElement('systemMemory', `${Math.round(data.system_resources.memory || 0)}%`);
            this.updateElement('systemDisk', `${Math.round(data.system_resources.disk || 0)}%`);
            this.updateElement('systemConnections', data.system_resources.connections || 0);
            this.updateCardStatus('systemResourceStatus', this.getSystemStatus(data.system_resources));
        }
        
        // 更新業務指標概覽 - 修復數據結構匹配
        if (data.business_metrics) {
            this.updateElement('businessMO', data.business_metrics.today_mo || 0);
            this.updateElement('businessLOT', data.business_metrics.today_lot || 0);
            this.updateElement('businessQuality', Math.round(data.business_metrics.quality_score || 0));
            this.updateElement('businessReports', data.business_metrics.reports_generated || 0);
            this.updateCardStatus('businessStatus', this.getBusinessStatus(data.business_metrics));
        }
        
        // 更新 Pipeline 系統概覽
        if (data.pipeline) {
            this.updateElement('pipelineTotal', data.pipeline.total_pipelines || 0);
            this.updateElement('pipelineActive', data.pipeline.active_pipelines || 0);
            this.updateElement('pipelineSuccessRate', `${Math.round(data.pipeline.success_rate || 0)}%`);
            this.updateElement('pipelineRedis', data.pipeline.redis_connected ? '已連接' : '離線');
            this.updateCardStatus('pipelineStatus', this.getPipelineStatus(data.pipeline));
        }
        
        // 更新廠商文件系統概覽
        if (data.vendor_file) {
            this.updateElement('vendorFileTotal', data.vendor_file.total_files_tracked || 0);
            this.updateElement('vendorFileActive', data.vendor_file.active_trackings || 0);
            this.updateElement('vendorFileSuccessRate', `${Math.round(data.vendor_file.success_rate || 0)}%`);
            this.updateElement('vendorFileVendors', Object.keys(data.vendor_file.vendor_statistics || {}).length);
            this.updateCardStatus('vendorFileStatus', this.getVendorFileStatus(data.vendor_file));
        }
    }
    
    updateDetailedPanels(data) {
        // 更新廠商統計
        if (data.email) {
            this.charts.updateVendorStats(data.email);
            
            // 更新 Code Comparison 統計
            this.updateElement('codeComparisonActive', data.email.code_comparison_active || 0);
            this.updateElement('codeComparisonPending', data.email.code_comparison_pending || 0);
            this.updateElement('codeComparisonAvgTime', `${Math.round(data.email.code_comparison_avg_duration || 0)}s`);
        }
        
        // 更新 Dramatiq 任務詳細統計
        if (data.dramatiq) {
            this.updateDramatiqTaskTypes(data.dramatiq);
            this.charts.updateWorkerStats(data.dramatiq);
        }
        
        // 更新系統資源詳細統計
        if (data.system) {
            this.charts.updateProgressBar('cpu', data.system.cpu_percent || 0);
            this.charts.updateProgressBar('memory', data.system.memory_percent || 0);
            this.charts.updateProgressBar('disk', data.system.disk_percent || 0);
            this.charts.updateServiceHealth(data.system);
        }
        
        // 更新檔案處理統計
        if (data.file) {
            this.charts.updateFileStats(data.file);
        }
    }
    
    updateElement(elementId, value) {
        const element = document.getElementById(elementId);
        if (element) {
            element.textContent = value;
            
            // 添加更新動畫
            element.classList.add('updated');
            setTimeout(() => {
                element.classList.remove('updated');
            }, 500);
        }
    }
    
    updateCardStatus(elementId, status) {
        const element = document.getElementById(elementId);
        if (element) {
            element.textContent = status.text;
            element.className = `card-status ${status.level}`;
        }
    }
    
    addAlert(alert) {
        // 添加到告警列表
        this.currentData.alerts.unshift(alert);
        
        // 限制告警列表長度
        if (this.currentData.alerts.length > 100) {
            this.currentData.alerts = this.currentData.alerts.slice(0, 100);
        }
        
        // 更新告警統計
        this.charts.updateAlertStats(this.currentData.alerts);
    }
    
    async refreshAllData() {
        try {
            showLoadingOverlay('刷新資料中...');
            
            // 重新載入所有資料
            await this.loadInitialData();
            
            console.log('資料刷新完成');
            
        } catch (error) {
            console.error('刷新資料失敗:', error);
            this.showError('資料刷新失敗');
        } finally {
            hideLoadingOverlay();
        }
    }
    
    async refreshNetworkData() {
        try {
            console.log('刷新網路監控資料...');
            
            // 載入網路監控資料
            const response = await fetch(`${window.location.origin}/dashboard/api/monitoring/network/metrics`);
            if (response.ok) {
                const result = await response.json();
                if (result.status === 'success') {
                    this.updateNetworkMetrics(result.data);
                }
            }
            
        } catch (error) {
            console.error('刷新網路監控資料失敗:', error);
            this.showError('網路監控資料刷新失敗');
        }
    }
    
    updateNetworkMetrics(data) {
        try {
            console.log('更新網路監控指標:', data);
            
            // 更新暫存任務統計
            if (data.staging_tasks) {
                this.updateElement('stagingPending', data.staging_tasks.pending || 0);
                this.updateElement('stagingProcessing', data.staging_tasks.processing || 0);
                this.updateElement('stagingCompleted', data.staging_tasks.completed || 0);
                this.updateElement('stagingFailed', data.staging_tasks.failed || 0);
            }
            
            // 更新處理任務統計
            if (data.process_tasks) {
                this.updateElement('processActive', data.process_tasks.active || 0);
                this.updateElement('processQueued', data.process_tasks.queued || 0);
                this.updateElement('processCompleted', data.process_tasks.completed || 0);
                this.updateElement('processErrors', data.process_tasks.errors || 0);
            }
            
            // 更新網路狀態
            if (data.network_status) {
                this.updateElement('networkConnection', data.network_status.connection || '未知');
                this.updateElement('networkLatency', data.network_status.latency || '未知');
                this.updateElement('networkUpload', data.network_status.upload_speed || '- MB/s');
                this.updateElement('networkDownload', data.network_status.download_speed || '- MB/s');
            }
            
            // 儲存網路監控數據
            this.currentData.network = data;
            
        } catch (error) {
            console.error('更新網路監控指標失敗:', error);
        }
    }
    
    // 狀態計算方法
    getEmailStatus(emailData) {
        const failed = emailData.failed || 0;
        const total = (emailData.pending || 0) + (emailData.processing || 0) + (emailData.completed || 0) + failed;
        
        if (total === 0) return { text: '無資料', level: 'info' };
        
        const failureRate = failed / total;
        if (failureRate > 0.1) return { text: '異常', level: 'error' };
        if (failureRate > 0.05) return { text: '警告', level: 'warning' };
        return { text: '正常', level: 'success' };
    }
    
    getDramatiqStatus(dramatiqData) {
        const pending = dramatiqData.total_pending || 0;
        const workers = Object.keys(dramatiqData.worker_status || {}).length;
        
        if (workers === 0) return { text: '無工作者', level: 'error' };
        if (pending > 75) return { text: '佇列過載', level: 'error' };
        if (pending > 15) return { text: '佇列繁忙', level: 'warning' };
        return { text: '正常', level: 'success' };
    }
    
    getSystemStatus(systemData) {
        const cpu = systemData.cpu_percent || 0;
        const memory = systemData.memory_percent || 0;
        const disk = systemData.disk_percent || 0;
        
        if (cpu > 95 || memory > 95 || disk > 95) return { text: '嚴重', level: 'error' };
        if (cpu > 80 || memory > 85 || disk > 85) return { text: '警告', level: 'warning' };
        return { text: '正常', level: 'success' };
    }
    
    getBusinessStatus(businessData) {
        const quality = businessData.data_quality_score || 0;
        
        if (quality < 70) return { text: '品質差', level: 'error' };
        if (quality < 85) return { text: '品質一般', level: 'warning' };
        return { text: '品質良好', level: 'success' };
    }
    
    getPipelineStatus(pipelineData) {
        const healthStatus = pipelineData.health_status || 'unknown';
        const redisConnected = pipelineData.redis_connected || false;
        const successRate = pipelineData.success_rate || 0;
        const activePipelines = pipelineData.active_pipelines || 0;
        
        if (!redisConnected) return { text: 'Redis離線', level: 'error' };
        if (healthStatus === 'critical') return { text: '嚴重', level: 'error' };
        if (healthStatus === 'warning') return { text: '警告', level: 'warning' };
        if (successRate < 80) return { text: '成功率低', level: 'warning' };
        if (activePipelines > 50) return { text: '負載高', level: 'warning' };
        return { text: '正常', level: 'success' };
    }
    
    getVendorFileStatus(vendorFileData) {
        const healthStatus = vendorFileData.health_status || 'unknown';
        const successRate = vendorFileData.success_rate || 0;
        const activeTrackings = vendorFileData.active_trackings || 0;
        const timeoutRate = vendorFileData.timeout_occurrences / Math.max(vendorFileData.total_files_tracked, 1) * 100;
        
        if (healthStatus === 'critical') return { text: '嚴重', level: 'error' };
        if (healthStatus === 'warning') return { text: '警告', level: 'warning' };
        if (successRate < 80) return { text: '成功率低', level: 'warning' };
        if (timeoutRate > 25) return { text: '超時率高', level: 'error' };
        if (timeoutRate > 10) return { text: '超時率高', level: 'warning' };
        if (activeTrackings > 100) return { text: '負載高', level: 'warning' };
        return { text: '正常', level: 'success' };
    }
    
    calculateSuccessRate(dramatiqData) {
        const taskTypeCounts = dramatiqData.task_type_counts || {};
        let totalCompleted = 0;
        let totalFailed = 0;
        
        Object.values(taskTypeCounts).forEach(counts => {
            totalCompleted += counts.completed || 0;
            totalFailed += counts.failed || 0;
        });
        
        const total = totalCompleted + totalFailed;
        if (total === 0) return '0%';
        
        return `${Math.round((totalCompleted / total) * 100)}%`;
    }
    
    updateDramatiqTaskTypes(dramatiqData) {
        // 更新所有 8 種 Dramatiq 任務類型的統計
        const taskTypes = [
            'code_comparison', 'csv_to_summary', 'compression', 'decompression',
            'email_processing', 'data_analysis', 'file_processing', 'batch_processing'
        ];
        
        const taskTypeCounts = dramatiqData.task_type_counts || {};
        
        taskTypes.forEach(taskType => {
            const counts = taskTypeCounts[taskType] || { active: 0, pending: 0, completed: 0, failed: 0 };
            const camelCaseType = this.toCamelCase(taskType);
            
            // 更新各個指標
            this.updateElement(`${camelCaseType}Active`, counts.active || 0);
            this.updateElement(`${camelCaseType}Pending`, counts.pending || 0);
            this.updateElement(`${camelCaseType}Completed`, counts.completed || 0);
            this.updateElement(`${camelCaseType}Failed`, counts.failed || 0);
            
            // 更新狀態
            const status = this.getTaskTypeStatus(counts);
            this.updateCardStatus(`${camelCaseType}Status`, status);
        });
    }
    
    toCamelCase(str) {
        return str.replace(/_([a-z])/g, (match, letter) => letter.toUpperCase());
    }
    
    getTaskTypeStatus(counts) {
        const total = (counts.active || 0) + (counts.pending || 0) + (counts.completed || 0) + (counts.failed || 0);
        
        if (total === 0) return { text: '無任務', level: 'info' };
        
        const failureRate = (counts.failed || 0) / total;
        const pendingRate = (counts.pending || 0) / total;
        
        if (failureRate > 0.25) return { text: '異常', level: 'error' };
        if (failureRate > 0.1 || pendingRate > 0.8) return { text: '警告', level: 'warning' };
        return { text: '正常', level: 'success' };
    }
    
    updateSystemStatus(status) {
        // 更新系統狀態指示器
        const statusIndicator = document.getElementById('statusIndicator');
        const statusText = document.getElementById('statusText');
        
        if (statusIndicator && statusText) {
            statusIndicator.className = 'status-indicator';
            
            switch (status.level) {
                case 'healthy':
                    statusIndicator.classList.add('healthy');
                    statusText.textContent = '系統正常';
                    break;
                case 'warning':
                    statusIndicator.classList.add('warning');
                    statusText.textContent = '系統警告';
                    break;
                case 'error':
                    statusIndicator.classList.add('error');
                    statusText.textContent = '系統錯誤';
                    break;
                case 'critical':
                    statusIndicator.classList.add('critical');
                    statusText.textContent = '系統嚴重錯誤';
                    break;
                default:
                    statusText.textContent = '系統狀態未知';
            }
        }
        
        // 觸發系統狀態更新事件
        window.dispatchEvent(new CustomEvent('systemStatusUpdate', {
            detail: status
        }));
    }
    
    showTaskDetails(taskDetails) {
        // 顯示任務詳細資訊模態框
        const modal = this.createTaskDetailModal(taskDetails);
        document.body.appendChild(modal);
        
        // 顯示模態框
        setTimeout(() => {
            modal.classList.add('show');
        }, 10);
        
        // 設置關閉事件
        const closeBtn = modal.querySelector('.modal-close');
        const overlay = modal.querySelector('.modal-overlay');
        
        const closeModal = () => {
            modal.classList.remove('show');
            setTimeout(() => {
                document.body.removeChild(modal);
            }, 300);
        };
        
        closeBtn.addEventListener('click', closeModal);
        overlay.addEventListener('click', closeModal);
        
        // ESC 鍵關閉
        const handleEsc = (e) => {
            if (e.key === 'Escape') {
                closeModal();
                document.removeEventListener('keydown', handleEsc);
            }
        };
        document.addEventListener('keydown', handleEsc);
    }
    
    createTaskDetailModal(taskDetails) {
        const modal = document.createElement('div');
        modal.className = 'task-detail-modal';
        modal.innerHTML = `
            <div class="modal-overlay"></div>
            <div class="modal-content">
                <div class="modal-header">
                    <h3>任務詳細資訊</h3>
                    <button class="modal-close">&times;</button>
                </div>
                <div class="modal-body">
                    <div class="task-info-grid">
                        <div class="info-item">
                            <label>任務 ID:</label>
                            <span>${taskDetails.id || 'N/A'}</span>
                        </div>
                        <div class="info-item">
                            <label>任務類型:</label>
                            <span>${taskDetails.type || 'N/A'}</span>
                        </div>
                        <div class="info-item">
                            <label>狀態:</label>
                            <span class="status ${taskDetails.status}">${taskDetails.status || 'N/A'}</span>
                        </div>
                        <div class="info-item">
                            <label>開始時間:</label>
                            <span>${this.formatDateTime(taskDetails.started_at)}</span>
                        </div>
                        <div class="info-item">
                            <label>執行時間:</label>
                            <span>${this.formatDuration(taskDetails.duration)}</span>
                        </div>
                        <div class="info-item">
                            <label>重試次數:</label>
                            <span>${taskDetails.retry_count || 0}</span>
                        </div>
                    </div>
                    
                    ${taskDetails.parameters ? `
                    <div class="task-parameters">
                        <h4>任務參數</h4>
                        <pre>${JSON.stringify(taskDetails.parameters, null, 2)}</pre>
                    </div>
                    ` : ''}
                    
                    ${taskDetails.error_message ? `
                    <div class="task-error">
                        <h4>錯誤訊息</h4>
                        <pre class="error-message">${taskDetails.error_message}</pre>
                    </div>
                    ` : ''}
                    
                    ${taskDetails.logs ? `
                    <div class="task-logs">
                        <h4>執行日誌</h4>
                        <div class="log-container">
                            ${taskDetails.logs.map(log => `
                                <div class="log-entry ${log.level}">
                                    <span class="log-time">${this.formatDateTime(log.timestamp)}</span>
                                    <span class="log-level">[${log.level.toUpperCase()}]</span>
                                    <span class="log-message">${log.message}</span>
                                </div>
                            `).join('')}
                        </div>
                    </div>
                    ` : ''}
                </div>
                <div class="modal-footer">
                    <button class="btn btn-secondary modal-close">關閉</button>
                    ${taskDetails.status === 'failed' ? `
                        <button class="btn btn-primary" onclick="retryTask('${taskDetails.id}')">重試任務</button>
                    ` : ''}
                </div>
            </div>
        `;
        
        return modal;
    }
    
    updateTrendCharts(trendData) {
        // 更新趨勢圖表
        if (this.charts) {
            this.charts.updateTrendData(trendData);
        }
        
        // 更新趨勢分析面板
        this.updateTrendAnalysisPanel(trendData);
    }
    
    updateTrendAnalysisPanel(trendData) {
        const trendPanel = document.getElementById('trendAnalysisPanel');
        if (!trendPanel) return;
        
        // 更新負載預測
        if (trendData.load_prediction) {
            const predictionElement = document.getElementById('loadPrediction');
            if (predictionElement) {
                const prediction = trendData.load_prediction;
                predictionElement.innerHTML = `
                    <h5>未來 24 小時負載預測</h5>
                    <div class="prediction-metrics">
                        <div class="prediction-item">
                            <span class="label">預期峰值:</span>
                            <span class="value ${prediction.peak_load > 80 ? 'warning' : ''}">${prediction.peak_load}%</span>
                        </div>
                        <div class="prediction-item">
                            <span class="label">預期時間:</span>
                            <span class="value">${this.formatDateTime(prediction.peak_time)}</span>
                        </div>
                        <div class="prediction-item">
                            <span class="label">建議:</span>
                            <span class="value">${prediction.recommendation}</span>
                        </div>
                    </div>
                `;
            }
        }
        
        // 更新異常檢測
        if (trendData.anomalies) {
            const anomaliesElement = document.getElementById('anomaliesDetection');
            if (anomaliesElement) {
                anomaliesElement.innerHTML = `
                    <h5>異常檢測</h5>
                    <div class="anomalies-list">
                        ${trendData.anomalies.map(anomaly => `
                            <div class="anomaly-item ${anomaly.severity}">
                                <span class="anomaly-type">${anomaly.type}</span>
                                <span class="anomaly-description">${anomaly.description}</span>
                                <span class="anomaly-time">${this.formatDateTime(anomaly.detected_at)}</span>
                            </div>
                        `).join('')}
                    </div>
                `;
            }
        }
    }
    
    // 資料格式化工具方法
    formatDateTime(timestamp) {
        if (!timestamp) return 'N/A';
        
        const date = new Date(timestamp);
        return date.toLocaleString('zh-TW', {
            year: 'numeric',
            month: '2-digit',
            day: '2-digit',
            hour: '2-digit',
            minute: '2-digit',
            second: '2-digit'
        });
    }
    
    formatDuration(seconds) {
        if (!seconds) return 'N/A';
        
        const hours = Math.floor(seconds / 3600);
        const minutes = Math.floor((seconds % 3600) / 60);
        const secs = Math.floor(seconds % 60);
        
        if (hours > 0) {
            return `${hours}h ${minutes}m ${secs}s`;
        } else if (minutes > 0) {
            return `${minutes}m ${secs}s`;
        } else {
            return `${secs}s`;
        }
    }
    
    formatBytes(bytes) {
        if (!bytes) return '0 B';
        
        const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
        const i = Math.floor(Math.log(bytes) / Math.log(1024));
        
        return `${(bytes / Math.pow(1024, i)).toFixed(1)} ${sizes[i]}`;
    }
    
    formatNumber(num) {
        if (!num) return '0';
        
        if (num >= 1000000) {
            return `${(num / 1000000).toFixed(1)}M`;
        } else if (num >= 1000) {
            return `${(num / 1000).toFixed(1)}K`;
        } else {
            return num.toString();
        }
    }
    
    showError(message) {
        // 顯示錯誤訊息
        const alertBanner = document.getElementById('alertBanner');
        const alertContent = document.getElementById('alertContent');
        
        if (alertBanner && alertContent) {
            alertContent.innerHTML = `<strong>錯誤</strong><span>${message}</span>`;
            alertBanner.className = 'alert-banner error';
            alertBanner.style.display = 'block';
            
            // 5秒後自動隱藏
            setTimeout(() => {
                alertBanner.style.display = 'none';
            }, 5000);
        }
    }
    
    destroy() {
        // 清理資源
        if (this.refreshInterval) {
            clearInterval(this.refreshInterval);
        }
        
        if (this.websocket) {
            this.websocket.close();
        }
        
        if (this.charts) {
            this.charts.destroy();
        }
    }
}

// 全域功能函數
function refreshEmailData() {
    console.log('刷新郵件資料');
    if (window.dashboardMain) {
        window.dashboardMain.refreshAllData();
    }
}

function refreshDramatiqData() {
    console.log('刷新 Dramatiq 資料');
    if (window.dashboardMain) {
        window.dashboardMain.refreshAllData();
    }
}

function refreshSystemData() {
    console.log('刷新系統資料');
    if (window.dashboardMain) {
        window.dashboardMain.refreshAllData();
    }
}

function refreshFileData() {
    console.log('刷新檔案資料');
    if (window.dashboardMain) {
        window.dashboardMain.refreshAllData();
    }
}

function refreshAlertData() {
    console.log('刷新告警資料');
    if (window.dashboardMain) {
        window.dashboardMain.refreshAllData();
    }
}

function refreshNetworkData() {
    console.log('刷新網路監控資料');
    if (window.dashboardMain) {
        window.dashboardMain.refreshNetworkData();
    }
}

function showNetworkDetail() {
    console.log('顯示網路監控詳細頁面');
    // 可以跳轉到詳細的網路監控頁面或顯示模態框
    window.open('/network-detail', '_blank');
}

function expandPanel(panelType) {
    console.log(`展開 ${panelType} 面板`);
    const panel = document.querySelector(`.${panelType}-panel`);
    if (panel) {
        panel.classList.toggle('expanded');
    }
}

async function acknowledgeAlert(alertId) {
    try {
        const response = await fetch(`/dashboard/api/alerts/${alertId}/acknowledge`, {
            method: 'POST'
        });
        
        if (response.ok) {
            console.log(`告警 ${alertId} 已確認`);
            // 刷新告警資料
            refreshAlertData();
        } else {
            console.error('確認告警失敗');
        }
    } catch (error) {
        console.error('確認告警錯誤:', error);
    }
}

function dismissAlert(alertId) {
    console.log(`忽略告警 ${alertId}`);
    // 從 DOM 中移除告警項目
    const alertItem = document.querySelector(`[data-alert-id="${alertId}"]`);
    if (alertItem) {
        alertItem.style.display = 'none';
    }
}

function clearAllAlerts() {
    console.log('清除所有告警');
    const alertList = document.getElementById('alertList');
    if (alertList) {
        alertList.innerHTML = '<div class="no-alerts">所有告警已清除</div>';
    }
}

async function retryTask(taskId) {
    try {
        const response = await fetch(`/dashboard/api/tasks/${taskId}/retry`, {
            method: 'POST'
        });
        
        if (response.ok) {
            console.log(`任務 ${taskId} 重試請求已發送`);
            // 刷新資料以顯示更新狀態
            if (window.dashboardMain) {
                window.dashboardMain.refreshAllData();
            }
        } else {
            console.error('重試任務失敗');
        }
    } catch (error) {
        console.error('重試任務錯誤:', error);
    }
}

function showTaskFilter() {
    const filterModal = createTaskFilterModal();
    document.body.appendChild(filterModal);
    
    setTimeout(() => {
        filterModal.classList.add('show');
    }, 10);
}

function createTaskFilterModal() {
    const modal = document.createElement('div');
    modal.className = 'task-filter-modal';
    modal.innerHTML = `
        <div class="modal-overlay"></div>
        <div class="modal-content">
            <div class="modal-header">
                <h3>任務篩選</h3>
                <button class="modal-close">&times;</button>
            </div>
            <div class="modal-body">
                <div class="filter-section">
                    <label>任務類型:</label>
                    <select id="taskTypeFilter" multiple>
                        <option value="code_comparison">Code Comparison</option>
                        <option value="csv_to_summary">CSV to Summary</option>
                        <option value="compression">Compression</option>
                        <option value="decompression">Decompression</option>
                        <option value="email_processing">Email Processing</option>
                        <option value="data_analysis">Data Analysis</option>
                        <option value="file_processing">File Processing</option>
                        <option value="batch_processing">Batch Processing</option>
                    </select>
                </div>
                <div class="filter-section">
                    <label>任務狀態:</label>
                    <select id="taskStatusFilter" multiple>
                        <option value="pending">待處理</option>
                        <option value="active">活躍</option>
                        <option value="completed">已完成</option>
                        <option value="failed">失敗</option>
                    </select>
                </div>
                <div class="filter-section">
                    <label>時間範圍:</label>
                    <select id="timeRangeFilter">
                        <option value="1h">過去 1 小時</option>
                        <option value="6h">過去 6 小時</option>
                        <option value="24h">過去 24 小時</option>
                        <option value="7d">過去 7 天</option>
                        <option value="30d">過去 30 天</option>
                    </select>
                </div>
            </div>
            <div class="modal-footer">
                <button class="btn btn-secondary modal-close">取消</button>
                <button class="btn btn-primary" onclick="applyTaskFilter()">套用篩選</button>
            </div>
        </div>
    `;
    
    // 設置關閉事件
    const closeBtn = modal.querySelector('.modal-close');
    const overlay = modal.querySelector('.modal-overlay');
    
    const closeModal = () => {
        modal.classList.remove('show');
        setTimeout(() => {
            document.body.removeChild(modal);
        }, 300);
    };
    
    closeBtn.addEventListener('click', closeModal);
    overlay.addEventListener('click', closeModal);
    
    return modal;
}

async function applyTaskFilter() {
    const taskTypes = Array.from(document.getElementById('taskTypeFilter').selectedOptions).map(opt => opt.value);
    const statuses = Array.from(document.getElementById('taskStatusFilter').selectedOptions).map(opt => opt.value);
    const timeRange = document.getElementById('timeRangeFilter').value;
    
    const filterParams = {
        task_types: taskTypes,
        statuses: statuses,
        time_range: timeRange
    };
    
    try {
        const response = await fetch(`${window.location.origin}/dashboard/api/tasks/filtered`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(filterParams)
        });
        
        if (response.ok) {
            const result = await response.json();
            if (result.status === 'success') {
                // 更新顯示篩選後的任務
                updateFilteredTasks(result.data);
            }
        }
    } catch (error) {
        console.error('套用篩選失敗:', error);
    }
    
    // 關閉篩選模態框
    const modal = document.querySelector('.task-filter-modal');
    if (modal) {
        modal.classList.remove('show');
        setTimeout(() => {
            document.body.removeChild(modal);
        }, 300);
    }
}

function updateFilteredTasks(filteredData) {
    // 更新任務顯示以反映篩選結果
    if (window.dashboardMain && window.dashboardMain.charts) {
        window.dashboardMain.charts.updateTaskTypeStats(filteredData);
    }
}

function showTrendAnalysis() {
    const trendModal = createTrendAnalysisModal();
    document.body.appendChild(trendModal);
    
    setTimeout(() => {
        trendModal.classList.add('show');
    }, 10);
    
    // 載入趨勢資料
    loadTrendAnalysisData();
}

function createTrendAnalysisModal() {
    const modal = document.createElement('div');
    modal.className = 'trend-analysis-modal';
    modal.innerHTML = `
        <div class="modal-overlay"></div>
        <div class="modal-content large">
            <div class="modal-header">
                <h3>趨勢分析</h3>
                <button class="modal-close">&times;</button>
            </div>
            <div class="modal-body">
                <div class="trend-tabs">
                    <button class="trend-tab active" data-tab="7d">7 天趨勢</button>
                    <button class="trend-tab" data-tab="30d">30 天趨勢</button>
                    <button class="trend-tab" data-tab="prediction">負載預測</button>
                    <button class="trend-tab" data-tab="anomalies">異常檢測</button>
                </div>
                <div class="trend-content">
                    <div id="trendChart" class="trend-chart-container">
                        <div class="loading">載入趨勢資料中...</div>
                    </div>
                    <div id="trendAnalysisPanel" class="trend-analysis-panel">
                        <div id="loadPrediction" class="prediction-section"></div>
                        <div id="anomaliesDetection" class="anomalies-section"></div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button class="btn btn-secondary modal-close">關閉</button>
            </div>
        </div>
    `;
    
    // 設置標籤切換事件
    const tabs = modal.querySelectorAll('.trend-tab');
    tabs.forEach(tab => {
        tab.addEventListener('click', (e) => {
            tabs.forEach(t => t.classList.remove('active'));
            e.target.classList.add('active');
            loadTrendData(e.target.dataset.tab);
        });
    });
    
    // 設置關閉事件
    const closeBtn = modal.querySelector('.modal-close');
    const overlay = modal.querySelector('.modal-overlay');
    
    const closeModal = () => {
        modal.classList.remove('show');
        setTimeout(() => {
            document.body.removeChild(modal);
        }, 300);
    };
    
    closeBtn.addEventListener('click', closeModal);
    overlay.addEventListener('click', closeModal);
    
    return modal;
}

async function loadTrendAnalysisData() {
    try {
        const response = await fetch(`${window.location.origin}/dashboard/api/trends/analysis`);
        if (response.ok) {
            const result = await response.json();
            if (result.status === 'success') {
                updateTrendDisplay(result.data);
            }
        }
    } catch (error) {
        console.error('載入趨勢分析資料失敗:', error);
    }
}

async function loadTrendData(timeRange) {
    try {
        const response = await fetch(`/dashboard/api/trends/${timeRange}`);
        if (response.ok) {
            const result = await response.json();
            if (result.status === 'success') {
                updateTrendChart(result.data, timeRange);
            }
        }
    } catch (error) {
        console.error('載入趨勢資料失敗:', error);
    }
}

function updateTrendDisplay(trendData) {
    const chartContainer = document.getElementById('trendChart');
    if (chartContainer) {
        // 這裡可以整合圖表庫來顯示趨勢圖表
        chartContainer.innerHTML = `
            <div class="trend-summary">
                <h4>系統負載趨勢</h4>
                <div class="trend-metrics">
                    <div class="trend-metric">
                        <span class="label">平均 CPU 使用率:</span>
                        <span class="value">${trendData.avg_cpu || 0}%</span>
                    </div>
                    <div class="trend-metric">
                        <span class="label">平均記憶體使用率:</span>
                        <span class="value">${trendData.avg_memory || 0}%</span>
                    </div>
                    <div class="trend-metric">
                        <span class="label">任務完成率:</span>
                        <span class="value">${trendData.completion_rate || 0}%</span>
                    </div>
                </div>
            </div>
        `;
    }
}

function updateTrendChart(data, timeRange) {
    const chartContainer = document.getElementById('trendChart');
    if (chartContainer) {
        chartContainer.innerHTML = `
            <div class="trend-chart">
                <h4>${timeRange === '7d' ? '7 天' : '30 天'}趨勢圖表</h4>
                <div class="chart-placeholder">
                    <p>趨勢圖表將在此顯示</p>
                    <p>時間範圍: ${timeRange}</p>
                    <p>資料點數: ${data.length || 0}</p>
                </div>
            </div>
        `;
    }
}

// 頁面載入完成後初始化
document.addEventListener('DOMContentLoaded', () => {
    console.log('DOM 載入完成，初始化儀表板...');
    
    // 創建全域儀表板實例
    window.dashboardMain = new DashboardMain();
    
    // 設置全域錯誤處理
    window.addEventListener('error', (event) => {
        console.error('全域錯誤:', event.error);
    });
    
    window.addEventListener('unhandledrejection', (event) => {
        console.error('未處理的 Promise 拒絕:', event.reason);
    });
});

// 頁面卸載時清理資源
window.addEventListener('beforeunload', () => {
    if (window.dashboardMain) {
        window.dashboardMain.destroy();
    }
});

async function showTaskHistory(taskType) {
    try {
        const response = await fetch(`/dashboard/api/tasks/${taskType}/history`);
        if (response.ok) {
            const result = await response.json();
            if (result.status === 'success') {
                const modal = window.dashboardMain.charts.createTaskHistoryChart(taskType, result.data);
                document.body.appendChild(modal);
                setTimeout(() => {
                    modal.classList.add('show');
                }, 10);
            }
        }
    } catch (error) {
        console.error('載入任務歷史失敗:', error);
    }
}

// 導出到全域作用域
window.DashboardMain = DashboardMain;
window.refreshEmailData = refreshEmailData;
window.refreshDramatiqData = refreshDramatiqData;
window.refreshSystemData = refreshSystemData;
window.refreshFileData = refreshFileData;
window.refreshAlertData = refreshAlertData;
window.expandPanel = expandPanel;
window.acknowledgeAlert = acknowledgeAlert;
window.dismissAlert = dismissAlert;
window.clearAllAlerts = clearAllAlerts;
window.retryTask = retryTask;
window.showTaskFilter = showTaskFilter;
window.showTaskHistory = showTaskHistory;
window.showTrendAnalysis = showTrendAnalysis;
window.applyTaskFilter = applyTaskFilter;