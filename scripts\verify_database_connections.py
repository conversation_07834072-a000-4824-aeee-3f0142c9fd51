#!/usr/bin/env python3
"""
數據庫連接與完整性驗證腳本
驗證所有後端服務在重構後能正確連接數據庫
"""

import os
import sys
import sqlite3
import json
from pathlib import Path
from datetime import datetime
from typing import Dict, List, Any, Optional

# 添加專案根目錄到 Python 路徑
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

# 設定中文編碼環境變數（Windows 兼容性）
if sys.platform.startswith('win'):
    import io
    sys.stdout = io.TextIOWrapper(sys.stdout.buffer, encoding='utf-8')
    sys.stderr = io.TextIOWrapper(sys.stderr.buffer, encoding='utf-8')
    os.environ['PYTHONIOENCODING'] = 'utf-8'
    os.environ['PYTHONUTF8'] = '1'
    os.environ['LANG'] = 'zh_TW.UTF-8'


class DatabaseVerifier:
    """數據庫連接驗證器"""
    
    def __init__(self):
        self.results = {
            'timestamp': datetime.now().isoformat(),
            'overall_status': 'unknown',
            'databases': {},
            'services': {},
            'errors': []
        }
        
        # 數據庫路徑配置
        self.database_paths = {
            'email_inbox': 'data/email_inbox.db',
            'email_inbox_alt': 'email_inbox.db',
            'email_inbox_frontend': 'frontend/email_inbox.db',
            'outlook': 'outlook.db',
            'eqc_task_status': 'data/eqc_task_status.db'
        }
    
    def verify_all(self) -> Dict[str, Any]:
        """執行完整的數據庫驗證"""
        print("🔍 開始數據庫連接與完整性驗證...")
        print("=" * 60)
        
        # 1. 檢查數據庫文件存在性
        self._check_database_files()
        
        # 2. 驗證數據庫連接
        self._verify_database_connections()
        
        # 3. 測試數據查詢功能
        self._test_data_queries()
        
        # 4. 測試數據寫入功能
        self._test_data_writes()
        
        # 5. 驗證後端服務數據庫配置
        self._verify_service_configurations()
        
        # 6. 檢查數據庫完整性
        self._check_database_integrity()
        
        # 計算總體狀態
        self._calculate_overall_status()
        
        # 生成報告
        self._generate_report()
        
        return self.results
    
    def _check_database_files(self):
        """檢查數據庫文件存在性"""
        print("📁 檢查數據庫文件存在性...")
        
        for db_name, db_path in self.database_paths.items():
            exists = Path(db_path).exists()
            size = Path(db_path).stat().st_size if exists else 0
            
            self.results['databases'][db_name] = {
                'path': db_path,
                'exists': exists,
                'size_bytes': size,
                'readable': False,
                'writable': False,
                'connection_test': False,
                'schema_valid': False
            }
            
            status = "✅" if exists else "❌"
            size_str = f"({size:,} bytes)" if exists else ""
            print(f"  {status} {db_name}: {db_path} {size_str}")
            
            if not exists:
                self.results['errors'].append(f"數據庫文件不存在: {db_path}")
    
    def _verify_database_connections(self):
        """驗證數據庫連接"""
        print("\n🔗 驗證數據庫連接...")
        
        for db_name, db_info in self.results['databases'].items():
            if not db_info['exists']:
                continue
                
            try:
                # 測試 SQLite 連接
                conn = sqlite3.connect(db_info['path'], timeout=10.0)
                conn.row_factory = sqlite3.Row
                
                # 測試基本查詢
                cursor = conn.execute("SELECT name FROM sqlite_master WHERE type='table';")
                tables = [row[0] for row in cursor.fetchall()]
                
                db_info['connection_test'] = True
                db_info['tables'] = tables
                db_info['table_count'] = len(tables)
                
                conn.close()
                
                print(f"  ✅ {db_name}: 連接成功，包含 {len(tables)} 個表")
                
            except Exception as e:
                db_info['connection_test'] = False
                db_info['connection_error'] = str(e)
                self.results['errors'].append(f"數據庫連接失敗 {db_name}: {e}")
                print(f"  ❌ {db_name}: 連接失敗 - {e}")
    
    def _test_data_queries(self):
        """測試數據查詢功能"""
        print("\n📊 測試數據查詢功能...")
        
        # 測試郵件數據庫查詢
        self._test_email_database_queries()
        
        # 測試任務狀態數據庫查詢
        self._test_task_database_queries()
    
    def _test_email_database_queries(self):
        """測試郵件數據庫查詢"""
        email_dbs = ['email_inbox', 'email_inbox_alt', 'email_inbox_frontend', 'outlook']
        
        for db_name in email_dbs:
            db_info = self.results['databases'].get(db_name)
            if not db_info or not db_info['connection_test']:
                continue
                
            try:
                conn = sqlite3.connect(db_info['path'], timeout=10.0)
                conn.row_factory = sqlite3.Row
                
                # 檢查郵件表是否存在
                tables = db_info.get('tables', [])
                email_table = None
                
                for table_name in ['emails', 'email', 'email_data']:
                    if table_name in tables:
                        email_table = table_name
                        break
                
                if email_table:
                    # 查詢郵件數量
                    count_result = conn.execute(f"SELECT COUNT(*) as count FROM {email_table}").fetchone()
                    email_count = count_result['count'] if count_result else 0
                    
                    # 查詢最新郵件
                    latest_result = conn.execute(f"""
                        SELECT * FROM {email_table} 
                        ORDER BY received_time DESC, created_at DESC 
                        LIMIT 1
                    """).fetchone()
                    
                    db_info['email_count'] = email_count
                    db_info['has_data'] = email_count > 0
                    db_info['latest_email'] = dict(latest_result) if latest_result else None
                    
                    print(f"  ✅ {db_name}: 查詢成功，包含 {email_count} 封郵件")
                    
                else:
                    db_info['email_count'] = 0
                    db_info['has_data'] = False
                    print(f"  ⚠️  {db_name}: 未找到郵件表")
                
                conn.close()
                
            except Exception as e:
                db_info['query_error'] = str(e)
                self.results['errors'].append(f"郵件數據庫查詢失敗 {db_name}: {e}")
                print(f"  ❌ {db_name}: 查詢失敗 - {e}")
    
    def _test_task_database_queries(self):
        """測試任務狀態數據庫查詢"""
        db_name = 'eqc_task_status'
        db_info = self.results['databases'].get(db_name)
        
        if not db_info or not db_info['connection_test']:
            return
            
        try:
            conn = sqlite3.connect(db_info['path'], timeout=10.0)
            conn.row_factory = sqlite3.Row
            
            tables = db_info.get('tables', [])
            
            # 檢查任務執行表
            if 'eqc_task_execution' in tables:
                count_result = conn.execute("SELECT COUNT(*) as count FROM eqc_task_execution").fetchone()
                task_count = count_result['count'] if count_result else 0
                
                db_info['task_execution_count'] = task_count
                print(f"  ✅ {db_name}: 任務執行記錄 {task_count} 條")
            
            # 檢查任務狀態表
            if 'eqc_task_status' in tables:
                count_result = conn.execute("SELECT COUNT(*) as count FROM eqc_task_status").fetchone()
                status_count = count_result['count'] if count_result else 0
                
                db_info['task_status_count'] = status_count
                print(f"  ✅ {db_name}: 任務狀態記錄 {status_count} 條")
            
            conn.close()
            
        except Exception as e:
            db_info['task_query_error'] = str(e)
            self.results['errors'].append(f"任務數據庫查詢失敗 {db_name}: {e}")
            print(f"  ❌ {db_name}: 查詢失敗 - {e}")
    
    def _test_data_writes(self):
        """測試數據寫入功能"""
        print("\n✏️  測試數據寫入功能...")
        
        # 測試郵件數據庫寫入
        self._test_email_database_writes()
        
        # 測試任務數據庫寫入
        self._test_task_database_writes()
    
    def _test_email_database_writes(self):
        """測試郵件數據庫寫入"""
        email_dbs = ['email_inbox', 'email_inbox_alt', 'email_inbox_frontend', 'outlook']
        
        for db_name in email_dbs:
            db_info = self.results['databases'].get(db_name)
            if not db_info or not db_info['connection_test']:
                continue
                
            try:
                conn = sqlite3.connect(db_info['path'], timeout=10.0)
                
                # 創建測試表（如果不存在）
                conn.execute("""
                    CREATE TABLE IF NOT EXISTS db_test_verification (
                        id INTEGER PRIMARY KEY,
                        test_data TEXT,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                    )
                """)
                
                # 插入測試數據
                test_id = f"test_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
                conn.execute(
                    "INSERT INTO db_test_verification (test_data) VALUES (?)",
                    (test_id,)
                )
                
                # 驗證插入
                result = conn.execute(
                    "SELECT * FROM db_test_verification WHERE test_data = ?",
                    (test_id,)
                ).fetchone()
                
                if result:
                    db_info['write_test'] = True
                    print(f"  ✅ {db_name}: 寫入測試成功")
                    
                    # 清理測試數據
                    conn.execute("DELETE FROM db_test_verification WHERE test_data = ?", (test_id,))
                else:
                    db_info['write_test'] = False
                    print(f"  ❌ {db_name}: 寫入測試失敗")
                
                conn.commit()
                conn.close()
                
            except Exception as e:
                db_info['write_test'] = False
                db_info['write_error'] = str(e)
                self.results['errors'].append(f"數據庫寫入測試失敗 {db_name}: {e}")
                print(f"  ❌ {db_name}: 寫入測試失敗 - {e}")
    
    def _test_task_database_writes(self):
        """測試任務數據庫寫入"""
        db_name = 'eqc_task_status'
        db_info = self.results['databases'].get(db_name)
        
        if not db_info or not db_info['connection_test']:
            return
            
        try:
            conn = sqlite3.connect(db_info['path'], timeout=10.0)
            
            # 測試任務狀態寫入
            test_task_id = f"test_task_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
            test_session_id = f"test_session_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
            
            # 插入測試任務執行記錄
            conn.execute("""
                INSERT INTO eqc_task_execution 
                (task_id, session_id, folder_path, status, started_at)
                VALUES (?, ?, ?, ?, ?)
            """, (test_task_id, test_session_id, "/test/path", "testing", datetime.now().isoformat()))
            
            # 驗證插入
            result = conn.execute(
                "SELECT * FROM eqc_task_execution WHERE task_id = ?",
                (test_task_id,)
            ).fetchone()
            
            if result:
                db_info['task_write_test'] = True
                print(f"  ✅ {db_name}: 任務寫入測試成功")
                
                # 清理測試數據
                conn.execute("DELETE FROM eqc_task_execution WHERE task_id = ?", (test_task_id,))
            else:
                db_info['task_write_test'] = False
                print(f"  ❌ {db_name}: 任務寫入測試失敗")
            
            conn.commit()
            conn.close()
            
        except Exception as e:
            db_info['task_write_test'] = False
            db_info['task_write_error'] = str(e)
            self.results['errors'].append(f"任務數據庫寫入測試失敗 {db_name}: {e}")
            print(f"  ❌ {db_name}: 任務寫入測試失敗 - {e}")
    
    def _verify_service_configurations(self):
        """驗證後端服務數據庫配置"""
        print("\n⚙️  驗證後端服務數據庫配置...")
        
        # 檢查 Flask 前端配置
        self._check_flask_frontend_config()
        
        # 檢查郵件服務配置
        self._check_email_service_config()
        
        # 檢查 EQC 服務配置
        self._check_eqc_service_config()
    
    def _check_flask_frontend_config(self):
        """檢查 Flask 前端配置"""
        try:
            from frontend.config import Config, config
            
            # 測試配置類
            dev_config = config['development']
            test_config = config['testing']
            prod_config = config['production']
            
            self.results['services']['flask_frontend'] = {
                'config_loaded': True,
                'development_db': dev_config.DATABASE_URL,
                'testing_db': test_config.DATABASE_URL,
                'production_db': prod_config.DATABASE_URL,
                'status': 'ok'
            }
            
            print(f"  ✅ Flask 前端: 配置載入成功")
            print(f"    - 開發環境: {dev_config.DATABASE_URL}")
            print(f"    - 測試環境: {test_config.DATABASE_URL}")
            print(f"    - 生產環境: {prod_config.DATABASE_URL}")
            
        except Exception as e:
            self.results['services']['flask_frontend'] = {
                'config_loaded': False,
                'error': str(e),
                'status': 'error'
            }
            self.results['errors'].append(f"Flask 前端配置載入失敗: {e}")
            print(f"  ❌ Flask 前端: 配置載入失敗 - {e}")
    
    def _check_email_service_config(self):
        """檢查郵件服務配置"""
        try:
            # 嘗試導入郵件數據庫類
            from src.infrastructure.adapters.database.email_database import EmailDatabase
            
            # 測試數據庫初始化
            email_db = EmailDatabase()
            
            self.results['services']['email_service'] = {
                'database_class_loaded': True,
                'initialization_test': True,
                'status': 'ok'
            }
            
            print(f"  ✅ 郵件服務: 數據庫類載入成功")
            
        except Exception as e:
            self.results['services']['email_service'] = {
                'database_class_loaded': False,
                'error': str(e),
                'status': 'error'
            }
            self.results['errors'].append(f"郵件服務數據庫配置失敗: {e}")
            print(f"  ❌ 郵件服務: 數據庫配置失敗 - {e}")
    
    def _check_eqc_service_config(self):
        """檢查 EQC 服務配置"""
        try:
            # 嘗試導入任務狀態數據庫類
            from src.database.task_status_db import TaskStatusDB, get_task_status_db
            
            # 測試數據庫初始化
            task_db = get_task_status_db()
            
            self.results['services']['eqc_service'] = {
                'database_class_loaded': True,
                'initialization_test': True,
                'db_path': task_db.db_path,
                'status': 'ok'
            }
            
            print(f"  ✅ EQC 服務: 任務數據庫類載入成功")
            print(f"    - 數據庫路徑: {task_db.db_path}")
            
        except Exception as e:
            self.results['services']['eqc_service'] = {
                'database_class_loaded': False,
                'error': str(e),
                'status': 'error'
            }
            self.results['errors'].append(f"EQC 服務數據庫配置失敗: {e}")
            print(f"  ❌ EQC 服務: 數據庫配置失敗 - {e}")
    
    def _check_database_integrity(self):
        """檢查數據庫完整性"""
        print("\n🔍 檢查數據庫完整性...")
        
        for db_name, db_info in self.results['databases'].items():
            if not db_info['connection_test']:
                continue
                
            try:
                conn = sqlite3.connect(db_info['path'], timeout=10.0)
                
                # 執行完整性檢查
                integrity_result = conn.execute("PRAGMA integrity_check").fetchone()
                is_ok = integrity_result and integrity_result[0] == 'ok'
                
                db_info['integrity_check'] = is_ok
                db_info['integrity_result'] = integrity_result[0] if integrity_result else 'unknown'
                
                if is_ok:
                    print(f"  ✅ {db_name}: 完整性檢查通過")
                else:
                    print(f"  ❌ {db_name}: 完整性檢查失敗 - {integrity_result[0] if integrity_result else 'unknown'}")
                    self.results['errors'].append(f"數據庫完整性檢查失敗 {db_name}: {integrity_result[0] if integrity_result else 'unknown'}")
                
                conn.close()
                
            except Exception as e:
                db_info['integrity_check'] = False
                db_info['integrity_error'] = str(e)
                self.results['errors'].append(f"數據庫完整性檢查失敗 {db_name}: {e}")
                print(f"  ❌ {db_name}: 完整性檢查失敗 - {e}")
    
    def _calculate_overall_status(self):
        """計算總體狀態"""
        total_checks = 0
        passed_checks = 0
        
        # 統計數據庫檢查結果
        for db_info in self.results['databases'].values():
            if db_info['exists']:
                total_checks += 1
                if db_info['connection_test']:
                    passed_checks += 1
        
        # 統計服務檢查結果
        for service_info in self.results['services'].values():
            total_checks += 1
            if service_info['status'] == 'ok':
                passed_checks += 1
        
        # 計算通過率
        pass_rate = (passed_checks / total_checks * 100) if total_checks > 0 else 0
        
        if pass_rate >= 90:
            self.results['overall_status'] = 'excellent'
        elif pass_rate >= 75:
            self.results['overall_status'] = 'good'
        elif pass_rate >= 50:
            self.results['overall_status'] = 'fair'
        else:
            self.results['overall_status'] = 'poor'
        
        self.results['pass_rate'] = pass_rate
        self.results['total_checks'] = total_checks
        self.results['passed_checks'] = passed_checks
    
    def _generate_report(self):
        """生成驗證報告"""
        print("\n" + "=" * 60)
        print("📋 數據庫驗證報告")
        print("=" * 60)
        
        # 總體狀態
        status_emoji = {
            'excellent': '🟢',
            'good': '🟡',
            'fair': '🟠',
            'poor': '🔴'
        }
        
        emoji = status_emoji.get(self.results['overall_status'], '❓')
        print(f"總體狀態: {emoji} {self.results['overall_status'].upper()}")
        print(f"通過率: {self.results['pass_rate']:.1f}% ({self.results['passed_checks']}/{self.results['total_checks']})")
        
        # 數據庫狀態摘要
        print(f"\n📊 數據庫狀態摘要:")
        for db_name, db_info in self.results['databases'].items():
            if db_info['exists']:
                status = "✅" if db_info['connection_test'] else "❌"
                size = f"({db_info['size_bytes']:,} bytes)" if db_info['size_bytes'] > 0 else ""
                print(f"  {status} {db_name}: {db_info['path']} {size}")
        
        # 服務狀態摘要
        print(f"\n⚙️  服務狀態摘要:")
        for service_name, service_info in self.results['services'].items():
            status = "✅" if service_info['status'] == 'ok' else "❌"
            print(f"  {status} {service_name}")
        
        # 錯誤摘要
        if self.results['errors']:
            print(f"\n❌ 發現的問題 ({len(self.results['errors'])}):")
            for i, error in enumerate(self.results['errors'], 1):
                print(f"  {i}. {error}")
        else:
            print(f"\n✅ 未發現問題")
        
        # 保存詳細報告
        report_path = "logs/database_verification_report.json"
        Path("logs").mkdir(exist_ok=True)
        
        with open(report_path, 'w', encoding='utf-8') as f:
            json.dump(self.results, f, ensure_ascii=False, indent=2)
        
        print(f"\n📄 詳細報告已保存至: {report_path}")


def main():
    """主函數"""
    print("🚀 數據庫連接與完整性驗證工具")
    print("=" * 60)
    
    verifier = DatabaseVerifier()
    results = verifier.verify_all()
    
    # 返回適當的退出碼
    if results['overall_status'] in ['excellent', 'good']:
        sys.exit(0)  # 成功
    elif results['overall_status'] == 'fair':
        sys.exit(1)  # 警告
    else:
        sys.exit(2)  # 錯誤


if __name__ == "__main__":
    main()