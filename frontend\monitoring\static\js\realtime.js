/**
 * Realtime Monitoring Module
 * 即時監控模組 JavaScript
 */

class RealtimeMonitoring {
    constructor() {
        this.websocket = null;
        this.isConnected = false;
        this.metrics = {};
        this.charts = {};
        this.updateInterval = 5000; // 5秒更新一次
        this.maxDataPoints = 50; // 最多保留50個數據點
        
        this.init();
    }
    
    init() {
        console.log('Realtime Monitoring: Initializing...');
        this.initializeWebSocket();
        this.setupCharts();
        this.bindEvents();
        this.startMetricsCollection();
    }
    
    bindEvents() {
        // 連接控制按鈕
        document.addEventListener('click', (e) => {
            if (e.target.matches('.connect-btn')) {
                this.handleConnect();
            }
            if (e.target.matches('.disconnect-btn')) {
                this.handleDisconnect();
            }
        });
        
        // 刷新按鈕
        document.addEventListener('click', (e) => {
            if (e.target.matches('.refresh-metrics-btn')) {
                this.refreshMetrics();
            }
        });
        
        // 時間範圍選擇
        document.addEventListener('change', (e) => {
            if (e.target.matches('#timeRange')) {
                this.handleTimeRangeChange(e);
            }
        });
        
        // 頁面可見性變化
        document.addEventListener('visibilitychange', () => {
            if (document.hidden) {
                this.pauseMonitoring();
            } else {
                this.resumeMonitoring();
            }
        });
    }
    
    initializeWebSocket() {
        try {
            const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
            const wsUrl = `${protocol}//${window.location.host}/ws/monitoring`;
            
            this.websocket = new WebSocket(wsUrl);
            
            this.websocket.onopen = (event) => {
                console.log('WebSocket connected');
                this.isConnected = true;
                this.updateConnectionStatus(true);
            };
            
            this.websocket.onmessage = (event) => {
                const data = JSON.parse(event.data);
                this.handleWebSocketMessage(data);
            };
            
            this.websocket.onclose = (event) => {
                console.log('WebSocket disconnected');
                this.isConnected = false;
                this.updateConnectionStatus(false);
                
                // 自動重連
                setTimeout(() => {
                    this.initializeWebSocket();
                }, 5000);
            };
            
            this.websocket.onerror = (error) => {
                console.error('WebSocket error:', error);
                this.showError('WebSocket 連接錯誤');
            };
            
        } catch (error) {
            console.error('Failed to initialize WebSocket:', error);
            this.fallbackToPolling();
        }
    }
    
    handleWebSocketMessage(data) {
        switch (data.type) {
            case 'metrics':
                this.updateMetrics(data.payload);
                break;
            case 'alert':
                this.handleAlert(data.payload);
                break;
            case 'status':
                this.updateSystemStatus(data.payload);
                break;
            default:
                console.log('Unknown message type:', data.type);
        }
    }
    
    fallbackToPolling() {
        console.log('Falling back to HTTP polling');
        this.startPolling();
    }
    
    startPolling() {
        setInterval(async () => {
            try {
                await this.fetchMetrics();
            } catch (error) {
                console.error('Polling error:', error);
            }
        }, this.updateInterval);
    }
    
    async fetchMetrics() {
        try {
            const response = await fetch('/monitoring/api/metrics/realtime');
            
            if (response.ok) {
                const metrics = await response.json();
                this.updateMetrics(metrics);
            }
        } catch (error) {
            console.error('Failed to fetch metrics:', error);
        }
    }
    
    updateMetrics(newMetrics) {
        // 更新本地指標數據
        Object.keys(newMetrics).forEach(key => {
            if (!this.metrics[key]) {
                this.metrics[key] = [];
            }
            
            // 添加時間戳
            const dataPoint = {
                timestamp: new Date(),
                value: newMetrics[key]
            };
            
            this.metrics[key].push(dataPoint);
            
            // 保持數據點數量限制
            if (this.metrics[key].length > this.maxDataPoints) {
                this.metrics[key].shift();
            }
        });
        
        // 更新顯示
        this.updateMetricsDisplay(newMetrics);
        this.updateCharts();
    }
    
    updateMetricsDisplay(metrics) {
        // CPU 使用率
        this.updateMetricCard('cpu', metrics.cpu_usage, '%', this.getCPUStatus(metrics.cpu_usage));
        
        // 記憶體使用率
        this.updateMetricCard('memory', metrics.memory_usage, '%', this.getMemoryStatus(metrics.memory_usage));
        
        // 磁碟使用率
        this.updateMetricCard('disk', metrics.disk_usage, '%', this.getDiskStatus(metrics.disk_usage));
        
        // 網路流量
        this.updateMetricCard('network', this.formatBytes(metrics.network_io), '/s', 'normal');
        
        // 活躍連接數
        this.updateMetricCard('connections', metrics.active_connections, '個', this.getConnectionStatus(metrics.active_connections));
        
        // 請求數/分鐘
        this.updateMetricCard('requests', metrics.requests_per_minute, '/min', 'normal');
    }
    
    updateMetricCard(metricName, value, unit, status) {
        const card = document.querySelector(`[data-metric="${metricName}"]`);
        if (!card) return;
        
        const valueElement = card.querySelector('.metric-value');
        const statusElement = card.querySelector('.metric-status');
        
        if (valueElement) {
            valueElement.textContent = this.formatNumber(value) + unit;
        }
        
        if (statusElement) {
            statusElement.className = `metric-status status-${status}`;
            statusElement.textContent = this.getStatusText(status);
        }
        
        // 更新卡片狀態顏色
        card.className = `metric-card metric-${status}`;
    }
    
    setupCharts() {
        // CPU 使用率圖表
        this.setupCPUChart();
        
        // 記憶體使用率圖表
        this.setupMemoryChart();
        
        // 網路流量圖表
        this.setupNetworkChart();
        
        // 系統負載圖表
        this.setupLoadChart();
    }
    
    setupCPUChart() {
        const ctx = document.getElementById('cpuChart');
        if (!ctx) return;
        
        this.charts.cpu = new Chart(ctx, {
            type: 'line',
            data: {
                labels: [],
                datasets: [{
                    label: 'CPU 使用率',
                    data: [],
                    borderColor: '#007bff',
                    backgroundColor: 'rgba(0, 123, 255, 0.1)',
                    tension: 0.4,
                    fill: true
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                animation: {
                    duration: 0 // 關閉動畫以提升性能
                },
                interaction: {
                    intersect: false
                },
                scales: {
                    x: {
                        type: 'time',
                        time: {
                            displayFormats: {
                                second: 'HH:mm:ss'
                            }
                        }
                    },
                    y: {
                        beginAtZero: true,
                        max: 100,
                        ticks: {
                            callback: function(value) {
                                return value + '%';
                            }
                        }
                    }
                },
                plugins: {
                    legend: {
                        display: false
                    }
                }
            }
        });
    }
    
    setupMemoryChart() {
        const ctx = document.getElementById('memoryChart');
        if (!ctx) return;
        
        this.charts.memory = new Chart(ctx, {
            type: 'line',
            data: {
                labels: [],
                datasets: [{
                    label: '記憶體使用率',
                    data: [],
                    borderColor: '#28a745',
                    backgroundColor: 'rgba(40, 167, 69, 0.1)',
                    tension: 0.4,
                    fill: true
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                animation: { duration: 0 },
                scales: {
                    x: {
                        type: 'time',
                        time: {
                            displayFormats: {
                                second: 'HH:mm:ss'
                            }
                        }
                    },
                    y: {
                        beginAtZero: true,
                        max: 100,
                        ticks: {
                            callback: function(value) {
                                return value + '%';
                            }
                        }
                    }
                },
                plugins: {
                    legend: {
                        display: false
                    }
                }
            }
        });
    }
    
    setupNetworkChart() {
        const ctx = document.getElementById('networkChart');
        if (!ctx) return;
        
        this.charts.network = new Chart(ctx, {
            type: 'line',
            data: {
                labels: [],
                datasets: [
                    {
                        label: '輸入流量',
                        data: [],
                        borderColor: '#17a2b8',
                        backgroundColor: 'rgba(23, 162, 184, 0.1)',
                        tension: 0.4
                    },
                    {
                        label: '輸出流量',
                        data: [],
                        borderColor: '#ffc107',
                        backgroundColor: 'rgba(255, 193, 7, 0.1)',
                        tension: 0.4
                    }
                ]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                animation: { duration: 0 },
                scales: {
                    x: {
                        type: 'time',
                        time: {
                            displayFormats: {
                                second: 'HH:mm:ss'
                            }
                        }
                    },
                    y: {
                        beginAtZero: true,
                        ticks: {
                            callback: function(value) {
                                return this.formatBytes(value);
                            }.bind(this)
                        }
                    }
                }
            }
        });
    }
    
    updateCharts() {
        Object.keys(this.charts).forEach(chartName => {
            const chart = this.charts[chartName];
            const metricData = this.metrics[chartName + '_usage'] || this.metrics[chartName];
            
            if (!metricData || metricData.length === 0) return;
            
            const labels = metricData.map(point => point.timestamp);
            const data = metricData.map(point => point.value);
            
            chart.data.labels = labels;
            chart.data.datasets[0].data = data;
            
            chart.update('none'); // 不使用動畫更新
        });
    }
    
    handleAlert(alert) {
        console.log('Received alert:', alert);
        
        // 顯示警告通知
        this.showAlert(alert.message, alert.severity);
        
        // 更新警告計數器
        this.updateAlertCounter(alert.severity);
        
        // 如果是嚴重警告，可能需要特殊處理
        if (alert.severity === 'critical') {
            this.handleCriticalAlert(alert);
        }
    }
    
    handleCriticalAlert(alert) {
        // 閃爍警告指示器
        const indicator = document.querySelector('.alert-indicator');
        if (indicator) {
            indicator.classList.add('critical-blink');
            
            setTimeout(() => {
                indicator.classList.remove('critical-blink');
            }, 5000);
        }
        
        // 可選：播放警告聲音
        if (this.shouldPlayAlertSound()) {
            this.playAlertSound();
        }
    }
    
    updateSystemStatus(status) {
        const statusIndicator = document.querySelector('.system-status');
        if (statusIndicator) {
            statusIndicator.className = `system-status status-${status.level}`;
            statusIndicator.textContent = status.message;
        }
    }
    
    updateConnectionStatus(connected) {
        const indicator = document.querySelector('.connection-status');
        if (indicator) {
            indicator.className = `connection-status ${connected ? 'connected' : 'disconnected'}`;
            indicator.textContent = connected ? '已連接' : '連接中斷';
        }
        
        const connectBtn = document.querySelector('.connect-btn');
        const disconnectBtn = document.querySelector('.disconnect-btn');
        
        if (connectBtn) connectBtn.disabled = connected;
        if (disconnectBtn) disconnectBtn.disabled = !connected;
    }
    
    startMetricsCollection() {
        this.metricsInterval = setInterval(() => {
            if (!this.isConnected) {
                this.fetchMetrics();
            }
        }, this.updateInterval);
    }
    
    pauseMonitoring() {
        console.log('Pausing monitoring (tab not visible)');
        if (this.metricsInterval) {
            clearInterval(this.metricsInterval);
        }
    }
    
    resumeMonitoring() {
        console.log('Resuming monitoring (tab visible)');
        this.startMetricsCollection();
    }
    
    handleConnect() {
        if (!this.isConnected) {
            this.initializeWebSocket();
        }
    }
    
    handleDisconnect() {
        if (this.websocket) {
            this.websocket.close();
        }
    }
    
    refreshMetrics() {
        this.fetchMetrics();
        this.showSuccess('指標已更新');
    }
    
    handleTimeRangeChange(e) {
        const range = parseInt(e.target.value);
        this.maxDataPoints = range;
        
        // 調整現有數據
        Object.keys(this.metrics).forEach(key => {
            if (this.metrics[key].length > range) {
                this.metrics[key] = this.metrics[key].slice(-range);
            }
        });
        
        this.updateCharts();
    }
    
    getCPUStatus(usage) {
        if (usage > 90) return 'critical';
        if (usage > 70) return 'warning';
        return 'normal';
    }
    
    getMemoryStatus(usage) {
        if (usage > 85) return 'critical';
        if (usage > 70) return 'warning';
        return 'normal';
    }
    
    getDiskStatus(usage) {
        if (usage > 90) return 'critical';
        if (usage > 80) return 'warning';
        return 'normal';
    }
    
    getConnectionStatus(connections) {
        if (connections > 1000) return 'warning';
        if (connections > 500) return 'info';
        return 'normal';
    }
    
    getStatusText(status) {
        const statusMap = {
            'normal': '正常',
            'info': '資訊',
            'warning': '警告',
            'critical': '嚴重'
        };
        
        return statusMap[status] || status;
    }
    
    formatNumber(num) {
        if (num >= 1000000) {
            return (num / 1000000).toFixed(1) + 'M';
        }
        if (num >= 1000) {
            return (num / 1000).toFixed(1) + 'K';
        }
        return num.toFixed(1);
    }
    
    formatBytes(bytes) {
        if (bytes === 0) return '0 B';
        const k = 1024;
        const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i];
    }
    
    shouldPlayAlertSound() {
        // 檢查使用者偏好或設定
        return localStorage.getItem('alertSoundEnabled') !== 'false';
    }
    
    playAlertSound() {
        // 播放警告聲音
        const audio = new Audio('/static/sounds/alert.mp3');
        audio.play().catch(e => console.log('Cannot play alert sound:', e));
    }
    
    showSuccess(message) {
        this.showNotification(message, 'success');
    }
    
    showError(message) {
        this.showNotification(message, 'error');
    }
    
    showAlert(message, severity) {
        this.showNotification(message, severity);
    }
    
    showNotification(message, type) {
        const notification = document.createElement('div');
        notification.className = `notification notification-${type}`;
        notification.textContent = message;
        
        const container = document.querySelector('.notifications-container') || document.body;
        container.appendChild(notification);
        
        // 自動移除通知
        setTimeout(() => {
            if (notification.parentNode) {
                notification.remove();
            }
        }, 5000);
    }
}

// 頁面載入完成後初始化
document.addEventListener('DOMContentLoaded', function() {
    if (document.querySelector('.realtime-monitoring-container')) {
        new RealtimeMonitoring();
    }
});