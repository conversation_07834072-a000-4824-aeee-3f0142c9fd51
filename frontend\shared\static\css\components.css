/* 
 * 組件樣式模組
 * 包含卡片、進度條、徽章、時間軸等組件樣式
 */

/* ==================== 統一卡片基礎樣式 ==================== */
.card-base {
    background: white;
    border-radius: var(--radius-lg);
    border: 1px solid var(--border-color);
    box-shadow: var(--shadow-sm);
    transition: var(--transition);
}

.card-base:hover {
    box-shadow: var(--shadow-lg);
    transform: translateY(-2px);
}

.control-card {
    padding: var(--spacing-md);
}

.side-card {
    background: white;
    border: 1px solid var(--border-color);
    border-radius: var(--radius-md);
    padding: var(--spacing-md);
    box-shadow: var(--shadow-xs);
    height: fit-content;
}

.side-card h4 {
    margin: 0 0 var(--spacing-sm) 0;
    font-size: var(--font-size-sm);
    color: var(--secondary-color);
    display: flex;
    align-items: center;
    gap: var(--spacing-xxs);
}

.side-card h4 i {
    color: var(--primary-color);
    font-size: 16px;
}

/* ==================== 資料夾輸入卡片樣式 ==================== */
.folder-input-card {
    background: #f8f9fa;
    border: 2px dashed #dee2e6;
    border-radius: var(--radius-lg);
    padding: var(--spacing-lg);
    text-align: center;
    transition: var(--transition);
}

.folder-input-card:hover {
    border-color: var(--primary-color);
    background: #f0f4ff;
}

.folder-input-card .folder-icon {
    font-size: 2.5em;
    color: var(--primary-color);
    margin-bottom: var(--spacing-sm);
}

.folder-input-card h3 {
    color: var(--secondary-color);
    margin-bottom: var(--spacing-md);
    font-weight: 600;
}

.folder-input-card .folder-input {
    width: 100%;
    padding: var(--spacing-sm);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-sm);
    margin-bottom: var(--spacing-sm);
    font-size: 14px;
}

.folder-input-card .primary-btn {
    width: 100%;
    background: var(--primary-color);
    color: white;
    border: none;
    padding: var(--spacing-sm) var(--spacing-md);
    border-radius: var(--radius-sm);
    font-size: 14px;
    font-weight: 600;
    cursor: pointer;
    transition: var(--transition);
}

.folder-input-card .primary-btn:hover {
    background: #5a6fd8;
    transform: translateY(-1px);
}

/* ==================== 進度顯示樣式 ==================== */
.progress-display {
    padding: var(--spacing-md);
    background: var(--gradient-light);
    border-radius: var(--radius-md);
    border: 1px solid var(--border-color);
}

.progress-step {
    font-weight: 600;
    color: var(--secondary-color);
    margin-bottom: var(--spacing-xxs);
    display: flex;
    align-items: center;
    gap: var(--spacing-xxs);
}

.progress-step.processing { color: var(--primary-color); }
.progress-step.success { color: var(--success-color); }
.progress-step.error { color: var(--error-color); }

.progress-detail {
    font-size: var(--font-size-xs);
    color: var(--text-muted);
    line-height: 1.4;
}

.progress-bar-container {
    margin-top: var(--spacing-sm);
}

.progress-bar {
    height: 8px;
    background: var(--border-color);
    border-radius: var(--radius-xs);
    overflow: hidden;
    margin-bottom: 5px;
}

.progress-fill {
    height: 100%;
    background: var(--gradient-primary);
    width: 0%;
    transition: var(--transition);
}

.progress-text {
    text-align: center;
    font-size: var(--font-size-xxs);
    color: var(--text-muted);
    font-weight: 500;
}

/* ==================== 徽章系統 ==================== */
.file-type-badge {
    padding: 4px 12px;
    border-radius: 15px;
    font-size: var(--font-size-xxs);
    font-weight: bold;
    text-transform: uppercase;
}

.badge-ft {
    background: #d4edda;
    color: #155724;
}

.badge-eqc {
    background: #d1ecf1;
    color: #0c5460;
}

.badge-cta {
    background: #f8d7da;
    color: #721c24;
}

.badge-matched {
    background: #d4edda;
    color: #155724;
}

.badge-unmatched {
    background: #fff3cd;
    color: #856404;
}

.status-badge {
    margin-left: auto;
    background: #e9ecef;
    color: var(--text-muted);
    padding: 6px 12px;
    border-radius: 12px;
    font-size: var(--font-size-xs);
    font-weight: 500;
}

.status-badge.success {
    background: #d4edda;
    color: #155724;
}

.status-badge.error {
    background: #f8d7da;
    color: #721c24;
}

/* ==================== 處理指示器 ==================== */
.processing-indicator {
    display: none;
    text-align: center;
    padding: 20px;
    color: var(--primary-color);
}

.spinner {
    border: 4px solid #f3f3f3;
    border-top: 4px solid var(--primary-color);
    border-radius: 50%;
    width: 40px;
    height: 40px;
    animation: spin 1s linear infinite;
    margin: 0 auto 15px;
}

/* ==================== 空狀態 ==================== */
.empty-state {
    text-align: center;
    padding: 60px 20px;
    color: var(--text-muted);
}

.empty-state i {
    font-size: 4em;
    margin-bottom: 20px;
    opacity: 0.3;
}

/* ==================== 圖例 ==================== */
.legend {
    display: flex;
    flex-wrap: wrap;
    gap: 15px;
    margin-top: 20px;
    padding: 15px;
    background: white;
    border-radius: var(--radius-md);
    border: 1px solid var(--border-color);
}

.legend-item {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: var(--font-size-xs);
}

.legend-dot {
    width: 12px;
    height: 12px;
    border-radius: 50%;
}

.dot-matched { background: var(--success-color); }
.dot-unmatched { background: var(--warning-color); }
.dot-cta { background: var(--error-color); }
.dot-ft { background: #007bff; }
.dot-eqc { background: var(--info-color); }

/* ==================== 檔案上傳區域樣式 ==================== */
.upload-zone {
    border: 2px dashed var(--border-color);
    border-radius: var(--radius-md);
    padding: 20px;
    text-align: center;
    transition: var(--transition);
    cursor: pointer;
    background: var(--bg-light);
}

.upload-zone:hover,
.upload-zone.dragover {
    border-color: var(--primary-color);
    background: #f0f3ff;
}

.upload-zone.uploading {
    border-color: var(--warning-color);
    background: #fffef0;
}

.upload-zone i {
    font-size: 2em;
    color: var(--primary-color);
    margin-bottom: 8px;
    display: block;
}

.upload-zone p {
    margin: 0 0 4px 0;
    font-weight: 500;
    color: var(--secondary-color);
}

.upload-zone small {
    color: var(--text-muted);
    font-size: var(--font-size-xxs);
}

/* ==================== 檔案上傳選項樣式 ==================== */
.upload-options {
    padding: 10px;
    background: var(--bg-light);
    border-radius: var(--radius-sm);
    border: 1px solid var(--border-color);
}

.checkbox-container {
    display: flex;
    align-items: center;
    gap: 8px;
    cursor: pointer;
    font-size: var(--font-size-xs);
    color: var(--secondary-color);
}

.checkbox-container input[type="checkbox"] {
    width: 16px;
    height: 16px;
    accent-color: var(--primary-color);
}

.upload-mode-selector {
    margin-left: 24px;
    display: flex;
    align-items: center;
    gap: 8px;
}

/* ==================== 統計資訊樣式 ==================== */
.stats-grid-new {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 12px;
    margin: 15px 0;
}

.stat-card {
    background: white;
    border: 1px solid var(--border-color);
    border-radius: var(--radius-md);
    padding: 12px;
    text-align: center;
}

.stat-card .value {
    font-size: 1.5em;
    font-weight: bold;
    color: var(--secondary-color);
    margin-bottom: 4px;
}

.stat-card .label {
    font-size: var(--font-size-xs);
    color: var(--text-muted);
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* ==================== 額外統計資訊樣式 ==================== */
.stats-grid-extra {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 12px;
    margin-top: 15px;
}

.stat-card-small {
    background: var(--gradient-light);
    border: 1px solid #d1d9ff;
    border-radius: var(--radius-sm);
    padding: 10px;
    text-align: center;
}

.stat-card-small .value {
    font-size: 1.3em;
    font-weight: bold;
    color: var(--primary-color);
    margin-bottom: 3px;
}

.stat-card-small .label {
    font-size: var(--font-size-xxs);
    color: var(--text-muted);
    text-transform: uppercase;
    letter-spacing: 0.3px;
}

/* ==================== 模式選擇器樣式 ==================== */
.mode-selector {
    background: rgba(116, 125, 237, 0.05);
    border: 1px solid rgba(116, 125, 237, 0.2);
    border-radius: var(--radius-md);
    padding: var(--spacing-sm);
    margin-bottom: var(--spacing-sm);
}

.mode-selector label {
    cursor: pointer;
    user-select: none;
    font-weight: 500;
}

.mode-selector input[type="checkbox"] {
    width: 18px;
    height: 18px;
    accent-color: var(--primary-color);
    cursor: pointer;
}

#processing-mode-indicator {
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 11px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    transition: all 0.3s ease;
    margin-left: auto;
}

.mode-sync {
    background: linear-gradient(135deg, #74b9ff, #0984e3);
    color: white;
}

.mode-async {
    background: linear-gradient(135deg, #fd79a8, #e84393);
    color: white;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.7; }
}
